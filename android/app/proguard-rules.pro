-keep class com.zing.zalo.**{ *; }
-keep enum com.zing.zalo.**{ *; }
-keep interface com.zing.zalo.**{ *; }

# Thêm các quy tắc cho lỗi missing classes
-dontwarn com.google.android.gms.location.LocationRequest$Builder
-dontwarn com.trackasia.android.offline.OfflineManager$Companion

# Giữ lớp LocationRequest từ Google GMS
-keep class com.google.android.gms.location.** { *; }

# Giữ lớp OfflineManager từ trackasia
-keep class com.trackasia.android.** { *; }
-keep class com.trackasia.android.offline.** { *; }

# Jackson rules
-keep class com.fasterxml.jackson.** { *; }
-dontwarn com.fasterxml.jackson.databind.**
-keepattributes *Annotation*,EnclosingMethod,Signature
-keepnames class com.fasterxml.jackson.** { *; }

# DOM related rules
-dontwarn org.w3c.dom.**
-dontwarn org.w3c.dom.bootstrap.**

# Java Beans rules
-keep class java.beans.** { *; }
-dontwarn java.beans.**
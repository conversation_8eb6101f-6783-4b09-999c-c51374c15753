plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
    id "com.google.gms.google-services"
}

android {
    namespace = "com.advn.hainong"
    compileSdk = 35

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = '17'
    }

    defaultConfig {
        applicationId = "com.advn.hainong"
        minSdk = 26
        targetSdk = 35
        versionCode = flutter.versionCode
        versionName = flutter.versionName
        multiDexEnabled true
    }

    signingConfigs {
        release {
            storeFile file('../../build_info/keystore')
            storePassword 'advn@2020'
            keyAlias 'advn_cho2nong'
            keyPassword 'advn@2020'
        }
    }

    buildTypes {
        release {
            signingConfig = signingConfigs.release
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        debug {
            signingConfig = signingConfigs.debug
        }
    }
}

flutter {
    source = "../.."
}

dependencies {
    implementation "androidx.multidex:multidex:2.0.1"
    implementation 'com.google.guava:guava:33.0.0-android'
    api "me.zalo:sdk-core:4.0.1020"
    api "me.zalo:sdk-auth:4.0.1020"
    api "me.zalo:sdk-openapi:4.0.1020"
}
#!/bin/bash

# Script để sửa tất cả các plugin có sử dụng Kotlin
# Đặt tất cả về Java 1.8 để đảm bảo tương thích

echo "<PERSON>ang cập nhật tất cả các plugin Flutter..."

# Công cụ tạm thời để lưu danh sách plugin
PLUGIN_LIST=$(mktemp)

# Tạo danh sách các plugin cần kiểm tra
cd ~/.pub-cache/hosted/pub.dev
find . -name "build.gradle" | grep -v "example" | sort > $PLUGIN_LIST

# Đếm số lượng file đã sửa
count=0

echo "Đã tìm thấy $(wc -l < $PLUGIN_LIST) plugin để kiểm tra."

# Biến lưu trữ plugin được cập nhật
updated_plugins=""

while IFS= read -r file
do
  # Lấy tên plugin từ đường dẫn file
  plugin_path=$(dirname "$file")
  plugin_name=$(echo "$plugin_path" | cut -d'/' -f2)

  echo "Đang kiểm tra: $plugin_name"

  # Chỉ xử lý với file build.gradle của plugin Kotlin
  if grep -q "apply plugin: 'kotlin-android'" "$file"; then
    echo "  Đây là plugin Kotlin, đang cập nhật..."

    # Tạo một file tạm để lưu nội dung mới
    temp_file=$(mktemp)

    # Đặt lại cấu hình Java version
    cat "$file" | awk '
    {
      # Xóa các dòng hiện có về compileOptions và kotlinOptions
      if ($0 ~ /compileOptions {/ || $0 ~ /kotlinOptions {/) {
        in_options_block = 1;
        next;
      }
      
      if (in_options_block && $0 ~ /}/) {
        in_options_block = 0;
        next;
      }
      
      if (in_options_block) {
        next;
      }
      
      print $0;
      
      # Thêm cấu hình mới sau android {
      if ($0 ~ /android {/) {
        print ""
        print "    compileOptions {"
        print "        sourceCompatibility JavaVersion.VERSION_1_8"
        print "        targetCompatibility JavaVersion.VERSION_1_8"
        print "    }"
        print ""
        print "    kotlinOptions {"
        print "        jvmTarget = '\''1.8'\''"
        print "    }"
      }
    }' > "$temp_file"

    # So sánh nội dung để xem có sự thay đổi
    if ! diff -q "$file" "$temp_file" > /dev/null; then
      # Áp dụng thay đổi
      cp "$temp_file" "$file"
      echo "  ✅ Đã cập nhật thành công"
      count=$((count + 1))
      updated_plugins="$updated_plugins\n  - $plugin_name"
    else
      echo "  ⏩ Không cần cập nhật"
    fi

    # Xóa file tạm
    rm "$temp_file"
  else
    echo "  ⏩ Không phải plugin Kotlin, bỏ qua"
  fi
done < "$PLUGIN_LIST"

# Xóa file tạm
rm "$PLUGIN_LIST"

echo -e "\nHoàn tất! Đã cập nhật $count plugin."
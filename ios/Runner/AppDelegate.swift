import UIKit
import Flutter
import ZaloSDK
import CallKit
import AVFAudio
import PushKit
import flutter_callkit_incoming

@main
@objc class AppDelegate: FlutterAppDelegate, PKPushRegistryDelegate, CallkitIncomingAppDelegate {
    override func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> Bool {
        if #available(iOS 10.0, *) {
            UNUserNotificationCenter.current().delegate = self as? UNUserNotificationCenterDelegate
        }
        GeneratedPluginRegistrant.register(with: self)

        // Setup VOIP
        setupVoIP()
        
        return super.application(application, didFinishLaunchingWithOptions: launchOptions)
    }
    
    private func setupVoIP() {
        let mainQueue = DispatchQueue.main
        let voipRegistry: PKPushRegistry = PKPushRegistry(queue: mainQueue)
        voipRegistry.delegate = self
        voipRegistry.desiredPushTypes = [PKPushType.voIP]
    }
    
    override func application(_ app: UIApplication, open url: URL, options: [UIApplication.OpenURLOptionsKey : Any] = [:]) -> Bool {
        return ZDKApplicationDelegate.sharedInstance().application(app, open: url, options: options)
    }

    override func application(_ application: UIApplication,
                              continue userActivity: NSUserActivity,
                              restorationHandler: @escaping ([UIUserActivityRestoring]?) -> Void) -> Bool {
        
        guard let handleObj = userActivity.handle,
              let isVideo = userActivity.isVideo else {
            return super.application(application, continue: userActivity, restorationHandler: restorationHandler)
        }
        
        let objData = handleObj.getDecryptHandle()
        let nameCaller = objData["nameCaller"] as? String ?? ""
        let handle = objData["handle"] as? String ?? ""
        let data = flutter_callkit_incoming.Data(
            id: UUID().uuidString,
            nameCaller: nameCaller,
            handle: handle,
            type: isVideo ? 1 : 0
        )
        
        SwiftFlutterCallkitIncomingPlugin.sharedInstance?.startCall(data, fromPushKit: true)
        
        return super.application(application, continue: userActivity, restorationHandler: restorationHandler)
    }
    
    // MARK: - PKPushRegistryDelegate
    
    func pushRegistry(_ registry: PKPushRegistry, didUpdate credentials: PKPushCredentials, for type: PKPushType) {
        let deviceToken = credentials.token.map { String(format: "%02x", $0) }.joined()
        print("Device Token: \(deviceToken)")
        SwiftFlutterCallkitIncomingPlugin.sharedInstance?.setDevicePushTokenVoIP(deviceToken)
    }
    
    func pushRegistry(_ registry: PKPushRegistry, didInvalidatePushTokenFor type: PKPushType) {
        SwiftFlutterCallkitIncomingPlugin.sharedInstance?.setDevicePushTokenVoIP("")
    }
    
    func pushRegistry(_ registry: PKPushRegistry, didReceiveIncomingPushWith payload: PKPushPayload, for type: PKPushType, completion: @escaping () -> Void) {
        NSLog("==================didReceiveIncomingPushWith==================")
        debugPrint(payload.dictionaryPayload)
        NSLog("==============================================================")
        
        guard type == .voIP else { return }
        
        let data = createCallData(from: payload)
        let appName = payload.dictionaryPayload["app_name"] as? String ?? ""
        let status = payload.dictionaryPayload["status"] as? String ?? ""
        let state = UIApplication.shared.applicationState
        let activeCalls = SwiftFlutterCallkitIncomingPlugin.sharedInstance?.activeCalls() ?? []
        
        NSLog("=========Ứng dụng đang ở chế độ \(state).============")
        
        // Handle incoming call
        if appName.isEmpty && status == "actionCallIncoming" {
            if activeCalls.isEmpty {
                if state != .active {
                    SwiftFlutterCallkitIncomingPlugin.sharedInstance?.showCallkitIncoming(data, fromPushKit: true)
                } else {
                    SwiftFlutterCallkitIncomingPlugin.sharedInstance?.sendEventCustom("com.hiennv.flutter_callkit_incoming.ACTION_CALL_INCOMING", body: ["extra": data.extra])
                }
            }
            return
        }
        
        // Handle other call actions
        if !activeCalls.isEmpty {
            NSLog("=========Cuộc gọi \(activeCalls).============")
            handleActiveCallStatus(payload, data: data)
        } else if state == .active {
            NSLog("=========Ứng dụng đang ở chế độ active.============")
            handleActiveCallStatus(payload, data: data)
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) { completion() }
    }
    
    private func createCallData(from payload: PKPushPayload) -> flutter_callkit_incoming.Data {
        let nameCaller = payload.dictionaryPayload["name"] as? String ?? ""
        let handle = payload.dictionaryPayload["phone"] as? String ?? ""
        let isVideo = (payload.dictionaryPayload["callType"] as? String ?? "") == "video"
        let state = UIApplication.shared.applicationState
        
        let data = flutter_callkit_incoming.Data(
            id: UUID().uuidString,
            nameCaller: nameCaller,
            handle: handle,
            type: isVideo ? 1 : 0
        )
        data.extra = mapPayloadToExtra(payload)
        let mutableExtra = NSMutableDictionary(dictionary: data.extra)
        let stateString: String
        switch state {
            case .active:
                stateString = "onForeground"
            case .background, .inactive:
                stateString = "onBackground"
            @unknown default:
                stateString = "onForeground"
        }
        mutableExtra["where"] = stateString
        data.extra = mutableExtra
        
        return data
    }

    private func mapPayloadToExtra(_ payload: PKPushPayload) -> NSDictionary {
        let keys = ["roomName", "receiverId", "dialerId", "status", "dialerImage", "callId", "email", "callType", "name", "phone", "deviceId"]
        var extra = [String: String]()
        for key in keys { 
            extra[key] = payload.dictionaryPayload[key] as? String ?? "" 
        }
        return extra as NSDictionary
    }
    
    private func handleActiveCallStatus(_ payload: PKPushPayload, data: flutter_callkit_incoming.Data) {
        let status = payload.dictionaryPayload["status"] as? String ?? ""
        NSLog("==================Cuộc gọi \(payload)==================")
        
        let plugin = SwiftFlutterCallkitIncomingPlugin.sharedInstance
        
        switch status {
            case "actionCallStart":
                plugin?.sendEventCustom("com.hiennv.flutter_callkit_incoming.ACTION_CALL_START", body: ["extra": data.extra])
            case "actionCallEnd":
                plugin?.sendEventCustom("com.hiennv.flutter_callkit_incoming.ACTION_CALL_ENDED", body: ["extra": data.extra])
            case "actionCallDecline":
                plugin?.sendEventCustom("com.hiennv.flutter_callkit_incoming.ACTION_CALL_DECLINE", body: ["extra": data.extra])
            default:
                NSLog("Unknown status: \(status)")
        }
    }
    
    // MARK: - CallkitIncomingAppDelegate
    
    func onAccept(_ call: Call, _ action: CXAnswerCallAction) {
        action.fulfill()
        NSLog("==================onAccept=================")
    }
    
    func onDecline(_ call: Call, _ action: CXEndCallAction) {
        action.fulfill()
        NSLog("==================onDecline===============")
    }
    
    func onEnd(_ call: Call, _ action: CXEndCallAction) {
        action.fulfill()
        NSLog("==================onEnd===================")
    }
    
    func onTimeOut(_ call: Call) {
        NSLog("==================onTimout===================")
    }
    
    func didActivateAudioSession(_ audioSession: AVAudioSession) {}
    
    func didDeactivateAudioSession(_ audioSession: AVAudioSession) {}
    
    func performRequest(parameters: [String: Any], completion: @escaping (Result<Any, Error>) -> Void) {}
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>BGTaskSchedulerPermittedIdentifiers</key>
	<array>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	</array>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>2 Nông +</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>Hai Nông</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>zalo</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>zalo-2595524567729539238</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.927066340238-50i59gsl5sb97nd3sq3asu05g0md1bde</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>app-1-927066340238-ios-597d46956bf1ccf36fc2df</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>Bundle ID</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.advn.cho2nong</string>
			</array>
		</dict>
<!--
		<dict>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>fb584352923239914</string>
			</array>
		</dict>
-->
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
<!--
	<key>FacebookAppID</key>
	<string>584352923239914</string>
	<key>FacebookClientToken</key>
	<string>********************************</string>
	<key>FacebookDisplayName</key>
	<string>Hai Nông</string>
-->
	<key>FirebaseAppDelegateProxyEnabled</key>
	<true/>
	<key>FirebaseDynamicLinksCustomDomains</key>
	<array>
		<string>https://cho2nong.page.link</string>
	</array>
	<key>FirebaseScreenReportingEnabled</key>
	<true/>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>2nông</string>
<!--
		<string>fbapi</string>
		<string>fbauth</string>
		<string>fb-messenger-share-api</string>
		<string>fbauth2</string>
		<string>fbshareextension</string>
-->
		<string>zalosdk</string>
		<string>zaloshareext</string>
		<string>https</string>
		<string>http</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
		<key>NSAllowsArbitraryLoadsInWebContent</key>
		<true/>
	</dict>
	<key>NSAppleMusicUsageDescription</key>
	<string>Music!</string>
	<key>NSLocalNetworkUsageDescription</key>
    <string>Ứng dụng cần quyền truy cập vào mạng cục bộ để phục vụ việc debug và kết nối thiết bị.</string>
    <key>NSBonjourServices</key>
	<array>
		<string>_dartobservatory._tcp</string>
	</array>
	<key>NSCalendarsUsageDescription</key>
	<string>Calendars</string>
	<key>NSCameraUsageDescription</key>
	<string>Ứng dụng cần truy cập camera của bạn để tải và lưu ảnh làm: ảnh đại diện và ảnh nền của người dùng, ảnh đăng bài viết, ảnh đăng sản phẩm, ảnh chẩn đoán loại sâu bệnh</string>
	<key>NSContactsUsageDescription</key>
	<string>contacts</string>
	<key>NSDocumentDirectory</key>
	<true/>
	<key>NSFaceIDUsageDescription</key>
	<string>Ứng dụng cần quyền truy xuất sinh trắc học trên thiết bị để thực hiện tính năng đăng nhập</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Ứng dụng cần truy cập tọa độ của thiết bị này để thực hiện cho chức năng "Chẩn đoán sâu bệnh"</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>Ứng dụng cần truy cập tọa độ của thiết bị này để thực hiện cho chức năng "Chẩn đoán sâu bệnh"</string>
	<key>NSLocationUsageDescription</key>
	<string>Ứng dụng cần truy cập tọa độ của thiết bị này để thực hiện cho chức năng "Chẩn đoán sâu bệnh"</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Ứng dụng cần truy cập tọa độ của thiết bị này để thực hiện cho chức năng "Chẩn đoán sâu bệnh"</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>Ứng dụng cần truy cập máy vi âm của thiết bị này để thực hiện cho chức năng "Đọc tin tức bằng giọng nói"</string>
	<key>NSMotionUsageDescription</key>
	<string>motion</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>"Ứng dụng Hai Nông" cần truy cập bộ nhớ của bạn để tải ảnh </string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Ứng dụng cần truy cập thư viện ảnh của bạn để tải và lưu ảnh làm: ảnh đại diện và ảnh nền của người dùng, ảnh đăng bài viết, ảnh đăng sản phẩm, ảnh chẩn đoán loại sâu bệnh</string>
	<key>NSRemindersUsageDescription</key>
	<string>reminders</string>
	<key>NSSpeechRecognitionUsageDescription</key>
	<string>speech</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>processing</string>
		<string>remote-notification</string>
		<string>voip</string>
	</array>
	<key>UIFileSharingEnabled</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UIStatusBarHidden</key>
	<false/>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
	<key>UISupportsDocumentBrowser</key>
	<true/>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>ZaloAppID</key>
	<string>2595524567729539238</string>
	<key>ZaloAppKey</key>
	<string>KDEnMIG5JtkGwwMOgVRf</string>
	<key>io.flutter.embedded_views_preview</key>
	<true/>
	<key>kTCCServiceMediaLibrary</key>
	<string>media</string>
	<key>GADApplicationIdentifier</key>
    <string>ca-app-pub-9002780038101525~1497467151</string>
    <key>SKAdNetworkItems</key>
    <array>
      <dict>
        <key>SKAdNetworkIdentifier</key>
        <string>cstr6suwn9.skadnetwork</string>
      </dict>
      <dict>
        <key>SKAdNetworkIdentifier</key>
        <string>4fzdc2evr5.skadnetwork</string>
      </dict>
      <dict>
        <key>SKAdNetworkIdentifier</key>
        <string>4pfyvq9l8r.skadnetwork</string>
      </dict>
      <dict>
        <key>SKAdNetworkIdentifier</key>
        <string>2fnua5tdw4.skadnetwork</string>
      </dict>
      <dict>
        <key>SKAdNetworkIdentifier</key>
        <string>ydx93a7ass.skadnetwork</string>
      </dict>
      <dict>
        <key>SKAdNetworkIdentifier</key>
        <string>5a6flpkh64.skadnetwork</string>
      </dict>
      <dict>
        <key>SKAdNetworkIdentifier</key>
        <string>p78axxw29g.skadnetwork</string>
      </dict>
      <dict>
        <key>SKAdNetworkIdentifier</key>
        <string>v72qych5uu.skadnetwork</string>
      </dict>
      <dict>
        <key>SKAdNetworkIdentifier</key>
        <string>ludvb6z3bs.skadnetwork</string>
      </dict>
      <dict>
        <key>SKAdNetworkIdentifier</key>
        <string>cp8zw746q7.skadnetwork</string>
      </dict>
      <dict>
        <key>SKAdNetworkIdentifier</key>
        <string>3sh42y64q3.skadnetwork</string>
      </dict>
      <dict>
        <key>SKAdNetworkIdentifier</key>
        <string>c6k4g5qg8m.skadnetwork</string>
      </dict>
      <dict>
        <key>SKAdNetworkIdentifier</key>
        <string>s39g8k73mm.skadnetwork</string>
      </dict>
      <dict>
        <key>SKAdNetworkIdentifier</key>
        <string>3qy4746246.skadnetwork</string>
      </dict>
      <dict>
        <key>SKAdNetworkIdentifier</key>
        <string>f38h382jlk.skadnetwork</string>
      </dict>
      <dict>
        <key>SKAdNetworkIdentifier</key>
        <string>hs6bdukanm.skadnetwork</string>
      </dict>
      <dict>
        <key>SKAdNetworkIdentifier</key>
        <string>v4nxqhlyqp.skadnetwork</string>
      </dict>
      <dict>
        <key>SKAdNetworkIdentifier</key>
        <string>wzmmz9fp6w.skadnetwork</string>
      </dict>
      <dict>
        <key>SKAdNetworkIdentifier</key>
        <string>yclnxrl5pm.skadnetwork</string>
      </dict>
      <dict>
        <key>SKAdNetworkIdentifier</key>
        <string>t38b2kh725.skadnetwork</string>
      </dict>
      <dict>
        <key>SKAdNetworkIdentifier</key>
        <string>7ug5zh24hu.skadnetwork</string>
      </dict>
      <dict>
        <key>SKAdNetworkIdentifier</key>
        <string>gta9lk7p23.skadnetwork</string>
      </dict>
      <dict>
        <key>SKAdNetworkIdentifier</key>
        <string>vutu7akeur.skadnetwork</string>
      </dict>
      <dict>
        <key>SKAdNetworkIdentifier</key>
        <string>y5ghdn5j9k.skadnetwork</string>
      </dict>
      <dict>
        <key>SKAdNetworkIdentifier</key>
        <string>n6fk4nfna4.skadnetwork</string>
      </dict>
      <dict>
        <key>SKAdNetworkIdentifier</key>
        <string>v9wttpbfk9.skadnetwork</string>
      </dict>
      <dict>
        <key>SKAdNetworkIdentifier</key>
        <string>n38lu8286q.skadnetwork</string>
      </dict>
      <dict>
        <key>SKAdNetworkIdentifier</key>
        <string>47vhws6wlr.skadnetwork</string>
      </dict>
      <dict>
        <key>SKAdNetworkIdentifier</key>
        <string>kbd757ywx3.skadnetwork</string>
      </dict>
      <dict>
        <key>SKAdNetworkIdentifier</key>
        <string>9t245vhmpl.skadnetwork</string>
      </dict>
      <dict>
        <key>SKAdNetworkIdentifier</key>
        <string>eh6m2bh4zr.skadnetwork</string>
      </dict>
      <dict>
        <key>SKAdNetworkIdentifier</key>
        <string>a2p9lx4jpn.skadnetwork</string>
      </dict>
      <dict>
        <key>SKAdNetworkIdentifier</key>
        <string>22mmun2rn5.skadnetwork</string>
      </dict>
      <dict>
        <key>SKAdNetworkIdentifier</key>
        <string>4468km3ulz.skadnetwork</string>
      </dict>
      <dict>
        <key>SKAdNetworkIdentifier</key>
        <string>2u9pt9hc89.skadnetwork</string>
      </dict>
      <dict>
        <key>SKAdNetworkIdentifier</key>
        <string>8s468mfl3y.skadnetwork</string>
      </dict>
      <dict>
        <key>SKAdNetworkIdentifier</key>
        <string>klf5c3l5u5.skadnetwork</string>
      </dict>
      <dict>
        <key>SKAdNetworkIdentifier</key>
        <string>ppxm28t8ap.skadnetwork</string>
      </dict>
      <dict>
        <key>SKAdNetworkIdentifier</key>
        <string>ecpz2srf59.skadnetwork</string>
      </dict>
      <dict>
        <key>SKAdNetworkIdentifier</key>
        <string>uw77j35x4d.skadnetwork</string>
      </dict>
      <dict>
        <key>SKAdNetworkIdentifier</key>
        <string>pwa73g5rt2.skadnetwork</string>
      </dict>
      <dict>
        <key>SKAdNetworkIdentifier</key>
        <string>mlmmfzh3r3.skadnetwork</string>
      </dict>
      <dict>
        <key>SKAdNetworkIdentifier</key>
        <string>578prtvx9j.skadnetwork</string>
      </dict>
      <dict>
        <key>SKAdNetworkIdentifier</key>
        <string>4dzt52r2t5.skadnetwork</string>
      </dict>
      <dict>
        <key>SKAdNetworkIdentifier</key>
        <string>e5fvkxwrpn.skadnetwork</string>
      </dict>
      <dict>
        <key>SKAdNetworkIdentifier</key>
        <string>8c4e2ghe7u.skadnetwork</string>
      </dict>
      <dict>
        <key>SKAdNetworkIdentifier</key>
        <string>zq492l623r.skadnetwork</string>
      </dict>
      <dict>
        <key>SKAdNetworkIdentifier</key>
        <string>3rd42ekr43.skadnetwork</string>
      </dict>
      <dict>
        <key>SKAdNetworkIdentifier</key>
        <string>3qcr597p9d.skadnetwork</string>
      </dict>
    </array>
    <key>com.apple.security.network.client</key>
    <true/>
    <key>com.apple.security.print</key>
    <true/>
</dict>
</plist>

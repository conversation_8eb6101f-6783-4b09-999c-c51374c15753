name: printing
description: >
  Plugin that allows Flutter apps to generate and print documents to
  compatible printers on Android, iOS, macOS, Windows, and Linux,
  as well as web print.
homepage: https://github.com/DavBfr/dart_pdf/tree/master/printing
repository: https://github.com/DavBfr/dart_pdf
issue_tracker: https://github.com/DavBfr/dart_pdf/issues
version: 5.9.2

environment:
  sdk: ">=2.12.0 <3.0.0"
  flutter: ">=1.16.0"

dependencies:
  ffi: ">=1.1.0 <3.0.0"
  flutter:
    sdk: flutter
  flutter_web_plugins:
    sdk: flutter
  http: ^0.13.0
  image: ">=3.0.1 <4.0.0"
  js: ^0.6.3
  meta: ">=1.3.0 <2.0.0"
  pdf: ^3.7.2
  plugin_platform_interface: ^2.0.0

dev_dependencies:
  flutter_lints: ^1.0.4
  flutter_test:
    sdk: flutter
  mockito: ^5.0.0

_dependency_overrides:
  pdf:
    path: ../pdf

flutter:
  plugin:
    platforms:
      android:
        package: net.nfet.flutter.printing
        pluginClass: PrintingPlugin
      ios:
        pluginClass: PrintingPlugin
      linux:
        pluginClass: PrintingPlugin
      macos:
        pluginClass: PrintingPlugin
      web:
        fileName: printing_web.dart
        pluginClass: PrintingPlugin
      windows:
        pluginClass: PrintingPlugin

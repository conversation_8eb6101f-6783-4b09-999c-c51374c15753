package com.example.video_cutter_native;

import android.content.Context;
import android.media.MediaExtractor;
import android.media.MediaFormat;
import android.media.MediaMuxer;
import android.media.MediaCodec;
import android.net.Uri;
import android.os.Environment;
import android.util.Log;

import androidx.annotation.NonNull;

import java.io.File;
import java.io.FileDescriptor;
import java.io.FileInputStream;
import java.nio.ByteBuffer;

import io.flutter.embedding.engine.plugins.FlutterPlugin;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;

public class VideoCutterNativePlugin implements FlutterPlugin, MethodChannel.MethodCallHandler {
  private MethodChannel channel;
  private Context context;
  private static final String TAG = "VideoCutterNative";

  @Override
  public void onAttachedToEngine(@NonNull FlutterPluginBinding flutterPluginBinding) {
    context = flutterPluginBinding.getApplicationContext();
    channel = new MethodChannel(flutterPluginBinding.getBinaryMessenger(), "video_cutter_native");
    channel.setMethodCallHandler(this);
  }

  @Override
  public void onMethodCall(@NonNull MethodCall call, @NonNull MethodChannel.Result result) {
    if (call.method.equals("trimVideo")) {
      String inputPath = call.argument("input");
      int startMs = call.argument("startMs");
      int endMs = call.argument("endMs");
      try {
        String output = trimVideo(inputPath, startMs, endMs);
        result.success(output);
      } catch (Exception e) {
        Log.e(TAG, "Error trimming video", e);
        result.error("TRIM_ERROR", e.getMessage(), null);
      }
    } else {
      result.notImplemented();
    }
  }

  private String trimVideo(String inputPath, int startMs, int endMs) throws Exception {
    File inputFile = new File(inputPath);
    File tempFile = File.createTempFile("trimmed", ".mp4", context.getCacheDir());

    MediaExtractor extractor = new MediaExtractor();
    extractor.setDataSource(inputFile.getPath());

    MediaMuxer muxer = new MediaMuxer(tempFile.getPath(), MediaMuxer.OutputFormat.MUXER_OUTPUT_MPEG_4);
    int videoTrackIndex = -1;
    int audioTrackIndex = -1;
    int videoTrackSource = -1;
    int audioTrackSource = -1;

    // Lưu trữ thông tin orientation để áp dụng vào video output
    int rotation = 0;

    // Tìm kiếm track video và audio
    for (int i = 0; i < extractor.getTrackCount(); i++) {
      MediaFormat format = extractor.getTrackFormat(i);
      String mime = format.getString(MediaFormat.KEY_MIME);
      
      // Xử lý track video
      if (mime.startsWith("video/")) {
        videoTrackSource = i;
        // Lấy thông tin rotation nếu có
        if (format.containsKey(MediaFormat.KEY_ROTATION)) {
          rotation = format.getInteger(MediaFormat.KEY_ROTATION);
          Log.d(TAG, "Video rotation: " + rotation);
        }
        // Áp dụng rotation vào MediaFormat của track video mới
        videoTrackIndex = muxer.addTrack(format);
      }
      // Xử lý track audio (nếu có)
      else if (mime.startsWith("audio/")) {
        audioTrackSource = i;
        audioTrackIndex = muxer.addTrack(format);
      }
    }

    if (videoTrackSource < 0) {
      throw new IllegalStateException("No video track found");
    }

    // Thiết lập orientation cho video output
    if (rotation != 0) {
      muxer.setOrientationHint(rotation);
    }

    // Bắt đầu quá trình muxing
    muxer.start();

    // Xử lý video track
    if (videoTrackIndex >= 0) {
      ByteBuffer buffer = ByteBuffer.allocate(512 * 1024);
      MediaCodec.BufferInfo info = new MediaCodec.BufferInfo();
      
      extractor.selectTrack(videoTrackSource);
      extractor.seekTo(startMs * 1000, MediaExtractor.SEEK_TO_CLOSEST_SYNC);
      
      while (true) {
        int sampleSize = extractor.readSampleData(buffer, 0);
        if (sampleSize < 0 || extractor.getSampleTime() > endMs * 1000) break;
        
        info.offset = 0;
        info.size = sampleSize;
        info.presentationTimeUs = extractor.getSampleTime();
        info.flags = extractor.getSampleFlags();
        
        muxer.writeSampleData(videoTrackIndex, buffer, info);
        extractor.advance();
      }
      
      extractor.unselectTrack(videoTrackSource);
    }

    // Xử lý audio track nếu có
    if (audioTrackIndex >= 0 && audioTrackSource >= 0) {
      ByteBuffer buffer = ByteBuffer.allocate(512 * 1024);
      MediaCodec.BufferInfo info = new MediaCodec.BufferInfo();
      
      extractor.selectTrack(audioTrackSource);
      extractor.seekTo(startMs * 1000, MediaExtractor.SEEK_TO_PREVIOUS_SYNC);
      
      while (true) {
        int sampleSize = extractor.readSampleData(buffer, 0);
        if (sampleSize < 0 || extractor.getSampleTime() > endMs * 1000) break;
        
        info.offset = 0;
        info.size = sampleSize;
        info.presentationTimeUs = extractor.getSampleTime();
        info.flags = extractor.getSampleFlags();
        
        muxer.writeSampleData(audioTrackIndex, buffer, info);
        extractor.advance();
      }
      
      extractor.unselectTrack(audioTrackSource);
    }

    // Dọn dẹp tài nguyên
    muxer.stop();
    muxer.release();
    extractor.release();

    return tempFile.getAbsolutePath();
  }

  @Override
  public void onDetachedFromEngine(@NonNull FlutterPluginBinding binding) {
    channel.setMethodCallHandler(null);
  }
}

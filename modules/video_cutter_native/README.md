# Video Cutter Native Plugin

Plugin Flutter cắt video sử dụng chức năng native trên iOS và Android mà không cần sử dụng FFmpeg.

## Tính năng

- Cắt video trên Android sử dụng MediaExtractor và MediaMuxer
- Cắt video trên iOS sử dụng AVAssetExportSession
- Hỗ trợ chọn khoảng thời gian bắt đầu và kết thúc

## Cài đặt

Thêm plugin vào file `pubspec.yaml` của dự án:

```yaml
dependencies:
  video_cutter_native: ^0.0.1
```

## C<PERSON>ch sử dụng

### Cắt video

```dart
import 'package:video_cutter_native/video_cutter_native.dart';

Future<void> trimVideo() async {
  final result = await VideoCutterNative.trimVideo(
    inputPath: '/path/to/video.mp4',
    startMs: 1000, // 1 giây
    endMs: 5000,   // 5 giây
  );
  
  print('Video đã cắt và lưu tại: $result');
}
```

### Sử dụng màn hình cắt video có sẵn

```dart
import 'package:video_cutter_native/cut_video_page.dart';

// Trong widget của bạn
Navigator.of(context).push(
  MaterialPageRoute(builder: (context) => const CutVideoPage()),
);
```

## Yêu cầu

- Flutter: >=3.7.0
- Dart: >=2.19.0
- iOS: 11.0+
- Android: API 21+

## Giấy phép

MIT 
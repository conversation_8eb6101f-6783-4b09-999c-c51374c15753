import Flutter
import UIKit
import AVFoundation

public class SwiftVideoCutterNativePlugin: NSObject, FlutterPlugin {
  public static func register(with registrar: FlutterPluginRegistrar) {
    let channel = FlutterMethodChannel(name: "video_cutter_native", binaryMessenger: registrar.messenger())
    let instance = SwiftVideoCutterNativePlugin()
    registrar.addMethodCallDelegate(instance, channel: channel)
  }

  public func handle(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
    if call.method == "trimVideo" {
      guard let args = call.arguments as? [String: Any],
            let inputPath = args["input"] as? String,
            let startMs = args["startMs"] as? Int,
            let endMs = args["endMs"] as? Int else {
        result(FlutterError(code: "INVALID_ARGUMENTS", message: nil, details: nil))
        return
      }

      trimVideo(inputPath: inputPath, startMs: startMs, endMs: endMs, result: result)
    } else {
      result(FlutterMethodNotImplemented)
    }
  }

  private func trimVideo(inputPath: String, startMs: Int, endMs: Int, result: @escaping FlutterResult) {
    let asset = AVAsset(url: URL(fileURLWithPath: inputPath))
    let start = CMTimeMake(value: Int64(startMs), timescale: 1000)
    let end = CMTimeMake(value: Int64(endMs), timescale: 1000)
    let timeRange = CMTimeRangeFromTimeToTime(start: start, end: end)

    let exportSession = AVAssetExportSession(asset: asset, presetName: AVAssetExportPresetHighestQuality)
    let outputPath = NSTemporaryDirectory() + "trimmed_\(Date().timeIntervalSince1970).mp4"
    exportSession?.outputURL = URL(fileURLWithPath: outputPath)
    exportSession?.outputFileType = .mp4
    exportSession?.timeRange = timeRange

    exportSession?.exportAsynchronously {
      if exportSession?.status == .completed {
        result(outputPath)
      } else {
        result(FlutterError(code: "EXPORT_FAILED", message: exportSession?.error?.localizedDescription, details: nil))
      }
    }
  }
} 
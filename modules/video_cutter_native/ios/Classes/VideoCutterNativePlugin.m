#import "VideoCutterNativePlugin.h"
#if __has_include(<video_cutter_native/video_cutter_native-Swift.h>)
#import <video_cutter_native/video_cutter_native-Swift.h>
#else
// Support project import fallback if the generated compatibility header
// is not copied when this plugin is created as a library.
// https://forums.swift.org/t/swift-static-libraries-dont-copy-generated-objective-c-header/19816
#import "video_cutter_native-Swift.h"
#endif

@implementation VideoCutterNativePlugin
+ (void)registerWithRegistrar:(NSObject<FlutterPluginRegistrar>*)registrar {
  [SwiftVideoCutterNativePlugin registerWithRegistrar:registrar];
}
@end 
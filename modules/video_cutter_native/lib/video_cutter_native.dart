import 'dart:async';
import 'package:flutter/services.dart';

/// Plugin để cắt video sử dụng chức năng native
class VideoCutterNative {
  static const MethodChannel _channel = MethodChannel('video_cutter_native');

  /// Hàm cắt video từ đường dẫn đầu vào
  /// 
  /// [inputPath] là đường dẫn đến file video cần cắt
  /// [startMs] là điểm bắt đầu (tính bằng mili giây)
  /// [endMs] là điểm kết thúc (tính bằng mili giây)
  /// 
  /// Trả về đường dẫn của file video sau khi cắt
  static Future<String?> trimVideo({
    required String inputPath,
    required int startMs,
    required int endMs,
  }) async {
    try {
      final result = await _channel.invokeMethod<String>('trimVideo', {
        'input': inputPath,
        'startMs': startMs,
        'endMs': endMs,
      });
      return result;
    } on PlatformException catch (e) {
      print('Lỗi khi cắt video: ${e.message}');
      return null;
    }
  }
} 
name: device_info
description: Flutter plugin providing detailed information about the device
  (make, model, etc.), and Android or iOS version the app is running on.
repository: https://github.com/flutter/plugins/tree/master/packages/device_info/device_info
issue_tracker: https://github.com/flutter/flutter/issues?q=is%3Aissue+is%3Aopen+label%3A%22p%3A+device_info%22
version: 2.0.3

environment:
  sdk: ">=2.12.0 <3.0.0"

flutter:
  plugin:
    platforms:
      android:
        package: io.flutter.plugins.deviceinfo
        pluginClass: DeviceInfoPlugin
      ios:
        pluginClass: FLTDeviceInfoPlugin

dependencies:
  flutter:
    sdk: flutter
  device_info_platform_interface: ^2.0.0
dev_dependencies:
  pedantic: ^1.10.0

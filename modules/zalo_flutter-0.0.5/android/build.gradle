group 'com.tiendung01023.zalo_flutter'
version '1.0-SNAPSHOT'

buildscript {
    ext.kotlin_version = '1.9.10'
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:7.2.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'

android {
    namespace 'com.tiendung01023.zalo_flutter'
    compileSdkVersion 35

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }
    defaultConfig {
        minSdkVersion 21
        multiDexEnabled true
    }
    lintOptions {
        disable 'InvalidPackage'
    }
    buildFeatures {
        buildConfig true
    }
    
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    
    kotlinOptions {
        jvmTarget = '1.8'
    }
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation 'com.google.guava:guava:27.0.1-android'
    api "me.zalo:sdk-core:4.0.1020"
    api "me.zalo:sdk-auth:4.0.1020"
    api "me.zalo:sdk-openapi:4.0.1020"
}

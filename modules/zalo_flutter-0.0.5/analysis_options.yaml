# Specify analysis options.
#
# Until there are meta linter rules, each desired lint must be explicitly enabled.
# See: https://github.com/dart-lang/linter/issues/288
#
# For a list of lints, see: http://dart-lang.github.io/linter/lints/
# See the configuration guide for more
# https://github.com/dart-lang/sdk/tree/master/pkg/analyzer#configuring-the-analyzer
#
# There are other similar analysis options files in the flutter repos,
# which should be kept in sync with this file:
#
#   - analysis_options.yaml (this file)
#   - https://github.com/flutter/plugins/blob/master/analysis_options.yaml
#   - https://github.com/flutter/engine/blob/master/analysis_options.yaml
#   - https://github.com/flutter/packages/blob/master/analysis_options.yaml
#
# This file contains the analysis options used by Flutter tools, such as IntelliJ,
# Android Studio, and the `flutter analyze` command.

analyzer:
  strong-mode:
    implicit-casts: false
    implicit-dynamic: false
  errors:
    # treat missing required parameters as a warning (not a hint)
    missing_required_param: warning
    # treat missing returns as a warning (not a hint)
    missing_return: warning
    # allow having TODOs in the code
    todo: ignore
    # allow self-reference to deprecated members (we do this because otherwise we have
    # to annotate every member in every test, assert, etc, when we deprecate something)
    deprecated_member_use_from_same_package: ignore
    # Ignore analyzer hints for updating pubspecs when using Future or
    # Stream and not importing dart:async
    # Please see https://github.com/flutter/flutter/pull/24528 for details.
    sdk_version_async_exported_from_core: ignore
    # Turned off until null-safe rollout is complete.
    unnecessary_null_comparison: ignore
  exclude:
    - "bin/cache/**"
      # Ignore protoc generated files
    - "dev/conductor/lib/proto/*"

linter:
  rules:
    # these rules are documented on and in the same order as
    # the Dart Lint rules page to make maintenance easier
    # https://github.com/dart-lang/linter/blob/master/example/all.yaml
    - always_declare_return_types
    - always_put_control_body_on_new_line
    # - always_put_required_named_parameters_first # we prefer having parameters in the same order as fields https://github.com/flutter/flutter/issues/10219
    - always_require_non_null_named_parameters
    - always_specify_types
    # - always_use_package_imports # we do this commonly
    - annotate_overrides
    # - avoid_annotating_with_dynamic # conflicts with always_specify_types
    - avoid_bool_literals_in_conditional_expressions
    # - avoid_catches_without_on_clauses # we do this commonly
    # - avoid_catching_errors # we do this commonly
    - avoid_classes_with_only_static_members
    # - avoid_double_and_int_checks # only useful when targeting JS runtime
    - avoid_dynamic_calls
    - avoid_empty_else
    - avoid_equals_and_hash_code_on_mutable_classes
    - avoid_escaping_inner_quotes
    - avoid_field_initializers_in_const_classes
    - avoid_function_literals_in_foreach_calls
    # - avoid_implementing_value_types # not yet tested
    - avoid_init_to_null
    # - avoid_js_rounded_ints # only useful when targeting JS runtime
    - avoid_null_checks_in_equality_operators
    # - avoid_positional_boolean_parameters # not yet tested
    # - avoid_print # not yet tested
    # - avoid_private_typedef_functions # we prefer having typedef (discussion in https://github.com/flutter/flutter/pull/16356)
    # - avoid_redundant_argument_values # not yet tested
    - avoid_relative_lib_imports
    - avoid_renaming_method_parameters
    - avoid_return_types_on_setters
    # - avoid_returning_null # there are plenty of valid reasons to return null
    # - avoid_returning_null_for_future # not yet tested
    - avoid_returning_null_for_void
    # - avoid_returning_this # there are plenty of valid reasons to return this
    # - avoid_setters_without_getters # not yet tested
    - avoid_shadowing_type_parameters
    - avoid_single_cascade_in_expression_statements
    - avoid_slow_async_io
    - avoid_type_to_string
    - avoid_types_as_parameter_names
    # - avoid_types_on_closure_parameters # conflicts with always_specify_types
    - avoid_unnecessary_containers
    - avoid_unused_constructor_parameters
    - avoid_void_async
    # - avoid_web_libraries_in_flutter # not yet tested
    - await_only_futures
    - camel_case_extensions
    - camel_case_types
    - cancel_subscriptions
    # - cascade_invocations # not yet tested
    - cast_nullable_to_non_nullable
    # - close_sinks # not reliable enough
    # - comment_references # blocked on https://github.com/dart-lang/linter/issues/1142
    # - constant_identifier_names # needs an opt-out https://github.com/dart-lang/linter/issues/204
    - control_flow_in_finally
    # - curly_braces_in_flow_control_structures # not required by flutter style
    - deprecated_consistency
    # - diagnostic_describe_all_properties # not yet tested
    - directives_ordering
    # - do_not_use_environment # we do this commonly
    - empty_catches
    - empty_constructor_bodies
    - empty_statements
    - exhaustive_cases
    - file_names
    - flutter_style_todos
    - hash_and_equals
    - implementation_imports
    # - invariant_booleans # too many false positives: https://github.com/dart-lang/linter/issues/811
    - iterable_contains_unrelated_type
    # - join_return_with_assignment # not required by flutter style
    - leading_newlines_in_multiline_strings
    - library_names
    - library_prefixes
    # - library_private_types_in_public_api
    # - lines_longer_than_80_chars # not required by flutter style
    - list_remove_unrelated_type
    # - literal_only_boolean_expressions # too many false positives: https://github.com/dart-lang/sdk/issues/34181
    - missing_whitespace_between_adjacent_strings
    - no_adjacent_strings_in_list
    # - no_default_cases # too many false positives
    - no_duplicate_case_values
    - no_logic_in_create_state
    # - no_runtimeType_toString # ok in tests; we enable this only in packages/
    # - noop_primitive_operations
    - non_constant_identifier_names
    - null_check_on_nullable_type_parameter
    - null_closures
    # - omit_local_variable_types # opposite of always_specify_types
    # - one_member_abstracts # too many false positives
    # - only_throw_errors # https://github.com/flutter/flutter/issues/5792
    - overridden_fields
    - package_api_docs
    - package_names
    - package_prefixed_library_names
    # - parameter_assignments # we do this commonly
    - prefer_adjacent_string_concatenation
    - prefer_asserts_in_initializer_lists
    # - prefer_asserts_with_message # not required by flutter style
    - prefer_collection_literals
    - prefer_conditional_assignment
    - prefer_const_constructors
    - prefer_const_constructors_in_immutables
    - prefer_const_declarations
    - prefer_const_literals_to_create_immutables
    # - prefer_constructors_over_static_methods # far too many false positives
    - prefer_contains
    # - prefer_double_quotes # opposite of prefer_single_quotes
    - prefer_equal_for_default_values
    # - prefer_expression_function_bodies # conflicts with https://github.com/flutter/flutter/wiki/Style-guide-for-Flutter-repo#consider-using--for-short-functions-and-methods
    - prefer_final_fields
    - prefer_final_in_for_each
    - prefer_final_locals
    - prefer_for_elements_to_map_fromIterable
    - prefer_foreach
    - prefer_function_declarations_over_variables
    - prefer_generic_function_type_aliases
    - prefer_if_elements_to_conditional_expressions
    - prefer_if_null_operators
    - prefer_initializing_formals
    - prefer_inlined_adds
    # - prefer_int_literals # conflicts with https://github.com/flutter/flutter/wiki/Style-guide-for-Flutter-repo#use-double-literals-for-double-constants
    - prefer_interpolation_to_compose_strings
    - prefer_is_empty
    - prefer_is_not_empty
    - prefer_is_not_operator
    - prefer_iterable_whereType
    # - prefer_mixin # https://github.com/dart-lang/language/issues/32
    - prefer_null_aware_operators
    # - prefer_relative_imports # incompatible with sub-package imports
    - prefer_single_quotes
    - prefer_spread_collections
    - prefer_typing_uninitialized_variables
    - prefer_void_to_null
    - provide_deprecation_message
    # - public_member_api_docs # enabled on a case-by-case basis; see e.g. packages/analysis_options.yaml
    - recursive_getters
    - sized_box_for_whitespace
    - slash_for_doc_comments
    # - sort_child_properties_last # not yet tested
    - sort_constructors_first
    # - sort_pub_dependencies # prevents separating pinned transitive dependencies
    - sort_unnamed_constructors_first
    - test_types_in_equals
    - throw_in_finally
    - tighten_type_of_initializing_formals
    # - type_annotate_public_apis # subset of always_specify_types
    - type_init_formals
    # - unawaited_futures # too many false positives
    - unnecessary_await_in_return
    - unnecessary_brace_in_string_interps
    - unnecessary_const
    # - unnecessary_final # conflicts with prefer_final_locals
    - unnecessary_getters_setters
    # - unnecessary_lambdas # has false positives: https://github.com/dart-lang/linter/issues/498
    - unnecessary_new
    - unnecessary_null_aware_assignments
    - unnecessary_null_checks
    - unnecessary_null_in_if_null_operators
    - unnecessary_nullable_for_final_variable_declarations
    - unnecessary_overrides
    - unnecessary_parenthesis
    # - unnecessary_raw_strings # not yet tested
    - unnecessary_statements
    - unnecessary_string_escapes
    - unnecessary_string_interpolations
    - unnecessary_this
    - unrelated_type_equality_checks
    # - unsafe_html # not yet tested
    - use_full_hex_values_for_flutter_colors
    - use_function_type_syntax_for_parameters
    # - use_if_null_to_convert_nulls_to_bools # not yet tested
    - use_is_even_rather_than_modulo
    - use_key_in_widget_constructors
    - use_late_for_private_fields_and_variables
    - use_named_constants
    - use_raw_strings
    - use_rethrow_when_possible
    # - use_setters_to_change_properties # not yet tested
    # - use_string_buffers # has false positives: https://github.com/dart-lang/sdk/issues/34182
    # - use_test_throws_matchers
    # - use_to_and_as_if_applicable # has false positives, so we prefer to catch this by code-review
    - valid_regexps
    - void_checks
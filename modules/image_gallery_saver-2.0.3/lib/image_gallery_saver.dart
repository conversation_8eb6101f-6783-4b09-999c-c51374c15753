import 'dart:async';
import 'package:flutter/services.dart';

class ImageGallerySaver {

  static FutureOr<dynamic> saveImage(Uint8List imageBytes,
      {int quality = 80,
      String? name,
      bool isReturnImagePathOfIOS = false}) async {
    MethodChannel channel = const MethodChannel('image_gallery_saver');
    final result = await channel.invokeMethod('saveImageToGallery', <String, dynamic>{
      'imageBytes': imageBytes,
      'quality': quality,
      'name': name,
      'isReturnImagePathOfIOS': isReturnImagePathOfIOS
    });
    return result;
  }

  /// Save the PNG，JPG，JPEG image or video located at [file] to the local device media gallery.
  static Future saveFile(String file,
      {String? name, bool isReturnPathOfIOS = false}) async {
    MethodChannel channel = const MethodChannel('image_gallery_saver');
    final result = await channel.invokeMethod('saveFileToGallery', <String, dynamic>{
      'file': file,
      'name': name,
      'isReturnPathOfIOS': isReturnPathOfIOS
    });
    return result;
  }
}

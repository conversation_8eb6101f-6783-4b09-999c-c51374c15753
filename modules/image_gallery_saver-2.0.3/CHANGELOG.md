## 2.0.3
- 1.Upgrade flutter version to 3.10.5
- 2.Android build tools are upgraded to 7.3.0
- 3.Optimize the Android plugin library code

## 2.0.2
- 1.Optimization android plugin

## 2.0.1
- 1.Upgrade flutter version to 3.10.2
- 2.Upgrade Android/ios plug-in related
- 3.Support Android 13
- 4.Support ios16

## 1.7.1
- optimization

## 1.7.0
- optimization

## 1.6.9
- optimization

## 1.6.8
- Support android 11 save

## 1.6.7
- fix ios bug

## 1.6.6
* fix ios bug

## 1.6.5
* fix android bug

## 1.6.4
* formatted code

## 1.6.3
* Save result return more message

## 1.6.2
* fix crash on iOS when granting permission

## 1.6.1
* fix iOS Swift5.1 error

## 1.6.0
* Support iOS return save path

## 1.5.0
* Save image with JPG and Support special quality(ios & Android)
* Support special Image name for Android
* Upgrade libraries and dependence
* fix docs
* Add more example

## 1.3.0

* Define clang module for static ios builds
* Cleanup example project

## 1.2.2

* Migrate to AndroidX
* optimize git ignore

## 1.2.1

* Support return path for Android.
* Fix bug(save video fail for Android).

## 1.2.0

* Support video save and file path to gallery
* Add example for save video and net image

## 1.1.0

* Upgrade kotlin(1.3.20) and gradle build plugin version(3.3.0).

## 1.0.0

* Updated Kotlin Gradle plugin version

## 0.1.2

* Remove hard coded path - image_gallery_saver in Android

## 0.1.1

* Updated README and Description

## 0.1.0

* Updated README and CHANGELOG
* Add LICENSE
* Add Test

## 0.0.2

*  Updated README and CHANGELOG

## 0.0.1

*  Initial Open Source release.

name: video_trimmer
description: A Flutter package for trimming videos. This supports retrieving, trimming, and storage of trimmed video files to the file system.
version: 1.1.3
homepage: https://github.com/sbis04/video_trimmer

environment:
  sdk: '>=2.16.2 <3.0.0'

dependencies:
  flutter:
    sdk: flutter

  video_player: ^2.4.0
  video_thumbnail: ^0.5.0
  path_provider: ^2.0.9
  intl: ^0.17.0
  path: ^1.8.0

dev_dependencies:
  flutter_lints: ^1.0.4
  lints: ^1.0.1

flutter:
  uses-material-design: true

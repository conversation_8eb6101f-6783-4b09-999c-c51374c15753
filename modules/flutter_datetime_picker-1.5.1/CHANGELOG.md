## [0.1.0] - initial submit
## [0.1.1] - add time picker and date time picker, add i18n
## [0.1.2] - fix bugs
## [0.1.3] - fix bugs
## [0.1.4] - change screenshot path
## [0.1.5] - change screenshot path
## [0.1.6] - sync
## [0.1.7] - pub.dartlang.org bug, re-submit
## [0.1.8] - update description
## [0.1.9] - update description
## [0.1.10] - update description
## [1.0.0] - release stable version
## [1.0.1] - fix day bug when changing month
## [1.0.2] - add Dutch support
## [1.0.3] - add Dutch in demo
## [1.0.4] - add Dutch in readme
## [1.0.5] - add max/min time limit for Date Picker, add theme
## [1.0.6] - add comments
## [1.0.7] - fix max/min time for datetime type picker
## [1.0.8] - add Russian
## [1.0.9] - fix readme
## [1.1.0] - fix readme
## [1.1.1] - add 3 more languages
## [1.1.2] - add Portuguese
## [1.1.3] - update date picker format
## [1.1.4] - add 2 more languages
## [1.1.5] - add Japanese
## [1.1.6] - update something
## [1.1.7] - update something
## [1.1.8] - update something
## [1.1.9] - add German
## [1.2.0] - add support for time zone
## [1.2.1] - add more languages
## [1.2.2] - add customize example
## [1.2.3] - fix confirm pop issue
## [1.2.4] - fix format
## [1.2.5] - add more languages
## [1.2.6] - add more languages
## [1.2.7] - add max/min time for date time picker
## [1.2.8] - fix bug
## [1.3.0] - fix many bugs
## [1.3.1] - add header color
## [1.3.2] - add more languages
## [1.3.4] - add more languages
## [1.3.5] - add 12 hour time picker with AM/PM
## [1.3.6] - fix error with Diagnosticable in newer Flutter versions
## [1.3.7] - fix some issues
## [1.3.8] - add Swedish language and fix issues
## [1.4.0] - support flutter 1.2.0
## [1.5.0] - so much update
## [1.5.1] - null safety
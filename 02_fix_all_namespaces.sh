#!/bin/bash

PUB_CACHE_DIR="${PUB_CACHE_DIR:-$HOME/.pub-cache/hosted/pub.dev}"

# Đếm số lượng file đã sửa
fixed_count=0

# Hàm để thêm namespace vào build.gradle
add_namespace() {
  local build_gradle="$1"
  local module_dir=$(dirname $(dirname "$build_gradle"))
  
  echo "Đang kiểm tra: $build_gradle"

  # Lấy tên package từ build.gradle hoặc AndroidManifest.xml
  local package_name=""
  
  # Thử lấy từ defaultConfig nếu có
  if grep -q "applicationId\|group" "$build_gradle"; then
    package_name=$(grep -o 'applicationId ["\047][^"\047]*["\047]\|group ["\047][^"\047]*["\047]' "$build_gradle" | head -1 | sed 's/applicationId //;s/group //;s/["\047]//g')
  fi
  
  # Nếu không có, thử lấy từ AndroidManifest.xml
  if [ -z "$package_name" ]; then
    # Tìm tất cả các file AndroidManifest.xml trong thư mục module
    manifest_files=$(find "$module_dir" -name "AndroidManifest.xml" -type f | grep -v "example" | grep -v "test")
    
    # Đọc package từ file AndroidManifest.xml đầu tiên tìm thấy
    for manifest in $manifest_files; do
      if [ -f "$manifest" ]; then
        local pkg=$(grep -o 'package="[^"]*"' "$manifest" | head -1 | sed 's/package="//;s/"//g')
        if [ -n "$pkg" ]; then
          package_name="$pkg"
          echo "  Lấy package từ manifest: $manifest"
          break
        fi
      fi
    done
  fi

  # Thêm namespace vào build.gradle (luôn ghi đè lên namespace cũ nếu có)
  echo "  Thêm/cập nhật namespace '$package_name' vào $build_gradle"
  
  # Kiểm tra xem namespace đã tồn tại hay chưa
  if grep -A10 "android {" "$build_gradle" | grep -q "namespace ['\"].*['\"]"; then
    # Nếu đã có namespace, thay thế nó
    #sed -i '' -E "s/namespace ['\"][^'\"]*['\"]*/namespace '$package_name'/" "$build_gradle"
    echo "  ✅ Đã cập nhật namespace thành '$package_name' trong $build_gradle"
  else
    # Nếu chưa có namespace, thêm mới
    awk -v pkg="$package_name" '
    /android {/ {
      print $0
      print "    namespace \047" pkg "\047"
      next
    }
    { print }
    ' "$build_gradle" > "$build_gradle.tmp"
    
    # Di chuyển file tạm thành file chính
    mv "$build_gradle.tmp" "$build_gradle"
    echo "  ✅ Đã thêm namespace mới '$package_name' vào $build_gradle"
  fi
  
  fixed_count=$((fixed_count + 1))
  return 0
}

echo "=== Bắt đầu quét và sửa namespace cho các thư viện ==="

# Tìm tất cả các tệp build.gradle trong .pub-cache
find "$PUB_CACHE_DIR" -path "*/android/build.gradle" -type f | grep -v "/example/" | while read -r build_gradle; do
  add_namespace "$build_gradle"
done

# Xóa import GradleException không cần thiết từ app/build.gradle
if grep -q "import org.gradle.api.GradleException" "android/app/build.gradle"; then
  echo -e "\n=== Sửa lỗi GradleException trong app/build.gradle ==="

  # Tạo file tạm không có dòng import
  grep -v "import org.gradle.api.GradleException" "android/app/build.gradle" > "android/app/build.gradle.tmp"
  
  # Di chuyển file tạm thành file chính
  mv "android/app/build.gradle.tmp" "android/app/build.gradle"
  
  echo "✅ Đã xóa import GradleException không cần thiết"
  fixed_count=$((fixed_count + 1))
fi

echo -e "\n=== Hoàn tất quá trình thêm namespace ==="
echo "Đã sửa $fixed_count file build.gradle"
#!/bin/bash

# Script để sửa lỗi tương thích giữa Java và Kotlin trong các thư viện Flutter

echo "Đang bắt đầu quy trình sửa lỗi tương thích giữa Java và Kotlin cho các thư viện..."

# Danh sách các thư viện có thể gây ra lỗi
plugins=(
  "qr_code_scanner-1.0.1"
  "photo_manager-3.6.4"
  "video_compress-3.1.1"
)

# Đường dẫn đến thư mục .pub-cache
PUB_CACHE_DIR="$HOME/.pub-cache/hosted/pub.dev"

# Đếm số lượng file đã sửa
count=0

for plugin in "${plugins[@]}"; do
  if [ -d "$PUB_CACHE_DIR/$plugin" ]; then
    echo "Đang kiểm tra plugin: $plugin"
    
    # Đường dẫn đến file build.gradle của plugin
    BUILD_GRADLE="$PUB_CACHE_DIR/$plugin/android/build.gradle"
    
    if [ -f "$BUILD_GRADLE" ]; then
      # Kiểm tra xem file đã có cấu hình kotlinOptions chưa
      if ! grep -q "kotlinOptions" "$BUILD_GRADLE"; then
        echo "Đang sửa file build.gradle cho plugin $plugin..."
        
        # Tạo file tạm để lưu nội dung mới
        tmp_file=$(mktemp)
        
        # Đọc file gốc vào biến
        build_gradle_content=$(cat "$BUILD_GRADLE")
        
        # Đảm bảo compileOptions có trong file
        if ! grep -q "compileOptions" "$BUILD_GRADLE"; then
          # Thêm cấu hình compileOptions và kotlinOptions vào cuối của block android
          # Tìm vị trí cuối của block android
          modified_content=$(echo "$build_gradle_content" | awk '
          {
            print $0;
            if ($0 ~ /buildFeatures {/) {
              in_build_features = 1;
            }
            if (in_build_features && $0 ~ /}/) {
              in_build_features = 0;
              print "    ";
              print "    compileOptions {";
              print "        sourceCompatibility JavaVersion.VERSION_1_8";
              print "        targetCompatibility JavaVersion.VERSION_1_8";
              print "    }";
              print "    ";
              print "    kotlinOptions {";
              print "        jvmTarget = '\''1.8'\''";
              print "    }";
            }
          }')
          
          # Nếu không tìm thấy buildFeatures, thêm vào trước dependencies
          if [ "$modified_content" = "$build_gradle_content" ]; then
            modified_content=$(echo "$build_gradle_content" | awk '
            {
              if ($0 ~ /^dependencies {/) {
                print "    compileOptions {";
                print "        sourceCompatibility JavaVersion.VERSION_1_8";
                print "        targetCompatibility JavaVersion.VERSION_1_8";
                print "    }";
                print "    ";
                print "    kotlinOptions {";
                print "        jvmTarget = '\''1.8'\''";
                print "    }";
                print "";
              }
              print $0;
            }')
          fi
        else
          # Chỉ thêm kotlinOptions sau compileOptions
          modified_content=$(echo "$build_gradle_content" | awk '
          {
            print $0;
            if ($0 ~ /compileOptions {/) {
              in_compile_options = 1;
            }
            if (in_compile_options && $0 ~ /}/) {
              in_compile_options = 0;
              print "    ";
              print "    kotlinOptions {";
              print "        jvmTarget = '\''1.8'\''";
              print "    }";
            }
          }')
        fi
        
        # Ghi nội dung đã sửa vào file tạm
        echo "$modified_content" > "$tmp_file"
        
        # So sánh nội dung trước và sau khi sửa
        if ! diff -q "$BUILD_GRADLE" "$tmp_file" > /dev/null; then
          # Nếu có sự khác biệt, áp dụng thay đổi
          cp "$tmp_file" "$BUILD_GRADLE"
          echo "✅ Đã sửa thành công file build.gradle cho $plugin"
          count=$((count + 1))
        else
          echo "⚠️ Không thể sửa file build.gradle cho $plugin"
        fi
        
        # Xóa file tạm
        rm "$tmp_file"
      else
        echo "⏩ Plugin $plugin đã có cấu hình kotlinOptions"
      fi
    else
      echo "⚠️ Không tìm thấy file build.gradle cho plugin $plugin"
    fi
  else
    echo "⚠️ Không tìm thấy plugin $plugin trong .pub-cache"
  fi
done

echo "Hoàn tất! Đã sửa $count file build.gradle."

# Đề xuất các bước tiếp theo
echo "Vui lòng chạy các lệnh sau để hoàn tất quá trình:"
echo "  flutter clean"
echo "  flutter pub get"
echo "  flutter run" 
{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
  
    {
      "name": "2NongAppMainV4",
      "request": "launch",
      "type": "dart",
      "program": "lib/main.dart",

    },
    {
      "name": "2NongAppMainV4 (profile mode)",
      "request": "launch",
      "type": "dart",
      "flutterMode": "profile"
    },
    {
      "name": "2NongAppMainV4 (release mode)",
      "request": "launch",
      "type": "dart",
      "flutterMode": "release"
    },
    {
      "name": "device_info-2.0.3",
      "cwd": "modules/device_info-2.0.3",
      "request": "launch",
      "type": "dart"
    },
    {
      "name": "device_info-2.0.3 (profile mode)",
      "cwd": "modules/device_info-2.0.3",
      "request": "launch",
      "type": "dart",
      "flutterMode": "profile"
    },
    {
      "name": "device_info-2.0.3 (release mode)",
      "cwd": "modules/device_info-2.0.3",
      "request": "launch",
      "type": "dart",
      "flutterMode": "release"
    },
    {
      "name": "flutter_datetime_picker-1.5.1",
      "cwd": "modules/flutter_datetime_picker-1.5.1",
      "request": "launch",
      "type": "dart"
    },
    {
      "name": "flutter_datetime_picker-1.5.1 (profile mode)",
      "cwd": "modules/flutter_datetime_picker-1.5.1",
      "request": "launch",
      "type": "dart",
      "flutterMode": "profile"
    },
    {
      "name": "flutter_datetime_picker-1.5.1 (release mode)",
      "cwd": "modules/flutter_datetime_picker-1.5.1",
      "request": "launch",
      "type": "dart",
      "flutterMode": "release"
    },
    {
      "name": "flutter_speech-2.0.1",
      "cwd": "modules/flutter_speech-2.0.1",
      "request": "launch",
      "type": "dart"
    },
    {
      "name": "flutter_speech-2.0.1 (profile mode)",
      "cwd": "modules/flutter_speech-2.0.1",
      "request": "launch",
      "type": "dart",
      "flutterMode": "profile"
    },
    {
      "name": "flutter_speech-2.0.1 (release mode)",
      "cwd": "modules/flutter_speech-2.0.1",
      "request": "launch",
      "type": "dart",
      "flutterMode": "release"
    },
    {
      "name": "image_gallery_saver-2.0.3",
      "cwd": "modules/image_gallery_saver-2.0.3",
      "request": "launch",
      "type": "dart"
    },
    {
      "name": "image_gallery_saver-2.0.3 (profile mode)",
      "cwd": "modules/image_gallery_saver-2.0.3",
      "request": "launch",
      "type": "dart",
      "flutterMode": "profile"
    },
    {
      "name": "image_gallery_saver-2.0.3 (release mode)",
      "cwd": "modules/image_gallery_saver-2.0.3",
      "request": "launch",
      "type": "dart",
      "flutterMode": "release"
    },
    {
      "name": "modal_bottom_sheet-2.1.2",
      "cwd": "modules/modal_bottom_sheet-2.1.2",
      "request": "launch",
      "type": "dart"
    },
    {
      "name": "modal_bottom_sheet-2.1.2 (profile mode)",
      "cwd": "modules/modal_bottom_sheet-2.1.2",
      "request": "launch",
      "type": "dart",
      "flutterMode": "profile"
    },
    {
      "name": "modal_bottom_sheet-2.1.2 (release mode)",
      "cwd": "modules/modal_bottom_sheet-2.1.2",
      "request": "launch",
      "type": "dart",
      "flutterMode": "release"
    },
    {
      "name": "package_info-2.0.2",
      "cwd": "modules/package_info-2.0.2",
      "request": "launch",
      "type": "dart"
    },
    {
      "name": "package_info-2.0.2 (profile mode)",
      "cwd": "modules/package_info-2.0.2",
      "request": "launch",
      "type": "dart",
      "flutterMode": "profile"
    },
    {
      "name": "package_info-2.0.2 (release mode)",
      "cwd": "modules/package_info-2.0.2",
      "request": "launch",
      "type": "dart",
      "flutterMode": "release"
    },
    {
      "name": "printing-5.9.2",
      "cwd": "modules/printing-5.9.2",
      "request": "launch",
      "type": "dart"
    },
    {
      "name": "printing-5.9.2 (profile mode)",
      "cwd": "modules/printing-5.9.2",
      "request": "launch",
      "type": "dart",
      "flutterMode": "profile"
    },
    {
      "name": "printing-5.9.2 (release mode)",
      "cwd": "modules/printing-5.9.2",
      "request": "launch",
      "type": "dart",
      "flutterMode": "release"
    },
    {
      "name": "video_cutter_native",
      "cwd": "modules/video_cutter_native",
      "request": "launch",
      "type": "dart"
    },
    {
      "name": "video_cutter_native (profile mode)",
      "cwd": "modules/video_cutter_native",
      "request": "launch",
      "type": "dart",
      "flutterMode": "profile"
    },
    {
      "name": "video_cutter_native (release mode)",
      "cwd": "modules/video_cutter_native",
      "request": "launch",
      "type": "dart",
      "flutterMode": "release"
    },
    {
      "name": "video_trimmer-1.1.3",
      "cwd": "modules/video_trimmer-1.1.3",
      "request": "launch",
      "type": "dart"
    },
    {
      "name": "video_trimmer-1.1.3 (profile mode)",
      "cwd": "modules/video_trimmer-1.1.3",
      "request": "launch",
      "type": "dart",
      "flutterMode": "profile"
    },
    {
      "name": "video_trimmer-1.1.3 (release mode)",
      "cwd": "modules/video_trimmer-1.1.3",
      "request": "launch",
      "type": "dart",
      "flutterMode": "release"
    },
    {
      "name": "zalo_flutter-0.0.5",
      "cwd": "modules/zalo_flutter-0.0.5",
      "request": "launch",
      "type": "dart"
    },
    {
      "name": "zalo_flutter-0.0.5 (profile mode)",
      "cwd": "modules/zalo_flutter-0.0.5",
      "request": "launch",
      "type": "dart",
      "flutterMode": "profile"
    },
    {
      "name": "zalo_flutter-0.0.5 (release mode)",
      "cwd": "modules/zalo_flutter-0.0.5",
      "request": "launch",
      "type": "dart",
      "flutterMode": "release"
    }
  ]
}
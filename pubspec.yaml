name: hainong
description: <PERSON>
publish_to: 'none'
version: 1.0.76+109
environment:
  sdk: ">=2.17.0 <4.0.0"

dependency_overrides:
  device_info:
    path: modules/device_info-2.0.3
  image_gallery_saver:
    path: modules/image_gallery_saver-2.0.3
  modal_bottom_sheet:
    path: modules/modal_bottom_sheet-2.1.2
  package_info:
    path: modules/package_info-2.0.2

  animated_text_kit: 4.2.1
  collection: 1.19.1
  device_info_plus: 11.3.0
  dio: 5.3.2
  file_picker: 10.1.6
  firebase_auth_platform_interface: 6.3.2
  firebase_core_platform_interface: 4.5.3
  ffi: 2.0.1
  flutter_inappwebview: 6.1.5
  flutter_local_notifications: 9.9.1
  geolocator_android: 4.1.7
  http: 1.3.0
  image: 4.5.4
  image_picker: 1.1.2
  intl: 0.18.0
  meta: 1.16.0
  photo_manager: 3.6.4
  pointer_interceptor: ^0.10.1+1
  shared_preferences: 2.3.3
  url_launcher: 6.1.0
  uuid: 4.5.1
  youtube_player_flutter: 9.0.4
  webview_flutter: 3.0.4
  wechat_picker_library: 1.0.0
  web: 1.1.0
  win32: 5.5.4

dependencies:
  flutter:
    sdk: flutter

  flutter_datetime_picker:
    path: modules/flutter_datetime_picker-1.5.1
  # flutter_quill:
  #   path: modules/flutter_quill-11.4.1
  flutter_speech:
    path: modules/flutter_speech-2.0.1
  printing:
    path: modules/printing-5.9.2
  video_cutter_native:
    path: modules/video_cutter_native
  video_trimmer:
    path: modules/video_trimmer-1.1.3
  zalo_flutter:
    path: modules/zalo_flutter-0.0.5

  advn_video: 0.1.0
  cached_network_image: 3.3.0
  calendar_date_picker2: ^1.0.6
  camera: 0.10.3+2
  carousel_slider: 5.0.0
  crop_image: 1.0.6
  dotted_border: ^2.0.0
  dropdown_button2: ^2.3.9
  expandable: 5.0.1
  external_path: 2.2.0
  firebase_core: 1.20.0
  firebase_analytics: 9.3.8
  firebase_auth: 3.3.8
  firebase_messaging: 12.0.0
  firebase_dynamic_links: 4.3.11
  flutter_animated_splash: 0.0.10
  flutter_bloc: ^8.0.1
  #flutter_facebook_auth: 7.1.2
  flutter_html: 3.0.0
  flutter_markdown: 0.6.14
  #flutter_native_splash: 2.2.18
  flutter_screenutil: 5.9.3
  flutter_staggered_grid_view: 0.7.0
  geolocator: 9.0.2
  #google_fonts: 6.2.1
  #google_mobile_ads: 2.0.0
  google_sign_in: 5.2.4
  graphic: 0.6.2
  hainong_chat_call_module: 0.0.31
  html_editor_enhanced: 2.6.0
  http_parser: 4.0.0
  just_audio: 0.9.46
  local_auth: 2.1.6
  location: ^5.0.0
  number_paginator: ^0.3.2
  permission_handler: 12.0.0+1
  photo_manager_image_provider: ^2.1.1
  photo_view: ^0.14.0
  qr_code_scanner: 1.0.1
  sentry_flutter: 8.14.2
  sign_in_with_apple: 7.0.1
  share_plus: 11.0.0
  textfield_tags: ^3.0.1
  trackasia_gl: 2.0.3
  video_compress: 3.1.1
  vsc_quill_delta_to_html: ^1.0.3
  webviewx: 0.2.2
  wechat_assets_picker: 9.0.0
  shimmer_animation: ^2.2.2
  flutter_slidable: ^3.1.2

dev_dependencies:
  flutter_lints: 4.0.0

flutter:
  uses-material-design: true
  assets:
    - assets/
    - assets/html/
    - assets/json/
    - assets/images/
    - assets/images/v2/
    - assets/images/v5/
    - assets/images/v7/
    - assets/images/v8/
    - assets/images/v9/
    - assets/images/v9/map/
    - assets/images/v9/map/weather/
    - assets/images/v9/map/pet/
    - assets/images/v10/
    - assets/images/v11/
    - assets/images/v12/
    - assets/images/chatbot/
    - assets/videos/
    - assets/sounds/

  fonts:
    - family: Roboto
      fonts:
        - asset: assets/fonts/Roboto-ExtraBold.ttf
          weight: 800
        - asset: assets/fonts/Roboto-SemiBold.ttf
          weight: 700
        - asset: assets/fonts/Roboto-Bold.ttf
          weight: 600
        - asset: assets/fonts/Roboto-Medium.ttf
          weight: 500
        - asset: assets/fonts/Roboto-Regular.ttf
          weight: 400
        - asset: assets/fonts/Roboto-Light.ttf
          weight: 300
        - asset: assets/fonts/Roboto-ExtraLight.ttf
          weight: 200
        - asset: assets/fonts/Roboto-Thin.ttf
          weight: 100
        - asset: assets/fonts/Roboto-Italic.ttf
#!/bin/bash

# Script này xóa thuộc tính package từ tất cả AndroidManifest.xml trong pub cache
# để tránh lỗi "Setting the namespace via the package attribute in the source AndroidManifest.xml is no longer supported"

# Đếm số lượng file đã sửa
fixed_count=0

# Hàm để xóa thuộc tính package từ AndroidManifest.xml
fix_manifest() {
  local manifest_file="$1"

  echo "Đang kiểm tra: $manifest_file"

  # Kiểm tra xem file có chứa thuộc tính package không
  if grep -q 'package="[^"]*"' "$manifest_file"; then
    # Lấy tên package để lưu trữ (có thể cần để tham khảo sau này)
    local package_name=$(grep -o 'package="[^"]*"' "$manifest_file" | sed 's/package="//;s/"//g')
    echo "  Package cũ: $package_name"

    # Xóa thuộc tính package từ manifest tag - sử dụng cách xử lý mới
    # Đ<PERSON>c nội dung file vào biến
    local content=$(cat "$manifest_file")

    # Xử lý bằng perl thay vì sed để có regex mạnh hơn
    perl -i -pe 's/(<manifest[^>]*)\s+package="[^"]*"([^>]*>)/$1$2/g' "$manifest_file"

    # Kiểm tra xem package đã được xóa chưa
    if grep -q 'package="[^"]*"' "$manifest_file"; then
      echo "  ⚠️ Không thể xóa thuộc tính package bằng perl, thử cách khác..."

      # Dùng awk để xử lý trường hợp perl không xử lý được
      awk '{gsub(/package="[^"]*"/, ""); print}' "$manifest_file" > "$manifest_file.tmp"
      mv "$manifest_file.tmp" "$manifest_file"

      # Kiểm tra lại
      if grep -q 'package="[^"]*"' "$manifest_file"; then
        echo "  ❌ Không thể xóa thuộc tính package từ $manifest_file"
        return 0
      fi
    fi

    echo "  ✅ Đã xóa thuộc tính package từ $manifest_file"

    # Hiển thị nội dung file sau khi sửa để kiểm tra
    echo "  Nội dung file sau khi sửa (10 dòng đầu):"
    head -n 10 "$manifest_file"

    return 1  # Trả về 1 nếu đã sửa
  else
    echo "  ✅ $manifest_file không có thuộc tính package"
    return 0  # Trả về 0 nếu không cần sửa
  fi
}

echo "=== Bắt đầu sửa AndroidManifest.xml trong pub cache ==="

# Lưu và đếm số lượng file đã được sửa
for manifest_file in $(find ~/.pub-cache/hosted/pub.dev/ -path "*/android/src/main/AndroidManifest.xml" | grep -v "/example/"); do
  fix_manifest "$manifest_file"
  # Ghi nhận kết quả của lệnh fix_manifest
  result=$?
  if [ $result -eq 1 ]; then
    fixed_count=$((fixed_count + 1))
  fi
done

echo -e "\n=== Hoàn tất quá trình sửa AndroidManifest.xml ==="
echo "Đã sửa $fixed_count AndroidManifest.xml"
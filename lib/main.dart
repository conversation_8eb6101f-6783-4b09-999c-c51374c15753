import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:chat_call_core/call_core.dart';
import 'package:chat_call_core/presentation/chat/models/stream_response_model.dart';
import 'package:chat_call_core/shared/helper/call_helper.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
//import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:flutter_animated_splash/flutter_animated_splash.dart' as splash;
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:hainong/splash_page.dart';
import 'package:hainong_chat_call_module/chat_call_core.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:package_info/package_info.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
// import 'package:flutter_quill/flutter_quill.dart';

import 'common/database_helper.dart';
import 'common/import_lib_system.dart';
import 'common/ui/import_lib_base_ui.dart';
import 'features/main/bloc/main_bloc.dart';

class PostHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(context) => super.createHttpClient(context)
    ..badCertificateCallback = (X509Certificate cert, String host, int port) => true;
}

void main() async {
  HttpOverrides.global = PostHttpOverrides();
  WidgetsFlutterBinding.ensureInitialized();
  MultiLanguage.setLanguage(setEnv: true, setLogin: true, setSplashBG: true);
  DBHelper.initDB();
  //MobileAds.instance.initialize();
  final env = (await SharedPreferences.getInstance()).getString('env')??'';
  if (env.isEmpty) {
    SentryFlutter.init((options) async {
      options.dsn = 'https://<EMAIL>/7';
      options.environment = 'live_${(await PackageInfo.fromPlatform()).version}';
      options.sampleRate = 0.1;
      options.tracesSampleRate = 0.1;
      options.enableAutoSessionTracking = false;
      options.diagnosticLevel = SentryLevel.error;
      Sentry.configureScope((scope) => scope.level = SentryLevel.error);
    }, appRunner: () => runApp(MyApp()));
  } else {
    SentryFlutter.init((options) async {
      options.dsn = 'https://<EMAIL>/7';
      options.environment = 'dev';
      options.sampleRate = 0.1;
      options.tracesSampleRate = 0.1;
      options.enableAutoSessionTracking = false;
      options.diagnosticLevel = SentryLevel.error;
      options.debug = false;
      Sentry.configureScope((scope) => scope.level = SentryLevel.error);
    }, appRunner: () => runApp(MyApp()));
  }
  //runApp(MyApp());
}

class MyApp extends StatefulWidget {
  final _myAppState = _MyAppState();
  MyApp({Key? key}) : super(key: key) {
    _initFirebase();
  }

  @override
  State<StatefulWidget> createState() => _myAppState;

  void onMessageOpen(RemoteMessage message) => _myAppState.onMessageOpen(message);

  void onMessage(RemoteMessage message) => _myAppState.onMessage(message);

  //hainong_chat_call_module
  void onCallSuggest(StreamResponseModel response, bool isSuggestCall) => _myAppState.onCallSuggest(response, isSuggestCall);

  Future<void> _initFirebase() async {
    //hainong_chat_call_module
    await ChatCallCore.initGetIt();
    Firebase.initializeApp().then((value) {
      FirebaseMessaging.instance.requestPermission();
      FirebaseMessaging.instance.setForegroundNotificationPresentationOptions(alert: true, badge: true, sound: true);
      FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) async {
        Timer(const Duration(milliseconds: 2000), () => onMessageOpen(message));
        if (Util.isNotifyCall(message)) await fcmCallMessageCallBack(message, where: "onOpenApp", callBack: (response) => onCallSuggest(response, true));
      });
      FirebaseMessaging.onMessage.listen((RemoteMessage message) async {
        if (Platform.isAndroid && !Util.isNotifyCall(message)) {
          final localNotify = FlutterLocalNotificationsPlugin();
          localNotify.show(
              message.notification.hashCode,
              message.notification?.title,
              message.notification?.body,
              const NotificationDetails(
                  android: AndroidNotificationDetails('high_importance_channel',
                      'High Importance Notifications',
                      channelDescription:
                      'This channel is used for important notifications.',
                      color: StyleCustom.primaryColor,
                      importance: Importance.high,
                      priority: Priority.high,
                      icon: 'ic_notification')),
              payload: jsonEncode(message.data));
        }
        onMessage(message);
        //hainong_chat_call_module
        if (Util.isNotifyCall(message)) await fcmCallMessageCallBack(message, where: "onMessage", callBack: (response) => onCallSuggest(response, true));
      });
      //hainong_chat_call_module
      FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);
      FirebaseAnalytics.instance.setAnalyticsCollectionEnabled(true);
    });
  }
}

class _MyAppState extends State<MyApp> {
  final _bloc = MainBloc(ChangeIndexState());
  final _navigatorKey = GlobalKey<NavigatorState>();

  @override
  void dispose() {
    //Util().logEventGA('custom_close_app');
    _bloc.close();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _bloc.stream.listen((state) {
      if (state is ClosePopupState) UtilUI.goBack(_navigatorKey.currentContext, false);
    });
    //Util().logEventGA('custom_open_app');
  }

  @override
  Widget build(BuildContext context) => ScreenUtilInit(
      designSize: const Size(1080, 1920), minTextAdapt: true,
      splitScreenMode: true, useInheritedMediaQuery: true,
      builder: (_,__) => MultiBlocProvider(
            providers: [BlocProvider<MainBloc>(create: (_) => _bloc)],
            child: MaterialApp(
                debugShowCheckedModeBanner: false,
                //hainong_chat_call_module
                navigatorObservers: [customNavigatorObserver],
                localizationsDelegates: const [
                  GlobalMaterialLocalizations.delegate,
                  GlobalCupertinoLocalizations.delegate,
                  GlobalWidgetsLocalizations.delegate,
                  // FlutterQuillLocalizations.delegate,
                ],
                theme: ThemeData(useMaterial3: false, fontFamily: 'Roboto',
                    textTheme: const TextTheme(
                        titleLarge: TextStyle(fontWeight: FontWeight.w300),
                        titleMedium: TextStyle(fontWeight: FontWeight.w300),
                        titleSmall: TextStyle(fontWeight: FontWeight.w300),
                        bodyLarge: TextStyle(fontWeight: FontWeight.w300),
                        bodyMedium: TextStyle(fontWeight: FontWeight.w300),
                        bodySmall: TextStyle(fontWeight: FontWeight.w300),
                        headlineLarge: TextStyle(fontWeight: FontWeight.w300),
                        headlineMedium: TextStyle(fontWeight: FontWeight.w300),
                        headlineSmall: TextStyle(fontWeight: FontWeight.w300),
                        displayLarge: TextStyle(fontWeight: FontWeight.w300),
                        displayMedium: TextStyle(fontWeight: FontWeight.w300),
                        displaySmall: TextStyle(fontWeight: FontWeight.w300),
                        labelLarge: TextStyle(fontWeight: FontWeight.w300),
                        labelMedium: TextStyle(fontWeight: FontWeight.w300),
                        labelSmall: TextStyle(fontWeight: FontWeight.w300),
                    ),
                    appBarTheme: AppBarTheme(backgroundColor: StyleCustom.primaryColor,
                        elevation: 5, surfaceTintColor: Colors.transparent, shadowColor: Colors.black,
                        iconTheme: IconThemeData(size: 64.sp, color: Colors.white)),
                    colorScheme: ColorScheme.fromSeed(onPrimary: Colors.white,
                        primary: StyleCustom.primaryColor, seedColor: Colors.white)
                ),
                home: splash.AnimatedSplash(type: splash.Transition.fade,
                    durationInSeconds: 0, backgroundColor: Colors.white, navigator: const SplashPage(),
                    child: Image.asset('assets/images/v2/bg_splash_temp.png', width: 1.sw, height: 1.sh, fit: BoxFit.cover)),
                navigatorKey: _navigatorKey,
                builder: (BuildContext context, Widget? child) {
                  final MediaQueryData data = MediaQuery.of(context);
                  DBHelperUtil().setSetting('text_scale', data.textScaler.scale(1.0).toString());
                  return MediaQuery(
                      data: data.copyWith(
                          textScaler: const TextScaler.linear(1.0),
                          boldText: false
                      ),
                      child: child??const SizedBox()
                  );
                }
            ))
  );

  void onMessageOpen(RemoteMessage message) => _bloc.add(MessageOpenEvent(message));

  void onMessage(RemoteMessage message) => _bloc.add(MessageEvent(message));

  //hainong_chat_call_module
  void onCallSuggest(StreamResponseModel response, bool isSuggestCall) =>
    _bloc.add(CallSuggestEvent(response, isSuggestCall: isSuggestCall));
}

import 'dart:async';
import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:hainong/common/api_client.dart';
import 'package:hainong/common/base_bloc.dart';
import 'package:hainong/common/base_response.dart';
import 'package:hainong/common/constants.dart';
import 'package:hainong/common/ui/button_image_widget.dart';
import 'package:hainong/common/ui/image_network_asset.dart';
import 'package:hainong/common/ui/import_lib_base_ui.dart';
import 'package:hainong/common/ui/loading.dart';
import '../function/support/mission/mission_bloc.dart';

export '../function/support/mission/mission_bloc.dart';

class CampaignBloc extends BaseBloc {
  TextEditingController? ctrSearch;
  ScrollController? scroller;
  int? _page;

  @override
  Future<void> close() async {
    ctrSearch?.dispose();
    scroller?.dispose();
    super.close();
  }

  CampaignBloc(String type) {
    on<LoadWardBaseEvent>((event, emit) async {
      emit(const BaseState(isShowLoading: true));
      //List<File> files = await Util.loadFilesFromNetwork(ProductRepository(), [ItemModel(id: 'download', name: event.idDistrict)]);
      //emit(files.isEmpty ? const BaseState() : LoadWardBaseState('Tải ảnh thành công'));
      final bytes = await ApiClient().getBytes(event.idDistrict, timeout: 120);
      if (bytes != null) {
        final rs = await ImageGallerySaver.saveImage(bytes, quality: 100);
        if (rs != null && rs is Map && rs['isSuccess'] == true) {
          emit(LoadWardBaseState('Tải ảnh thành công'));
          return;
        }
      }
      emit(const BaseState());
    });
    switch(type) {
      case 'list': _initListBloc(); break;
      case 'detail': _initDetailBloc(); break;
    }
  }

  ///Start Common
  void showQR(context, Future<PermissionStatus> Function({List<Permission>? arrayPer}) funCheckPermissions, funShare, String qr, String link) {
    if (qr.isEmpty) return;
    showDialog(context: context, barrierDismissible: false,
        builder: (_) {
          return Stack(children: [
            Container(decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(8)),
                child: Column(children: [
                  ImageNetworkAsset(path: qr, width: 0.8.sw, height: 0.8.sw),
                  Row(children: [
                    ButtonImageWidget(0, () => downloadQR(funCheckPermissions, qr),
                        Icon(Icons.file_download_outlined, color: const Color(0xFF1AB686), size: 100.sp)),
                    SizedBox(width: 80.sp),
                    ButtonImageWidget(0, () => funShare(link: link), Icon(Icons.share, color: const Color(0xFF1AB686), size: 80.sp))
                  ], mainAxisSize: MainAxisSize.min),
                  SizedBox(height: 40.sp)
                ], mainAxisSize: MainAxisSize.min)),
            Padding(padding: EdgeInsets.only(left: 0.8.sw, bottom: 0.8.sw + 140.sp),
                child: ButtonImageWidget(50, () => Navigator.pop(context),
                    Padding(padding: const EdgeInsets.all(5), child: Icon(Icons.clear, color: Colors.white, size: 48.sp)),
                    color: const Color(0xFF1AB686))),
            Loading(this)
          ], alignment: Alignment.center);
        });
  }

  void downloadQR(Future<PermissionStatus> Function({List<Permission>? arrayPer}) funCheckPermissions, String qr) async {
    if (qr.isEmpty) return;
    final List<Permission> pers = [];
    if (Platform.isAndroid) {
      final deviceInfo = await DeviceInfoPlugin().androidInfo;
      if (deviceInfo.version.sdkInt > 32) {
        pers.add(Permission.audio);
        pers.add(Permission.videos);
        pers.add(Permission.photos);
      }
    }
    if (pers.isEmpty) pers.add(Permission.storage);
    final value = await funCheckPermissions(arrayPer: pers);
    if (value == PermissionStatus.granted) add(LoadWardBaseEvent(qr));
  }
  //End Common

  ///Start List
  void _initListBloc() {
    _initList();
    on<MissionEmptyEvent>((event, emit) => emit(MissionEmptyState(event.value)));
    on<ShowClearSearchEvent>((event, emit) => emit(ShowClearSearchState(event.value)));
    on<LoadMissionsEvent>((event, emit) async {
      emit(const BaseState(isShowLoading: true));
      final resp = await ApiClient().getAPI(Constants().apiVersion + 'campaigns?limit=20&page=$_page&keyword=' +
          ctrSearch!.text.trim(), BaseResponse(isJson: true));
      if (resp.checkOK()) _handleLoadList(resp.data);
      emit(LoadMissionsState(resp));
    });
    on<JoinMissionEvent>((event, emit) async {
      emit(const BaseState(isShowLoading: true));
      bool check = false;
      dynamic resp;
      if (event.idParent > 0) {
        resp = await ApiClient().postJsonAPI(Constants().apiVersion + 'campaigns/${event.idParent}/join_campaign', null, null, dataIsJson: true);
        check = resp.checkOK();
        if (check) {
          data![event.idSub]['is_joined'] = resp.data['is_joined'];
          data![event.idSub]['campaign_code'] = resp.data['campaign_code'];
        }
      } else {
        check = true;
        resp = BaseResponse(success: true, data: {});
      }
      emit(JoinMissionState([resp, check ? event.idSub : -1]));
    });
    _loadMore();
  }

  void _initList() {
    data = [];
    _page = 1;
    ctrSearch = TextEditingController();
    scroller = ScrollController();
    scroller!.addListener(_listenScroll);
  }

  void _listenScroll() {
    if (_page! > 0 && scroller!.position.maxScrollExtent == scroller!.position.pixels) _loadMore();
  }

  void _loadMore() {
    add(LoadMissionsEvent(0, '', '', false));
  }

  void _handleLoadList(List list) {
    if (list.isEmpty) _page = 0;
    else {
      data!.addAll(list);
      _page = list.length == 20 ? _page! + 1 : 0;
    }
  }

  Future<void> reload({bool reload = true}) async {
    data.clear();
    _page = 1;
    _loadMore();
  }

  void clear() {
    ctrSearch!.text = '';
    add(ShowClearSearchEvent(false));
    reload();
  }

  void join(int index) => add(JoinMissionEvent(data![index]['id'], index));

  void reloadDetail(int index, dynamic value) {
    data[index] = value;
    add(JoinMissionEvent(0, index));
  }
  //End List

  ///Start Detail
  void _initDetailBloc() {
    on<JoinMissionEvent>((event, emit) async {
      emit(const BaseState(isShowLoading: true));
      final resp = await ApiClient().postJsonAPI(Constants().apiVersion + 'campaigns/${event.idParent}/join_campaign', null, null, dataIsJson: true);
      final check = resp.checkOK();
      if (check) {
        data!['is_joined'] = resp.data['is_joined'];
        data!['campaign_code'] = resp.data['campaign_code'];
      }
      emit(JoinMissionState(resp));
    });
  }

  void joinDetail() => add(JoinMissionEvent(data['id'], 0));
  //End Detail
}
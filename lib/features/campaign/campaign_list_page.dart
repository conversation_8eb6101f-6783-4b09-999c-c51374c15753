import 'dart:io';
import 'package:flutter/services.dart';
import 'package:hainong/common/ui/box_dec_custom.dart';
import 'package:hainong/common/ui/button_image_widget.dart';
import 'package:hainong/common/ui/image_network_asset.dart';
import 'package:hainong/common/ui/import_lib_ui.dart';
import 'package:hainong/common/ui/label_custom.dart';
import 'package:hainong/common/ui/loading.dart';
import 'package:hainong/common/ui/permission_image_page_state.dart';
import '../login/login_onetime_page.dart';
import 'campaign_bloc.dart';
import 'campaign_detail_page.dart';

class CampaignListPage extends BasePage {
  CampaignListPage({Key? key}):super(key: key, pageState: _CampaignListState());
}

class _CampaignListState extends PermissionImagePageState {
  @override
  void loadFiles(List<File> files) {}

  @override
  void initState() {
    bloc = CampaignBloc('list');
    super.initState();
    bloc!.stream.listen((state) {
      if (state is LoadMissionsState) isResponseNotError(state.resp);
      else if (state is LoadWardBaseState) UtilUI.showCustomDialog(context, state.list);
      else if (state is JoinMissionState && isResponseNotError(state.resp[0]) && state.resp[0].data.isNotEmpty) {
        UtilUI.showCustomDialog(context, 'Bạn đã tham gia thành công');
      }
    });
  }

  @override
  Widget build(BuildContext context, {Color color = Colors.white}) {
    final ctr = bloc as CampaignBloc;
    return GestureDetector(child: Scaffold(appBar: AppBar(elevation: 5, titleSpacing: 0,
        centerTitle: true, title: UtilUI.createLabel('Các chương trình sự kiện'),
        bottom: PreferredSize(preferredSize: Size(0.5.sw, 140.sp), child: Container(
          decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(10.sp)),
          padding: EdgeInsets.all(30.sp), margin: EdgeInsets.fromLTRB(40.sp, 0, 40.sp, 40.sp),
          child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
            ButtonImageWidget(100, _search, Image.asset('assets/images/ic_search.png', width: 42.sp, color: const Color(0xFF8B8D8A))),
            Expanded(child: TextField(controller: ctr.ctrSearch,
              onChanged: (value) {
                if (value.length == 1) bloc!.add(ShowClearSearchEvent(true));
                if (value.isEmpty) bloc!.add(ShowClearSearchEvent(false));
              },
              onSubmitted: (value) => _search(),
              textInputAction: TextInputAction.search,
              decoration: InputDecoration(hintStyle: TextStyle(fontSize: 36.sp, color: const Color(0xFF959595)),
                hintText: 'Nhập từ khóa hoặc nội dung tìm kiếm', contentPadding: EdgeInsets.symmetric(horizontal: 40.sp), isDense: true,
                border: const UnderlineInputBorder(borderSide: BorderSide.none))
            )),
            BlocBuilder(bloc: bloc, buildWhen: (_,newS) => newS is ShowClearSearchState,
              builder: (_,state) {
                bool show = false;
                if (state is ShowClearSearchState) show = state.value;
                return show ? Padding(padding: EdgeInsets.only(right: 20.sp), child: ButtonImageWidget(100, _clear,
                  Icon(Icons.clear, size: 48.sp, color: const Color(0xFF676767)))) : const SizedBox();
              })
          ])))),
      body: Stack(children: [
        BlocBuilder(bloc: bloc, buildWhen: (_,newS) => newS is LoadMissionsState,
          builder: (_,__) => RefreshIndicator(child: ListView.separated(padding: EdgeInsets.all(40.sp),
            separatorBuilder: (context, index) => SizedBox(height: 40.sp),
            physics: const AlwaysScrollableScrollPhysics(), itemCount: ctr.data.length,
            itemBuilder: (_,index) => BlocBuilder(bloc: bloc, buildWhen: (_,newS) => newS is JoinMissionState && newS.resp[1] == index,
              builder: (_,__) {
                final item = ctr.data[index];
                return _EventItem(item, () => _gotoDetail(index), () => _join(index),
                        () => ctr.showQR(context, funCheckPermissions, _shareQR,
                            item['campaign_qr']??'', item['campaign_referral_link']??''),
                        () => ctr.downloadQR(funCheckPermissions, item['campaign_qr']??''),
                        () => _shareQR(link: item['campaign_referral_link']??''));
              }),
            controller: ctr.scroller), onRefresh: ctr.reload)),
        Loading(bloc)
      ])), onTap: clearFocus);
  }

  void _search() {
    clearFocus();
    (bloc as CampaignBloc).reload();
  }

  void _clear() {
    clearFocus();
    (bloc as CampaignBloc).clear();
  }

  void _gotoDetail(int index) {
    final ctr = bloc as CampaignBloc;
    UtilUI.goToNextPage(context, CampaignDetailPage(ctr.data[index], index: index, reloadList: ctr.reloadDetail));
  }

  void _join(int index) {
    if (!constants.isLogin) {
      UtilUI.showCustomDialog(context, MultiLanguage.get('msg_login_create_account')).whenComplete(() {
        UtilUI.logout();
        UtilUI.clearAllPages(context);
        UtilUI.goToPage(context, LoginOneTimePage(), null);
      });
      return;
    }
    (bloc as CampaignBloc).join(index);
  }

  void _shareQR({String link = ''}) {
    if (link.isEmpty) return;
    UtilUI.shareTo(context, link, 'Open Share Dialog', 'Campaign Management Screen', hasDomain: true);
  }
}

class _EventItem extends StatelessWidget {
  final dynamic item;
  final Function funDetail, funJoin, funShowQR, funDownloadQR, funShareQR;
  const _EventItem(this.item, this.funDetail, this.funJoin, this.funShowQR, this.funDownloadQR, this.funShareQR, {Key? key}):super(key: key);
  @override
  Widget build(BuildContext context) => ButtonImageWidget(0, funDetail, Container(
    decoration: BoxDecCustom(radius: 10),
    child: Column(children: [
      ClipRRect(borderRadius: const BorderRadius.vertical(top: Radius.circular(10)), child:
        ImageNetworkAsset(path: item['image']??'', height: 0.22.sh, width: 1.sw)),
      Padding(padding: EdgeInsets.all(40.sp), child: Column(children: [
        LabelCustom(item['name']??'', color: Colors.black54, size: 50.sp, line: 2),
        if (item['is_joined'] == true && (item['campaign_code']??'').isNotEmpty) Padding(padding: EdgeInsets.only(top: 40.sp),
          child: Column(children: [
            Row(children: [
              LabelCustom('Mã giới thiệu:  ', color: Colors.black54, size: 40.sp, weight: FontWeight.normal),
              ButtonImageWidget(0, () => _copyCode(context), Row(children: [
                LabelCustom(item['campaign_code'] + ' ', color: Colors.black54, size: 40.sp),
                Icon(Icons.copy, size: 60.sp, color: Colors.black45)
              ], mainAxisSize: MainAxisSize.min))
            ]),
            SizedBox(height: 40.sp),
            Row(children: [
              LabelCustom('QR giới thiệu:  ', color: Colors.black54, size: 40.sp, weight: FontWeight.normal),
              Expanded(child: Row(children: [
                ButtonImageWidget(0, funShowQR, Image.asset('assets/images/v11/ic_qr2_ref_v11.png', width: 60.sp)),
                ButtonImageWidget(0, funDownloadQR, Icon(Icons.file_download_outlined, color: const Color(0x5F000000), size: 80.sp)),
                ButtonImageWidget(0, funShareQR, Icon(Icons.share, color: const Color(0x5F000000), size: 80.sp))
              ], mainAxisAlignment: MainAxisAlignment.spaceBetween))
            ])
          ])),
        SizedBox(height: 20.sp),
        Row(children: [
          Icon(Icons.access_time_sharp, color: Colors.orange, size: 42.sp),
          Expanded(child: LabelCustom(_getTime(), color: Colors.black54, size: 40.sp, weight: FontWeight.normal)),
          item['is_joined'] == true ? Container(padding: EdgeInsets.all(20.sp),
              decoration: BoxDecCustom(radius: 5, bgColor: Colors.green, hasShadow: false),
              child: LabelCustom('Đã tham gia', size: 40.sp, weight: FontWeight.normal)) :
            ButtonImageWidget(5, funJoin, Padding(padding: EdgeInsets.all(20.sp),
              child: LabelCustom('Tham gia', size: 40.sp, weight: FontWeight.normal)), color: Colors.orange)
        ])
      ], crossAxisAlignment: CrossAxisAlignment.start))
    ])
  ));

  String _getTime() => ' ' +
    Util.strDateToString(item['start_date']??'', pattern: 'dd/MM/yyyy') + ' - ' +
    Util.strDateToString(item['end_date']??'', pattern: 'dd/MM/yyyy');

  void _copyCode(context) {
    Clipboard.setData(ClipboardData(text: item['campaign_code']));
    ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text("Mã đã được sao chép")));
  }
}
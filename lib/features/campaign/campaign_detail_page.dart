import 'dart:io';
import 'package:flutter/services.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:hainong/common/ui/button_image_widget.dart';
import 'package:hainong/common/ui/image_network_asset.dart';
import 'package:hainong/common/ui/import_lib_ui.dart';
import 'package:hainong/common/ui/label_custom.dart';
import 'package:hainong/common/ui/loading.dart';
import 'package:hainong/common/ui/box_dec_custom.dart';
import 'package:hainong/common/ui/permission_image_page_state.dart';
import '../login/login_onetime_page.dart';
import '../profile/ui/show_avatar_page.dart';
import 'campaign_bloc.dart';

class CampaignDetailPage extends BasePage {
  final int? index;
  final Function? reloadList;
  CampaignDetailPage(dynamic detail, {this.index, this.reloadList, Key? key}):super(key: key, pageState: _DetailState(detail));
}

class _DetailState extends PermissionImagePageState {
  _DetailState(dynamic detail) {
    bloc = CampaignBloc('detail');
    bloc!.data = {};
    bloc!.data.addAll(detail);
  }

  @override
  void loadFiles(List<File> files) {}

  @override
  void initState() {
    super.initState();
    bloc!.stream.listen((state) {
      if (state is JoinMissionState && isResponseNotError(state.resp)) {
        setState(() {});
        UtilUI.showCustomDialog(context, 'Bạn đã tham gia thành công');
        final reload = (widget as CampaignDetailPage).reloadList;
        if (reload != null) reload((widget as CampaignDetailPage).index, state.resp.data);
      } else if (state is LoadWardBaseState) UtilUI.showCustomDialog(context, state.list);
    });
  }

  @override
  Widget build(BuildContext context, {Color color = Colors.white}) {
    final ctr = bloc as CampaignBloc;
    return Scaffold(appBar: AppBar(elevation: 5, centerTitle: true, title: UtilUI.createLabel('Chi tiết chương trình sự kiện')),
      body: Stack(children: [
        ImageNetworkAsset(path: ctr.data['image']??'', width: 1.sw, height: 0.35.sh),

        Container(height: 1.sh - 0.3.sh, margin: EdgeInsets.only(top: 0.3.sh), padding: EdgeInsets.only(top: 80.sp),
          decoration: BoxDecoration(color: color,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(50)),
            boxShadow: [BoxShadow(color: Colors.grey.withOpacity(0.1), spreadRadius: 3,
                blurRadius: 5, offset: const Offset(0, 3))]
          ),
          child: ListView(padding: EdgeInsets.fromLTRB(60.sp, 0, 60.sp, 60.sp), children: [
            LabelCustom(ctr.data['name']??'', color: Colors.black54, size: 50.sp),

            if (ctr.data['is_joined'] == true && (ctr.data['campaign_code']??'').isNotEmpty) Padding(padding: EdgeInsets.only(top: 40.sp),
              child: Column(children: [
                Row(children: [
                  LabelCustom('Mã giới thiệu: ', color: Colors.black54, size: 40.sp, weight: FontWeight.normal),
                  ButtonImageWidget(0, () => _copyCode(ctr.data['campaign_code']), Row(children: [
                    LabelCustom(ctr.data['campaign_code'] + ' ', color: Colors.black54, size: 42.sp),
                    Icon(Icons.copy, size: 60.sp, color: Colors.black45)
                  ], mainAxisSize: MainAxisSize.min))
                ], mainAxisAlignment: MainAxisAlignment.start),
                SizedBox(height: 40.sp),
                Row(children: [
                  LabelCustom('QR giới thiệu:  ', color: Colors.black54, size: 40.sp, weight: FontWeight.normal),
                  Expanded(child: Row(children: [
                    ButtonImageWidget(0, () => ctr.showQR(context, funCheckPermissions, _shareQR,
                        bloc!.data['campaign_qr']??'', ''),
                        Image.asset('assets/images/v11/ic_qr2_ref_v11.png', width: 60.sp)),
                    ButtonImageWidget(0, () => ctr.downloadQR(funCheckPermissions, bloc!.data['campaign_qr']??''),
                        Icon(Icons.file_download_outlined, color: const Color(0x5F000000), size: 80.sp)),
                    ButtonImageWidget(0, _shareQR, Icon(Icons.share, color: const Color(0x5F000000), size: 80.sp))
                  ], mainAxisAlignment: MainAxisAlignment.spaceBetween))
                ])
              ])),

            Padding(padding: EdgeInsets.only(top: 20.sp, bottom: 40.sp), child: Row(children: [
              Icon(Icons.access_time_sharp, color: Colors.orange, size: 42.sp),
              Expanded(child: LabelCustom(_getTime(ctr.data), color: Colors.black54, size: 40.sp, weight: FontWeight.normal)),
              ctr.data['is_joined'] == true ? Container(padding: EdgeInsets.all(20.sp),
                  decoration: BoxDecCustom(radius: 5, bgColor: Colors.green, hasShadow: false),
                  child: LabelCustom('Đã tham gia', size: 40.sp, weight: FontWeight.normal)) :
              ButtonImageWidget(5, _join, Padding(padding: EdgeInsets.all(20.sp),
                  child: LabelCustom('Tham gia', size: 40.sp, weight: FontWeight.normal)), color: Colors.orange)
            ])),

            Html(data: ctr.data['description']??'',
                shrinkWrap: true,
                style: {
                  "html,body,p,span": Style(fontSize: FontSize(18), fontFamily: 'Tahoma, Geneva, sans-serif', padding: HtmlPaddings.zero, margin: Margins.zero)
                },
                extensions: [
                  TagExtension(tagsToExtend: {"img"},
                    builder: (ext) {
                      final src = ext.attributes['src'] ?? '';
                      return GestureDetector(
                        onTap: () {
                          if (!src.contains('http')) return;
                          UtilUI.goToNextPage(context, ShowAvatarPage(src));
                        },
                        child: Image.network(src, width: 1.sw, fit: BoxFit.fitWidth,
                          errorBuilder: (_,__,___) => Image.asset('assets/images/ic_default.png',
                            width: 1.sw, fit: BoxFit.fitWidth)
                      ));
                    }
                  )
                ],
                onAnchorTap: (url,_,__) {
                  if (url == null || !url.contains('http') || Util.isImage(url)) return;
                  launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
                })
          ])),
        Loading(bloc)
      ])
    );
  }

  String _getTime(item) => ' ' + Util.strDateToString(item['start_date']??'', pattern: 'dd/MM/yyyy') + ' - ' +
      Util.strDateToString(item['end_date']??'', pattern: 'dd/MM/yyyy');

  void _copyCode(code) {
    Clipboard.setData(ClipboardData(text: code));
    ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text("Mã đã được sao chép")));
  }

  void _join() {
    if (!constants.isLogin) {
      UtilUI.showCustomDialog(context, MultiLanguage.get('msg_login_create_account')).whenComplete(() {
        UtilUI.logout();
        UtilUI.clearAllPages(context);
        UtilUI.goToPage(context, LoginOneTimePage(), null);
      });
      return;
    }
    (bloc as CampaignBloc).joinDetail();
  }

  void _shareQR({String link = ''}) {
    link = bloc!.data['campaign_referral_link']??'';
    if (link.isEmpty) return;
    UtilUI.shareTo(context, link, 'Open Share Dialog', 'Campaign Management Screen', hasDomain: true);
  }
}
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:hainong/common/ui/import_lib_ui.dart';
import 'package:hainong/common/ui/label_custom.dart';
import 'package:hainong/common/ui/loading.dart';
import 'package:hainong/features/cart/cart_model.dart';
import 'order_bloc.dart';
import 'order_ui.dart';
import 'order_refund_rejection_page.dart';

class OrderRefundDtlPage extends BasePage {
  final Function funReload;
  OrderRefundDtlPage(OrderModel order, bool isMine, this.funReload, {int? idBusiness, Key? key}) :
    super(pageState: _OrderRefundDtlPageState(order, isMine, idBusiness), key: key);
}

class _OrderRefundDtlPageState extends BasePageState {

  _OrderRefundDtlPageState(OrderModel order, bool isMine, int? idBusiness) {
    bloc = OrderBloc('refund_detail', detail: order, idBusiness: idBusiness, isMine: isMine);
    bloc!.stream.listen((state) {
      if (state is LoadCountStatusState && state.resp is String) {
        _reload();
        UtilUI.showCustomDialog(context, state.resp, isNewUI: true);
      }
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    bloc!.data = this;
  }

  @override
  Widget build(BuildContext context, {Color color = Colors.white}) {
    final ctr = bloc as OrderBloc;
    final bool isShop = ctr.isShop();
    const green = Color(0xFF1AB686), grey = Color(0xFF818181);

    return Scaffold(appBar: AppBar(titleSpacing: 0, centerTitle: true, elevation: 2,
        title: UtilUI.createLabel('Chi tiết trả hàng')),
        backgroundColor: const Color(0xFFF5F5F5),
        body: Stack(children: [
          SafeArea(minimum: EdgeInsets.only(bottom: 40.sp), child: Column(children: [
            Flexible(child: ListView(children: [
              ctr.detail!.refund_complaint_status == null || ctr.detail!.refund_complaint_status == 'none'
              ? RefundStatus(ctr.detail!.refund_invoice_status??'', isShop,
                BlocBuilder(bloc: bloc, buildWhen: (o,n) => n is LoadCountStatusState, builder: (_,__) =>
                  LabelCustom('vào ' + Util.strDateToString(ctr.refundDtl['updated_at']??'0-0-0T0:0:0',
                    pattern: 'dd-MM-yyyy HH:mm'), size: 30.sp, color: grey, weight: FontWeight.normal))) :
              ComplaintStatus(ctr.detail!.refund_complaint_status??'', isShop,
                  BlocBuilder(bloc: bloc, buildWhen: (o,n) => n is LoadCountStatusState, builder: (_,__) =>
                      LabelCustom('vào ' + Util.strDateToString(ctr.refundDtl['updated_at']??'0-0-0T0:0:0',
                          pattern: 'dd-MM-yyyy HH:mm'), size: 30.sp, color: grey, weight: FontWeight.normal))),

              if (ctr.detail!.refund_invoice_status == 'approved')
              Container(padding: EdgeInsets.all(40.sp), margin: EdgeInsets.only(bottom: 20.sp),
                color: color, child: Row(children: [
                  Image.asset('assets/images/v10/ic_ready_to_ship_v10.png', width: 46.sp),
                  SizedBox(width: 20.sp),
                  Expanded(child: BlocBuilder(bloc: bloc, buildWhen: (o,n) => n is LoadCountStatusState && n.resp == true,
                    builder: (_,__) {
                      if (ctr.list == null) return const SizedBox();
                      return Column(children: [
                        Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                          LabelCustom('Thông tin vận chuyển', size: 27.sp, weight: FontWeight.w400, color: Colors.black),
                          GestureDetector(onTap: () {
                            UtilUI.goToNextPage(context, OrderShippingLogs(ctr.list!, ctr.refundDtl['tracking_code']??''));
                          }, child: LabelCustom('Xem', size: 27.sp, weight: FontWeight.w400, color: green)),
                        ]),

                        Padding(padding: EdgeInsets.symmetric(vertical: 20.sp),
                          child: LabelCustom('${ctr.refundDtl['shipment_provider']??''} - ${ctr.refundDtl['tracking_code']??''}',
                            size: 27.sp, weight: FontWeight.w400, color: grey)),

                        OrderShippingLog(ctr.list![0]['status_text'], ctr.list![0]['modified_at'])
                      ], crossAxisAlignment: CrossAxisAlignment.start);
                    }
                ))
              ], crossAxisAlignment: CrossAxisAlignment.start)),

              if (ctr.detail!.refund_invoice_status == 'approved')
              OrderAddress(ctr.detail!.name, ctr.detail!.shipping_address, ctr.detail!.phone_number, 'lấy', isShop),

              Container(color: color,
                padding: EdgeInsets.all(40.sp), margin: EdgeInsets.only(bottom: 20.sp),
                child: Column(children: [
                  ShopUI(ctr.detail!, isShop),

                  OrderProductList(ctr.detail!.items, ctr, -1, isDetail: true),
                  Divider(height: 80.sp, thickness: 0.2, color: Colors.grey),

                  BlocBuilder(bloc: bloc, buildWhen: (o,n) => n is LoadCountStatusState,
                    builder: (_,__) {
                      if (ctr.refundDtl == null) return const SizedBox();
                      String reason = ctr.refundDtl['reason']??'';
                      if (reason.isEmpty) reason = ctr.refundDtl['request']??'';
                      return Column(children: [
                        OrderReason('Mã yêu cầu trả hàng', ctr.refundDtl['refund_invoice_number']??'', grey),
                        Padding(padding: EdgeInsets.only(top: 20.sp, bottom: reason.isEmpty ? 0 : 20.sp),
                          child: OrderReason('Yêu cầu vào', Util.strDateToString(ctr.refundDtl['created_at']??'2000-01-01T01:01:01', pattern: 'dd-MM-yyyy HH:mm'), grey)),
                        if (reason.isNotEmpty) OrderReason('Lý do', reason, grey)
                      ]);
                    })
                ])
              ),

              if ((ctr.detail!.refund_invoice_status == 'requested' || ctr.detail!.refund_invoice_status == 'rejected')
                  && ctr.detail!.refund_complaint_status == null && ctr.detail!.refund_complaint_status != 'cancelled')
                Container(color: color, padding: EdgeInsets.all(40.sp),
                  child: Column(children: [
                    LabelCustom('Lý do yêu cầu trả hàng từ người mua', color: Colors.black, size: 40.sp),
                    SizedBox(height: 20.sp),
                    BlocBuilder(bloc: bloc, buildWhen: (o,n) => n is LoadCountStatusState,
                      builder: (_,__) => LabelCustom(ctr.refundDtl['request']??'', color: grey, size: 30.sp)),
                    BlocBuilder(bloc: bloc,
                        buildWhen: (o,n) => n is LoadCountStatusState,
                        builder: (_,__) => ctr.refundDtl == null ? const SizedBox() :
                        AlignedGridView.count(crossAxisCount: 4, shrinkWrap: true,
                            mainAxisSpacing: 20.sp, crossAxisSpacing: 20.sp,
                            padding: EdgeInsets.only(top: 20.sp),
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: (ctr.refundDtl['buyer_images']??[]).length,
                            itemBuilder: (_,index) => RefundViewImageVideo(ctr.refundDtl['buyer_images'][index]['name']??'')))
                  ], crossAxisAlignment: CrossAxisAlignment.start)),

              if (ctr.detail!.refund_invoice_status == 'rejected' && ctr.detail!.refund_complaint_status == null && ctr.detail!.refund_complaint_status != 'cancelled')
                BlocBuilder(bloc: bloc, buildWhen: (o,n) => n is LoadCountStatusState,
                  builder: (_,__) => Container(color: color, padding: EdgeInsets.all(40.sp), margin: EdgeInsets.only(top: 20.sp),
                    child: Column(children: [
                      LabelCustom('Lý do từ chối yêu cầu', color: Colors.black, size: 40.sp),
                      SizedBox(height: 20.sp),
                      LabelCustom(ctr.refundDtl['reason']??'', color: grey, size: 30.sp),
                      AlignedGridView.count(crossAxisCount: 4, shrinkWrap: true,
                        mainAxisSpacing: 20.sp, crossAxisSpacing: 20.sp,
                        padding: EdgeInsets.only(top: 20.sp),
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: (ctr.refundDtl['seller_images']??[]).length,
                        itemBuilder: (_,index) => RefundViewImageVideo(ctr.refundDtl['seller_images'][index]['name']??''))
                    ], crossAxisAlignment: CrossAxisAlignment.start)))
            ], shrinkWrap: true, padding: EdgeInsets.only(top: 20.sp))),

            if (ctr.detail!.refund_invoice_status == 'requested')
              Padding(padding: EdgeInsets.only(top: 40.sp), child: isShop ? Row(children: [
                OrderButton('Từ chối', () => _shopReject(ctr)),
                OrderButton('Chấp nhận', ctr.shopAccept, color: green)
              ], mainAxisAlignment: MainAxisAlignment.spaceAround) : OrderButton('Hủy yêu cầu', ctr.buyerCancel)),

            if (!isShop && ctr.detail!.refund_invoice_status == 'rejected' && ctr.detail!.refund_complaint_status == null)
              Padding(padding: EdgeInsets.only(top: 40.sp), child: OrderButton('Khiếu nại đến Hai Nông', ctr.buyerComplaint)),

            if (!isShop && ctr.detail!.refund_complaint_status == 'pending')
              Padding(padding: EdgeInsets.only(top: 40.sp), child: OrderButton('Hủy yêu cầu', ctr.buyerCancelComplaint)),
          ], crossAxisAlignment: CrossAxisAlignment.center, mainAxisSize: MainAxisSize.min)),
          Loading(bloc)
        ])
    );
  }

  void _reload() {
    setState(() {});
    (widget as OrderRefundDtlPage).funReload();
  }

  void _shopReject(OrderBloc ctr) => UtilUI.goToNextPage(context,
    OrderRefundRejectionPage(ctr.refundDtl!, ctr.detail!, ctr.isMine!, _reload), funCallback: (value) {
      if (value != null) setState(() => (bloc as OrderBloc).refundDtl = value);
    });
}
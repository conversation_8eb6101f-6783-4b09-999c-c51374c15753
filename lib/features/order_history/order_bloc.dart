import 'dart:async';
import 'package:hainong/features/order_history/order_shipment_setting_page.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/material.dart';
import 'package:hainong/common/api_client.dart';
import 'package:hainong/common/base_response.dart';
import 'package:hainong/common/base_bloc.dart';
import 'package:hainong/common/constants.dart';
import 'package:hainong/common/models/item_list_model.dart';
import 'package:hainong/common/ui/print_preview_pdf.dart';
import 'package:hainong/common/ui/textfield_custom.dart';
import 'package:hainong/common/util/util.dart';
import 'package:hainong/common/util/util_ui.dart';
import '../cart/cart_model.dart';
import '../function/support/mission/mission_bloc.dart';
import '../function/tool/diagnose_pests/diagnose_pests_bloc.dart';
export '../function/tool/diagnose_pests/diagnose_pests_bloc.dart';

class LoadCountStatusEvent extends BaseEvent {
  final bool? hasAddLast;
  final dynamic value;
  LoadCountStatusEvent({this.hasAddLast, this.value});
}
class LoadCountStatusState extends BaseState {
  final dynamic resp;
  LoadCountStatusState(this.resp);
}

class TurnOnOffEvent extends BaseEvent {
  final dynamic key, value;
  TurnOnOffEvent(this.key, this.value);
}
class TurnOnOffState extends BaseState {
  final dynamic key, value;
  TurnOnOffState(this.key, this.value);
}

class ConnectEvent extends BaseEvent {
  final int index;
  String account, secretKey;
  ConnectEvent(this.index, this.account, this.secretKey);
}

class OrderBloc extends BaseBloc {
  TextEditingController? ctrSearch, ctrNote;
  FocusNode? fcSearch;
  ScrollController? scroller;
  String? orderType, status;
  int? idBusiness, page, indexProvider;
  bool? isMine;
  dynamic list, providers, refundDtl, services;
  Map? optValues;
  OrderModel? detail;

  @override
  Future<void> close() async {
    list?.clear();
    optValues?.clear();
    if (providers != null && providers is! DateTime) providers?.clear();
    if (services != null && services is! DateTime) services?.clear();
    if (page != 100 && refundDtl != null) { /// clear refund detail
      if (refundDtl is! ItemModel) refundDtl!.clear();
      refundDtl = null;
    }
    ctrNote?.dispose();
    ctrSearch?.dispose();
    if (fcSearch != null) {
      if (fcSearch!.hasListeners) fcSearch!.removeListener(_listenFocus);
      fcSearch?.dispose();
    }
    if (scroller != null) {
      scroller!.removeListener(_listenScroll);
      scroller!.dispose();
    }
    super.close();
  }

  OrderBloc(String type, {this.orderType, this.idBusiness, this.detail, this.status, this.isMine, this.refundDtl, this.optValues}) {
    if (type == 'list' || type == 'shipment_selection' || type == 'refund' || type == 'refund_rejection' || type == 'report') {
      on<ShowClearSearchEvent>((event, emit) => emit(ShowClearSearchState(event.value)));
    }

    switch(type) {
      case 'dashboard':
        on<LoadCountStatusEvent>((event, emit) async {
          emit(const BaseState(isShowLoading: true));
          String param = idBusiness == null ? '' : '?business_association_id=$idBusiness';
          dynamic resp = await ApiClient().getAPI(Constants().apiVersion + 'logistics/count_invoice_by_status' + param, BaseResponse(isJson: true));
          emit(data!.isResponseNotError(resp, isNewUI: true) ? LoadCountStatusState(resp.data) : const BaseState());
        });
        break;
      case 'shipment_setting': _initShipmentSetting(); break;
      case 'list': _initOrderList(); break;
      case 'shipment_selection': _initShipmentSelection(); break;
      case 'refund_detail': _initRefundDetail(); break;
      case 'report': _initReport(); break;

      case 'detail': _initOrderDetail(); return;
      case 'refund': _initRefund(); return;
      case 'refund_rejection': _initRefundRejection(); return;
    }
    /// apply for type = dashboard/shipment_setting/list/shipment_selection/refund detail/report
    add(LoadCountStatusEvent());
  }

  /// Start Common
  Future<int> _getShipmentProviders({bool? hasAddLast, bool? isBack, int? idBusiness}) async {
    String path;
    if (idBusiness == null) {
      final prefs = await SharedPreferences.getInstance();
      path = 'logistics/get_list_shipping_provider?shop_id=' + (prefs.getInt('shop_id')??'').toString();
    } else path = 'logistics/get_list_shipping_provider?business_association_id=$idBusiness';
    dynamic resp = await ApiClient().getAPI(Constants().apiVersion + path, BaseResponse(isJson: true));
    if (data!.isResponseNotError(resp, isNewUI: true, isBack: isBack)) {
      if (resp.data.isEmpty) return 0;
      list!.addAll(resp.data);
      if (hasAddLast == true) list!.add(true);
      return 1;
    }
    return -1;
  }

  void refundOrReject(List images) {
    list = images;
    add(LoadCountStatusEvent());
  }

  Future<bool> checkInfo() async {
    page = 1;
    final prefs = await SharedPreferences.getInstance();
    String address = prefs.getString('address')??'', province = prefs.getString('province_id')??'',
      district = prefs.getString('district_id')??'', ward = prefs.getString('ward_id')??'';
    page = null;
    return address.isEmpty || province.isEmpty || district.isEmpty || ward.isEmpty;
  }
  // End Common

  /// Start Shipment Setting
  Future<void> _initShipmentSetting() async {
    list = [];
    on<ExpandEvent>((event, emit) {
      final array = event.value.split('_');
      final index = int.parse(array[0]);
      if (list![index]['expand'] == null) list![index].putIfAbsent('expand', () => array[0] + '_on');
      else list![index].update('expand', (v) => array[0] + (array[1] == 'off' ? '_on' : '_off'));
      emit(ExpandState('', list![index]['expand']));
    });

    on<TurnOnOffEvent>((event, emit) async {
      final value = (list![event.key]['available_status']??'inactive') == 'inactive' ? 'active' : 'inactive';
      if (value == 'active' && !(list![event.key]['has_been_linked']??false)) {
        data!.isResponseNotError(BaseResponse(data: 'Bạn chưa kết nối với đơn vị vận chuyển này. Hãy tạo kết nối trước'), hasClearError: true, isNewUI: true);
        if (!(list![event.key]['expand']??false)) add(ExpandEvent(list![event.key]['expand']??(event.key.toString()+'_off')));
        return;
      }

      final confirm = await UtilUI.showCustomDialog(data!.context, 'Xác nhận thiết lập Đơn vị vận chuyển',
          lblCancel: 'Hủy', lblOK: 'Xác nhận', isActionCancel: true, isNewUI: true);
      if (!(confirm == true)) return;

      emit(const BaseState(isShowLoading: true));
      final prefs = await SharedPreferences.getInstance();
      final resp = await ApiClient().postAPI(Constants().apiVersion + 'logistics/update_connection_status',
          'PUT', BaseResponse(), body: {
            'shop_id': (prefs.getInt('shop_id')??'').toString(),
            'shipping_id': list![event.key]['id'].toString(),
            'status': value
          });
      if (data!.isResponseNotError(resp, passString: true, hasClearError: true, isNewUI: true)) {
        list![event.key].update('available_status', (v) => value, ifAbsent: () => value);
        emit(TurnOnOffState(event.key, ''));
        UtilUI.showCustomDialog(data!.context, value == 'active' ? 'Bạn đã kết nối và thiết lập thành công với đơn vị vận chuyển' : 'Ngắt kết nối với đơn vị vận chuyển thành công', isNewUI: true);
      } else emit(const BaseState());
    });

    /// load shipment provider list
    on<LoadCountStatusEvent>((event, emit) async {
      emit(const BaseState(isShowLoading: true));
      emit((await _getShipmentProviders(hasAddLast: true)) > 0 ? LoadCountStatusState(true) : const BaseState());
    });

    on<ConnectEvent>((event, emit) async {
      emit(const BaseState(isShowLoading: true));
      final prefs = await SharedPreferences.getInstance();
      final resp = await ApiClient().postAPI(Constants().apiVersion + 'logistics/connect',
          'POST', BaseResponse(), body: {
            'shop_id': (prefs.getInt('shop_id')??'').toString(),
            'shipping_id': list![event.index]['id'].toString(),
            if (event.account.isNotEmpty) 'client_id': event.account,
            'secret_key': event.secretKey
          });
      if (data!.isResponseNotError(resp, passString: true, hasClearError: true, isNewUI: true)) {
        list![event.index].update('has_been_linked', (v) => true, ifAbsent: () => true);
        list![event.index].update('available_status', (v) => 'active', ifAbsent: () => 'active');
        emit(TurnOnOffState(event.index, ''));
        UtilUI.showCustomDialog(data!.context, 'Bạn đã kết nối và thiết lập thành công với đơn vị vận chuyển', isNewUI: true);
      }
      emit(const BaseState());
    });
  }

  void switchStatus(int index) => add(TurnOnOffEvent(index, ''));

  void connect(int index, String account, String secretKey) {
    UtilUI.showCustomDialog(data!.context, 'Xác nhận thiết lập Đơn vị vận chuyển',
      lblCancel: 'Hủy', lblOK: 'Xác nhận', isActionCancel: true, isNewUI: true).then((value) {
        if (value == true) add(ConnectEvent(index, account, secretKey));
      });
  }
  // End Shipment Setting

  /// Start Order List
  Future<void> _initOrderList() async {
    page = 1;
    status ??= '';
    list = [];

    /// handle searching: ShowClearSearchEvent

    /// load order list
    on<LoadCountStatusEvent>((event, emit) async {
      emit(const BaseState(isShowLoading: true));
      String path = 'invoice_users?';
      if (idBusiness != null) path += 'business_association_id=$idBusiness';
      if (indexProvider != null) path += '&shipping_id=' + providers[indexProvider]['id'].toString();
      if (ctrSearch!.text.trim().isNotEmpty) path += '&sku_keyword=' + ctrSearch!.text.trim();
      if (optValues != null) {
        path += '&info_invoice=both&start_date=' + Util.dateToString(optValues!['start'], pattern: 'dd/MM/yyyy');
        path += '&end_date=' + Util.dateToString(optValues!['end'], pattern: 'dd/MM/yyyy');
      }

      final resp = await ApiClient().getAPI(Constants().apiVersion + path + '&page=$page&limit=20&status=$status&type=$orderType', OrdersModel());
      if (data!.isResponseNotError(resp, isNewUI: true)) {
        final temp = resp.data.list;
        list!.addAll(temp);
        page = temp.length == 20 ? page! + 1 : 0;
      }
      emit(LoadCountStatusState(true));
    });

    /// load shipment provider
    on<ConnectEvent>((event, emit) async {
      if (event.index > -2) {
        if (indexProvider != event.index) {
          indexProvider = event.index;
          if (indexProvider == -1) indexProvider = null;
          emit(LoadCountStatusState(false));
          search();
        }
        return;
      }

      String shopId = '';
      final prefs = await SharedPreferences.getInstance();
      shopId = (prefs.getInt('shop_id')??'').toString();
      dynamic resp = await ApiClient().getAPI(Constants().apiVersion + 'logistics/get_list_shipping_provider?shop_id='+shopId, BaseResponse(isJson: true));
      if (resp.checkOK() && resp.data.isNotEmpty) {
        providers = resp.data;
        if (status != 'pending' && status != '') emit(LoadCountStatusState(false));
      }
    });

    on<ExpandEvent>((event, emit) {
      final array = event.value.split('_');
      final index = int.parse(array[0]);
      list![index].expanded = array[0] + (list![index].expanded == null ? '_on' : (array[1] == 'off' ? '_on' : '_off'));
      emit(ExpandState(array[0], list![index].expanded));
    });

    /// change order status
    on<TurnOnOffEvent>((event, emit) {
      indexProvider = null;
      emit(TurnOnOffState('', status!));
      status = event.value;
      emit(TurnOnOffState('', status!));
      search();
    });

    ctrSearch = TextEditingController();
    scroller = ScrollController();
    scroller!.addListener(_listenScroll);
    if (orderType == 'sales') add(ConnectEvent(-2, '', ''));
  }

  void _listenScroll() {
    if (page! > 0 && !state.isShowLoading && scroller!.position.maxScrollExtent == scroller!.position.pixels) add(LoadCountStatusEvent());
  }

  void search() {
    page = 1;
    list!.clear();
    add(LoadCountStatusEvent());
  }

  void clear() {
    page = 1;
    list!.clear();
    ctrSearch!.text = '';
    add(ShowClearSearchEvent(false));
    add(LoadCountStatusEvent());
  }

  void changeStatus(String newStt) {
    if (status != newStt) add(TurnOnOffEvent('', newStt));
  }
  // End Order List

  /// Start Order Detail
  Future<void> _initOrderDetail() async {
    /// get order detail
    on<LoadCountStatusEvent>((event, emit) async {
      emit(const BaseState(isShowLoading: true));
      String path = 'invoice_users/${isMine == true ? '' : 'shop/'}${detail!.id}';
      if (idBusiness != null) path = 'business/business_associations/$idBusiness/invoices/${detail!.id}';
      final resp = await ApiClient().getAPI(Constants().apiVersion + path, OrderModel());

      await _getShippingLogs(detail!.id);

      if (data!.isResponseNotError(resp, isNewUI: true)) {
        detail = resp.data;
        emit(LoadCountStatusState(event.hasAddLast));
      } else emit(const BaseState());
    });

    /// get shipping logs
    on<ShowClearSearchEvent>((event, emit) async {
      emit(const BaseState(isShowLoading: true));
      emit(ShowClearSearchState(await _getShippingLogs(detail!.id, isShowDialog: event.value)));
    });

    /// set order status
    on<TurnOnOffEvent>((event, emit) async {
      emit(const BaseState(isShowLoading: true));
      String path = 'invoice_users${event.key == 'done' ? '/shop' : ''}/${detail!.id}';
      if (idBusiness != null && event.key == 'done') path = 'business/business_associations/$idBusiness/invoices/${detail!.id}?status=done';
      final isComplaint = event.key.contains('complaint');
      if (isComplaint) path = 'invoice_users/${detail!.id}/request_complaint';
      final resp = await ApiClient().postAPI(Constants().apiVersion + path, 'PUT', BaseResponse(isJson: isComplaint), body: isComplaint ? {
        'complaint_status': event.value
      } : {
        'remark': event.value,
        'status': event.key
      });
      if (data!.isResponseNotError(resp, passString: !isComplaint, isNewUI: true)) {
        if (isComplaint) {
          detail!.complaint_status = event.value;
          String msg = '';
          switch(event.key) {
            case 'complaint_pending':
              msg = 'Khiếu nại của bạn đã được gửi đến Hai Nông, chúng tôi sẽ xử lý và liên hệ bạn trong 3-4 ngày làm việc!';
              break;
            case 'complaint_cancelled':
              msg = 'Khiếu nại của bạn đã bị hủy';
          }
          emit(LoadCountStatusState(msg));
          return;
        }

        detail!.status = event.key;
        emit(TurnOnOffState(isShop(), event.key));
      } else emit(const BaseState());
    });

    /// print bill
    on<ConnectEvent>((event, emit) async {
      emit(const BaseState(isShowLoading: true));
      final resp = await ApiClient().getBytes(detail!.awb_file_url!, timeout: 120);
      if (resp != null) {
        UtilUI.goToNextPage(data!.context, PrintPreviewPDF(build: (_) => resp));
      }
      emit(const BaseState());
    });
  }

  bool isNotCancel() => detail!.status != 'cancelled' && detail!.status != 'rejected' && detail!.status != 'admin_rejected';

  bool isShop() => !(isMine == true) || idBusiness != null;

  void loadDetail() => add(detail!.items.isEmpty ? LoadCountStatusEvent() : ShowClearSearchEvent(false));

  Future<bool> _getShippingLogs(int id, {bool isShowDialog = false, String type = 'invoice_user'}) async {
    if (list == null && isNotCancel()) {
      final resp = await ApiClient().getList('logistics/shipping_logs?object_id=$id&type=$type&');
      if (resp.isNotEmpty) list = resp;
    }
    return list != null && list!.isNotEmpty && isShowDialog;
  }

  void reloadDetail() => add(LoadCountStatusEvent(hasAddLast: true));

  void cancel() {
    final reason = (isShop() ? 'Từ chối' : 'Hủy') + ' đơn';
    UtilUI.showConfirmDialog(data!.context, 'Lý do ' + reason.toLowerCase(), 'Nhập lý do', 'Lý do không được trống',
        title: reason, lblOK: 'XÁC NHẬN ' + reason.toUpperCase(), line: 3, inputType: TextInputType.multiline,
        action: TextInputAction.newline, maxLength: 150, padding: const EdgeInsets.all(10)).then((value) {
      if (value is String) add(TurnOnOffEvent(isShop() ? 'rejected' : 'cancelled', value));
    });
  }

  bool canRefund() {
    if (isShop() || detail!.status != 'delivered') return false;
    if (detail!.delivered_at == null || detail!.delivered_at == '') return false;
    final timeDelivery = Util.stringToDateTime(detail!.delivered_at!);
    if (DateTime.now().difference(timeDelivery).inDays > 3) return false;
    return true;
  }

  bool canComplaint() {
    if (detail!.complaint_status == 'pending') return false;
    if (detail!.delivered_at != null && detail!.delivered_at!.isNotEmpty) {
      final timeDelivery = Util.stringToDateTime(detail!.delivered_at!);
      if (DateTime.now().difference(timeDelivery).inDays > 3) return false;
    }
    return detail!.complaint_status == null && (detail!.status == 'ready_to_ship' || detail!.status == 'shipped' || detail!.status == 'delivered');
  }

  void print() {
    if (detail!.awb_file_url == null || detail!.awb_file_url!.isEmpty) {
      UtilUI.showCustomDialog(data!.context, 'Mẩu in không tồn tại', isError: true, isNewUI: true);
      return;
    }
    add(ConnectEvent(0, '', ''));
  }
  // End Order Detail

  /// Start Shipment Unit Selection
  Future<void> _initShipmentSelection() async {
    /// handle add/remove images: ShowClearSearchEvent

    /// load shipment provider list
    list = [];
    services = [];
    optValues = {
      'pickOption': 'pickOption@cod@Lấy tận nơi',
      'shipmentType': 'shipmentType@exp@Express nhanh < 20kg',
      'transport': 'transport@road@Đường bộ',
      'shipmentFee': 0.0
    };
    on<LoadCountStatusEvent>((event, emit) async {
      emit(const BaseState(isShowLoading: true));
      switch (await _getShipmentProviders(isBack: true, idBusiness: idBusiness)) {
        case 0:
          UtilUI.showCustomDialog(data!.context, 'Danh sách đơn vị vận chuyển không có', isError: true, isNewUI: true)
              .then((value) => UtilUI.goBack(data!.context, false));
          break;
        case 1:
          for (int i = list!.length - 1; i > -1; i--) {
            if (!(list![i]['has_been_linked']??false) || list![i]['available_status'] != 'active') {
              list!.removeAt(i);
            }
          }

          if(list!.isNotEmpty) {

            /// get options here

            final temp = 'shipmentUnit@0@${list![0]['shipping_code']}@on';
            list![0].update('selected', (v) => temp, ifAbsent: () => temp);
            optValues!.update('shipmentUnit', (v) => temp, ifAbsent: () => temp);
            emit(LoadCountStatusState(false)); // state = false => load list success
            emit(TurnOnOffState('shipmentUnit@0@${list![0]['shipping_code']}', temp));
            if (list![0]['shipping_code'] == 'VTP') _calculateFee(checkEmpty: false);
            return;
          }

          UtilUI.showCustomDialog(data!.context, 'Chưa có thiết lập hoặc kết nối với đơn vị vận chuyển',
            title: 'Thiết lập ngay', isNewUI: true, isActionCancel: true, lblCancel: 'Hủy').then((value) {
              value == true ? UtilUI.goToNextPage(data!.context, OrderShipmentSettingPage(reload: () => add(LoadCountStatusEvent()))) :
                UtilUI.goBack(data!.context, false);
            });
          break;
      }
      emit(const BaseState());
    });

    /// change options
    on<TurnOnOffEvent>((event, emit) {
      if (optValues!.containsValue(event.value)) return;
      final value = event.key + (event.value.contains('@off') ? '@on' : '@off');
      final temp = _setOption(event.key, value);
      if (temp != null) emit(TurnOnOffState(temp[0], temp[1]));
      emit(TurnOnOffState(event.key, value));

      if (event.key.toString().contains('shipmentUnit')) {
        emit(ExpandState('orderService', '@expand'));
        final shipUnit = list![int.parse(value.split('@')[1])];
        final isVTP = shipUnit['shipping_code'] == 'VTP';
        if (isVTP) {
          if (services!.isNotEmpty) {
            final arrayService = (optValues!['orderService']??'').toString().split('@');
            if (arrayService.length > 2) emit(ExpandState('shipmentFee', _getFeeShipFromService()));
            return;
          }
        } else {
          optValues!.update('shipmentFee', (v) => 0.0);
          emit(ExpandState('shipmentFee', 0.0));
        }
        _calculateFee();

      } else if (event.key.toString().contains('orderService')) {
        final service = services![int.parse(value.split('@')[1])];
        final fee = (service['fee']??0.0).toDouble();
        optValues!.update('shipmentFee', (v) => fee);
        emit(ExpandState('shipmentFee', fee));
        if (!value.contains('@expand')) emit(ExpandState('orderServiceAdd', '@expand'));
      }
    });

    /// change options
    on<ExpandEvent>((event, emit) {
      ///'pickOption@cod@Lấy tận nơi'
      ///'pickOption@post@Gửi bưu cục'
      ///'shipmentType@exp@Express nhanh < 20kg'
      ///'shipmentType@bbs@BBS lớn >= 20kg'
      ///'transport@road@Đường bộ'
      ///'transport@fly@Đường bay'
      ///'orderService@ser@Dịch vụ chính'

      if (event.value == 'off' || event.type == 'shipmentFee') {
        emit(ExpandState(event.type, event.value));
        return;
      }

      switch(event.type) {
        case 'pickOption':
        case 'shipmentType':
        case 'transport':
        case 'orderService':
        case 'orderServiceAdd':
          if (event.type == 'shipmentType') {
            final shipUnit = list![int.parse(optValues!['shipmentUnit'].split('@')[1])];
            final isGHTK = shipUnit['shipping_code'] == 'GHTK';

            if (isGHTK && event.value.contains('@exp') && double.parse(
                TextFieldCustom.stringToDouble(ctrSearch!.text)) > 20000) return;
            else if (isGHTK && event.value.contains('@bbs') && double.parse(
                TextFieldCustom.stringToDouble(ctrSearch!.text)) < 20000) return;
          }

          optValues!.update(event.type, (value) => event.value);
          emit(ExpandState(event.type, event.value));
          if (event.type == 'shipmentType' || event.type == 'transport') _calculateFee();
          return;
      }
    });

    /// calculate shipment fee and create shipment
    on<ConnectEvent>((event, emit) async {
      emit(const BaseState(isShowLoading: true));
      final shipUnit = list![int.parse(optValues!['shipmentUnit'].split('@')[1])];
      final isGHTK = shipUnit['shipping_code'] == 'GHTK';
      final isVTP = shipUnit['shipping_code'] == 'VTP';
      final body = {
        "invoice_id": detail!.id,
        "shipping_id": shipUnit['id'],
        "package_info": {
          "weight": ctrSearch!.text.isEmpty ? 0 : double.parse(TextFieldCustom.stringToDouble(ctrSearch!.text))
        }
      };

      if (isGHTK) {
        body.addAll({
          "shipment_info": {
            "shipment_type": optValues!['shipmentType'].split('@')[1],
            if (optValues!['shipmentType'].contains('@exp')) "transport": optValues!['transport'].split('@')[1],
            "pick_option": optValues!['pickOption'].split('@')[1],
            "order_note": ctrNote!.text.trim()
          }
        });
      }

      /// calculate shipment fee
      dynamic resp;
      if (event.index < 2) {
        if (!isVTP || services!.isEmpty) {
          String temp = idBusiness == null ? '' : '?business_association_id=$idBusiness';
          resp = await ApiClient().postJsonAPI(Constants().apiVersion + 'logistics/get_shipping_fee' + temp, null, body, dataIsJson: true);
          if (ctrSearch!.text.isEmpty) resp = null;
        }

        resp ??= BaseResponse(success: true, data: isVTP ? [] : {'fee': 0.0});

        if (resp.msg == 'invalid_address_format') {
          emit(LoadCountStatusState(resp.data));
          return;
        }
        if (data!.isResponseNotError(resp, isNewUI: true)) {
          dynamic fee = 0.0;
          if (isGHTK) {
            fee = (resp.data['fee']??0.0).toDouble();
            optValues!.update('shipmentFee', (v) => fee);
            emit(ExpandState('shipmentFee', fee));

            if (event.index == 1) {
              _confirmCreateShipment();
              return;
            }
          } else if (isVTP) {
            if (services!.isEmpty && resp.data.isNotEmpty) services!.addAll(resp.data);
            emit(ExpandState('shipmentFee', _getFeeShipFromService()));
            emit(ExpandState('orderService', '@expand'));
            if (event.index == 1) {
              _confirmCreateShipment();
              return;
            }
          }
        }
      }

      /// create shipment
      if (event.index == 2) {
        emit(const BaseState(isShowLoading: true));
        if (isVTP) {
          final array = optValues!['orderService'].split('@');
          String serviceAdd = '', service = '';
          if (array.length > 2) {
            service = array[2];
            final subServices = (services![int.parse(array[1])]['extra_service'])??[];
            for(var item in subServices) {
              if ((item['selected']??'').contains('@on')) serviceAdd += ',' + (item['service_code']??'');
            }
            serviceAdd = serviceAdd.replaceFirst(',', '');
          }

          body.addAll({
            "shipment_info": {
              "order_service": service,
              "order_service_add": serviceAdd,
              "order_note": ctrNote!.text.trim()
            }
          });
        }

        resp = await ApiClient().postJsonAPI(Constants().apiVersion + 'logistics/create_order', null, body, dataIsJson: true);
        if (resp.msg == 'invalid_address_format') {
          emit(LoadCountStatusState(resp.data['data']));
          return;
        }
        if (data!.isResponseNotError(resp, isNewUI: true, passString: true)) {
          emit(LoadCountStatusState(true)); // state = true => create shipment success
          return;
        }
      }

      emit(const BaseState());
    });

    ctrNote = TextEditingController();
    ctrSearch = TextEditingController();
    fcSearch = FocusNode();
    fcSearch!.addListener(_listenFocus);
  }

  dynamic _setOption(String optKey, String value) {
    List<String> array = optKey.split('@');
    String type = array[0];

    int index = int.parse(array[1]);
    switch(type) {
      case 'shipmentUnit':
        list![index].update('selected', (v) => value, ifAbsent: () => value);
        break;
      case 'orderService':
        services![index].update('selected', (v) => value, ifAbsent: () => value);
        break;
      case 'orderServiceAdd':
        final arrayService = (optValues!['orderService']??'').toString().split('@');
        final subServices = services![int.parse(arrayService[1])]['extra_service'];
        subServices![index].update('selected', (v) => value, ifAbsent: () => value);
        return null;
      default: return null;
    }

    array = (optValues![type]??'$type@-1@NONE@off').split('@');
    optValues!.update(type, (v) => value, ifAbsent: () => value);

    index = int.parse(array[1]);
    if (index > -1) {
      String temp = '${array[0]}@${array[1]}@${array[2]}';
      value = temp + '@off';
      switch(type) {
        case 'shipmentUnit':
          list![index].update('selected', (v) => value, ifAbsent: () => value);
          return [temp, value];
        case 'orderService':
          services![index].update('selected', (v) => value, ifAbsent: () => value);
          return [temp, value];
      }
    }
    return null;
  }

  void _listenFocus() {
    if (fcSearch!.hasFocus) ctrSearch!.addListener(_listenText);
    else Timer(const Duration(seconds: 1), () => ctrSearch!.removeListener(_listenText));
  }

  void _listenText() {
    if (!fcSearch!.hasFocus) _calculateFee(showAlert: true);
  }

  void changeWeight(control, value) {
    if (ctrSearch!.text.isEmpty) {
      optValues!.update('shipmentFee', (v) => 0.0);
      add(ExpandEvent(0.0, type: 'shipmentFee'));
      return;
    }

    final shipUnit = list![int.parse(optValues!['shipmentUnit'].split('@')[1])];
    final isGHTK = shipUnit['shipping_code'] == 'GHTK';
    if (isGHTK && optValues!['shipmentType'].contains('@exp') && double.parse(TextFieldCustom.stringToDouble(ctrSearch!.text)) > 20000) {
      ctrSearch!.removeListener(_listenText);
      UtilUI.showCustomDialog(data!.context, 'Tổng khối lượng đơn hàng trên 20kg sẽ chọn loại đơn hàng là "BBS lớn >= 20kg"', isNewUI: true, isError: true)
        .whenComplete(() => add(ExpandEvent('shipmentType@bbs@BBS lớn >= 20kg', type: 'shipmentType')));
    } else if (isGHTK && optValues!['shipmentType'].contains('@bbs') && double.parse(TextFieldCustom.stringToDouble(ctrSearch!.text)) < 20000) {
      ctrSearch!.removeListener(_listenText);
      UtilUI.showCustomDialog(data!.context, 'Tổng khối lượng đơn hàng dưới 20kg sẽ chọn loại đơn hàng là "Express nhanh < 20kg"', isNewUI: true, isError: true)
        .whenComplete(() => add(ExpandEvent('shipmentType@exp@Express nhanh < 20kg', type: 'shipmentType')));
    }
  }

  double _getFeeShipFromService() {
    final arrayService = (optValues!['orderService']??'').toString().split('@');
    if (arrayService.length > 2) {
      final service = services![int.parse(arrayService[1])];
      final fee = (service['fee']??0.0).toDouble();
      optValues!.update('shipmentFee', (v) => fee);
      return fee;
    }
    return 0.0;
  }

  void _calculateFee({int hasCreateShipment = 0, bool showAlert = false, bool checkEmpty = true}) {
    if (checkEmpty && ctrSearch!.text.isEmpty) {
      if (showAlert) {
        UtilUI.showCustomDialog(data!.context, 'Nhập tổng khối lượng (g)',
          isNewUI: true, isError: true).whenComplete(() => fcSearch!.requestFocus());
      }
    } else add(ConnectEvent(hasCreateShipment, '', ''));
  }

  void _confirmCreateShipment() => UtilUI.showCustomDialog(data!.context,
    'Xác nhận tạo vận đơn\n(Phí ship  ${Util.doubleToString(optValues!['shipmentFee'], preFix: '₫')})',
    isNewUI: true, isActionCancel: true, lblCancel: 'Hủy', alignMessageText: TextAlign.center).then((value) {
      if (value == true) _calculateFee(hasCreateShipment: 2, showAlert: true);
    });

  void createShipment() {
    final shipUnit = list![int.parse(optValues!['shipmentUnit'].split('@')[1])];
    switch(shipUnit['shipping_code']) {
      case 'GHTK':
        if (detail!.price_total > 20000000 && optValues!['shipmentType'].contains('@exp')) {
          UtilUI.showCustomDialog(data!.context, 'Tổng giá trị đơn hàng phải trên ₫10.000 và ít hơn ₫20.000.000 đối với đơn hàng là "Express nhanh < 20kg"', isNewUI: true, isError: true);
          return;
        }
        if (detail!.price_total > 50000000 && optValues!['shipmentType'].contains('@bbs')) {
          UtilUI.showCustomDialog(data!.context, 'Tổng giá trị đơn hàng phải trên ₫10.000 và ít hơn ₫50.000.000 đối với đơn hàng là "BBS lớn >= 20kg"', isNewUI: true, isError: true);
          return;
        }
        break;

      case 'VTP':
        if (ctrSearch!.text.isEmpty) {
          UtilUI.showCustomDialog(data!.context, 'Nhập tổng khối lượng (g)',
              isNewUI: true, isError: true).whenComplete(() => fcSearch!.requestFocus());
          return;
        }
        if (!(optValues!['orderService']??'').contains('@on')) {
          UtilUI.showCustomDialog(data!.context, 'Hãy chọn dịch vụ chính để lấy thông tin phí ship', isNewUI: true, isError: true);
          return;
        }
        break;
    }

    _calculateFee(hasCreateShipment: 1, showAlert: true);
  }
  // End Shipment Unit Selection

  /// Start Refund
  Future<void> _initRefund() async {
    /// handle add/remove images/videos: ShowClearSearchEvent
    page = 0; /// handle for 1 video

    on<LoadCountStatusEvent>((event, emit) async {
      emit(const BaseState(isShowLoading: true));
      final resp = await ApiClient().postAPI(Constants().apiVersion + 'refund_invoices', 'POST', BaseResponse(), body: {
        'invoice_user_id': detail!.id.toString(),
        'remark': orderType??''
      }, realFiles: list);
      emit(LoadCountStatusState(resp));
    });
  }
  // End Refund

  /// Start Refund Detail
  Future<void> _initRefundDetail() async {
    /// load refund detail
    refundDtl = {};
    on<LoadCountStatusEvent>((event, emit) async {
      emit(const BaseState(isShowLoading: true));
      final resp = await ApiClient().getData('refund_invoices/${detail!.refund_invoice_id}?type=' + (isMine == true ? 'purchase' : 'sales'));
      if (resp != null) {
        refundDtl = resp;
        emit(LoadCountStatusState(false));
        if (detail!.refund_invoice_status == 'approved') {
          await _getShippingLogs(refundDtl['id'], type: 'refund_invoice');
          if (list != null && list!.isNotEmpty) emit(LoadCountStatusState(true));
        }
      } else emit(const BaseState());
    });

    /// set order status
    on<TurnOnOffEvent>((event, emit) async {
      emit(const BaseState(isShowLoading: true));
      final isComplaint = event.key.contains('complaint');
      final resp = await ApiClient().postAPI(Constants().apiVersion + 'refund_invoices/' +
          refundDtl['id'].toString() + (isComplaint ? '/request_complaint' : ''), 'PUT', BaseResponse(isJson: true),
          body: {isComplaint ? 'complaint_status' : 'status': event.value});
      if (data!.isResponseNotError(resp, isNewUI: true)) {
        refundDtl = resp.data;
        detail!.refund_invoice_status = resp.data['status']??detail!.refund_invoice_status;
        detail!.refund_complaint_status = resp.data['complaint_status']??detail!.refund_complaint_status;

        String msg = '';
        switch(event.key) {
          case 'approved':
            await _getShippingLogs(refundDtl['id'], type: 'refund_invoice');
            msg = 'Xác nhận tạo đơn hoàn trả thành công';
            break;
          case 'cancelled':
            msg = 'Yêu cầu trả hàng của bạn đã được hủy';
            break;
          case 'complaint_pending':
            msg = 'Khiếu nại của bạn đã được gửi đến Hai Nông, chúng tôi sẽ xử lý và liên hệ bạn trong 3-4 ngày làm việc!';
            break;
          case 'complaint_cancelled':
            msg = 'Khiếu nại của bạn đã bị hủy';
        }
        emit(LoadCountStatusState(msg));
      } else emit(const BaseState());
    });
  }

  void shopAccept() => UtilUI.showCustomDialog(data!.context, 'Xác nhận tạo đơn hoàn trả',
      lblOK: 'Xác nhận', lblCancel: 'Hủy', isActionCancel: true, isNewUI: true).then((value) {
    if (value == true) add(TurnOnOffEvent('approved', 'approved'));
  });

  void buyerCancel() => UtilUI.showCustomDialog(data!.context, 'Xác nhận hủy yêu cầu trả hàng',
      lblOK: 'Xác nhận', lblCancel: 'Hủy', isActionCancel: true, isNewUI: true).then((value) {
    if (value == true) add(TurnOnOffEvent('cancelled', 'cancelled'));
  });

  void buyerComplaint({bool isRefund = true}) => UtilUI.showCustomDialog(data!.context, 'Gửi khiếu nại đến Quản trị viên Hai Nông',
      lblOK: 'Xác nhận', lblCancel: 'Hủy', isActionCancel: true, isNewUI: true).then((value) {
    if (value == true) add(TurnOnOffEvent('complaint_pending', 'pending'));
  });

  void buyerCancelComplaint({bool isRefund = true}) => UtilUI.showCustomDialog(data!.context, 'Xác nhận hủy yêu cầu khiếu nại',
      lblOK: 'Xác nhận', lblCancel: 'Hủy', isActionCancel: true, isNewUI: true).then((value) {
    if (value == true) add(TurnOnOffEvent('complaint_cancelled', 'cancelled'));
  });
  // End Refund Detail

  /// Start Refund Rejection
  Future<void> _initRefundRejection() async {
    /// handle add/remove images/videos: ShowClearSearchEvent
    page = 0; /// handle for 1 video

    on<LoadCountStatusEvent>((event, emit) async {
      emit(const BaseState(isShowLoading: true));
      final resp = await ApiClient().postAPI(Constants().apiVersion +
        'refund_invoices/${refundDtl['id']}', 'PUT', BaseResponse(isJson: true), body: {
          'status': 'rejected',
          'remark': ctrSearch!.text
        }, realFiles: list);
      if (data!.isResponseNotError(resp, isNewUI: true)) {
        refundDtl = resp.data;
        detail!.refund_invoice_status = 'rejected';
        emit(LoadCountStatusState(true));
      } else emit(const BaseState());
    });

    ctrSearch = TextEditingController();
    fcSearch = FocusNode();
  }
  // End Refund Rejection

  /// Start Report
  Future<void> _initReport() async {
    optValues = {};
    indexProvider = 0;
    final now = DateTime.now();
    providers = DateTime(now.year, 1, 1);
    services = DateTime(now.year, 12, 31);
    refundDtl = ItemModel(id: 'year', name: 'Năm này');
    list = [
      ItemModel(id: 'today', name: 'Hôm nay'),
      ItemModel(id: 'month', name: 'Tháng này'),
      ItemModel(id: 'quarter', name: 'Quý này'),
      ItemModel(id: 'year', name: 'Năm này'),
      ItemModel(id: 'option', name: 'Tuỳ chọn')
    ];
    on<LoadCountStatusEvent>((event, emit) async {
      emit(LoadCountStatusState(false));
      if (event.value == true) return;
      emit(const BaseState(isShowLoading: true));
      String param = Util.dateToString(providers, pattern: 'dd/MM/yyyy') + '&end_date=' + Util.dateToString(services, pattern: 'dd/MM/yyyy');
      if (idBusiness != null) param += '&business_association_id=$idBusiness';
      final resp = await ApiClient().getAPI(Constants().apiVersion + 'invoice_users/report?start_date=' + param, BaseResponse(isJson: true));
      optValues = data!.isResponseNotError(resp, isNewUI: true) ? resp.data : {};
      emit(LoadCountStatusState(true));
    });
  }

  void changeTab(int index) {
    if (indexProvider != index) {
      indexProvider = index;
      add(ShowClearSearchEvent(true));
    }
  }
  // End Report
}

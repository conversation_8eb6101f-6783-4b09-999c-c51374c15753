import 'package:hainong/common/ui/button_image_widget.dart';
import 'package:hainong/common/ui/import_lib_ui.dart';
import 'package:hainong/common/ui/label_custom.dart';
import 'package:hainong/common/ui/loading.dart';
import '../cart/cart_model.dart';
import '../function/support/mission/mission_bloc.dart';
import 'order_bloc.dart';
import 'order_detail2_page.dart';
import 'order_refund_detail_page.dart';
import 'order_ui.dart';

class OrderHistory2ListPage extends BasePage {
  final Function? fnReload;
  OrderHistory2ListPage({this.fnReload, int? idBusiness, String? orderType, String? status, Map? extend, Key? key}) :
    super(pageState: _OrderHisPage2ListState(idBusiness, orderType, status, extend), key:key);
}
class _OrderHisPage2ListState extends BasePageState implements IBaseBloc {
  _OrderHisPage2ListState(int? idBusiness, String? orderType, String? status, Map? extend) {
    alive = true;
    bloc = OrderBloc('list', idBusiness: idBusiness, orderType: orderType, status: status, optValues: extend);
  }

  @override
  void dispose() {
    final reload = (widget as OrderHistory2ListPage).fnReload;
    if (reload != null) reload();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    bloc!.data = this;
  }

  @override
  Widget build(BuildContext context, {Color color = Colors.white}) {
    super.build(context);
    final ctr = bloc as OrderBloc;
    return Scaffold(appBar: AppBar(titleSpacing: 0, centerTitle: true, elevation: 0,
      title: UtilUI.createLabel(ctr.orderType == 'purchase' ? 'Đơn mua' : 'Đơn bán'),
      bottom: PreferredSize(preferredSize: Size(0.5.sw, ctr.optValues == null ? 140.sp : 240.sp),
        child: Column(children: [
          Container(
            decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(15)),
            padding: EdgeInsets.all(30.sp), margin: EdgeInsets.fromLTRB(40.sp, 0, 40.sp, 40.sp),
            child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
              ButtonImageWidget(100, ctr.search, Image.asset('assets/images/ic_search.png', width: 42.sp, color: const Color(0xFF8B8D8A))),
              Expanded(child: TextField(controller: ctr.ctrSearch,
                  onChanged: (value) {
                    if (value.length == 1) bloc!.add(ShowClearSearchEvent(true));
                    if (value.isEmpty) bloc!.add(ShowClearSearchEvent(false));
                  },
                  onSubmitted: (value) => ctr.search(),
                  textInputAction: TextInputAction.search,
                  decoration: InputDecoration(hintStyle: TextStyle(fontSize: 36.sp, color: const Color(0xFF959595)),
                      hintText: 'Nhập mã đơn hàng', contentPadding: EdgeInsets.symmetric(horizontal: 40.sp), isDense: true,
                      border: const UnderlineInputBorder(borderSide: BorderSide.none))
              )),
              BlocBuilder(bloc: bloc, buildWhen: (oldS, newS) => newS is ShowClearSearchState,
                  builder: (context, state) {
                    bool show = false;
                    if (state is ShowClearSearchState) show = state.value;
                    return show ? Padding(padding: EdgeInsets.only(right: 20.sp), child: ButtonImageWidget(100, ctr.clear,
                        Icon(Icons.clear, size: 48.sp, color: const Color(0xFF676767)))) : const SizedBox();
                  })
            ])),
          if (ctr.optValues != null)
          Padding(child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
            LabelCustom('Từ ngày: ' + Util.dateToString(ctr.optValues!['start'], pattern: 'dd/MM/yyyy'), size: 40.sp, weight: FontWeight.normal),
            LabelCustom('Đến ngày: ' + Util.dateToString(ctr.optValues!['end'], pattern: 'dd/MM/yyyy'), size: 40.sp, weight: FontWeight.normal),
          ]), padding: EdgeInsets.fromLTRB(40.sp, 0, 40.sp, 40.sp))
        ]))),
      backgroundColor: const Color(0xFFF5F5F5),
      body: Stack(children: [
        Column(children: [
          SingleChildScrollView(scrollDirection: Axis.horizontal, child: Row(children: [
            _TabItem('Chờ xác nhận', 'pending', ctr),
            _TabItem('Chờ lấy hàng', 'ready_to_ship', ctr),
            _TabItem('Đang giao hàng', 'shipped', ctr),
            _TabItem('Đã giao', 'delivered', ctr),
            _TabItem('Đã hủy', 'cancelled', ctr),
            _TabItem('Trả hàng', 'returned', ctr),
            //_TabItem('Hoàn thành', 'done', ctr),
          ])),

          if (ctr.orderType == 'sales') BlocBuilder(bloc: bloc, buildWhen: (o,n) => n is TurnOnOffState || (n is LoadCountStatusState && n.resp == false),
            builder: (_,state) {
              if ((ctr.status != 'pending' && ctr.status != '') || (state is LoadCountStatusState && state.resp == false)) {
                return Padding(child: ButtonImageWidget(0, _showDialog,
                  Container(margin: EdgeInsets.all(20.sp), padding: EdgeInsets.all(20.sp),
                    decoration: BoxDecoration(border: Border.all(color: const Color(0xFFFFAD26))),
                    child: Row(children: [
                      Expanded(child: Column(children: [
                        LabelCustom('Đơn vị vận chuyển', color: const Color(0xFFFFAD26), size: 40.sp, weight: FontWeight.w500),
                        if (ctr.indexProvider != null && ctr.indexProvider! > -1) Padding(padding: EdgeInsets.only(top: 10.sp),
                          child: LabelCustom(ctr.providers[ctr.indexProvider]['shipping_name'],
                              color: const Color(0xFFFFAD26), size: 36.sp, weight: FontWeight.w400)),
                      ])),
                      Icon(Icons.arrow_drop_down, color: const Color(0xFFFFAD26), size: 64.sp)
                    ])), color: Colors.white), padding: EdgeInsets.symmetric(vertical: 16.sp));
              }
              return SizedBox(height: 16.sp);
            }
          ),

          Expanded(child: RefreshIndicator(onRefresh: () async => ctr.search(),
            child: BlocBuilder(bloc: bloc, buildWhen: (o, n) => n is LoadCountStatusState && n.resp == true,
              builder: (context, state) {
                if (ctr.list == null || ctr.list!.isEmpty) {
                  return Column(children: [
                    SizedBox(height: 180.sp),
                    Image.asset('assets/images/v10/ic_empty_list_v10.png', width: 150.sp),
                    LabelCustom('Chưa có đơn hàng', size: 30.sp, color: const Color(0xFF818181), weight: FontWeight.normal)
                  ]);
                }

                final isShop = ctr.isShop();
                return ListView.separated(padding: EdgeInsets.zero, shrinkWrap: true, controller: ctr.scroller,
                  physics: const AlwaysScrollableScrollPhysics(), itemCount: ctr.list!.length,
                  separatorBuilder: (_,__) => SizedBox(height: 16.sp),
                  itemBuilder: (_,i) {
                    final OrderModel item = ctr.list![i];
                    return ButtonImageWidget(0, () => _openDetail(i),
                        Container(padding: EdgeInsets.all(40.sp), color: Colors.white,
                          child: Column(children: [
                            ShopUI(item, ctr.orderType == 'sales'),

                            OrderProductList(item.items, ctr, i, size: 40.sp),
                            if (item.items.length > 1) Padding(padding: EdgeInsets.only(top: 40.sp),
                              child: ButtonImageWidget(0, () => ctr.add(ExpandEvent(item.expanded??'${i}_off')),
                                BlocBuilder(bloc: bloc, buildWhen: (o,n) => n is ExpandState && n.type == i.toString(),
                                  builder: (_,__) {
                                    final isExp = (item.expanded??'${i}_off').contains('_on');
                                    return Padding(padding: const EdgeInsets.all(5),
                                      child: LabelCustom(isExp ? 'Thu gọn' : 'Xem thêm sản phẩm', color: const Color(0xFF818181), size: 27.sp, weight: FontWeight.normal));
                                  }))),

                            Divider(height: 80.sp, color: Colors.black, thickness: 0.2),
                            Row(children: [
                              LabelCustom('${item.items.length} sản phẩm', color: const Color(0xFF818181),
                                  size: 40.sp, weight: FontWeight.normal),
                              Expanded(child: Row(mainAxisAlignment: MainAxisAlignment.end, children: [
                                LabelCustom('  Thành tiền: ', color: Colors.black, size: 40.sp, weight: FontWeight.normal),
                                Flexible(child: LabelCustom(Util.doubleToString(item.price_total, preFix: '₫'),
                                  color: const Color(0xFF1AB686), size: 40.sp, weight: FontWeight.normal))
                              ], crossAxisAlignment: CrossAxisAlignment.start))
                            ], crossAxisAlignment: CrossAxisAlignment.start),

                            Divider(height: 80.sp, color: Colors.black, thickness: 0.2),
                            _StatusItem(ctr.list![i], isShop, () => _openDetail(i, isRefund: true), ctr.orderType == 'sales')
                          ])
                        )
                    );
                  });
              })
          ))
        ]),
        Loading(bloc)
      ]));
  }

  void _openDetail(int index, {bool? isRefund}) {
    final ctr = bloc as OrderBloc;
    UtilUI.goToNextPage(context, isRefund == true ? OrderRefundDtlPage(ctr.list![index], ctr.orderType == 'purchase', ctr.search, idBusiness: ctr.idBusiness) :
      OrderDtl2Page(ctr.list![index], ctr.orderType == 'purchase', ctr.search, idBusiness: ctr.idBusiness));
  }

  void _showDialog() => showDialog(context: context, builder: (_) {
    final List list = (bloc as OrderBloc).providers;
    final List<Widget> children = [];
    children.add(const Divider(height: 0.2, thickness: 0.2, color: Colors.black45));
    children.add(ButtonImageWidget(0, () => UtilUI.goBack(context, -1),
        Container(padding: EdgeInsets.all(40.sp), child: LabelCustom('Tất cả đơn vị vận chuyển', size: 40.sp,
            color: Colors.black87, weight: FontWeight.normal), width: 1.sw)));
    for (int i = 0; i < list.length; i++) {
      children.add(const Divider(height: 0.2, thickness: 0.2, color: Colors.black45));
      children.add(ButtonImageWidget(0, () => UtilUI.goBack(context, i),
        Container(padding: EdgeInsets.all(40.sp), child: LabelCustom(list[i]['shipping_name'], size: 40.sp,
          color: Colors.black87, weight: FontWeight.normal), width: 1.sw)));
    }
    return AlertDialog(scrollable: true, contentPadding: EdgeInsets.zero, titlePadding: EdgeInsets.all(40.sp),
      title: LabelCustom('Chọn nhà cung cấp vận chuyển', color: Colors.black87, size: 40.sp, align: TextAlign.center),
      content: Column(children: children, mainAxisSize: MainAxisSize.min, crossAxisAlignment: CrossAxisAlignment.start));
  }).then((value) {
    if (value != null) bloc!.add(ConnectEvent(value, '', ''));
  });
}

class _TabItem extends StatelessWidget {
  final String label, status;
  final OrderBloc bloc;
  const _TabItem(this.label, this.status, this.bloc);
  @override
  Widget build(BuildContext context) => ButtonImageWidget(0, () => bloc.changeStatus(status), BlocBuilder(bloc: bloc,
    buildWhen: (o,n) => n is TurnOnOffState && n.value == status, builder: (_,__) {
      final active = status == bloc.status;
      return Container(padding: EdgeInsets.all(40.sp),
        decoration: BoxDecoration(color: Colors.white,
          border: Border(bottom: BorderSide(color: Color(active ? 0xFFFFAD26 : 0xFFFFFFFF), width: 2))),
        child: LabelCustom(label, color: Color(active ? 0xFFFFAD26 : 0xFF818181), size: 40.sp,
          weight: active ? FontWeight.bold : FontWeight.normal)
      );
    }));
}

class _StatusItem extends StatelessWidget {
  final OrderModel item;
  final bool isShop, isSalers;
  final Function funRefDetail;
  const _StatusItem(this.item, this.isShop, this.funRefDetail, this.isSalers);
  @override
  Widget build(BuildContext context) {
    final bool isCancel = item.status == 'cancelled' || item.status == 'rejected' ||
        item.status == 'admin_rejected' || item.status == 'returned' || item.complaint_status != null;
    final String asset = item.status == 'pending' || item.status == 'ready_to_ship' ? 'ready_to_ship' : 'shipped';
    final Color color = Color(isCancel ? 0xFFFFAD26 : 0xFF1AB686);
    String label = item.remark;
    if (item.refund_invoice_status != null) {
      label = 'opt_ref_' + item.refund_invoice_status!;
      if (item.refund_complaint_status != null) label = 'opt_cpt_' + item.refund_complaint_status!;
    } else {
      label = 'opt_ord_' + item.status;
      if (item.status == 'shipped' && item.shipping_pick_option == 'post') label += '_post';
      else if (item.status == 'cancelled' && isSalers) label += '_salers';
      if (item.complaint_status != null) label = 'opt_cpt_' + item.complaint_status!;
    }
    label = MultiLanguage.get(label);
    return Row(children: [
      if (!isCancel) Padding(padding: const EdgeInsets.only(right: 10),
          child: Image.asset('assets/images/v10/ic_${asset}_v10.png', color: color, width: 48.sp)),
      Expanded(child: LabelCustom(label, color: color, size: 40.sp, weight: FontWeight.normal)),
      item.status == 'returned' ? ButtonImageWidget(5, funRefDetail,
        Padding(padding: EdgeInsets.all(20.sp), child: LabelCustom('Chi tiết trả hàng', size: 40.sp, weight: FontWeight.normal)), color: color) :
        Icon(Icons.arrow_forward_ios, size: 48.sp, color: const Color(0xFF818181))
    ]);
  }
}
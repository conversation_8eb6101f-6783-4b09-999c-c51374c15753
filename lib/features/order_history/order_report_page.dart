import 'package:flutter_datetime_picker/flutter_datetime_picker.dart';
import 'package:hainong/common/ui/box_dec_custom.dart';
import 'package:hainong/common/ui/button_image_widget.dart';
import 'package:hainong/common/ui/import_lib_ui.dart';
import 'package:hainong/common/ui/label_custom.dart';
import 'package:hainong/common/ui/loading.dart';
import 'package:hainong/common/ui/tab_item.dart';
import '../function/support/mission/mission_bloc.dart';
import 'order_history2_list_page.dart';
import 'order_bloc.dart';

class OrderReportPage extends BasePage {
  OrderReportPage({int? idBusiness, Key? key}) : super(pageState: _OrderReportPageState(idBusiness: idBusiness), key:key);
}
class _OrderReportPageState extends BasePageState implements IBaseBloc {
  _OrderReportPageState({int? idBusiness}) {
    bloc = OrderBloc('report', idBusiness: idBusiness);
  }

  @override
  void initState() {
    super.initState();
    bloc!.data = this;
  }

  @override
  Widget build(_, {Color color = Colors.white}) {
    final ctr = bloc as OrderBloc;
    return Scaffold(appBar: AppBar(titleSpacing: 0, centerTitle: true, elevation: 0,
      title: UtilUI.createLabel('Báo cáo tổng quan')), backgroundColor: color,
      body: Stack(children: [
        ListView(padding: EdgeInsets.all(40.sp), children: [
          LabelCustom('Doanh thu bán hàng', size: 45.sp, color: const Color(0xFF4B4B4B), weight: FontWeight.normal),
          Padding(padding: EdgeInsets.symmetric(vertical: 40.sp),
            child: LabelCustom('Thời gian báo cáo', size: 38.sp, color: const Color(0xFF4B4B4B), weight: FontWeight.normal)),
          Row(children: [
            _OptionItem(ctr, 'option', _showDay, icon: Icons.arrow_drop_down),
            SizedBox(width: 20.sp),
            _OptionItem(ctr, 'start', _selectDate),
            SizedBox(width: 20.sp),
            _OptionItem(ctr, 'end', () => _selectDate(isStart: false))
          ]),
          Padding(child: BlocBuilder(bloc: bloc, buildWhen: (_,n) => n is ShowClearSearchState,
            builder: (_,__) => Row(children: [
              TabItem('Doanh thu chờ xử lý', 0, ctr.indexProvider == 0, ctr.changeTab, size: 42.sp),
              TabItem('Doanh thu thực tế', 1, ctr.indexProvider == 1, ctr.changeTab, size: 42.sp)
            ])), padding: EdgeInsets.only(top: 40.sp, bottom: 20.sp)),
          BlocBuilder(bloc: bloc, buildWhen: (_,n) => n is ShowClearSearchState || (n is LoadCountStatusState && n.resp == true),
            builder: (_,__) {
              if (ctr.indexProvider == 0) {
                return Column(children: [
                  _Item('Chờ xác nhận', ctr.optValues!['pending'], 'pending', _gotoOrderList),
                  _Item('Chờ lấy hàng', ctr.optValues!['ready_to_ship'], 'ready_to_ship', _gotoOrderList),
                  _Item('Đang giao', ctr.optValues!['shipped'], 'shipped', _gotoOrderList),
                ]);
              }
              return _Item('Giao thành công', ctr.optValues!['delivered'], 'delivered', _gotoOrderList);
            })
        ]),
        Loading(bloc)
      ]));
  }

  void _showDay() {
    final ctr = bloc as OrderBloc;
    UtilUI.showOptionDialog(context, 'Chọn thời gian báo cáo', ctr.list, ctr.refundDtl.id).then((value) {
      if (value != null) {
        ctr.refundDtl.setValue(value.id, value.name);
        final now = DateTime.now();
        switch(value.id) {
          case 'today':
            ctr.providers = ctr.services = now;
            break;
          case 'month':
            ctr.providers = DateTime(now.year, now.month, 1);
            ctr.services = DateTime(now.year, now.month + 1, 1).add(const Duration(days: -1));
            break;
          case 'quarter':
            if (now.month < 4) {
              ctr.providers = DateTime(now.year, 1, 1);
              ctr.services = DateTime(now.year, 3, 31);
            } else if (now.month < 7) {
              ctr.providers = DateTime(now.year, 4, 1);
              ctr.services = DateTime(now.year, 6, 30);
            } else if (now.month < 10) {
              ctr.providers = DateTime(now.year, 7, 1);
              ctr.services = DateTime(now.year, 9, 30);
            } else if (now.month < 12) {
              ctr.providers = DateTime(now.year, 10, 1);
              ctr.services = DateTime(now.year, 12, 31);
            }
            break;
          case 'year':
            ctr.providers = DateTime(now.year, 1, 1);
            ctr.services = DateTime(now.year, 12, 31);
            break;
          default:
            bloc!.add(LoadCountStatusEvent(value: true));
            return;
        }
        bloc!.add(LoadCountStatusEvent());
      }
    });
  }

  void _selectDate({bool isStart = true}) {
    final ctr = bloc as OrderBloc;
    if (ctr.refundDtl.id == 'option') {
      DatePicker.showDatePicker(context,
          minTime: isStart ? DateTime.now().add(const Duration(days: -3650)) : ctr.providers,
          maxTime: isStart ? ctr.services : DateTime.now(), showTitleActions: true,
          currentTime: isStart ? ctr.providers : ctr.services, locale: LocaleType.vi,
          onConfirm: (DateTime date) {
            isStart ? ctr.providers = date : ctr.services = date;
            bloc!.add(LoadCountStatusEvent());
          });
    }
  }

  void _gotoOrderList(String status) {
    final ctr = bloc as OrderBloc;
    UtilUI.goToNextPage(context, OrderHistory2ListPage(orderType: 'sales',
        status: status, extend: {
          'start': ctr.providers!,
          'end': ctr.services!
        }));
  }
}

class _OptionItem extends StatelessWidget {
  final OrderBloc bloc;
  final String type;
  final Function fnAction;
  final IconData icon;
  const _OptionItem(this.bloc, this.type, this.fnAction, {this.icon = Icons.calendar_month, Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Expanded(child: ButtonImageWidget(5, fnAction,
        Container(padding: EdgeInsets.all(20.sp),
            decoration: BoxDecCustom(hasShadow: false, hasBorder: true, width: 1, bgColor: Colors.transparent, radius: 5),
            child: Row(children: [
                Expanded(child: BlocBuilder(bloc: bloc, buildWhen: (_,n) => n is LoadCountStatusState && n.resp == false,
                    builder: (_,__) {
                        dynamic label = bloc.refundDtl!.name;
                        switch(type) {
                            case 'start': label = bloc.providers!; break;
                            case 'end': label = bloc.services!;
                        }
                        if (label is DateTime) label = Util.dateToString(label, pattern: 'dd/MM/yyyy');
                        return LabelCustom(label, color: const Color(0xFF4B4B4B), size: 40.sp, weight: FontWeight.normal);
                    })),
                Icon(icon, color: const Color(0xFF888888), size: 48.sp)
            ])
        )));
  }
}

class _Item extends StatelessWidget {
  final String title, status;
  final dynamic value;
  final Function fnAction;
  const _Item(this.title, this.value, this.status, this.fnAction, {Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) => ButtonImageWidget(0, () => fnAction(status),
      Padding(child: Row(mainAxisAlignment: MainAxisAlignment.end, children: [
        LabelCustom(title, size: 40.sp, color: const Color(0xFF4B4B4B), weight: FontWeight.normal),
        if (value != null) Expanded(child: LabelCustom(Util.doubleToString(value!.toDouble(), supFix: ' vnđ'), size: 40.sp,
            color: const Color(0xFF0275D8), weight: FontWeight.normal, align: TextAlign.right)),
      ]), padding: EdgeInsets.symmetric(vertical: 20.sp)));
}
import 'package:flutter/services.dart';
import 'package:hainong/common/ui/box_dec_custom.dart';
import 'package:hainong/common/ui/button_image_widget.dart';
import 'package:hainong/common/ui/image_network_asset.dart';
import 'package:hainong/common/ui/import_lib_ui.dart';
import 'package:hainong/common/ui/label_custom.dart';
import 'package:hainong/common/ui/loading.dart';
import 'package:hainong/common/ui/textfield_custom.dart';
import 'package:hainong/features/cart/cart_model.dart';
import '../profile/ui/profile_edit_page.dart';
import 'order_bloc.dart';
import 'order_ui.dart';

//import 'dart:io';
//import 'package:dotted_border/dotted_border.dart';
//import 'package:hainong/common/models/file_byte.dart';
//import 'package:hainong/common/models/item_list_model.dart';
//import 'package:hainong/common/ui/button_image_circle_widget.dart';
//import 'package:hainong/common/ui/permission_image_page_state.dart';
//import '../function/support/mission/mission_bloc.dart';

class OrderShipmentSelectPage extends BasePage {
  final Function funReload;
  OrderShipmentSelectPage(OrderModel order, this.funReload, {int? idBusiness, Key? key}) :
    super(pageState: _OrderShipmentSelectPageState(order, idBusiness: idBusiness), key: key);
}

//class _OrderShipmentSelectPageState extends PermissionImagePageState implements IBaseBloc {
class _OrderShipmentSelectPageState extends BasePageState implements IBaseBloc {
  final Color grey = const Color(0xFF818181);

  _OrderShipmentSelectPageState(OrderModel order, {int? idBusiness}) {
    /*images = [];
    multiSelect = true;
    pass = true;
    maxSelect = 5;
    setOnlyImage();*/
    bloc = OrderBloc('shipment_selection', detail: order, isMine: false, idBusiness: idBusiness);
    bloc!.data = this;
    bloc!.stream.listen((state) {
      if (state is LoadCountStatusState) {
        if (state.resp == true) {
          UtilUI.showCustomDialog(context, 'Xác nhận tạo vận đơn thành công', isNewUI: true).whenComplete(() {
            (widget as OrderShipmentSelectPage).funReload();
            UtilUI.goBack(context, true);
          });
        } else if (state.resp is String) {
          UtilUI.showCustomDialog(context, state.resp, lblCancel: 'Hủy', lblOK: 'Tiếp tục',
            isActionCancel: true, isNewUI: true).then((value) {
              if (value == true) UtilUI.goToNextPage(context, ProfileEditPage());
            });
        }
      }
    });
  }

  /*@override
  void showLoadingPermission({bool value = true}) => bloc!.add(LoadingEvent(value));

  @override
  loadFiles(List<File> files) async {
    showLoadingPermission();
    maxSelect = 5;
    bool overSize = false;
    int size = getSize();
    for (int i = 0; i < files.length; i++) {
      if (size + files[i].lengthSync() > 102400000) {
        overSize = true;
        break;
      }
      size += files[i].lengthSync();
      if (images!.length < 5) images!.add(FileByte(files[i].readAsBytesSync(), files[i].path));
    }
    bloc!.add(ShowClearSearchEvent(true));
    if (overSize) UtilUI.showCustomDialog(context, MultiLanguage.get('msg_file_100mb'), isNewUI: true);
  }*/

  @override
  Widget build(BuildContext context, {Color color = Colors.white}) {
    final ctr = bloc as OrderBloc;
    return Scaffold(appBar: AppBar(titleSpacing: 0, centerTitle: true, elevation: 2,
        title: UtilUI.createLabel('Lựa chọn đơn vị vận chuyển')),
      backgroundColor: color,
      body: Stack(children: [
        SafeArea(minimum: EdgeInsets.only(bottom: 40.sp),
          child: GestureDetector(onTap: clearFocus, child: Column(crossAxisAlignment: CrossAxisAlignment.center, children: [
            Flexible(child: ListView(padding: EdgeInsets.only(bottom: 40.sp), children: [
              OrderAddress(ctr.detail!.seller_name, ctr.detail!.seller_address, ctr.detail!.seller_phone, 'lấy', false, size: 40.sp),
              Divider(height: 20.sp, thickness: 20.sp, color: const Color(0xFFF5F5F5)),
              Padding(child: Column(children: [
                Row(children: [
                  LabelCustom('Đơn vị vận chuyển', color: Colors.black, size: 40.sp, weight: FontWeight.w400),
                  LabelCustom(' *', color: Colors.red, size: 40.sp),
                ]),
                BlocBuilder(bloc: bloc, buildWhen: (o,n) => n is LoadCountStatusState && n.resp == false,
                  builder: (_,state) => ListView.separated(physics: const NeverScrollableScrollPhysics(),
                    padding: EdgeInsets.only(top: 40.sp), shrinkWrap: true, itemCount: ctr.list!.length,
                    separatorBuilder: (_,__) => SizedBox(height: 40.sp),
                    itemBuilder: (_,index) {
                      final item = ctr.list![index];
                      return _ShipmentUnitItem(ctr, item, 'shipmentUnit@$index@${item['shipping_code']}');
                    }))
              ]), padding: EdgeInsets.all(40.sp)),
              Divider(height: 0.2, color: grey, thickness: 0.2),

              BlocBuilder(bloc: bloc, buildWhen: (oldS, newS) => newS is TurnOnOffState && newS.key.contains('shipmentUnit'),
                builder: (_, state) {
                  if ((ctr.optValues!['shipmentUnit']??'').contains('@VTP')) return const SizedBox();
                  return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                    Wrap(children: [
                      Container(margin: EdgeInsets.all(40.sp),
                          padding: EdgeInsets.symmetric(horizontal: 40.sp, vertical: 20.sp),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey, width: 0.2), borderRadius: BorderRadius.circular(5),
                          ),
                          child: ButtonImageWidget(5, () => bloc!.add(ExpandEvent(ctr.optValues!['pickOption'], type: 'pickOption')),
                              Row(children: [
                                Flexible(child: BlocBuilder(bloc: bloc, buildWhen: (o,n) => n is ExpandState && n.type == 'pickOption',
                                    builder: (_,__) => LabelCustom(ctr.optValues!['pickOption'].split('@')[2],
                                        color: Colors.black, size: 40.sp, weight: FontWeight.w400))),
                                const SizedBox(width: 10),
                                Icon(Icons.arrow_drop_down_outlined, size: 64.sp, color: Colors.black26)
                              ], mainAxisSize: MainAxisSize.min))
                      ),
                      BlocBuilder(bloc: bloc, buildWhen: (o,n) => n is ExpandState && n.type == 'pickOption',
                          builder: (_,state) {
                            if (state is! ExpandState || state.value == 'off') return const SizedBox();
                            return Container(padding: EdgeInsets.all(40.sp), margin: EdgeInsets.fromLTRB(40.sp, 0, 40.sp, 40.sp),
                                decoration: BoxDecCustom(), child: Column(children: [
                                  _PickItem(ctr, 'Lấy tận nơi', 'pickOption@cod@Lấy tận nơi', 'pickOption'),
                                  SizedBox(height: 40.sp),
                                  _PickItem(ctr, 'Gửi bưu cục', 'pickOption@post@Gửi bưu cục', 'pickOption')
                                ], crossAxisAlignment: CrossAxisAlignment.start));
                          })
                    ], direction: Axis.vertical),
                    Divider(height: 20.sp, thickness: 20.sp, color: const Color(0xFFF5F5F5))
                  ]);
                }),

              BlocBuilder(bloc: bloc, buildWhen: (oldS, newS) => newS is TurnOnOffState && newS.key.contains('shipmentUnit'),
                builder: (_, state) {
                  if ((ctr.optValues!['shipmentUnit']??'').contains('@VTP')) return const SizedBox();
                  return Container(decoration: BoxDecoration(border: Border(bottom: BorderSide(color: grey, width: 0.2))),
                      child: Column(children: [
                        LabelCustom('Tạo đơn hàng', color: Colors.black, size: 40.sp, weight: FontWeight.w400, align: TextAlign.left),
                        SizedBox(height: 40.sp),
                        BlocBuilder(bloc: bloc, buildWhen: (o,n) => n is ExpandState && n.type == 'shipmentType',
                            builder: (_,__) {
                              return Row(children: [
                                Expanded(child: _PickItem(ctr, 'Express nhanh < 20kg', 'shipmentType@exp@Express nhanh < 20kg', 'shipmentType')),
                                SizedBox(width: 40.sp),
                                Expanded(child: _PickItem(ctr, 'BBS lớn >= 20kg', 'shipmentType@bbs@BBS lớn >= 20kg', 'shipmentType'))
                              ]);
                            }),
                        BlocBuilder(bloc: bloc, buildWhen: (o,n) => n is ExpandState && n.type == 'shipmentType' ||
                                n is ExpandState && n.type == 'transport',
                            builder: (_,stateTran) {
                              if (ctr.optValues!['shipmentType'].contains('@bbs')) return const SizedBox();
                              return Padding(padding: EdgeInsets.only(left: 40.sp, top: 40.sp), child: Column(children: [
                                _PickItem(ctr, 'Đường bộ', 'transport@road@Đường bộ', 'transport'),
                                SizedBox(height: 40.sp),
                                _PickItem(ctr, 'Đường bay', 'transport@fly@Đường bay', 'transport')
                              ], crossAxisAlignment: CrossAxisAlignment.start));
                            })
                      ], crossAxisAlignment: CrossAxisAlignment.start), padding: EdgeInsets.all(40.sp));
                }),

              Padding(padding: EdgeInsets.all(40.sp),
                child: LabelCustom('Sản phẩm', color: Colors.black, size: 40.sp, weight: FontWeight.w400, align: TextAlign.left)),
              ListView.separated(padding: EdgeInsets.symmetric(horizontal: 40.sp), itemCount: ctr.detail!.items.length, shrinkWrap: true,
                  separatorBuilder: (_,__) => SizedBox(height: 40.sp), physics: const NeverScrollableScrollPhysics(),
                  itemBuilder: (_,i) => _ProductItem(ctr.detail!.items[i])),

              Padding(padding: EdgeInsets.all(40.sp), child: Row(children: [
                LabelCustom('Tổng khối lượng', color: Colors.black, size: 40.sp, weight: FontWeight.w400),
                Expanded(child: Container(margin: EdgeInsets.only(left: 40.sp),
                  decoration: const BoxDecoration(
                    border: Border(bottom: BorderSide(color: Colors.black38, width: 0.5))
                  ),
                  child: Row(children: [
                    Expanded(child: TextFieldCustom(ctr.ctrSearch!, ctr.fcSearch, null,
                      'Nhập khối lượng', padding: EdgeInsets.zero, isOdd: false,
                      align: TextAlign.right, border: InputBorder.none, onChanged: ctr.changeWeight,
                      inputAction: TextInputAction.done, type: TextInputType.number,
                      inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'[0-9]'))]
                    )),
                    LabelCustom(' g', color: Colors.black, size: 40.sp, weight: FontWeight.w400),
                    Icon(Icons.arrow_drop_down, color: Colors.black38, size: 64.sp)
                  ])
                )),
              ])),

              Padding(padding: EdgeInsets.all(40.sp), child: Row(children: [
                Padding(padding: EdgeInsets.only(bottom: 30.sp),
                    child: LabelCustom('Thu hộ               ', color: Colors.black, size: 40.sp, weight: FontWeight.w400)),
                Expanded(child: Container(margin: EdgeInsets.only(left: 40.sp),
                    padding: EdgeInsets.only(bottom: 30.sp),
                    decoration: const BoxDecoration(
                        border: Border(bottom: BorderSide(color: Colors.black38, width: 0.5))
                    ),
                    child: LabelCustom(Util.doubleToString(ctr.detail!.price_total, preFix: '₫'),
                        color: Colors.black, size: 40.sp, weight: FontWeight.w400, align: TextAlign.right)
                )),
              ])),

              Padding(padding: EdgeInsets.fromLTRB(40.sp, 40.sp, 40.sp, 0), child: Row(children: [
                Padding(padding: EdgeInsets.only(bottom: 30.sp),
                    child: LabelCustom('Phí ship             ', color: Colors.black, size: 40.sp, weight: FontWeight.w400)),
                Expanded(child: Container(margin: EdgeInsets.only(left: 40.sp),
                    padding: EdgeInsets.only(bottom: 30.sp),
                    decoration: const BoxDecoration(
                        border: Border(bottom: BorderSide(color: Colors.black38, width: 0.5))
                    ),
                    child: BlocBuilder(bloc: bloc, buildWhen: (o,n) => n is ExpandState && n.type == 'shipmentFee',
                      builder: (_,__) => LabelCustom(Util.doubleToString(ctr.optValues!['shipmentFee'], preFix: '₫'),
                        color: const Color(0xFFE9A242), size: 40.sp, weight: FontWeight.w400, align: TextAlign.right))
                )),
              ])),
              //Padding(padding: EdgeInsets.fromLTRB(0, 20.sp, 40.sp, 40.sp),
              Padding(padding: EdgeInsets.fromLTRB(0, 20.sp, 40.sp, 0),
                child: LabelCustom('*Người bán trả phí ship', color: const Color(0xFFE9A242),
                size: 30.sp, weight: FontWeight.w400, style: FontStyle.italic, align: TextAlign.right)),

              /*Padding(padding: EdgeInsets.symmetric(horizontal: 40.sp), child: Row(children: [
                LabelCustom('Ảnh đơn hàng       ', color: Colors.black, size: 40.sp, weight: FontWeight.w400),
                BlocBuilder(bloc: bloc, buildWhen: (o,n) => n is ShowClearSearchState,
                  builder: (_,__) {
                    if (images!.length == maxSelect) return const SizedBox();
                    return ButtonImageWidget(5, _selectImage, DottedBorder(color: const Color(0xFF1AB686), padding: EdgeInsets.zero,
                        borderType: BorderType.RRect, radius: const Radius.circular(10), dashPattern: const <double>[3, 3],
                        child: SizedBox(width: 0.25.sw, height: 0.2.sw - 40.sp,
                            child: Column(mainAxisSize: MainAxisSize.min, children: [
                              Icon(Icons.camera_alt_outlined, size: 80.sp, color: const Color(0xFF1AB686)),
                              LabelCustom('Thêm hình ảnh', color: const Color(0xFF1AB686), size: 22.sp, weight: FontWeight.w400),
                              BlocBuilder(bloc: bloc, buildWhen: (o,n) => n is ShowClearSearchState,
                                  builder: (_,__) => LabelCustom('${images!.length}/5',
                                      color: const Color(0xFF1AB686), size: 22.sp, weight: FontWeight.w400))
                            ], mainAxisAlignment: MainAxisAlignment.center)
                        )));
                  }),

                Expanded(child: SizedBox(height: 0.2.sw,
                  child: BlocBuilder(bloc: bloc, buildWhen: (o,n) => n is ShowClearSearchState,
                    builder: (_,__) => ListView.separated(itemCount: images!.length,
                        scrollDirection: Axis.horizontal,
                        padding: EdgeInsets.only(left: images!.length == maxSelect ? 0 : 40.sp), shrinkWrap: true,
                        separatorBuilder: (_,__) => SizedBox(width: 20.sp),
                        itemBuilder: (_,index) {
                          final double w = 0.25.sw;
                          return Stack(alignment: Alignment.topRight, children: [
                            Container(width: w, height: w, alignment: Alignment.center,
                              margin: EdgeInsets.fromLTRB(0, 20.sp, 20.sp, 20.sp),
                              decoration: BoxDecoration(borderRadius: BorderRadius.circular(10),
                                image: DecorationImage(fit: BoxFit.cover, image:
                                  Image.file(File(images![index].name), width: w, height: w).image))),
                            Container(
                              decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(20)),
                              child: ButtonImageCircleWidget(20, () {
                                  images!.removeAt(index);
                                  bloc!.add(ShowClearSearchEvent(true));
                              }, child: const Icon(Icons.highlight_remove, color: Colors.black45, size: 25))
                            )
                          ]);
                        }
                    )
                ))),
              ], mainAxisSize: MainAxisSize.min)),*/

              Padding(padding: EdgeInsets.all(40.sp), child: Row(children: [
                LabelCustom('Ghi chú              ', color: Colors.black, size: 40.sp, weight: FontWeight.w400),
                Expanded(child: Container(margin: EdgeInsets.only(left: 40.sp),
                  decoration: const BoxDecoration(
                    border: Border(bottom: BorderSide(color: Colors.black38, width: 0.5))
                  ),
                  child: TextFieldCustom(ctr.ctrNote!, null, null, 'Nhập ghi chú',
                    isOdd: false, border: InputBorder.none, maxLine: 0, padding: EdgeInsets.zero,
                    inputAction: TextInputAction.newline, type: TextInputType.multiline
                  )
                ))
              ])),

              Divider(height: 0.2, thickness: 0.2, color: grey),
              Padding(child: BlocBuilder(buildWhen: (_,n) => n is ExpandState && n.type == 'orderService' && n.value == '@expand',
                bloc: bloc, builder: (_,__) {
                  if ((ctr.optValues!['shipmentUnit']??'').contains('@VTP')) {
                    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                      LabelCustom('Dịch vụ chính', color: Colors.black, size: 40.sp, weight: FontWeight.w400, align: TextAlign.left),
                      ListView.separated(padding: EdgeInsets.only(top: 40.sp, bottom: 60.sp),
                        shrinkWrap: true, physics: const NeverScrollableScrollPhysics(),
                        itemCount: ctr.services.length, separatorBuilder: (_,__) => SizedBox(height: 40.sp),
                        itemBuilder: (_,i) {
                          final item = ctr.services![i];
                          return _ShipmentUnitItem(ctr, item, 'orderService@$i@${item['service_code']}',
                              viewKey: 'service_name', activeColor: const Color(0xFF1AB686));
                        }),

                      LabelCustom('Dịch vụ cộng thêm', color: Colors.black, size: 40.sp, weight: FontWeight.w400, align: TextAlign.left),
                      BlocBuilder(buildWhen: (_,n) => n is ExpandState && n.type == 'orderServiceAdd' && n.value == '@expand',
                        bloc: bloc, builder: (_,__) {
                          final array = (ctr.optValues!['orderService']??'').toString().split('@');
                          if (array.length < 2) return const SizedBox();
                          int index = int.parse(array[1]);
                          final serviceAdds = ctr.services[index]['extra_service']??[];
                          return ListView.separated(padding: EdgeInsets.only(top: 40.sp),
                            shrinkWrap: true, physics: const NeverScrollableScrollPhysics(),
                            itemCount: serviceAdds.length, separatorBuilder: (_,__) => SizedBox(height: 40.sp),
                            itemBuilder: (_,i) {
                              final item = serviceAdds[i];
                              return _ShipmentUnitItem(ctr, item, 'orderServiceAdd@$i@${item['service_code']}',
                                  viewKey: 'service_name', isRadio: false, activeColor: const Color(0xFF1AB686));
                            });
                        }),
                    ]);
                  }
                  return const SizedBox();
                }
              ), padding: EdgeInsets.all(40.sp))

            ], shrinkWrap: true)),

            SizedBox(height: 40.sp),
            OrderButton('Tạo vận đơn', ctr.createShipment, color: const Color(0xFF1AB686))
        ]))),
        Loading(bloc)
      ])
    );
  }

  /*void _selectImage() {
    maxSelect = 5;
    if (images!.length < maxSelect) {
      maxSelect = maxSelect - images!.length;
      checkPermissions(ItemModel(id: languageKey.lblGallery));
    }
  }*/
}

class _ShipmentUnitItem extends StatelessWidget {
  final OrderBloc bloc;
  final Map item;
  final String optKey, viewKey;
  final Color color, activeColor;
  final bool isRadio;
  const _ShipmentUnitItem(this.bloc, this.item, this.optKey, {this.color = const Color(0xFF818181), this.viewKey = 'shipping_name',
    this.activeColor = Colors.orange, this.isRadio = true, Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) => BlocBuilder(buildWhen: (o,n) => n is TurnOnOffState && n.key == optKey,
    bloc: bloc, builder: (_,state) {
      final isOn = (item['selected']??optKey+'@off').contains('@on');
      return GestureDetector(onTap: () => bloc.add(TurnOnOffEvent(optKey, item['selected']??optKey+'@off')),
        child: Row(children: [
          Icon(isOn ? (isRadio ? Icons.radio_button_checked : Icons.check_box) : (isRadio ? Icons.radio_button_off : Icons.check_box_outline_blank), color: isOn ? activeColor : color, size: 48.sp),
          const SizedBox(width: 10),
          Flexible(child: LabelCustom((item[viewKey]??''), color: isOn ? activeColor : color, size: 40.sp, weight: FontWeight.w400))
        ]));
    }
  );
}

class _PickItem extends StatelessWidget {
  final OrderBloc bloc;
  final String value, optKey, title;
  const _PickItem(this.bloc, this.title, this.value, this.optKey, {Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    final isOn = value == bloc.optValues![optKey];
    return GestureDetector(onTap: _selected, child: Row(children: [
      Icon(isOn ? Icons.check_circle : Icons.radio_button_off, color: Colors.green, size: 48.sp),
      const SizedBox(width: 10),
      Flexible(child: LabelCustom(title, color: Colors.black, size: 40.sp, weight: FontWeight.w400))
    ], mainAxisSize: MainAxisSize.min));
  }

  void _selected() {
    bloc.add(ExpandEvent(value, type: optKey));
    if (optKey == 'pickOption') bloc.add(ExpandEvent('off', type: optKey));
  }
}

class _ProductItem extends StatelessWidget {
  final CartDtlModel item;
  const _ProductItem(this.item);

  @override
  Widget build(BuildContext context) => Container(
    decoration: BoxDecoration(
      border: Border.all(color: Colors.black38, width: 0.2), borderRadius: BorderRadius.circular(10)
    ),
    child: IntrinsicHeight(child: Row(children: [
      Container(padding: EdgeInsets.all(20.sp), child: ClipRRect(borderRadius: BorderRadius.circular(80),
        child: ImageNetworkAsset(path: item.image, width: 80, height: 80))),

      Container(width: 0.2, decoration: BoxDecoration(border: Border.all(color: Colors.black38, width: 0.2))),

      Expanded(child: Column(children: [
        Expanded(child: Container(width: 1.sw, padding: EdgeInsets.all(20.sp), alignment: Alignment.centerLeft,
          decoration: const BoxDecoration(border: Border(bottom: BorderSide(color: Colors.black38, width: 0.2))),
          child: LabelCustom(item.product_name, color: Colors.black, size: 40.sp, weight: FontWeight.w400)
        )),

        Expanded(child: IntrinsicHeight(child: Row(children: [
          Expanded(flex: 4, child: Padding(padding: EdgeInsets.all(20.sp), child: Row(children: [
            LabelCustom('SL: ', color: Colors.black, size: 40.sp, weight: FontWeight.w400),
            Flexible(child: LabelCustom(Util.doubleToString(item.quantity), color: Colors.black, size: 40.sp, weight: FontWeight.w400, align: TextAlign.center))
          ]))),

          Container(width: 0.2, decoration: BoxDecoration(border: Border.all(color: Colors.black38, width: 0.2))),

          Expanded(flex: 6, child: Padding(padding: EdgeInsets.all(20.sp),
            child: LabelCustom(Util.doubleToString((item.price - Util.reducePrice(item)) * item.quantity, preFix: '₫'),
                color: Colors.black, size: 40.sp, weight: FontWeight.w400, align: TextAlign.right)))
        ], mainAxisAlignment: MainAxisAlignment.spaceBetween)))
      ]))
    ]))
  );
}
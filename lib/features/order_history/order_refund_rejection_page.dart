import 'dart:io';
import 'dart:typed_data';
import 'package:dotted_border/dotted_border.dart';
import 'package:hainong/common/ui/button_image_circle_widget.dart';
import 'package:hainong/common/ui/button_image_widget.dart';
import 'package:hainong/common/ui/textfield_custom.dart';
import 'package:video_compress/video_compress.dart';
import 'package:video_thumbnail/video_thumbnail.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:hainong/common/models/file_byte.dart';
import 'package:hainong/common/models/item_list_model.dart';
import 'package:hainong/common/ui/image_network_asset.dart';
import 'package:hainong/common/ui/import_lib_ui.dart';
import 'package:hainong/common/ui/label_custom.dart';
import 'package:hainong/common/ui/loading.dart';
import 'package:hainong/common/ui/permission_image_page_state.dart';
import 'package:hainong/features/cart/cart_model.dart';
import '../function/support/mission/mission_bloc.dart';
import 'order_bloc.dart';
import 'order_ui.dart';

class OrderRefundRejectionPage extends BasePage {
  final Function funReload;
  OrderRefundRejectionPage(Map refundDtl, OrderModel order, bool isMine, this.funReload, {int? idBusiness, Key? key}) :
    super(pageState: _OrderRefundRejectionPageState(refundDtl, order, isMine, idBusiness), key: key);
}

class _OrderRefundRejectionPageState extends PermissionImagePageState {
  final List<dynamic> _thumbnail = [];

  _OrderRefundRejectionPageState(Map refundDtl, OrderModel order, bool isMine, int? idBusiness) {
    images = [];
    multiSelect = true;
    pass = true;
    maxSelect = 5;
    bloc = OrderBloc('refund_rejection', detail: order, idBusiness: idBusiness, isMine: isMine, refundDtl: refundDtl);
    bloc!.stream.listen((state) {
      if (state is LoadCountStatusState && state.resp == true) {
        (widget as OrderRefundRejectionPage).funReload();
        UtilUI.showCustomDialog(context, 'Xác nhận từ chối trả hàng thành công', isNewUI: true).whenComplete(() => UtilUI.goBack(context, (bloc as OrderBloc).refundDtl));
      }
    });
  }

  @override
  void dispose() {
    (bloc as OrderBloc).page = 100; /// do not clear refund detail
    _thumbnail.clear();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    bloc!.data = this;
  }

  @override
  Widget build(BuildContext context, {Color color = Colors.white}) {
    final ctr = bloc as OrderBloc;
    const grey = Color(0xFF818181);
    return Scaffold(appBar: AppBar(titleSpacing: 0, centerTitle: true, elevation: 2,
        title: UtilUI.createLabel('Chi tiết trả hàng')),
        backgroundColor: const Color(0xFFF5F5F5),
        body: Stack(children: [
          SafeArea(minimum: EdgeInsets.only(bottom: 40.sp), child: Column(children: [
            Flexible(child: ListView(children: [
              Container(color: color,
                padding: EdgeInsets.all(40.sp), margin: EdgeInsets.only(top: 1, bottom: 20.sp),
                child: Column(children: [
                  Row(children: const [
                    RefundStatusTimeline(true, hasLine: false),
                    RefundStatusTimeline(false),
                    RefundStatusTimeline(false),
                  ]),
                  Padding(padding: EdgeInsets.symmetric(vertical: 40.sp), child: Row(children: const [
                    RefundStatusLabel('Người bán đang xem xét', true),
                    RefundStatusLabel('Đơn vị vận chuyển điều phối trung chuyển', false, hasPadding: true),
                    RefundStatusLabel('Hoàn hàng', false),
                  ], crossAxisAlignment: CrossAxisAlignment.start)),
                  Row(children: [
                    LabelCustom(MultiLanguage.get('opt_ref2_${ctr.detail!.refund_invoice_status}'), size: 40.sp, color: Colors.orange, weight: FontWeight.normal),
                    Image.asset('assets/images/v10/ic_refund_process_v10.png', height: 120.sp)
                  ], mainAxisAlignment: MainAxisAlignment.spaceBetween)
                ])
              ),

              Container(color: color,
                padding: EdgeInsets.all(40.sp), margin: EdgeInsets.only(bottom: 20.sp),
                child: Column(children: [
                  Row(children: [
                    ClipRRect(borderRadius: BorderRadius.circular(50),
                      child: ImageNetworkAsset(path: ctr.detail!.seller_image, width: 80.sp, height: 80.sp,
                        asset: 'assets/images/v2/ic_avatar_drawer_v2.png', error: 'assets/images/v2/ic_avatar_drawer_v2.png')),
                    LabelCustom('  ' + ctr.detail!.seller_name, size: 36.sp, color: Colors.black, weight: FontWeight.normal)
                  ]),

                  OrderProductList(ctr.detail!.items, ctr, -1, isDetail: true),
                  Divider(height: 80.sp, thickness: 0.2, color: Colors.grey),

                  Column(children: [
                    OrderReason('Mã yêu cầu trả hàng', ctr.refundDtl['refund_invoice_number']??'', grey),
                    SizedBox(height: 20.sp),
                    OrderReason('Yêu cầu vào', Util.strDateToString(ctr.refundDtl['created_at']??'2000-01-01T01:01:01', pattern: 'dd-MM-yyyy HH:mm'), grey)
                  ])
                ])
              ),

              Container(color: color, padding: EdgeInsets.all(40.sp),
                margin: EdgeInsets.only(bottom: 20.sp),
                child: Column(children: [
                  LabelCustom('Lý do yêu cầu trả hàng từ người mua', color: Colors.black, size: 40.sp),
                  SizedBox(height: 20.sp),
                  LabelCustom(ctr.refundDtl['request']??'', color: grey, size: 30.sp),
                  AlignedGridView.count(crossAxisCount: 4, shrinkWrap: true,
                      mainAxisSpacing: 20.sp, crossAxisSpacing: 20.sp,
                      padding: EdgeInsets.only(top: 20.sp),
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: (ctr.refundDtl['buyer_images']??[]).length,
                      itemBuilder: (_,index) => RefundViewImageVideo(ctr.refundDtl['buyer_images'][index]['name']??''))
                ], crossAxisAlignment: CrossAxisAlignment.start)),

              Container(color: color, padding: EdgeInsets.all(40.sp),
                child: Column(children: [
                  LabelCustom('Lý do từ chối yêu cầu', color: Colors.black, size: 40.sp),
                  TextFieldCustom(ctr.ctrSearch!, ctr.fcSearch, null, 'Nhập lý do từ chối tại đây',
                      padding: EdgeInsets.symmetric(vertical: 20.sp), maxLine: 0,
                      inputAction: TextInputAction.newline, type: TextInputType.multiline,
                      border: const UnderlineInputBorder(
                        borderSide: BorderSide(color: Colors.black38, width: 0.5))
                  ),

                  BlocBuilder(bloc: bloc, buildWhen: (o,n) => n is ShowClearSearchState,
                      builder: (_,__) => AlignedGridView.count(crossAxisCount: 4,
                        mainAxisSpacing: 40.sp, crossAxisSpacing: 40.sp, shrinkWrap: true,
                        padding: EdgeInsets.only(top: 40.sp), itemCount: images!.length,
                        physics: const NeverScrollableScrollPhysics(),
                        itemBuilder: (_,index) {
                          final double w = 0.25.sw - 40.sp;
                          final isImage = Util.isImage(images![index].name);
                          return Stack(alignment: Alignment.topRight, children: [
                            Container(width: w, height: w, alignment: Alignment.center,
                              decoration: BoxDecoration(
                                image: DecorationImage(fit: BoxFit.cover, image: (isImage || _thumbnail[index] == null ?
                                  Image.file(File(images![index].name), width: w, height: w) :
                                  Image.memory(_thumbnail[index] as Uint8List, width: w, height: w)).image)),
                              child: isImage ? const SizedBox() : Icon(Icons.play_circle_filled, color: Colors.white, size: 80.sp)),
                            Container(width: 25, height: 25, margin: EdgeInsets.all(10.sp),
                              decoration: BoxDecoration(color: Colors.black54, borderRadius: BorderRadius.circular(20)),
                              child: ButtonImageCircleWidget(25, () {
                                final isNotImage = !Util.isImage(images![index].name);
                                images!.removeAt(index);
                                _thumbnail.removeAt(index);
                                if (isNotImage) ctr.page = 0;
                                bloc!.add(ShowClearSearchEvent(true));
                              }, child: const Icon(Icons.close, color: Colors.white, size: 20)))
                          ]);
                        }
                      )),

                  Padding(padding: EdgeInsets.symmetric(vertical: 40.sp), child:
                      Row(children: [
                        ButtonImageWidget(5, _selectImageVideo, DottedBorder(color: grey,
                          padding: EdgeInsets.all(20.sp), dashPattern: const <double>[3, 3],
                          borderType: BorderType.RRect, radius: const Radius.circular(5),
                          child: SizedBox(width: 0.2.sw - 40.sp, height: 0.2.sw - 40.sp,
                              child: Column(children: [
                                Icon(Icons.camera_alt_outlined, size: 80.sp, color: grey),
                                LabelCustom('Thêm hình ảnh', color: grey, size: 22.sp, weight: FontWeight.w400),
                                BlocBuilder(bloc: bloc, buildWhen: (o,n) => n is ShowClearSearchState,
                                    builder: (_,__) => LabelCustom('${images!.length - ctr.page!}/5',
                                        color: grey, size: 22.sp, weight: FontWeight.w400)),
                              ], mainAxisAlignment: MainAxisAlignment.center)))),
                        SizedBox(width: 40.sp),
                        ButtonImageWidget(5, () => _selectImageVideo(isImage: false),
                          DottedBorder(color: grey, padding: EdgeInsets.all(20.sp),
                              borderType: BorderType.RRect, radius: const Radius.circular(5),
                              dashPattern: const <double>[3, 3],
                              child: SizedBox(width: 0.2.sw - 40.sp, height: 0.2.sw - 40.sp,
                                  child: Column(children: [
                                    Icon(Icons.video_camera_back_outlined, size: 80.sp, color: grey),
                                    LabelCustom('Thêm video', color: grey, size: 22.sp, weight: FontWeight.w400),
                                    BlocBuilder(bloc: bloc, buildWhen: (o,n) => n is ShowClearSearchState,
                                        builder: (_,__) => LabelCustom('${ctr.page}/1', color: grey, size: 22.sp, weight: FontWeight.w400)),
                                  ], mainAxisAlignment: MainAxisAlignment.center))))
                      ])),
                  LabelCustom('Hãy đăng tải tối đa 5 hình ảnh và 1 video, tổng dung lượng không vượt quá 100Mb để thấy rõ tình trạng sản phẩm nhận được, còn nguyên seal, tem, hộp', color: grey, size: 30.sp, weight: FontWeight.w400),
                ], crossAxisAlignment: CrossAxisAlignment.start))
            ], shrinkWrap: true, padding: EdgeInsets.only(top: 20.sp))),

            Padding(padding: EdgeInsets.only(top: 40.sp), child: OrderButton('Xác nhận', _reject, color: const Color(0xFF1AB686)))
          ], crossAxisAlignment: CrossAxisAlignment.center, mainAxisSize: MainAxisSize.min)),
          Loading(bloc)
        ])
    );
  }

  @override
  void showLoadingPermission({bool value = true}) => bloc!.add(LoadingEvent(value));

  @override
  loadFiles(List<File> files) async {
    showLoadingPermission();
    maxSelect = 5;
    final ctr = bloc as OrderBloc;
    bool overSize = false;
    int size = getSize();
    for (int i = 0; i < files.length; i++) {
      if (size + files[i].lengthSync() > 102400000) {
        overSize = true;
        break;
      }
      size += files[i].lengthSync();

      if (Util.isImage(files[i].path)) {
        if (images!.length - ctr.page! < maxSelect) {
          _thumbnail.add(null);
          images!.add(FileByte(files[i].readAsBytesSync(), files[i].path));
        }
      } else if (ctr.page == 0) {
        if (files[i].path.contains('.mov')) {
          MediaInfo? mediaInfo = await VideoCompress.compressVideo(files[i].path, quality: VideoQuality.MediumQuality);
          images!.add(FileByte(mediaInfo!.file!.readAsBytesSync(), mediaInfo.file!.path));
          await VideoCompress.deleteAllCache();
        } else images!.add(FileByte(files[i].readAsBytesSync(), files[i].path));

        ctr.page = 1;
        final listByte = await VideoThumbnail.thumbnailData(video: files[i].path, quality: 80);
        _thumbnail.add(listByte);
      }
    }
    bloc!.add(ShowClearSearchEvent(true));
    if (overSize) UtilUI.showCustomDialog(context, MultiLanguage.get('msg_file_100mb'), isNewUI: true);
  }

  void _selectImageVideo({bool isImage = true}) {
    maxSelect = 5;
    final countVideo = (bloc as OrderBloc).page;
    if ((isImage && images!.length - countVideo! > maxSelect - 1) || (!isImage && countVideo == 1)) return;
    if (isImage) {
      maxSelect = maxSelect - images!.length + countVideo!;
      setOnlyImage();
    } else {
      maxSelect = 1;
      setOnlyVideo();
    }
    checkPermissions(ItemModel(id: languageKey.lblGallery));
  }

  void _reject() {
    final ctr = bloc as OrderBloc;
    if (ctr.ctrSearch!.text.trim().isEmpty) {
      UtilUI.showCustomDialog(context, 'Hãy nhập lý do từ chối yêu cầu trả hàng', isNewUI: true, isError: true).whenComplete(() => ctr.fcSearch!.requestFocus());
      return;
    }
    if (images!.isEmpty) {
      UtilUI.showCustomDialog(context, 'Hãy chọn ít nhất 1 hình ảnh hoặc 1 video', isNewUI: true, isError: true);
      return;
    }
    ctr.refundOrReject(images!);
  }
}
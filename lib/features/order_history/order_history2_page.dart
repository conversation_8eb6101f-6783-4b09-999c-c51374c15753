import 'package:hainong/common/ui/button_image_widget.dart';
import 'package:hainong/common/ui/import_lib_ui.dart';
import 'package:hainong/common/ui/label_custom.dart';
import 'package:hainong/common/ui/loading.dart';
import 'order_bloc.dart';
import 'order_history2_list_page.dart';
import 'order_shipment_setting_page.dart';

class OrderHistory2Page extends BasePage {
  final bool? isOwnerBusiness;
  OrderHistory2Page({this.isOwnerBusiness, int? idBusiness, Key? key}) : super(pageState: _OrderHisPage2State(idBusiness: idBusiness), key:key);
}
class _OrderHisPage2State extends BasePageState implements IBaseBloc {
  bool isBusiness = false;
  _OrderHisPage2State({int? idBusiness}) {
    bloc = OrderBloc('dashboard', idBusiness: idBusiness);
  }

  @override
  void initState() {
    SharedPreferences.getInstance().then((value) {
      setState(() => isBusiness = value.getBool('in_business')??false);
    });
    super.initState();
    bloc!.data = this;
  }

  @override
  Widget build(BuildContext context, {Color color = Colors.white}) {
    bool hasSetting = (bloc as OrderBloc).idBusiness == null || ((widget as OrderHistory2Page).isOwnerBusiness == true && isBusiness);
    final space = SizedBox(width: 30.sp);
    return Scaffold(appBar: AppBar(titleSpacing: 0, centerTitle: true, elevation: 0,
      title: UtilUI.createLabel('Lịch sử đơn hàng')), backgroundColor: const Color(0xFFF5F5F5),
      body: Stack(children: [
        ListView(physics: const NeverScrollableScrollPhysics(), children: [
          if ((bloc as OrderBloc).idBusiness == null)
          _SessionItem('buy', 'Đơn mua', Row(mainAxisAlignment: MainAxisAlignment.spaceAround, children: [
            _StatusItemBuy('Chờ xác nhận', 'pending', bloc, _gotoList),
            _StatusItemBuy('Chờ lấy hàng', 'ready_to_ship', bloc, _gotoList),
            _StatusItemBuy('Đang giao hàng', 'shipped', bloc, _gotoList),
            _StatusItemBuy('Đã giao', 'delivered', bloc, _gotoList)
          ]), _gotoList),

          _SessionItem('order', 'Đơn khách đặt', IntrinsicHeight(child: Row(children: [
            _StatusItemOrder('Chờ xác nhận', 'pending', bloc, _gotoList),
            space,
            _StatusItemOrder('Đơn hủy', 'cancelled', bloc, _gotoList),
            space,
            _StatusItemOrder('Trả hàng', 'returned', bloc, _gotoList),
            space,
            _StatusItemOrder('Đang giao', 'shipped', bloc, _gotoList)
          ])), () => _gotoList(type: 'sales'), margin: EdgeInsets.only(bottom: 20.sp)),

          if (hasSetting) ButtonImageWidget(0, () => UtilUI.goToNextPage(context, OrderShipmentSettingPage()),
            Container(padding: EdgeInsets.all(40.sp), child: Row(children: [
              Image.asset('assets/images/v10/ic_delivery_v10.png', width: 64.sp, height: 64.sp),
              LabelCustom('  Thiết lập đơn vị vận chuyển', color: Colors.black, weight: FontWeight.w500, size: 45.sp)
            ])), color: Colors.white)
        ]),
        Loading(bloc)
      ]));
  }

  void _gotoList({String type = 'purchase', String? status}) => UtilUI.goToNextPage(context, OrderHistory2ListPage(
    idBusiness: (bloc as OrderBloc).idBusiness, orderType: type, status: status, fnReload: () => bloc!.add(LoadCountStatusEvent())));
}

class _StatusItemBuy extends StatelessWidget {
  final String title, status;
  final Function fnGotoList;
  final BaseBloc? bloc;
  const _StatusItemBuy(this.title, this.status, this.bloc, this.fnGotoList, {Key? key}) : super(key:key);
  @override
  Widget build(BuildContext context) => Flexible(child: ButtonImageWidget(10, () => fnGotoList(status: status),
    Column(children: [
      Stack(alignment: Alignment.topRight, children: [
        Padding(padding: const EdgeInsets.fromLTRB(8, 12, 8, 0),
          child: Image.asset('assets/images/v10/ic_${status}_v10.png', width: 70.sp, height: 70.sp)),
        BlocBuilder(bloc: bloc, buildWhen: (o,n) => n is LoadCountStatusState && n.resp is Map,
          builder: (_,state) {
            int count = 0;
            if (state is LoadCountStatusState) count = state.resp['purchase_invoice'][status]??0;
            if (count == 0) return const SizedBox();
            return Container(padding: EdgeInsets.all(8.sp), width: 60.sp, height: 60.sp, alignment: Alignment.center,
              decoration: BoxDecoration(color: const Color(0xFFFFAD26),
                borderRadius: BorderRadius.circular(50)),
              child: LabelCustom(count < 100 ? count.toString() : '99+', weight: FontWeight.w500, size: 20.sp),
            );
          })
      ]),
      SizedBox(height: 20.sp),
      LabelCustom(title, color: const Color(0xFF818181), weight: FontWeight.w400, size: 30.sp, align: TextAlign.center)
    ])));
}

class _StatusItemOrder extends StatelessWidget {
  final String title, status;
  final Function fnGotoList;
  final BaseBloc? bloc;
  const _StatusItemOrder(this.title, this.status, this.bloc, this.fnGotoList, {Key? key}) : super(key:key);
  @override
  Widget build(BuildContext context) => Expanded(child: ButtonImageWidget(0, () => fnGotoList(type: 'sales', status: status),
    Padding(padding: EdgeInsets.all(30.sp), child: Column(children: [
      BlocBuilder(bloc: bloc, buildWhen: (o,n) => n is LoadCountStatusState && n.resp is Map,
          builder: (_,state) {
            int count = 0;
            if (state is LoadCountStatusState) count = state.resp['sales_invoice'][status]??0;
            return LabelCustom(count.toString(), color: count > 0 ? const Color(0xFFFFAD26) : Colors.black, weight: FontWeight.w500, size: 36.sp);
          }),
      SizedBox(height: 20.sp),
      LabelCustom(title, color: const Color(0xFF818181), weight: FontWeight.w400, size: 30.sp, align: TextAlign.center)
    ])
  ), color: const Color(0xFFF5F5F5)));
}

class _SessionItem extends StatelessWidget {
  final String asset, title;
  final Function fnGotoList;
  final EdgeInsets? margin;
  final Widget status;
  const _SessionItem(this.asset, this.title, this.status, this.fnGotoList, {this.margin, Key? key}) : super(key:key);
  @override
  Widget build(BuildContext context) => Container(margin: margin??EdgeInsets.symmetric(vertical: 20.sp),
    padding: EdgeInsets.all(40.sp), color: Colors.white, child: Column(children: [
      Padding(child: Row(children: [
        Image.asset('assets/images/v10/ic_${asset}_v10.png', width: 64.sp, height: 64.sp),
        Expanded(child: LabelCustom('  $title', color: Colors.black, weight: FontWeight.w500, size: 45.sp)),
        ButtonImageWidget(5,fnGotoList, LabelCustom(asset == 'buy' ? 'Xem lịch sử mua hàng >' : 'Xem lịch sử đơn hàng >',
          color: const Color(0xFF818181), weight: FontWeight.w400, size: 30.sp, align: TextAlign.right)),
      ]), padding: EdgeInsets.fromLTRB(20.sp, 0, 20.sp, 40.sp)),
      Padding(padding: EdgeInsets.symmetric(vertical: 30.sp), child: status)
    ])
  );
}
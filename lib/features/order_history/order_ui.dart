import 'dart:typed_data';
import 'package:hainong/common/multi_language.dart';
import 'package:video_thumbnail/video_thumbnail.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hainong/common/style_custom.dart';
import 'package:hainong/common/ui/button_image_widget.dart';
import 'package:hainong/common/ui/image_network_asset.dart';
import 'package:hainong/common/ui/import_lib_base_ui.dart';
import 'package:hainong/common/util/util.dart';
import 'package:hainong/common/ui/label_custom.dart';
import 'package:hainong/common/util/util_ui.dart';
import '../profile/ui/show_avatar_page.dart';
import '../cart/cart_model.dart';
import 'order_bloc.dart';

class OrderProductList extends StatefulWidget {
  final int index;
  final double? size;
  final List<CartDtlModel> list;
  final OrderBloc bloc;
  final bool? isDetail;
  const OrderProductList(this.list, this.bloc, this.index, {Key? key, this.isDetail, this.size}) : super(key: key);
  @override
  _ProductListState createState() => _ProductListState();
}

class _ProductListState extends State<OrderProductList> with AutomaticKeepAliveClientMixin {

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return BlocBuilder(bloc: widget.bloc, buildWhen: (o,n) => n is ExpandState && n.type == widget.index.toString(),
        builder: (_,__) {
          if (widget.list.isEmpty) return const SizedBox();

          final isExp = widget.isDetail == true || (widget.bloc.list![widget.index].expanded??'${widget.index}_off').contains('_on');
          return ListView.separated(padding: EdgeInsets.only(top: 40.sp), shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              separatorBuilder: (_,__) => SizedBox(height: 40.sp), itemCount: isExp ? widget.list.length : 1,
              itemBuilder: (_,i) {
                final item = widget.list[i];
                return Row(children: [
                  ClipRRect(borderRadius: BorderRadius.circular(8),
                    child: ImageNetworkAsset(path: item.image, width: 160.sp, height: 140.sp)),
                  SizedBox(width: 20.sp),
                  Expanded(child: Column(crossAxisAlignment: CrossAxisAlignment.end, children: [
                    Row(children: [
                      Expanded(child: LabelCustom(item.product_name, color: Colors.black, size: widget.size??30.sp, weight: FontWeight.w400))
                    ]),

                    Padding(padding: EdgeInsets.symmetric(vertical: 12.sp), child: Row(children: [
                      LabelCustom(item.unit_name, color: const Color(0xFF818181), size: widget.size??30.sp, weight: FontWeight.w400),
                      Expanded(child: LabelCustom('x' + Util.doubleToString(item.quantity),
                          color: Colors.black, size: widget.size??30.sp, weight: FontWeight.w400, align: TextAlign.right))
                    ])),

                    Wrap(alignment: WrapAlignment.end, runAlignment: WrapAlignment.end,
                        crossAxisAlignment: WrapCrossAlignment.end, children: [
                      LabelCustom(Util.doubleToString(item.price * item.quantity, preFix: '₫'), color: Colors.black, weight: FontWeight.w400,
                          size: widget.size??30.sp, decoration: item.referral_code.isEmpty ? TextDecoration.none : TextDecoration.lineThrough),
                      if (item.referral_code.isNotEmpty) LabelCustom(Util.doubleToString((item.price - Util.reducePrice(item)) * item.quantity, preFix: '  ₫'),
                          color: const Color(0xFFFFAD26), weight: FontWeight.w400, size: widget.size??30.sp)
                    ])
                  ]))
                ]);
              });
        });
  }
}

class OrderShippingLogs extends StatelessWidget {
  final List list;
  final String id;
  const OrderShippingLogs(this.list, this.id, {Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) => Scaffold(backgroundColor: const Color(0xFFF5F5F5),
    appBar: AppBar(title: LabelCustom('Thông tin vận chuyển', size: 50.sp, align: TextAlign.center), centerTitle: true, elevation: 5),
    body: Column(mainAxisSize: MainAxisSize.min, children: [
      Container(padding: EdgeInsets.all(40.sp), color: Colors.white,
        child: Row(children: [
          Expanded(flex: 3, child: LabelCustom('Mã vận đơn', size: 40.sp, color: Colors.black, weight: FontWeight.w600, align: TextAlign.center)),
          SizedBox(width: 110.sp),
          Expanded(flex: 7, child: LabelCustom(id, size: 40.sp, color: const Color(0xFF818181), weight: FontWeight.w400)),
        ])),
      Expanded(child: ListView.builder(padding: EdgeInsets.zero, itemCount: list.length,
        shrinkWrap: true, physics: const AlwaysScrollableScrollPhysics(),
        itemBuilder: (_,i) {
          if (i > 0 && i < list.length - 1) return _StatusShipping(list[i]);
          if (i == 0) return _StatusShipping(list[i], textColor: StyleCustom.primaryColor, pointColor: StyleCustom.primaryColor, lineTopColor: Colors.transparent);
          return _StatusShipping(list[i], lineBottomColor: Colors.transparent);
        }))
    ]));
  }

class _StatusShipping extends StatelessWidget {
  final Map item;
  final Color lineTopColor, lineBottomColor, pointColor, textColor;
  const _StatusShipping(this.item, {this.lineTopColor = Colors.grey, this.lineBottomColor = Colors.grey,
    this.pointColor = Colors.grey, this.textColor = const Color(0x80000000)});
  @override
  Widget build(BuildContext context) => Container(color: Colors.white, child: IntrinsicHeight(child: Row(children: [
    Expanded(flex: 3, child: Column(mainAxisSize: MainAxisSize.min, children: [
      LabelCustom(Util.strDateToString(item['updated_at']??'0-0-0T0:0:0', pattern: 'dd/MM/yyyy'), size: 40.sp, color: textColor, weight: FontWeight.normal),
      LabelCustom(Util.strDateToString(item['updated_at']??'0-0-0T0:0:0', pattern: 'HH:mm a'), size: 40.sp, color: textColor, weight: FontWeight.normal),
    ], crossAxisAlignment: CrossAxisAlignment.end)),
    SizedBox(width: 40.sp),
    Column(mainAxisSize: MainAxisSize.min, children: [
      Expanded(child: Container(width: 1, color: lineTopColor)),
      Container(width: 10, height: 10, decoration: BoxDecoration(color: pointColor, borderRadius: BorderRadius.circular(10))),
      Expanded(child: Container(width: 1, color: lineBottomColor))
    ]),
    Expanded(flex: 7, child: Padding(padding: EdgeInsets.all(40.sp),
        child: LabelCustom(item['status_text']??'', size: 40.sp, color: textColor, weight: FontWeight.normal)))
  ])));
}

class OrderButton extends StatelessWidget {
  final Function fnAction;
  final String label;
  final Color color;
  const OrderButton(this.label, this.fnAction, {this.color = const Color(0xFFFFAD26), Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) => ButtonImageWidget(6, fnAction, Container(padding: EdgeInsets.all(24.sp),
      width: 0.38.sw, child: LabelCustom(label, color: Colors.white, size: 30.sp, align: TextAlign.center)), color: color);
}

class OrderReason extends StatelessWidget {
  final String title, value;
  final Color color;
  const OrderReason(this.title, this.value, this.color, {Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) => Row(children: [
    LabelCustom(title, size: 30.sp, color: color, weight: FontWeight.normal),
    SizedBox(width: 40.sp),
    Flexible(child: LabelCustom(value, size: 30.sp, color: color, weight: FontWeight.normal)),
  ], mainAxisAlignment: MainAxisAlignment.spaceBetween, crossAxisAlignment: CrossAxisAlignment.start);
}

class OrderAddress extends StatelessWidget {
  final String name, address, phone, type;
  final bool isShop;
  final double? size;
  const OrderAddress(this.name, this.address, this.phone, this.type, this.isShop, {this.size, Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    const color = Color(0xFF818181);
    return Container(color: Colors.white,
        padding: EdgeInsets.all(40.sp), margin: EdgeInsets.only(top: 1, bottom: 20.sp),
        child: Row(children: [
          Image.asset('assets/images/v2/ic_location_v2.png', width: 40.sp, color: color),
          SizedBox(width: 20.sp),
          Expanded(child: Column(children: [
            LabelCustom('Địa chỉ $type hàng', size: size??27.sp, weight: FontWeight.w400, color: Colors.black),
            Padding(padding: EdgeInsets.symmetric(vertical: 20.sp),
                child: LabelCustom(name, size: size??27.sp, weight: FontWeight.w400, color: color)),
            LabelCustom(isShop ? Util().maskedTitle(phone, rest: 2) : phone, size: size??27.sp, weight: FontWeight.w400, color: color),
            SizedBox(height: 20.sp),
            LabelCustom(isShop ? Util().maskedTitle(address, rest: 12) : address, size: size??27.sp, weight: FontWeight.w400, color: color)
          ], crossAxisAlignment: CrossAxisAlignment.start))
        ], crossAxisAlignment: CrossAxisAlignment.start)
    );
  }
}

class OrderShippingLog extends StatelessWidget {
  final String? status, modifiedAt;
  const OrderShippingLog(this.status, this.modifiedAt, {Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) => IntrinsicHeight(child: Row(children: [
    Column(mainAxisAlignment: MainAxisAlignment.start, children: [
      Container(width: 5, height: 5, decoration: BoxDecoration(
          color: const Color(0xFF1AB686), borderRadius: BorderRadius.circular(10)
      ), margin: EdgeInsets.only(top: 12.sp)),
      Expanded(child: Container(width: 0.5, color: const Color(0xFF818181), margin: EdgeInsets.only(bottom: 8.sp)))
    ]),
    SizedBox(width: 20.sp),
    Expanded(child: Column(children: [
      LabelCustom(status??'', size: 27.sp, weight: FontWeight.w400, color: const Color(0xFF1AB686)),
      SizedBox(height: 10.sp),
      LabelCustom(Util.strDateToString(modifiedAt??'2000-01-01T01:01:01', pattern: 'dd-MM-yyyy HH:mm'),
          size: 18.sp, weight: FontWeight.w400, color: const Color(0xFF818181))
    ], crossAxisAlignment: CrossAxisAlignment.start))
  ], crossAxisAlignment: CrossAxisAlignment.start));
}

class RefundStatusTimeline extends StatelessWidget {
  final bool active, hasLine;
  final double point;
  const RefundStatusTimeline(this.active, {Key? key, this.point = 3.0, this.hasLine = true}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    final color = Color(active ? 0xFF1AB686 : 0xFF818181);
    return Row(children: [
      SizedBox(width: 1.sw/(hasLine ? point : point * 2) - 38.sp, child: Divider(color: hasLine ? Colors.black45 : Colors.transparent, height: 0.5, thickness: 0.5)),
      Container(width: 8, height: 8, decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10), color: color
      )),
    ]);
  }
}

class RefundStatusLabel extends StatelessWidget {
  final String title;
  final bool active, hasPadding;
  const RefundStatusLabel(this.title, this.active, {Key? key, this.hasPadding = false}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    final child = LabelCustom(title, size: 40.sp, color: Color(active ? 0xFF1AB686 : 0xFF818181), weight: FontWeight.normal, align: TextAlign.center);
    return Expanded(child: hasPadding ? Padding(padding: EdgeInsets.symmetric(horizontal: 20.sp), child: child) : child);
  }
}

class RefundViewImageVideo extends StatefulWidget {
  final String url;
  const RefundViewImageVideo(this.url, {Key? key}) : super(key: key);
  @override
  _ImageVideoState createState() => _ImageVideoState();
}

class _ImageVideoState extends State<RefundViewImageVideo> {
  Uint8List? bytes;

  @override
  void initState() {
    super.initState();
    VideoThumbnail.thumbnailData(video: widget.url, imageFormat: ImageFormat.WEBP, quality: 75).then((value) {
      setState(() => bytes = value);
    });
  }

  @override
  Widget build(BuildContext context) {
    Widget child;
    if (Util.isImage(widget.url)) child = ImageNetworkAsset(path: widget.url, width: 0.25.sw, height: 0.16.sw);
    else if (bytes != null) {
      child = Container(width: 0.25.sw, height: 0.16.sw,
        decoration: BoxDecoration(
            image: DecorationImage(fit: BoxFit.cover, image: Image.memory(bytes!).image)
        ),
        child: Icon(Icons.play_circle, color: Colors.white, size: 64.sp),
      );
    } else child = const SizedBox();
    return ButtonImageWidget(10, () => UtilUI.goToNextPage(context, ShowAvatarPage(widget.url, asset: 'ic_default')),
        ClipRRect(child: child, borderRadius: BorderRadius.circular(10)));
  }
}

class RefundStatus extends StatelessWidget {
  final String status;
  final bool isShop;
  final Widget? cancelTime;
  const RefundStatus(this.status, this.isShop, this.cancelTime, {Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    final isNotCancel = status != 'cancelled', activeStt = status == 'requested' || status == 'rejected';
    return Container(color: Colors.white,
        padding: EdgeInsets.all(40.sp), margin: EdgeInsets.only(top: 1, bottom: 20.sp),
        child: isNotCancel ? Column(children: [
          Row(children: [
            RefundStatusTimeline(activeStt, hasLine: false),
            RefundStatusTimeline(status == 'approved'),
            RefundStatusTimeline(status == 'received'),
          ]),
          Padding(padding: EdgeInsets.symmetric(vertical: 40.sp), child: Row(children: [
            RefundStatusLabel(MultiLanguage.get('opt_ref3_$status'), activeStt),
            RefundStatusLabel('Đơn vị vận chuyển điều phối trung chuyển', status == 'approved', hasPadding: true),
            RefundStatusLabel('Hoàn hàng', status == 'received'),
          ], crossAxisAlignment: CrossAxisAlignment.start)),
          Row(children: [
            LabelCustom(MultiLanguage.get('opt_ref2_$status'), size: 40.sp, color: Colors.orange, weight: FontWeight.normal),
            Image.asset('assets/images/v10/ic_${status == 'received' ? 'checked' : 'refund_process'}_v10.png', height: 120.sp)
          ], mainAxisAlignment: MainAxisAlignment.spaceBetween)
        ]) : Row(children: [
          Column(children: [
            LabelCustom(MultiLanguage.get('opt_ref2_$status'), size: 40.sp, color: Colors.orange, weight: FontWeight.normal),
            Padding(padding: EdgeInsets.symmetric(vertical: 10.sp), child: cancelTime),
            LabelCustom(isShop ? 'Người mua đã hủy yêu cầu' : 'Bạn đã hủy yêu cầu', size: 30.sp, color: const Color(0xFF818181), weight: FontWeight.normal)
          ], crossAxisAlignment: CrossAxisAlignment.start),
          Image.asset('assets/images/v10/ic_refund_reject_v10.png', height: 120.sp)
        ], mainAxisAlignment: MainAxisAlignment.spaceBetween, crossAxisAlignment: CrossAxisAlignment.center)
    );
  }
}

class ComplaintStatus extends StatelessWidget {
  final String status;
  final bool isShop;
  final Widget? cancelTime;
  const ComplaintStatus(this.status, this.isShop, this.cancelTime, {Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    final isNotCancel = status != 'cancelled', activeStt = status == 'pending';
    return Container(color: Colors.white,
        padding: EdgeInsets.all(40.sp), margin: EdgeInsets.only(top: 1, bottom: 20.sp),
        child: isNotCancel ? Column(children: [
          Row(children: [
            RefundStatusTimeline(activeStt, hasLine: false, point: 2.0),
            RefundStatusTimeline(status == 'resolved' || status == 'rejected', point: 2.0)
          ]),
          Padding(padding: EdgeInsets.symmetric(vertical: 40.sp), child: Row(children: [
            RefundStatusLabel('Hai Nông đang xử lý', activeStt),
            RefundStatusLabel('Kết thúc', status == 'resolved' || status == 'rejected'),
          ], crossAxisAlignment: CrossAxisAlignment.start)),
          Row(children: [
            LabelCustom(MultiLanguage.get('opt_cpt2_$status'), size: 40.sp, color: Colors.orange, weight: FontWeight.normal),
            Image.asset('assets/images/v10/ic_refund_process_v10.png', height: 120.sp)
          ], mainAxisAlignment: MainAxisAlignment.spaceBetween)
        ]) : Row(children: [
          Column(children: [
            LabelCustom(MultiLanguage.get('opt_cpt2_$status'), size: 40.sp, color: Colors.orange, weight: FontWeight.normal),
            Padding(padding: EdgeInsets.symmetric(vertical: 10.sp), child: cancelTime),
            LabelCustom(isShop ? 'Người mua đã hủy khiếu nại' : 'Bạn đã hủy khiếu nại', size: 30.sp, color: const Color(0xFF818181), weight: FontWeight.normal)
          ], crossAxisAlignment: CrossAxisAlignment.start),
          Image.asset('assets/images/v10/ic_refund_reject_v10.png', height: 120.sp)
        ], mainAxisAlignment: MainAxisAlignment.spaceBetween, crossAxisAlignment: CrossAxisAlignment.center)
    );
  }
}

class ShopUI extends StatelessWidget {
  final OrderModel detail;
  final bool isShop;
  const ShopUI(this.detail, this.isShop, {Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) => Row(children: [
      ClipRRect(borderRadius: BorderRadius.circular(50),
          child: ImageNetworkAsset(path: isShop ? detail.buyer_image??'' : detail.seller_image, width: 100.sp, height: 100.sp,
              asset: 'assets/images/v2/ic_avatar_drawer_v2.png', error: 'assets/images/v2/ic_avatar_drawer_v2.png')),
      LabelCustom('  ' + (isShop ? detail.buyer_name??'' : detail.seller_name), size: 40.sp, color: Colors.black, weight: FontWeight.normal)
    ]);
}
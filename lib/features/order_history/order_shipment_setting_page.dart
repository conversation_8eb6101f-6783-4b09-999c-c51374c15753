import 'package:flutter/cupertino.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:hainong/common/count_down_bloc.dart';
import 'package:hainong/common/ui/button_image_widget.dart';
import 'package:hainong/common/ui/image_network_asset.dart';
import 'package:hainong/common/ui/import_lib_ui.dart';
import 'package:hainong/common/ui/label_custom.dart';
import 'package:hainong/common/ui/loading.dart';
import 'package:hainong/common/ui/textfield_custom.dart';
import 'order_bloc.dart';

class OrderShipmentSettingPage extends BasePage {
  final Function? reload;
  OrderShipmentSettingPage({this.reload, Key? key}) : super(pageState: _OrderShipmentSettingState(), key:key);
}
class _OrderShipmentSettingState extends BasePageState implements IBaseBloc {

  @override
  void dispose() {
    final reload = (widget as OrderShipmentSettingPage).reload;
    if (reload != null) reload();
    super.dispose();
  }

  @override
  void initState() {
    bloc = OrderBloc('shipment_setting');
    super.initState();
    bloc!.data = this;
  }

  @override
  Widget build(BuildContext context, {Color color = Colors.white}) => Scaffold(
    appBar: AppBar(titleSpacing: 0, centerTitle: true, elevation: 5,
      title: UtilUI.createLabel('Thiết lập đơn vị vận chuyển')),
    backgroundColor: const Color(0xFFF5F5F5), body: Stack(children: [
      Column(children: [
        Container(color: Colors.white, padding: EdgeInsets.all(40.sp),
            child: Row(children: [
              LabelCustom('Đơn vị vận chuyển', size: 36.sp, color: Colors.black, weight: FontWeight.normal),
              LabelCustom(' *', size: 36.sp, color: Colors.red)
            ])
          ),
        Expanded(child: BlocBuilder(buildWhen: (oldS,newS) => newS is LoadCountStatusState && newS.resp,
            bloc: bloc, builder: (_,__) {
              final ctr = bloc as OrderBloc;
              return ListView.separated(padding: EdgeInsets.zero, itemCount: ctr.list!.length,
                separatorBuilder: (_,index) => BlocBuilder(bloc: bloc,
                  buildWhen: (o,n) => n is ExpandState && n.value == ctr.list![index]['expand'],
                  builder: (_,__) {
                    final isExp = ctr.list![index]['expand'].toString().contains('_on');
                    Widget? content;
                    switch (ctr.list![index]['shipping_code']) {
                      case 'VTP': content = _ViettelPost(index, ctr.connect,
                          'https://id.viettelpost.vn/Account/Register',
                          'https://www.youtube.com/watch?v=TpJOGAHRCv0'); break;
                      case 'GHTK': content = _ViettelPost(index, ctr.connect,
                          'https://khachhang.giaohangtietkiem.vn/web/dang-ky',
                          'https://giaohangtietkiem.vn/chinh-sach-bao-mat-ghtk/hoi-dap/danh-muc/?category=tai-khoan&slug=tao-tai-khoan-ghtk-nhu-the-nao', hint: 'GHTK'); break;
                      default: const SizedBox(height: 1);
                    }
                    return Container(constraints: BoxConstraints(maxHeight: isExp?double.infinity:1), child: content);
                  }
                ),
                itemBuilder: (_,index) => index < ctr.list!.length - 1 ?
                  Container(padding: EdgeInsets.all(40.sp),
                    color: Colors.white, child: Row(children: [
                      ImageNetworkAsset(path: ctr.list![index]['shipping_image']??'', width: 80.sp, height: 80.sp),
                      SizedBox(width: 20.sp),
                      Expanded(child: LabelCustom(ctr.list![index]['shipping_name']??'', color: Colors.black, size: 36.sp)),
                      BlocBuilder(bloc: bloc, buildWhen: (o,n) => n is TurnOnOffState && n.key == index,
                          builder: (_,state) => Transform.scale(scale: 0.75,
                              child: CupertinoSwitch(trackColor: const Color(0xFFA4A4A4),
                                  activeColor: const Color(0xFF1AB686), thumbColor: Colors.white,
                                  value: (ctr.list![index]['available_status']??'inactive') == 'active',
                                  onChanged: (value) => ctr.switchStatus(index)))
                      ),
                      ButtonImageWidget(0, () => ctr.add(ExpandEvent(ctr.list![index]['expand']??(index.toString()+'_off'))),
                        BlocBuilder(buildWhen: (o,n) => n is ExpandState && n.value == ctr.list![index]['expand'],
                          bloc: bloc, builder: (_,state) {
                            final isExp = ctr.list![index]['expand'].toString().contains('_on');
                            return Transform.rotate(angle: isExp ? -1.57 : 1.57,
                              child: Icon(Icons.arrow_forward_ios, size: 60.sp, color: Colors.black));
                          }))
                    ])) : const SizedBox()
              );
            }))
      ]),
      Loading(bloc)
    ]));
}

class _ViettelPost extends StatefulWidget {
  final int index;
  final Function fnConnect;
  final String link, hint, guid;
  const _ViettelPost(this.index, this.fnConnect, this.link, this.guid, {this.hint = 'Viettel Post', Key? key}):super(key: key);
  @override
  State<StatefulWidget> createState() => _ViettelPostState();
}
class _ViettelPostState extends State<_ViettelPost> {
  final TextEditingController ctrAcct = TextEditingController(), ctrPass = TextEditingController();
  final FocusNode fcAcct = FocusNode(), fcPass = FocusNode();
  final CountDownBloc bloc = CountDownBloc();

  @override
  void dispose() {
    ctrAcct.dispose();
    ctrPass.dispose();
    fcAcct.dispose();
    fcPass.dispose();
    bloc.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) => Container(padding: EdgeInsets.all(40.sp),
    decoration: const BoxDecoration(color: Colors.white,
      border: Border(bottom: BorderSide(color: Color(0xFFF5F5F5)))),
    child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      LabelCustom('Tài khoản', size: 30.sp, weight: FontWeight.w500, color: const Color(0xFF818181)),
      Padding(padding: EdgeInsets.only(top: 20.sp, bottom: 40.sp),
        child: TextFieldCustom(ctrAcct, fcAcct, fcPass, 'Nhập tài khoản Email ' + widget.hint)),
      LabelCustom('Mật khẩu', size: 30.sp, weight: FontWeight.w500, color: const Color(0xFF818181)),
      Padding(padding: EdgeInsets.only(top: 20.sp, bottom: 40.sp),
        child: BlocBuilder(bloc: bloc, buildWhen: (o,n) => n is CountDownState,
          builder: (_,state) {
            bool isOn = false;
            if (state is CountDownState) isOn = state.value == 1;
            return TextFieldCustom(ctrPass, fcPass, null, 'Nhập mật khẩu',
              inputAction: TextInputAction.done, isPassword: !isOn,
              suffix: ButtonImageWidget(10, () => bloc.add(CountDownEvent(value: isOn?0:1)),
                Padding(padding: EdgeInsets.all(20.sp), child: Image.asset('assets/images/ic_eye_' +
                  (isOn ? 'open' : 'close') + '.png', width: 60.sp, height: 60.sp))),
              constraint: BoxConstraints(maxWidth: 100.sp, maxHeight: 100.sp),
              onSubmit: _connect);
          }
        )),
      Row(children: [
        GestureDetector(onTap: () => launchUrl(Uri.parse(widget.link), mode: LaunchMode.externalApplication),
          child: LabelCustom('Đăng ký', size: 40.sp, weight: FontWeight.w500, color: const Color(0xFF1AB686), decoration: TextDecoration.underline)),
        const SizedBox(width: 8),
        GestureDetector(onTap: () => launchUrl(Uri.parse(widget.guid), mode: LaunchMode.externalApplication),
            child: LabelCustom('Hướng dẫn', size: 40.sp, weight: FontWeight.w500, color: const Color(0xFF1AB686), decoration: TextDecoration.underline)),
        const Expanded(child: SizedBox()),
        ButtonImageWidget(6, _connect,
          Padding(padding: EdgeInsets.symmetric(vertical: 20.sp, horizontal: 30.sp),
            child: LabelCustom('Kết nối', size: 40.sp)), color: const Color(0xFF1AB686))
      ])
    ]));

  void _connect() {
    if (ctrAcct.text.trim().isEmpty) {
      UtilUI.showCustomDialog(context, 'Nhập tài khoản', isNewUI: true, isError: true).whenComplete(() => fcAcct.requestFocus());
      return;
    }
    if (ctrPass.text.trim().isEmpty) {
      UtilUI.showCustomDialog(context, 'Nhập mật khẩu', isNewUI: true, isError: true).whenComplete(() => fcPass.requestFocus());
      return;
    }
    widget.fnConnect(widget.index, ctrAcct.text.trim(), ctrPass.text.trim());
  }
}

class _SaveTransport extends StatefulWidget {
  final int index;
  final Function fnConnect;
  const _SaveTransport(this.index, this.fnConnect, {Key? key}):super(key: key);
  @override
  State<StatefulWidget> createState() => _SaveTransportState();
}
class _SaveTransportState extends State<_SaveTransport> {
  final TextEditingController ctrPass = TextEditingController();
  final FocusNode fcPass = FocusNode();
  final CountDownBloc bloc = CountDownBloc();

  @override
  void dispose() {
    ctrPass.dispose();
    fcPass.dispose();
    bloc.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) => Container(padding: EdgeInsets.all(40.sp),
    decoration: const BoxDecoration(color: Colors.white,
      border: Border(bottom: BorderSide(color: Color(0xFFF5F5F5)))),
    child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      LabelCustom('Key kết nối', size: 30.sp, weight: FontWeight.w500, color: const Color(0xFF818181)),
      Padding(padding: EdgeInsets.only(top: 20.sp, bottom: 40.sp),
        child: BlocBuilder(bloc: bloc, buildWhen: (o,n) => n is CountDownState,
          builder: (_,state) {
            bool isOn = false;
            if (state is CountDownState) isOn = state.value == 1;
            return TextFieldCustom(ctrPass, fcPass, null, 'Nhập khóa kết nối tài khoản GHTK',
              inputAction: TextInputAction.done, isPassword: !isOn,
              suffix: ButtonImageWidget(10, () => bloc.add(CountDownEvent(value: isOn?0:1)),
                Padding(padding: EdgeInsets.all(20.sp), child: Image.asset('assets/images/ic_eye_' +
                  (isOn ? 'open' : 'close') + '.png', width: 60.sp, height: 60.sp))),
              constraint: BoxConstraints(maxWidth: 100.sp, maxHeight: 100.sp),
              onSubmit: _connect);
          }
        )),
      Align(alignment: Alignment.centerRight, child: ButtonImageWidget(6, _connect,
          Padding(padding: EdgeInsets.symmetric(vertical: 20.sp, horizontal: 30.sp),
            child: LabelCustom('Kết nối', size: 40.sp)), color: const Color(0xFF1AB686)))
    ]));

  void _connect() {
    if (ctrPass.text.trim().isEmpty) {
      UtilUI.showCustomDialog(context, 'Nhập khóa kết nối', isNewUI: true, isError: true).whenComplete(() => fcPass.requestFocus());
      return;
    }
    widget.fnConnect(widget.index, '', ctrPass.text.trim());
  }
}
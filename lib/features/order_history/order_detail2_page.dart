import 'package:hainong/common/ui/import_lib_ui.dart';
import 'package:hainong/common/ui/label_custom.dart';
import 'package:hainong/common/ui/loading.dart';
import 'package:hainong/features/cart/cart_model.dart';
import 'package:hainong/features/profile/ui/profile_edit_page.dart';
import '../function/support/mission/mission_bloc.dart';
import 'order_bloc.dart';
import 'order_refund_page.dart';
import 'order_shipment_select_page.dart';
import 'order_ui.dart';

class OrderDtl2Page extends BasePage {
  final Function funReload;
  OrderDtl2Page(OrderModel order, bool isMine, this.funReload, {int? idBusiness, Key? key}) :
    super(pageState: _OrderDtl2PageState(order, isMine, idBusiness), key: key);
}

class _OrderDtl2PageState extends BasePageState {
  //bool isLive = true;

  _OrderDtl2PageState(OrderModel order, bool isMine, int? idBusiness) {
    bloc = OrderBloc('detail', detail: order, idBusiness: idBusiness, isMine: isMine);
    bloc!.stream.listen((state) {
      if (state is TurnOnOffState) {
        (widget as OrderDtl2Page).funReload();
        state.value == 'done' ? UtilUI.showCustomDialog(context, 'Hoàn thành đơn hàng thành công', isNewUI: true)
            .whenComplete(() => UtilUI.goBack(context, true)) :
        UtilUI.showCustomDialog(context, state.key ? 'Bạn đã từ chối thành công đơn hàng này' :
          'Đơn hàng của bạn đã bị hủy. Tiếp tục mua sắm nhé!', isNewUI: true).whenComplete(() => UtilUI.goBack(context, true));
      } else if (state is ShowClearSearchState && state.value) {
        final ctr = bloc as OrderBloc;
        UtilUI.goToNextPage(context, OrderShippingLogs(ctr.list!, ctr.detail!.tracking_code??''));
      } else if (state is LoadCountStatusState && (state.resp == true || state.resp is String)) {
        setState(() {});
        (widget as OrderDtl2Page).funReload();
        if (state.resp is String) UtilUI.showCustomDialog(context, state.resp, isNewUI: true);
      }
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    bloc!.data = this;
    (bloc as OrderBloc).loadDetail();
    /*SharedPreferences.getInstance().then((prefs) {
      isLive = (prefs.getString('env')??'').isEmpty;
      setState(() {});
    });*/
  }

  @override
  Widget build(BuildContext context, {Color color = Colors.white}) {
    final ctr = bloc as OrderBloc;
    final space = SizedBox(height: 20.sp);
    final isNotCancel = ctr.isNotCancel(), isShop = ctr.isShop();
    const green = Color(0xFF1AB686), grey = Color(0xFF818181);
    return Scaffold(appBar: AppBar(titleSpacing: 0, centerTitle: true, elevation: 2,
      title: UtilUI.createLabel('Chi tiết đơn hàng')),
      backgroundColor: const Color(0xFFF5F5F5),
      body: Stack(children: [
        SafeArea(minimum: EdgeInsets.only(bottom: 40.sp),
          child: Column(crossAxisAlignment: CrossAxisAlignment.center, mainAxisSize: MainAxisSize.min, children: [
            Flexible(child: ListView(padding: EdgeInsets.symmetric(vertical: 20.sp), shrinkWrap: true, children: [
              if (ctr.detail!.complaint_status != null)
                ComplaintStatus(ctr.detail!.complaint_status??'', isShop,
                    LabelCustom('vào ${Util.strDateToString(ctr.detail!.updated_at,
                        pattern: 'dd-MM-yyyy HH:mm')}', size: 30.sp, color: grey, weight: FontWeight.normal)),

              if (ctr.detail!.status != 'pending') Container(color: Colors.white, padding: EdgeInsets.all(40.sp), margin: isNotCancel ? null : EdgeInsets.only(bottom: 20.sp),
                  child: isNotCancel ? Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
                    Image.asset('assets/images/v10/ic_ready_to_ship_v10.png', width: 46.sp),
                    SizedBox(width: 20.sp),
                    Expanded(child: BlocBuilder(bloc: bloc, buildWhen: (o,n) => n is ShowClearSearchState && n.value == false,
                      builder: (_,__) {
                        if (ctr.list == null) return const SizedBox();
                        return Column(children: [
                          Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                            LabelCustom('Thông tin vận chuyển', size: 27.sp, weight: FontWeight.w400, color: Colors.black),
                            GestureDetector(onTap: () => bloc!.add(ShowClearSearchEvent(true)),
                                child: LabelCustom('Xem', size: 27.sp, weight: FontWeight.w400, color: green)),
                          ]),

                          Padding(padding: EdgeInsets.symmetric(vertical: 20.sp),
                              child: LabelCustom('${ctr.detail!.shipment_provider??''} - ${ctr.detail!.tracking_code??''}', size: 27.sp, weight: FontWeight.w400, color: grey)),

                          OrderShippingLog(ctr.list![0]['status_text'], ctr.list![0]['modified_at'])
                        ], crossAxisAlignment: CrossAxisAlignment.start);
                      }
                    ))
                  ]) : Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                    Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                      LabelCustom('Đã hủy đơn hàng', size: 30.sp, color: Colors.orange, weight: FontWeight.normal),
                      SizedBox(height: 10.sp),
                      LabelCustom('vào${Util.strDateToString(ctr.detail!.updated_at, pattern: ' dd-MM-yyyy HH:mm')}', weight: FontWeight.w400, size: 27.sp, color: grey),
                    ]),
                    Image.asset('assets/images/v10/ic_checked_v10.png', height: 120.sp)
                  ])
                ),

              if (isNotCancel) OrderAddress(ctr.detail!.name,
                ctr.detail!.shipping_address, ctr.detail!.phone_number, 'nhận', isShop),

              Container(color: Colors.white,
                padding: EdgeInsets.all(40.sp), margin: EdgeInsets.only(bottom: 20.sp),
                child: Column(children: [
                  ShopUI(ctr.detail!, isShop),

                  OrderProductList(ctr.detail!.items, ctr, -1, isDetail: true),
                  Divider(height: 80.sp, thickness: 0.2, color: Colors.grey),

                  isNotCancel ? _totalUI(ctr, space, grey) : Column(children: [
                    OrderReason('Yêu cầu bởi', MultiLanguage.get('opt_ord2_' + ctr.detail!.status), grey),
                    Padding(padding: EdgeInsets.symmetric(vertical: 20.sp),
                      child: OrderReason('Yêu cầu vào', Util.strDateToString(ctr.detail!.updated_at, pattern: 'dd-MM-yyyy HH:mm'), grey)),
                    OrderReason('Lý do', ctr.detail!.remark, grey)
                  ])
                ])
              ),

              if (isNotCancel) Container(color: Colors.white,
                padding: EdgeInsets.all(40.sp), margin: EdgeInsets.only(bottom: 20.sp),
                child: Column(children: [
                  Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                    LabelCustom('Mã đơn hàng', size: 27.sp, color: Colors.black),
                    LabelCustom(ctr.detail!.sku, size: 27.sp, color: Colors.black),
                  ]),
                  space,
                  Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                    LabelCustom('Thời gian đặt hàng', weight: FontWeight.w400, size: 27.sp, color: grey),
                    LabelCustom(Util.strDateToString(ctr.detail!.created_at, pattern: 'dd-MM-yyyy HH:mm'), weight: FontWeight.w400, size: 27.sp, color: grey),
                  ]),
                  if (ctr.detail!.shipped_at != null) Padding(padding: EdgeInsets.only(top: 20.sp),
                  child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                    LabelCustom('Thời gian giao hàng cho vận chuyển', weight: FontWeight.w400, size: 27.sp, color: grey),
                    LabelCustom(Util.strDateToString(ctr.detail!.shipped_at!, pattern: 'dd-MM-yyyy HH:mm'), weight: FontWeight.w400, size: 27.sp, color: grey),
                  ])),
                  if (ctr.detail!.delivered_at != null) Padding(padding: EdgeInsets.only(top: 20.sp),
                  child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                    LabelCustom('Thời gian đã giao hàng đến bạn', weight: FontWeight.w400, size: 27.sp, color: grey),
                    LabelCustom(Util.strDateToString(ctr.detail!.delivered_at!, pattern: 'dd-MM-yyyy HH:mm'), weight: FontWeight.w400, size: 27.sp, color: grey),
                  ]))
                ])
              )
            ])),

            Row(mainAxisAlignment: MainAxisAlignment.spaceAround, children: [
              if (ctr.detail!.status == 'pending' || ctr.detail!.status == 'ready_to_ship')
                OrderButton(isShop ? 'Từ chối đơn': 'Huỷ đơn hàng', ctr.cancel),
              if (isShop && ctr.detail!.status == 'ready_to_ship') OrderButton('In đơn', ctr.print),
              if (isShop && ctr.detail!.status == 'pending') OrderButton('Xác nhận', _confirm, color: const Color(0xFF1AB686)),
              //if (isShop && ctr.detail!.status == 'pending' && !isLive) OrderButton('Xác nhận', _confirm, color: const Color(0xFF1AB686)),
              //if (isShop && ctr.detail!.status == 'pending' && isLive) OrderButton('Hoàn thành', _confirm, color: const Color(0xFF1AB686)),
              if (ctr.canRefund()) OrderButton('Yêu cầu trả hàng', _showReasonRefund),
              if (!isShop && ctr.canComplaint()) OrderButton('Khiếu nại đến Hai Nông', () => ctr.buyerComplaint(isRefund: false)),
              if (!isShop && ctr.detail!.complaint_status == 'pending')
                OrderButton('Hủy khiếu nại', () => ctr.buyerCancelComplaint(isRefund: false))
            ])
          ])),
        Loading(bloc)
      ])
    );
  }

  void _confirm() async {
    /*if (isLive) {
      bloc!.add(TurnOnOffEvent('done', 'Shop hoàn thành đơn'));
      return;
    }*/

    final ctr = bloc as OrderBloc;
    if (ctr.page == 1) return;
    if (await ctr.checkInfo()) {
      UtilUI.showCustomDialog(context, 'Bạn cần bổ sung địa chỉ giao hàng trong hồ sơ người dùng',
        lblCancel: 'Hủy', lblOK: 'Tiếp tục', isActionCancel: true, isNewUI: true).then((value) {
          if (value == true) UtilUI.goToNextPage(context, ProfileEditPage());
        });
      return;
    }

    if (ctr.detail!.price_total < 10000 || ctr.detail!.price_total > 50000000) {
      UtilUI.showCustomDialog(context, 'Tổng giá trị đơn hàng phải trên ₫10.000 và ít hơn ₫50.000.000', isNewUI: true, isError: true);
      return;
    }
    UtilUI.goToNextPage(context, OrderShipmentSelectPage(ctr.detail!, ctr.reloadDetail, idBusiness: ctr.idBusiness));
  }

  void _showReasonRefund() => UtilUI.showConfirmDialog(context, 'Lý do trả hàng', 'Nhập lý do', 'Lý do không được trống',
    title: 'Trả hàng', lblOK: 'YÊU CẦU TRẢ HÀNG', line: 3, inputType: TextInputType.multiline,
    action: TextInputAction.newline, maxLength: 150, padding: EdgeInsets.all(20.sp)).then((value) {
      if (value is String) {
        final ctr = bloc as OrderBloc;
        UtilUI.goToNextPage(context, OrderRefundPage(ctr.detail!, value, ctr.reloadDetail));
      }
    });

  Widget _totalUI(OrderBloc ctr, Widget space, Color grey) => Column(children: [
    if (ctr.detail!.coupon_code != null) Padding(
        padding: EdgeInsets.only(bottom: 20.sp), child: Row(children: [
      LabelCustom('Tổng tiền  ', size: 30.sp, color: Colors.black, weight: FontWeight.normal),
      Expanded(child: LabelCustom(Util.doubleToString(ctr.detail!.price_total + (ctr.detail!.max_discount??.0), preFix: '₫'),
          size: 30.sp, color: Colors.black, weight: FontWeight.normal, align: TextAlign.end))
    ])),

    if (ctr.detail!.coupon_code != null) Padding(
        padding: EdgeInsets.only(bottom: 20.sp), child: Row(children: [
      LabelCustom('Giảm giá  ', size: 30.sp, color: Colors.red, weight: FontWeight.normal),
      Expanded(child: LabelCustom(Util.doubleToString(ctr.detail!.max_discount!, preFix: '₫'),
          size: 30.sp, color: Colors.red, weight: FontWeight.normal, align: TextAlign.end))
    ])),

    Row(children: [
      LabelCustom('Thành tiền  ', size: 30.sp, color: Colors.black, weight: FontWeight.normal),
      Expanded(child: LabelCustom(Util.doubleToString(ctr.detail!.price_total, preFix: '₫'),
          size: 30.sp, color: Colors.black, weight: FontWeight.normal, align: TextAlign.end))
    ]),

    space,
    Row(children: [
      LabelCustom('Vui lòng thanh toán ', size: 30.sp, color: grey, weight: FontWeight.normal),
      LabelCustom(Util.doubleToString(ctr.detail!.price_total, preFix: '₫'),
          size: 30.sp, color: Colors.orange, weight: FontWeight.normal, align: TextAlign.end),
      LabelCustom(' khi nhận hàng.', size: 30.sp, color: grey, weight: FontWeight.normal)
    ])
  ]);
}
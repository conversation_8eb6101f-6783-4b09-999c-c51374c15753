import 'dart:math';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class TrackasiaUtils {
  static Future<Uint8List> svgToBytes(String assetPath) async {
    final byteData = await rootBundle.load(assetPath);
    return byteData.buffer.asUint8List();
  }

  // Modern color palette with harmonious gradients
  static final List<Color> colors = [
    const Color(0xFF4CAF50), // Modern green
    const Color(0xFF2196F3), // Material blue
    const Color(0xFFFF9800), // Warm orange
    const Color(0xFF9C27B0), // Purple accent
    const Color(0xFFF44336), // Modern red
    const Color(0xFF607D8B), // Blue grey
    const Color(0xFF795548), // Brown
    const Color(0xFFE91E63), // Pink
  ];

  // Gradient colors for better visual appeal
  static final List<List<Color>> gradientColors = [
    [const Color(0xFF4CAF50), const Color(0xFF66BB6A)], // Green gradient
    [const Color(0xFF2196F3), const Color(0xFF42A5F5)], // Blue gradient
    [const Color(0xFFFF9800), const Color(0xFFFFB74D)], // Orange gradient
    [const Color(0xFF9C27B0), const Color(0xFFBA68C8)], // Purple gradient
    [const Color(0xFFF44336), const Color(0xFFEF5350)], // Red gradient
    [const Color(0xFF607D8B), const Color(0xFF78909C)], // Blue grey gradient
    [const Color(0xFF795548), const Color(0xFF8D6E63)], // Brown gradient
    [const Color(0xFFE91E63), const Color(0xFFF06292)], // Pink gradient
  ];

  // Dynamic sizing configuration based on cluster count
  static Map<String, double> getClusterDimensions(int clusterSize) {
    double width, height, strokeWidth, textSize;

    if (clusterSize < 2) {
      width = height = 40.0;
      strokeWidth = 4.0;
      textSize = 3.0;
    } else if (clusterSize < 5) {
      width = height = 40.0;
      strokeWidth = 4.0;
      textSize = 3.0;
    } else if (clusterSize < 10) {
      width = height = 42.0;
      strokeWidth = 4.0;
      textSize = 3.0;
    } else if (clusterSize < 25) {
      width = height = 42.0;
      strokeWidth = 4.0;
      textSize = 3.0;
    } else if (clusterSize < 50) {
      width = height = 42.0;
       strokeWidth = 5.0;
      textSize = 4.0;
    } else if (clusterSize < 100) {
      width = height = 46.0;
      strokeWidth = 5.0;
      textSize = 5.0;
    } else if (clusterSize < 200) {
      width = height = 46.0;
      strokeWidth = 5.0;
      textSize = 5.0;
    } else if (clusterSize < 500) {
      width = height = 46.0;
      strokeWidth = 5.0;
      textSize = 5.0;
    } else if (clusterSize < 1000) {
      width = height =46.0;
      strokeWidth = 5.0;
      textSize = 5.0;
    } else if (clusterSize < 2000) {
      width = height = 50.0;
      strokeWidth = 5.0;
      textSize = 5.0;
    } else if (clusterSize < 5000) {
      width = height = 50.0;
      strokeWidth = 5.0;
      textSize = 5.0;
    } else if (clusterSize < 10000) {
      width = height = 50.0;
      strokeWidth = 5.0;
      textSize = 5.0;
    } else {
      width = height = 50.0;
      strokeWidth = 5.0;
      textSize = 5.0;
    }

    return {
      'width': width,
      'height': height,
      'strokeWidth': strokeWidth,
      'textSize': textSize,
    };
  }

  static Future<Uint8List?> createDonutChartPng(int clusterSize, {double? width, double? height, double? strokeWidth}) async {
    final dimensions = getClusterDimensions(clusterSize);
    final chartWidth = width ?? dimensions['width']!;
    final chartHeight = height ?? dimensions['height']!;
    final chartStrokeWidth = strokeWidth ?? dimensions['strokeWidth']!;
    final textSize = dimensions['textSize']!;
    final double radius = min(chartWidth, chartHeight) / 2 - chartStrokeWidth / 2;
    final ui.PictureRecorder pictureRecorder = ui.PictureRecorder();
    final ui.Canvas canvas = ui.Canvas(pictureRecorder);
    final ui.Offset center = ui.Offset(chartWidth / 2, chartHeight / 2);
    Map<Color, double> segments = getClusterSegments(clusterSize);
    final glowRadius = radius +
        (clusterSize < 10
            ? 5.0
            : clusterSize > 1000
                ? 4.0
                : 3.0);
    final glowOpacity = clusterSize < 10
        ? 0.25
        : clusterSize > 1000
            ? 0.15
            : 0.2;
    final glowPaint = ui.Paint()
      ..color = Colors.white.withOpacity(glowOpacity)
      ..style = ui.PaintingStyle.stroke
      ..strokeWidth = clusterSize < 10
          ? 4.0
          : clusterSize > 1000
              ? 3.0
              : 3.5
      ..maskFilter = const ui.MaskFilter.blur(ui.BlurStyle.normal, 4);
    canvas.drawCircle(center, glowRadius, glowPaint);
    if (clusterSize < 25) {
      final backgroundPaint = ui.Paint()
        ..color = Colors.black.withOpacity(0.1)
        ..style = ui.PaintingStyle.fill;
      canvas.drawCircle(center, radius + chartStrokeWidth / 4, backgroundPaint);
    }
    double startAngle = -pi / 2;
    int colorIndex = 0;
    segments.forEach((color, percentage) {
      final gradientIntensity = clusterSize < 10
          ? 1.0
          : clusterSize > 1000
              ? 1.0
              : 0.9;
      final gradient = ui.Gradient.sweep(
        center,
        gradientColors[colorIndex % gradientColors.length].map((c) => c.withOpacity(gradientIntensity)).toList(),
        null,
        ui.TileMode.clamp,
        startAngle,
        startAngle + (2 * pi * percentage),
      );

      final ui.Paint paint = ui.Paint()
        ..shader = gradient
        ..style = ui.PaintingStyle.stroke
        ..strokeWidth = chartStrokeWidth
        ..strokeCap = ui.StrokeCap.round;

      final double sweepAngle = 2 * pi * percentage;
      canvas.drawArc(Rect.fromCircle(center: center, radius: radius), startAngle, sweepAngle, false, paint);

      startAngle += sweepAngle;
      colorIndex++;
    });

    final textColor = clusterSize < 10 ? Colors.white : Colors.white;
    final shadowIntensity = clusterSize < 10 ? 0.8 : 0.5;

    final textPainter = TextPainter(
      text: TextSpan(
        text: _formatClusterText(clusterSize),
        style: TextStyle(
          color: textColor,
          fontSize: textSize,
          fontWeight: FontWeight.bold,
          shadows: [
            Shadow(offset: const Offset(0, 1), blurRadius: 3, color: Colors.black.withOpacity(shadowIntensity)),
            Shadow(offset: const Offset(1, 0), blurRadius: 2, color: Colors.black.withOpacity(shadowIntensity * 0.5)),
          ],
        ),
      ),
      textDirection: TextDirection.ltr,
    );

    textPainter.layout();
    final textOffset = Offset(
      center.dx - textPainter.width / 2,
      center.dy - textPainter.height / 2,
    );
    textPainter.paint(canvas, textOffset);

    // Enhanced inner shadow for better depth perception
    final innerShadowOpacity = clusterSize < 10 ? 0.15 : 0.1;
    final innerShadowPaint = ui.Paint()
      ..color = Colors.black.withOpacity(innerShadowOpacity)
      ..style = ui.PaintingStyle.stroke
      ..strokeWidth = clusterSize < 10 ? 1.5 : 1.0
      ..maskFilter = const ui.MaskFilter.blur(ui.BlurStyle.inner, 1.5);

    canvas.drawCircle(center, radius - chartStrokeWidth / 2, innerShadowPaint);

    final ui.Image image = await pictureRecorder.endRecording().toImage(chartWidth.toInt(), chartHeight.toInt());
    final ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    return byteData?.buffer.asUint8List();
  }

  // Format cluster text with appropriate abbreviations
  static String _formatClusterText(int clusterSize) {
    if (clusterSize >= 1000000) {
      return '${(clusterSize / 1000000).toStringAsFixed(1)}M';
    } else if (clusterSize >= 1000) {
      return '${(clusterSize / 1000).toStringAsFixed(1)}K';
    } else {
      return clusterSize.toString();
    }
  }

  static Map<Color, double> getClusterSegments(int clusterSize) {
    Map<Color, double> segments = {};

    if (clusterSize == 1) {
      // Single item - solid circle with good visibility
      segments[colors[0]] = 1.0;
    } else if (clusterSize < 3) {
      // Very small clusters - bold, high-contrast design
      segments[colors[0]] = 0.7;
      segments[colors[1]] = 0.3;
    } else if (clusterSize < 5) {
      // Small clusters - clear segmentation
      segments[colors[0]] = 0.6;
      segments[colors[1]] = 0.4;
    } else if (clusterSize < 10) {
      // Small clusters with three segments
      segments[colors[0]] = 0.5;
      segments[colors[1]] = 0.35;
      segments[colors[2]] = 0.15;
    } else if (clusterSize < 25) {
      // Medium-small clusters - four-segment design
      segments[colors[0]] = 0.45;
      segments[colors[1]] = 0.3;
      segments[colors[2]] = 0.15;
      segments[colors[3]] = 0.1;
    } else if (clusterSize < 50) {
      // Medium clusters - balanced five-segment
      segments[colors[0]] = 0.4;
      segments[colors[1]] = 0.25;
      segments[colors[2]] = 0.2;
      segments[colors[3]] = 0.1;
      segments[colors[4]] = 0.05;
    } else if (clusterSize < 100) {
      // Medium-large clusters - six-segment
      segments[colors[0]] = 0.35;
      segments[colors[1]] = 0.25;
      segments[colors[2]] = 0.2;
      segments[colors[3]] = 0.12;
      segments[colors[4]] = 0.05;
      segments[colors[5]] = 0.03;
    } else if (clusterSize < 200) {
      // Large clusters - seven-segment
      segments[colors[0]] = 0.32;
      segments[colors[1]] = 0.24;
      segments[colors[2]] = 0.18;
      segments[colors[3]] = 0.14;
      segments[colors[4]] = 0.08;
      segments[colors[5]] = 0.03;
      segments[colors[6]] = 0.01;
    } else if (clusterSize < 500) {
      // Large clusters - complex distribution
      segments[colors[0]] = 0.3;
      segments[colors[1]] = 0.22;
      segments[colors[2]] = 0.18;
      segments[colors[3]] = 0.15;
      segments[colors[4]] = 0.1;
      segments[colors[5]] = 0.04;
      segments[colors[6]] = 0.01;
    } else if (clusterSize < 1000) {
      // Very large clusters - full eight-segment
      segments[colors[0]] = 0.28;
      segments[colors[1]] = 0.2;
      segments[colors[2]] = 0.17;
      segments[colors[3]] = 0.15;
      segments[colors[4]] = 0.12;
      segments[colors[5]] = 0.05;
      segments[colors[6]] = 0.02;
      segments[colors[7]] = 0.01;
    } else if (clusterSize < 2000) {
      // Huge clusters - emphasis on primary segments
      segments[colors[0]] = 0.3;
      segments[colors[1]] = 0.22;
      segments[colors[2]] = 0.18;
      segments[colors[3]] = 0.15;
      segments[colors[4]] = 0.1;
      segments[colors[5]] = 0.03;
      segments[colors[6]] = 0.015;
      segments[colors[7]] = 0.005;
    } else if (clusterSize < 5000) {
      // Massive clusters - dominant primary colors
      segments[colors[0]] = 0.32;
      segments[colors[1]] = 0.25;
      segments[colors[2]] = 0.2;
      segments[colors[3]] = 0.15;
      segments[colors[4]] = 0.06;
      segments[colors[5]] = 0.015;
      segments[colors[6]] = 0.005;
    } else {
      // Extreme clusters - maximum primary emphasis
      segments[colors[0]] = 0.35;
      segments[colors[1]] = 0.28;
      segments[colors[2]] = 0.22;
      segments[colors[3]] = 0.12;
      segments[colors[4]] = 0.025;
      segments[colors[5]] = 0.005;
    }

    return segments;
  }

  // Additional utility for creating custom color palettes
  static List<Color> generateHarmoniousColors(int count) {
    List<Color> harmonious = [];
    const baseHue = 210.0; // Blue base
    const saturation = 0.7;
    const lightness = 0.6;

    for (int i = 0; i < count; i++) {
      final hue = (baseHue + (i * 360 / count)) % 360;
      harmonious.add(HSLColor.fromAHSL(1.0, hue, saturation, lightness).toColor());
    }

    return harmonious;
  }

  static Color getClusterColor(int clusterSize, int index) {
    if (clusterSize < 100) {
      return colors[index % 3];
    } else if (clusterSize < 1000) {
      return colors[index % 5];
    } else {
      return colors[index % colors.length];
    }
  }

  static double getMapIconSize(int clusterSize) {
    if (clusterSize < 10) return 0.6;
    if (clusterSize < 50) return 0.7;
    if (clusterSize < 100) return 0.8;
    if (clusterSize < 200) return 0.9;
    if (clusterSize < 500) return 1.0;
    if (clusterSize < 1000) return 1.1;
    if (clusterSize < 2000) return 1.2;
    if (clusterSize < 5000) return 1.2;
    if (clusterSize < 10000) return 1.2;
    return 1.4;
  }

  // Get stroke width scaling for map display
  static double getStrokeWidthScale(int clusterSize) {
    return (clusterSize < 100)
        ? 0.8
        : (clusterSize < 1000)
            ? 1.0
            : (clusterSize < 2000)
                ? 1.2
                : (clusterSize < 5000)
                    ? 1.1
                    : 1.3;
  }
}

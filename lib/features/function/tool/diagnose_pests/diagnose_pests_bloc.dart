import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:geolocator/geolocator.dart';
import 'package:hainong/common/api_client.dart';
import 'package:hainong/common/database_helper.dart';
import 'package:hainong/common/import_lib_system.dart';
import 'package:hainong/common/models/file_byte.dart';
import 'package:hainong/features/comment/model/comment_model.dart';
import 'package:hainong/features/function/info_news/market_price/market_price_bloc.dart';
import 'package:hainong/features/function/info_news/weather/weather_list_province_model.dart';
import 'package:hainong/features/main/bloc/main_bloc.dart';
import 'package:hainong/features/rating/rating_bloc.dart';
import 'package:hainong/features/shop/ui/import_ui_shop.dart';
import 'package:hainong/features/home/<USER>';
import '../suggestion_map/suggest_model.dart';
import 'diagnose_pests_repository.dart';
import 'model/plant_model.dart';

class DiagnosePestsState extends BaseState {
  const DiagnosePestsState({isShowLoading = false}) : super(isShowLoading: isShowLoading);
}
abstract class DiagnosePestsEvent extends BaseEvent {}

class UploadFileDiagnosePestsEvent extends DiagnosePestsEvent {
  final String catalogue, lat, lng;
  final List<FileByte> files;
  UploadFileDiagnosePestsEvent(this.files, this.catalogue, this.lat, this.lng);
}
class UploadFileDiagnosePestsState extends DiagnosePestsState {
  final BaseResponse response;
  UploadFileDiagnosePestsState(this.response);
}

class LoadListProvinceEvent extends DiagnosePestsEvent{}
class LoadListProvincesState extends DiagnosePestsState {
  final BaseResponse response;
  LoadListProvincesState(this.response);
}

class LoadListDistrictEvent extends DiagnosePestsEvent{
  final String idProvince;
  LoadListDistrictEvent(this.idProvince);
}
class LoadListDistrictState extends DiagnosePestsState {
  final BaseResponse response;
  LoadListDistrictState(this.response);
}

class ShowImageDiagnosePestsEvent extends DiagnosePestsEvent {
  final bool isShowLoading;
  ShowImageDiagnosePestsEvent({this.isShowLoading = false});
}
class ShowImageDiagnosePestsState extends DiagnosePestsState {
  ShowImageDiagnosePestsState({isShowLoading = false}):super(isShowLoading: isShowLoading);
}

class CreatePostDiagnosePestsEvent extends DiagnosePestsEvent {
  final String title;
  final List<FileByte> files;
  CreatePostDiagnosePestsEvent(this.files, this.title);
}
class CreatePostDiagnosePestsState extends DiagnosePestsState {
  final BaseResponse response;
  CreatePostDiagnosePestsState(this.response);
}

class LoadCatalogueEvent extends DiagnosePestsEvent{
  final int? id;
  LoadCatalogueEvent({this.id});
}
class LoadCatalogueState extends DiagnosePestsState{
  final List list;
  LoadCatalogueState(this.list);
}

class ChangeCatalogueEvent extends DiagnosePestsEvent{}
class ChangeCatalogueState extends DiagnosePestsState{}

class ChangePestEvent extends DiagnosePestsEvent{}
class ChangePestState extends DiagnosePestsState{}

class LoadDiagnosticEvent extends DiagnosePestsEvent{
  final String idPlant;
  LoadDiagnosticEvent(this.idPlant);
}
class LoadDiagnosticState extends DiagnosePestsState {
  final List<ItemModel> list;
  LoadDiagnosticState(this.list);
}

class LoadListPlantEvent extends DiagnosePestsEvent{}
class LoadListPlantState extends DiagnosePestsState{
  final List<ItemModel> list;
  LoadListPlantState(this.list);
}

class LoadDiagnosticHistoryEvent extends DiagnosePestsEvent{
  final int page;
  final String diagnosticId, plantId;
  LoadDiagnosticHistoryEvent(this.page,{required this.plantId, required this.diagnosticId});
}
class LoadDiagnosticHistoryState extends DiagnosePestsState {
  final BaseResponse response;
  const LoadDiagnosticHistoryState(this.response);
}

class ShowPercentEvent extends DiagnosePestsEvent {
  final bool value;
  ShowPercentEvent(this.value);
}
class ShowPercentState extends DiagnosePestsState {
  final bool value;
  const ShowPercentState(this.value);
}

class LoadOptionEvent extends DiagnosePestsEvent {}
class LoadOptionState extends DiagnosePestsState {
  final String value;
  final dynamic popup;
  LoadOptionState(this.value, {this.popup});
}

class CheckPopupCameraEvent extends DiagnosePestsEvent {
  final bool value, isSave;
  CheckPopupCameraEvent({this.value = true, this.isSave = false});
}
class CheckPopupCameraState extends DiagnosePestsState {}

class SendFeedbackEvent extends DiagnosePestsEvent {
  final List ids;
  final String content;
  SendFeedbackEvent(this.content, this.ids);
}
class SendFeedbackState extends DiagnosePestsState {
  final BaseResponse resp;
  SendFeedbackState(this.resp);
}

class FillTerByPlantEvent extends DiagnosePestsEvent {}
class FillTerByPlantState extends DiagnosePestsState {}

class FillTerByPestEvent extends DiagnosePestsEvent {}
class FillTerByPestState extends DiagnosePestsState {}

class CreateDiagnostisPestEvent extends DiagnosePestsEvent {
  final List<FileByte> files;
  final String pest_name, description, tree_name, province_id, district_id, address, lat, lng;
  final dynamic rate, trainIds;
  CreateDiagnostisPestEvent(this.province_id, this.district_id, this.address,
      this.tree_name, this.pest_name, this.description, this.files,
      {this.lat = '', this.lng = '', this.rate, this.trainIds});
}
class CreateDiagnostisPestSuccessState extends DiagnosePestsState {
  final dynamic resp;
  CreateDiagnostisPestSuccessState(this.resp);
}

class LoadSymptomMalnutritionEvent extends DiagnosePestsEvent {
  final int id;
  LoadSymptomMalnutritionEvent(this.id);
}
class LoadSymptomMalnutritionState extends DiagnosePestsState {
  final dynamic resp;
  LoadSymptomMalnutritionState(this.resp);
}
class LoadTreatmentState extends DiagnosePestsState {
  final dynamic resp;
  LoadTreatmentState(this.resp);
}
class LoadPreventionState extends DiagnosePestsState {
  final dynamic resp;
  LoadPreventionState(this.resp);
}
class LoadGrowingGuideState extends DiagnosePestsState {
  final dynamic resp;
  LoadGrowingGuideState(this.resp);
}

class ExpandEvent extends DiagnosePestsEvent {
  final dynamic value, type;
  ExpandEvent(this.value, {this.type});
}
class ExpandState extends DiagnosePestsState {
  final dynamic type, value;
  ExpandState(this.type, this.value);
}

class DetectEvent extends DiagnosePestsEvent {
  final int index;
  final FileByte image;
  final Uint8List? model;
  final List<String>? labels;
  DetectEvent(this.index, this.image, {this.model, this.labels});
}
class DetectResultEvent extends DiagnosePestsEvent {
  final int index;
  final FileByte image;
  final bool isShowLoading;
  DetectResultEvent(this.index, this.image, {this.isShowLoading = false});
}
class DetectState extends DiagnosePestsState {
  final int index;
  final FileByte image;
  final bool stopDetect;
  DetectState(this.index, this.image, {this.stopDetect = false, isShowLoading = false}):super(isShowLoading: isShowLoading);
}

class ShowResultEvent extends DiagnosePestsEvent {
  final int index;
  ShowResultEvent(this.index);
}
class ShowResultState extends DiagnosePestsState {
  final int index;
  ShowResultState(this.index);
}

class DiagnosePestsBloc extends BaseBloc {
  final repository = DiagnosePestsRepository();
  int? countDetecting;
  //List<String>? _labels;
  //bool? isAPI;

  /*@override
  Future<void> close() async {
    if (_labels != null) {
      //data?['interpreter'].close();
      data?.clear();
      _labels!.clear();
    }
    super.close();
  }*/

  DiagnosePestsBloc(DiagnosePestsState init, {String type = ''}) : super(init: init) {
    on<CountNotificationMainEvent>((event, emit) async {
      final resp = await ApiClient().getAPI(Constants().apiVersion + 'notifications/count', BaseResponse());
      emit(CountNotificationMainState(resp, 0, null));
    });
    on<CreatePostDiagnosePestsEvent>((event, emit) async {
      emit(const DiagnosePestsState(isShowLoading: true));
      final response = await HomeRepository().createPost([], [], event.title, '', '', realFiles: event.files);
      emit(CreatePostDiagnosePestsState(response));
    });
    on<SendFeedbackEvent>((event, emit) async {
      emit(const DiagnosePestsState(isShowLoading: true));
      final Map<String, String> body = {'feedback': event.content};
      for(int i = event.ids.length - 1; i > -1; i--) {
        body.putIfAbsent('training_data_ids[$i]', () => event.ids[i].toString());
      }
      final resp = await ApiClient().postAPI(Constants().apiVersion + 'training_data/feedback', 'PUT', BaseResponse(), body: body);
      emit(SendFeedbackState(resp));
    });

    if (type == 'result_ext' || type == 'result_ext_nutrition' || type == 'result_ext_medicine') {
      on<ExpandEvent>((event, emit) {
        final array = event.value.split('_');
        data!.update(array[0], (v) => array[0] + (array[1] == 'off' ? '_on' : '_off'));
        emit(ExpandState(array[0], data![array[0]]));
      });
    }

    if (type == 'result_ext' || type == 'result_ext_relate_pest_catalogue' || type == 'result_ext_nutrition' || type == 'diagnostic') {
      on<ChangeCatalogueEvent>((event, emit) => emit(ChangeCatalogueState()));
    }

    if (type == 'diagnostic' || type == 'contribute') {
      on<ShowImageDiagnosePestsEvent>((event, emit) => emit(ShowImageDiagnosePestsState(isShowLoading: event.isShowLoading)));
    }

    if (type == 'diagnostic' || type == 'result_ext' || type == 'contribute' || type == 'contribute2') {
      on<GetLocationEvent>((event, emit) async {
        emit(const DiagnosePestsState(isShowLoading: true));
        final resp = await ApiClient().getAPI2('${Constants().apiVersion}locations/address_full?lat=${event.lat}&lng=${event.lon}', hasHeader: false);
        if (resp.isNotEmpty) {
          dynamic json = jsonDecode(resp);
          Util.checkKeyFromJson(json, 'success') && json['success'] && Util.checkKeyFromJson(json, 'data') ?
          emit(GetLocationState(BaseResponse(success: true, data: json['data']))) : emit(const BaseState());
        } else emit(const BaseState());
      });
    }

    if (type == 'contribute' || type == 'contribute2') {
      on<CreateDiagnostisPestEvent>((event, emit) async {
        emit(const DiagnosePestsState(isShowLoading: true));
        dynamic response;
        String lat = event.lat, lng = event.lng;
        if (lat.isEmpty || lng.isEmpty) {
          response = await ApiClient().getAPI(Constants().apiVersion + 'locations/latlong?address=${event.address}', WeatherListModel(), hasHeader: false);
          if (response.checkOK()) {
            lat = response.data.lat;
            lng = response.data.lng;
          } else {
            try {
              final loc = await Geolocator.getCurrentPosition();
              lat = loc.latitude.toString();
              lng = loc.longitude.toString();
            } catch (_) {}
          }
        }

        response = await repository.createDiagnosticContribute(event.files,
            event.province_id, event.district_id, event.address, event.tree_name,
            event.pest_name, event.description, lat, lng, rate: event.rate, trainIds: event.trainIds);
        emit(CreateDiagnostisPestSuccessState(response));
      });
    }

    switch(type) {
      case 'result_ext':
        on<ChangePestEvent>((event, emit) => emit(ChangePestState()));
        on<LoadSymptomMalnutritionEvent>((event, emit) async {
          emit(const BaseState(isShowLoading: true));
          final param = '/${event.id}?objectable_type=Diagnostic';

          dynamic resp = await ApiClient().getData('symptoms_malnutritions' + param, hasHeader: false, getError: true);
          if (resp != null) {
            emit(LoadSymptomMalnutritionState(BaseResponse(success: resp['error'] == null,
                data: resp['error']??{'cause': resp['reason']??'', 'symptom': resp['description']??''})));
          }

          resp = null;
          resp = await ApiClient().getData('treatments' + param, hasHeader: false, getError: true);
          if (resp != null) emit(LoadTreatmentState(BaseResponse(success: resp is! Map, data: resp is Map ? resp['error'] : resp)));

          resp = null;
          resp = await ApiClient().getData('preventions' + param, hasHeader: false, getError: true);
          emit(resp != null ? LoadPreventionState(BaseResponse(success: resp['error'] == null, data: resp['error']??(resp['description']??''))) : const BaseState());
        });
        return;
      case 'result_ext_relate_pest_catalogue':
        on<LoadCatalogueEvent>((event, emit) async {
          final response = await repository.loadRelatePestCatalogue(event.id!);
          if (response.isNotEmpty) emit(LoadCatalogueState(response));
        });
        return;
      case 'result_ext_medicine':
        add(CountNotificationMainEvent());
        on<LoadSymptomMalnutritionEvent>((event, emit) async {
          dynamic resp = await ApiClient().getData('medicines/${event.id}', hasHeader: false, getError: true);
          if (resp != null) emit(LoadSymptomMalnutritionState(BaseResponse(success: resp is! Map, data: resp is Map ? resp['error'] : resp)));

          resp = null;
          resp = await ApiClient().getData('safety_warnings', hasHeader: false, getError: true);
          if (resp != null) emit(LoadTreatmentState(BaseResponse(success: resp is! Map, data: resp is Map ? resp['error'] : resp)));
        });
        return;
      case 'result_ext_fertilizer':
        add(CountNotificationMainEvent());
        on<LoadSymptomMalnutritionEvent>((event, emit) async {
          dynamic resp = await ApiClient().getData('recommend_fertilizers/${event.id}', getError: true);
          if (resp != null) emit(LoadSymptomMalnutritionState(BaseResponse(success: resp['error'] == null, data: resp['error']??resp)));
        });
        on<PostRatingEvent>((event, emit) async {
          emit(const BaseState(isShowLoading: true));
          final resp = await ApiClient().postAPI(Constants().apiVersion + 'comments', 'POST', CommentModel(), body: {
            'content': event.type, 'rate': event.point.toString(),
            'commentable_type': event.type, 'commentable_id': event.id.toString()
          });
          emit(PostRatingState(resp));
        });
        return;
      case 'result_ext_nutrition':
        on<LoadSymptomMalnutritionEvent>((event, emit) async {
          emit(const BaseState(isShowLoading: true));
          final param = '/${event.id}?objectable_type=Nutrition';

          dynamic resp = await ApiClient().getData('symptoms_malnutritions' + param, hasHeader: false, getError: true);
          if (resp != null) {
            emit(LoadSymptomMalnutritionState(BaseResponse(success: resp['error'] == null,
                data: resp['error']??{'cause': resp['reason']??'', 'symptom': resp['description']??''})));
          }

          resp = null;
          resp = await ApiClient().getData('treatments' + param, hasHeader: false, getError: true);
          if (resp != null) emit(LoadPreventionState(BaseResponse(success: resp is! Map, data: resp is Map ? resp['error'] : resp)));

          resp = null;
          resp = await ApiClient().getData('preventions' + param, hasHeader: false, getError: true);
          if (resp != null) emit(LoadTreatmentState(BaseResponse(success: resp['error'] == null, data: resp['error']??(resp['description']??''))));

          resp = null;
          resp = await ApiClient().getData('growing_guides' + param, hasHeader: false, getError: true);
          emit(resp != null ? LoadGrowingGuideState(BaseResponse(success: resp is String, data: resp is String?resp:resp['error'])) : const BaseState());
        });
        return;
      case 'diagnostic':
        //isAPI = true;
        //rootBundle.load('assets/models/yolo11m.tflite').then((value) {
        //  data = value.buffer.asUint8List();
          /*data = {};

          final temp = Interpreter.fromBuffer(value.buffer.asUint8List(), options: InterpreterOptions());
          temp.allocateTensors();

          int inputSize = temp.getInputTensor(0).shape[1];
          int dims = temp.getOutputTensor(0).shape[1];
          int outputLength = dims * temp.getOutputTensor(0).shape[2];

          data!.putIfAbsent('interpreter', () => temp);
          data!.putIfAbsent('inputSize', () => inputSize);
          data!.putIfAbsent('dims', () => dims);
          data!.putIfAbsent('outputLength', () => outputLength);*/
        //});
        /*_labels = [
          "Người", "Xe đạp", "Ô tô", "Xe máy", "Máy bay", "Xe buýt",
          "Tàu hỏa", "Xe tải", "Thuyền", "Đèn giao thông", "Vòi cứu hỏa", "Biển báo",
          "Đồng hồ đỗ xe", "Ghế dài", "Chim", "Mèo", "Chó", "Ngựa", "Cừu", "Bò",
          "Voi", "Gấu", "Ngựa vằn", "Hươu cao cổ", "Ba lô", "Dù", "Túi xách",
          "Cà vạt", "Vali", "Đĩa bay", "Trượt tuyết", "Ván trượt tuyết", "Bóng thể thao", "Diều",
          "Gậy bóng chày", "Găng tay bóng chày", "Ván trượt", "Ván lướt sóng", "Vợt tennis",
          "Chai", "Ly rượu vang", "Cốc", "Nĩa", "Dao", "Thìa", "Bát", "Chuối",
          "Táo", "Bánh sandwich", "Cam", "Bông cải xanh", "Cà rốt", "Xúc xích", "Pizza",
          "Bánh rán", "Bánh ngọt", "Ghế", "Ghế dài", "Cây trồng trong chậu", "Giường", "Bàn ăn",
          "Bồn cầu", "Tivi", "Máy tính xách tay", "Chuột", "Điều khiển từ xa", "Bàn phím", "Điện thoại di động",
          "Lò vi sóng", "Lò nướng", "Máy nướng bánh mì", "Bồn rửa", "Tủ lạnh", "Sách", "Đồng hồ",
          "Bình hoa", "Kéo", "Gấu bông", "Máy sấy tóc", "Bàn chải đánh răng"
        ];*/
        on<UploadFileDiagnosePestsEvent>((event, emit) async {
          emit(const ShowPercentState(true));
          final response = await repository.uploadFileDiagnostic(event.files, event.catalogue, event.lat, event.lng);
          emit(const ShowPercentState(false));
          emit(UploadFileDiagnosePestsState(response));
        });
        on<LoadCatalogueEvent>((event, emit) async {
          final db = DBHelperUtil();
          final cat = await db.getSetting('pest_catalogue');
          if (cat != null && cat.isNotEmpty) {
            final plants = PlantsModel().fromJson(jsonDecode(cat));
            emit(LoadCatalogueState(plants.list));
          }

          final response = await repository.loadCatalogue();
          if (response.checkOK() && response.data.list.isNotEmpty) {
            emit(LoadCatalogueState(response.data.list));
            db.setSetting('pest_catalogue', jsonEncode(response.data.list));
          }
        });
        on<ChangePestEvent>((event, emit) => emit(ChangePestState()));
        on<CheckPopupCameraEvent>((event, emit) {
          emit(CheckPopupCameraState());
          if (event.isSave) {
            SharedPreferences.getInstance().then((prefs) {
              prefs.setString('popup_camera', event.value ? 'hidden' : 'show');
            });
            ApiClient().postAPI2(Constants().apiVersion + 'popup_toggles', body: {
              'module_name':'traning_data',
              'popup_type':'camera',
              'popup_value':event.value?'show':'hidden'
            });
          }
        });
        on<ShowPercentEvent>((event, emit) => emit(ShowPercentState(event.value)));
        on<LoadOptionEvent>((event, emit) async {
          String guid = '', popup;
          dynamic response = await ApiClient().getAPI(Constants().apiVersion + 'base/option?key=guid_camera_ai', Options(), hasHeader: false);
          if (response.checkOK() && response.data.list.isNotEmpty) guid = response.data.list[0].value;
          if (guid.isEmpty) return;

          response = await ApiClient().getAPI2(Constants().apiVersion + 'popup_toggles?module_name=traning_data&popup_type=camera');
          if (response.isNotEmpty) {
            try {
              response = jsonDecode(response);
              if (response['success'] == true && Util.checkKeyFromJson(response, 'data')) {
                popup = response['data'][0]['popup_value']??'show';
                emit(LoadOptionState(guid, popup: popup == 'show'));
                final prefs = await SharedPreferences.getInstance();
                prefs.setString('popup_camera', popup);
                return;
              }
            } catch (_) {}
          }

          final prefs = await SharedPreferences.getInstance();
          emit(LoadOptionState(guid, popup: (prefs.getString('popup_camera')??'show') == 'show'));
        });
        on<ShowResultEvent>((event, emit) => emit(ShowResultState(event.index)));
        on<DetectResultEvent>((event, emit) {
          emit(DetectState(event.index, event.image, isShowLoading: event.isShowLoading, stopDetect: true));
        });
        on<DetectEvent>(_detectImage);
        return;
      case 'contribute':
        on<LoadListProvinceEvent>((event, emit) async {
          emit(const BaseState(isShowLoading: true));
          final resp = await ApiClient().getAPI('${Constants().apiVersion}locations/list_provinces', ItemListModel(), hasHeader: false);
          emit(LoadListProvincesState(resp));
        });
        on<LoadListDistrictEvent>((event, emit) async {
          emit(const BaseState(isShowLoading: true));
          final resp = await ApiClient().getAPI('${Constants().apiVersion}locations/list_districts?province_id=${event.idProvince}', ItemListModel(), hasHeader: false);
          emit(LoadListDistrictState(resp));
        });
        on<SetLocationEvent>((event, emit) => emit(SetLocationState()));
        on<UpdateStatusEvent>((event, emit) async {
          emit(const BaseState(isShowLoading: true));
          final response = await ApiClient().postAPI(Constants().apiPerVer + 'training_contribution_data/${event.id}',
              'PUT', BaseResponse(), body: {"approve": event.status});
          emit(UpdateStatusState(response, event.status));
        });
        return;
      case 'history':
        on<LoadDiagnosticEvent>((event, emit) async {
          final resp = await repository.loadCatalogue(passUnknown: true);
          if (resp.checkOK()) {
            final List<PlantModel> plants = resp.data.list;
            final List<ItemModel> list = [];
            final PlantModel plant = plants.firstWhere((element) => element.id ==  event.idPlant, orElse: ()=> PlantModel());
            for(var item in plant.diagnostics) {
              // item.name += '\n(${plant.name})';
              list.add(item);
            }
            emit(LoadDiagnosticState(list));
          }
        });
        on<LoadListPlantEvent>((event, emit) async{
          emit(const DiagnosePestsState(isShowLoading: true));
          final resp = await repository.loadListPlant();
          if(resp.checkOK()){
            final List<ItemModel> list = resp.data.list;
            emit(LoadListPlantState(list));
          }
        });
        on<FillTerByPlantEvent>((event, emit) async{
          emit(FillTerByPlantState());
        });
        on<FillTerByPestEvent>((event, emit) async{
          emit(FillTerByPestState());
        });
        on<LoadDiagnosticHistoryEvent>((event, emit) async {
          emit(LoadDiagnosticHistoryState(await repository.loadDiagnosticHistory(event.page,event.plantId, event.diagnosticId)));
        });
        return;
    }

    /*on<GetLatLonAddressEvent>((event, emit) async {
      emit(const BaseState(isShowLoading: true));
      final response = await ApiClient().getAPI('${Constants().apiVersion}locations/latlong?address=${event.address}',
          WeatherListModel(), hasHeader: false);
      response.checkOK() ? emit(GetLatLonAddressState(response, response.data.lat, response.data.lng)) : emit(const BaseState());
    });*/
  }

  FutureOr<void> _detectImage(DetectEvent event, Emitter<BaseState> emit) async {
    event.image.resetDetect();
    countDetecting ??= 0;
    countDetecting = countDetecting! + 1;
    event.image.start = Util.dateToString(DateTime.now(), pattern: 'HH:mm:ss');
    emit(DetectState(event.index, event.image, isShowLoading: true));

    //if (isAPI != null) {
      //dynamic resp = await ApiClient().postAPI('http://*************:7777/predict', 'POST', null, hasHeader: false,
      //    fullPath: true, realFiles: [event.image], paramFile: 'img', dataIsJson: true, timeout: 60, forceTimeout: true);

      bool loading = false;
      if (countDetecting != null) {
        countDetecting = countDetecting! - 1;
        loading = countDetecting! > 0;
        if (countDetecting! <= 0) countDetecting = null;
      }

      /// ------------- hide detecting image ---------------
      //if (resp.data != null && resp.data is List && resp.data.isNotEmpty) {
      //  event.image.detections = resp.data;
      //  event.image.detectSize = 640.0;
      //} else event.image.detectSize = -1.0;
      /// ------------- hide detecting image ---------------
      event.image.detectSize = -1.0;

      event.image.end = Util.dateToString(DateTime.now(), pattern: 'HH:mm:ss');
      emit(DetectState(event.index, event.image, isShowLoading: loading, stopDetect: true));
      //return;
    //}

    /*ReceivePort receivePort = ReceivePort();
    await Isolate.spawn(isolateDetect, receivePort.sendPort);
    receivePort.first.then((sendPort) {
      ReceivePort responsePort = ReceivePort();
      sendPort.send([responsePort.sendPort, event.image.bytes, 0.1, 60.0, data, _labels]);
      //sendPort.send([responsePort.sendPort, event.image.name, 0.3, 60.0, event.model, _labels]);

      responsePort.first.then((value) {
        bool loading = false;
        if (countDetecting != null) {
          countDetecting = countDetecting! - 1;
          loading = countDetecting! > 0;
          if (countDetecting! <= 0) countDetecting = null;
        }

        if (value == null) event.image.detectSize = -1.0;
        else {
          event.image.detections = value[0];
          event.image.detectSize = value[1];
        }
        event.image.end = Util.dateToString(DateTime.now(), pattern: 'HH:mm:ss');

        add(DetectResultEvent(event.index, event.image, isShowLoading: loading));
      });
    });*/
  }
}
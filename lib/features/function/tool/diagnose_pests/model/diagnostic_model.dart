import 'package:hainong/common/models/item_list_model.dart';
import 'package:hainong/common/util/util.dart';

class DiagnosticModel {
  final List<Diagnostic> diagnostics = [];
  final List<Summary> summaries = [], nuti_summaries = [];
  final Map<String, dynamic> tree = {};
  final List<String> images = [];
  dynamic trees;
  late List ids;
  DiagnosticModel fromJson(Map<String, dynamic> json) {
    if (Util.checkKeyFromJson(json, 'list')) json['list'].forEach((ele) => diagnostics.add(Diagnostic().fromJson(ele)));
    if (Util.checkKeyFromJson(json, 'summaries')) json['summaries'].forEach((ele) => summaries.add(Summary().fromJson(ele)));
    if (Util.checkKeyFromJson(json, 'nuti_summaries')) json['nuti_summaries'].forEach((ele) => nuti_summaries.add(Summary().fromJson(ele)));
    if (Util.checkKeyFromJson(json, 'category')) tree.addAll(json['category']);
    ids = Util.checkKeyFromJson(json, 'training_data_ids') ? json['training_data_ids'] : [];
    if (Util.checkKeyFromJson(json, 'same_categories')) trees = json['same_categories'];
    if (Util.checkKeyFromJson(json, 'summary_images')) json['summary_images'].forEach((ele) => images.add(ele));
    if (Util.checkKeyFromJson(json, 'nuti_summary_images')) json['nuti_summary_images'].forEach((ele) => images.add(ele));
    return this;
  }

  void clear() {
    ids.clear();
    tree.clear();
    summaries.clear();
    nuti_summaries.clear();
    diagnostics.clear();
    if (trees != null) trees.clear();
  }
}

class Diagnostic {
  final List<Summary> predicts = [], nuti_predicts = [];
  String item_id, message, image;
  Diagnostic({this.item_id = '', this.message = '', this.image = ''});
  Diagnostic fromJson(Map<String, dynamic> json) {
    bool success = Util.getValueFromJson(json, 'success', false);
    item_id = Util.getValueFromJson(json, 'item_id', '');
    image = Util.getValueFromJson(json, 'image', '');
    if (success) {
      if (Util.checkKeyFromJson(json, 'predict')) json['predict'].forEach((ele) => predicts.add(Summary().fromJson(ele)));
      if (Util.checkKeyFromJson(json, 'nuti_predict')) json['nuti_predict'].forEach((ele) => nuti_predicts.add(Summary().fromJson(ele)));
    } else message = Util.getValueFromJson(json, 'message', '');
    return this;
  }
}

class Summary {
  int id;
  double percent;
  String suggest;
  String suggest_en;
  ItemListModel images = ItemListModel();
  Summary({this.id=-1,this.percent=0.0,this.suggest='',this.suggest_en=''});
  Summary fromJson(Map<String, dynamic> json) {
    percent = Util.getValueFromJson(json, 'percent', 0.0).toDouble();
    suggest = Util.getValueFromJson(json, 'suggest', '');
    suggest_en = Util.getValueFromJson(json, 'suggest_en', '');
    if (Util.checkKeyFromJson(json, 'aidiagnostic_images')) {
      final temp = json['aidiagnostic_images'];
      if (temp.isNotEmpty) images.fromJson(temp);
    }
    if (Util.checkKeyFromJson(json, 'aidiagnostic')) id = Util.getValueFromJson(json['aidiagnostic'], 'id', -1);
    if (Util.checkKeyFromJson(json, 'ainutrition')) id = Util.getValueFromJson(json['ainutrition'], 'id', -1);
    return this;
  }
}
/*
import 'dart:isolate';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:image/image.dart' as img;
import 'package:tflite_flutter/tflite_flutter.dart';

Future<ByteBuffer> imageToByteListFloat32(Map params) async {
  //final bytes = await params['image'].readAsBytes();
  img.Image? image = img.decodeImage(params['image']);
  //img.Image? image = img.decodeImage(File(params['image']).readAsBytesSync());
  if (image == null) {
    throw Exception("Không decode được ảnh");
  }

  final int inputSize = params['inputSize'];
  final img.Image resizedImage = img.copyResize(image, width: inputSize, height: inputSize, interpolation: img.Interpolation.nearest);

  Float32List convertedBytes = Float32List(inputSize * inputSize * 3);
  int bufferIndex = 0, pixel;
  final double mean = params['mean'];
  final double std = params['std'];

  for (int y = 0; y < inputSize; y++) {
    for (int x = 0; x < inputSize; x++) {
      pixel = resizedImage.getPixel(x, y);
      convertedBytes[bufferIndex++] = (img.getRed(pixel) - mean) / std;
      convertedBytes[bufferIndex++] = (img.getGreen(pixel) - mean) / std;
      convertedBytes[bufferIndex++] = (img.getBlue(pixel) - mean) / std;
    }
  }
  return convertedBytes.buffer;
}

Future<List<Map<String, dynamic>>?> getResult(Map params) async {
  int predictions = params['predictions'], inputSize = params['inputSize'], classIndex;
  Float32List output = params['output'];
  final percentThreshold = params['percent'] / 100.0;

  // Pre-allocate list với capacity dự kiến
  final detections = List<Map<String, dynamic>>.empty(growable: true);

  double cx, cy, w, h, maxScore;
  for (int i = 0; i < predictions; i++) {
    cx = output[i];
    cy = output[predictions + i];
    w = output[2 * predictions + i];
    h = output[3 * predictions + i];

    /*List<double> classScores = [];
    for (int j = 4; j < params['dims']; j++) {
      classScores.add(output[j * predictions + i]);
    }
    maxScore = 0.0;
    classIndex = -1;
    for (int j = 0; j < classScores.length; j++) {
      if (classScores[j] > maxScore) {
        maxScore = classScores[j];
        classIndex = j;
      }
    }*/

    maxScore = 0.0;
    classIndex = -1;
    for (var j = 4; j < params['dims']; j++) {
      final score = output[j * predictions + i];
      if (score > maxScore) {
        maxScore = score;
        classIndex = j - 4;
      }
    }

    if (maxScore > params['threshold'] && maxScore > percentThreshold) {
      detections.add({
        'box': {
          'x1': (cx - w / 2) * inputSize,
          'y1': (cy - h / 2) * inputSize,
          'x2': w * inputSize,
          'y2': h * inputSize
        },
        'confidence': maxScore,
        'name': params['labels'][classIndex]
      });
    }
  }

  return detections.isEmpty ? null : detections;
}

void isolateDetect(SendPort sendPort) async {
  ReceivePort port = ReceivePort();
  sendPort.send(port.sendPort);

  await for (var message in port) {
    SendPort responsePort = message[0];
    try {
      ///Step 1: load model
      Interpreter interpreter = Interpreter.fromBuffer(message[4], options: InterpreterOptions());
      interpreter.allocateTensors();

      int inputSize = interpreter.getInputTensor(0).shape[1];
      int dims = interpreter.getOutputTensor(0).shape[1];
      int predictions = interpreter.getOutputTensor(0).shape[2];

      ///Step 2: create input
      ByteBuffer inputBuffer = await compute(imageToByteListFloat32, {
        'image': message[1],
        'inputSize': inputSize,
        'mean': 127.5,
        'std': 127.5,
      });

      ///Step 3: create output
      Float32List output = Float32List(dims * predictions);

      ///Step 4: run inference
      interpreter.run(inputBuffer, output.buffer);

      ///Step 5: get result
      List<Map<String, dynamic>>? detections = await compute(getResult, {
        'predictions': predictions,
        'threshold': message[2],
        'percent': message[3],
        'dims': dims,
        'inputSize': inputSize,
        'output': output,
        'labels': message[5]
      });

      interpreter.close();

      if (detections != null && detections.isNotEmpty) {
        responsePort.send([detections, inputSize.toDouble()]);
        continue;
      }
    } catch (e) {
      //print(e);
    }
    responsePort.send(null);
  }
}
*/
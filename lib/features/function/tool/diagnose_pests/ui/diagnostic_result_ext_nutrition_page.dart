import 'package:hainong/common/models/file_byte.dart';
import 'diagnostic_result_ui.dart';
import 'diagnostic_result_base_page.dart';

class DiagnosticResultExtNutritionPage extends BasePage {
  final int index;
  DiagnosticResultExtNutritionPage(this.index, DiagnosticModel result, String shareContent, List<FileByte> images, loc,
      {Key? key}) : super(key: key, pageState: _DiaResultExtNutritionPageState(result, shareContent, images, loc));
}
class _DiaResultExtNutritionPageState extends DiaResultBasePageState {
  int _tabIndex = 1;
  String _symptom = '', _cause = '', _treatment = '', _growingGuide = '';
  final List _preventions = [];
  _DiaResultExtNutritionPageState(result, shareContent, images, loc) : super(result, shareContent, images, [], loc, type: 'result_ext_nutrition');

  @override
  String getTitle() => 'Kết quả chẩn đo<PERSON>';

  @override
  void listenBloc(state) {
    if (state is LoadSymptomMalnutritionState && isResponseNotError(state.resp)) {
      _symptom = state.resp.data['symptom'];
      _cause = state.resp.data['cause'];
      bloc!.data.putIfAbsent('symptom', () => 'symptom_off');
      bloc!.data.putIfAbsent('cause', () => 'cause_off');
    } else if (state is LoadPreventionState && isResponseNotError(state.resp)) {
      _preventions.addAll(state.resp.data);
      for (var ele in _preventions) {
        bloc!.data.putIfAbsent(ele['id'].toString(), () => ele['id'].toString() + '_off');
      }
    } else if (state is LoadTreatmentState && isResponseNotError(state.resp, passString: true)) {
      _treatment = state.resp.data;
    } else if (state is LoadGrowingGuideState && isResponseNotError(state.resp, passString: true)) {
      _growingGuide = state.resp.data;
    } else super.listenBloc(state);
  }

  @override
  void initState() {
    super.initState();
    bloc!.add(LoadSymptomMalnutritionEvent(result.nuti_summaries[(widget as DiagnosticResultExtNutritionPage).index].id));
    bloc!.data = {};
  }

  @override
  Widget createUI() {
    final summary = result.nuti_summaries[(widget as DiagnosticResultExtNutritionPage).index];
    return ListView(padding: EdgeInsets.zero, children: [
      Padding(padding: EdgeInsets.all(40.sp), child: Row(children: [
        Expanded(child: Column(children: [
          LabelCustom(summary.suggest, size: 48.sp, weight: FontWeight.w500, color: const Color(0xFF002500)),
          SizedBox(height: 10.sp),
          LabelCustom(summary.suggest_en, size: 40.sp, weight: FontWeight.w500, color: const Color(0xFFCDCDCD)),
        ], crossAxisAlignment: CrossAxisAlignment.start)),
        ImageNetworkAsset(path: result.tree['icon'] ?? '', width: 120.sp, height: 120.sp),
        LabelCustom('  ' + (result.tree['name']??''), size: 48.sp, color: const Color(0xFF1865B1))
      ], mainAxisAlignment: MainAxisAlignment.end)),

      Padding(padding: EdgeInsets.symmetric(horizontal: 40.sp), child: LabelCustom('1. Nguyên nhân', size: 54.sp, color: Colors.black)),
      ShowHtml(bloc, _cause, 'cause'),

      Padding(padding: EdgeInsets.symmetric(horizontal: 40.sp), child: LabelCustom('2. Triệu chứng', size: 54.sp, color: Colors.black)),
      ShowHtml(bloc, _symptom, 'symptom'),

      BlocBuilder(bloc: bloc, builder: (_,__) => Column(children: [
        Row(children: [
          ResultTabItem('Biện pháp\nphòng ngừa', 0, _tabIndex == 0, _changeTab),
          ResultTabItem('Biện pháp\nđiều trị', 1, _tabIndex == 1, _changeTab),
          ResultTabItem('Hướng dẫn\ncanh tác', 2, _tabIndex == 2, _changeTab)
        ]),
        if (_tabIndex == 0) _preventions.isEmpty ? Container(alignment: Alignment.centerLeft, padding: EdgeInsets.all(40.sp),
          child: const DataUpdating()) : (ListView.builder(physics: const NeverScrollableScrollPhysics(), shrinkWrap: true,
            itemCount: _preventions.length, itemBuilder: (_,index) {
              final treatment = _preventions[index];
              return Column(children: [
                LabelCustom(treatment['name']??'', size: 60.sp, color: const Color(0xFF1DA644)),
                Util.checkKeyFromJson(treatment, 'object_results') ?
                  (treatment['object_results'] is String ? Padding(padding: EdgeInsets.only(top: 20.sp),
                      child: MyHtml(treatment['object_results']))
                  : ExpandItemList(bloc, treatment['object_results'], treatment['id'].toString(), hasImage: true)) :
                Padding(padding: EdgeInsets.only(top: 20.sp, bottom: 40.sp), child: const DataUpdating())
              ], crossAxisAlignment: CrossAxisAlignment.start);
            }, padding: EdgeInsets.all(40.sp))),
        if (_tabIndex == 1) Container(padding: EdgeInsets.all(40.sp), alignment: Alignment.centerLeft, child: _treatment.isEmpty ? const DataUpdating() : MyHtml(_treatment)),
        if (_tabIndex == 2) Container(padding: EdgeInsets.all(40.sp), alignment: Alignment.centerLeft, child:  _growingGuide.isEmpty ? const DataUpdating() : MyHtml(_growingGuide))
      ]), buildWhen: (oldS, newS) => newS is ChangeCatalogueState || newS is LoadTreatmentState),
    ]);
  }

  @override
  Widget buttonShare() => IconButton(onPressed: () => bloc!.add(CreatePostDiagnosePestsEvent(images, shareContent)),
      icon: Icon(Icons.share, color: Colors.white, size: 56.sp));

  void _changeTab(int index) {
    if (_tabIndex == index) return;
    setState(() => _tabIndex = index);
    //bloc!.add(ChangeCatalogueEvent());
  }
}
import 'dart:typed_data';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:hainong/common/models/file_byte.dart';
import 'package:hainong/common/ui/image_network_asset.dart';
import 'package:hainong/common/ui/label_custom.dart';
import 'package:hainong/common/ui/import_lib_ui.dart';
import 'package:hainong/common/ui/loading.dart';
import '../diagnose_pests_bloc.dart';

class DiagnosticComparePage extends BasePage {
 final String address, shareContent;
 final List<FileByte> images;
 final List<dynamic> ids;
 final List<String> imageDiagnostic;
 DiagnosticComparePage(this.address, this.shareContent, this.imageDiagnostic, this.images, this.ids,
     {Key? key}) : super(key: key, pageState: _DiagnosticCompareImageState());
}

class _DiagnosticCompareImageState extends BasePageState {
  int _indexUser = 0, _indexDiagnostic = 0;
  @override
  void initState() {
    bloc = DiagnosePestsBloc(const DiagnosePestsState());
    super.initState();
    bloc!.stream.listen((state) {
      if (state is CreatePostDiagnosePestsState && isResponseNotError(state.response)) {
        UtilUI.showCustomDialog(context, 'Kết quả chẩn đoán đã được đăng lên tường thành công', title: 'Thông báo');
      } else if (state is SendFeedbackState && isResponseNotError(state.resp, passString: true)) {
        UtilUI.showCustomDialog(context, 'Đã gửi phản hồi thành công', title: 'Thông báo');
      }
    });
  }

  @override
  Widget build(BuildContext context, {Color color = Colors.white}) {
    final page = widget as DiagnosticComparePage;
    return Stack(children: [
      Scaffold(appBar: AppBar(titleSpacing: 0, centerTitle: true, title: UtilUI.createLabel('So sánh hình ảnh'),
          actions: [
            if (page.shareContent.isNotEmpty)
            IconButton(onPressed: () => bloc!.add(CreatePostDiagnosePestsEvent(page.images, page.shareContent)),
              icon: Icon(Icons.share, color: Colors.white, size: 56.sp)),
            IconButton(onPressed: _feedback, icon: Icon(Icons.feedback, color: Colors.white, size: 56.sp))
          ]),
        body: ListView(padding: EdgeInsets.all(40.sp), children: [
            LabelCustom('I. Ảnh hệ thống', size: 60.sp, color: const Color(0xFF1DA644)),
            SizedBox(height: 20.sp),

            if (page.imageDiagnostic.isNotEmpty)
            ClipRRect(borderRadius: BorderRadius.circular(10), child: page.imageDiagnostic.length == 1 ?
              ImageNetworkAsset(path: page.imageDiagnostic[0], height: 0.24.sh, width: 1.sw - 80.sp):
              CarouselSlider.builder(itemCount: page.imageDiagnostic.length,
                options: CarouselOptions(viewportFraction: 1,
                    onPageChanged: (i,_) => setState(() => _indexDiagnostic = i)
                ),
                itemBuilder: (_,i,__) => ImageNetworkAsset(path: page.imageDiagnostic[i], height: 0.24.sh, width: 1.sw - 80.sp)
            )),
            if (page.imageDiagnostic.length > 1) SlidePoints(page.imageDiagnostic, _indexDiagnostic),

            SizedBox(height: 40.sp),
            LabelCustom('II. Ảnh của người dùng', size: 60.sp, color: const Color(0xFF1DA644)),
            SizedBox(height: 20.sp),

            ClipRRect(borderRadius: BorderRadius.circular(10), child: page.images.length == 1 ?
              Image.memory(Uint8List.fromList(page.images[0].bytes),
                  fit: BoxFit.cover, height: 0.24.sh, width: 1.sw - 80.sp):
              CarouselSlider.builder(itemCount: page.images.length,
                options: CarouselOptions(viewportFraction: 1,
                    onPageChanged: (i,_) => setState(() => _indexUser = i)
                ),
                itemBuilder: (_,i,__) => Image.memory(Uint8List.fromList(page.images[i].bytes),
                    fit: BoxFit.cover, height: 0.24.sh, width: 1.sw - 80.sp)
            )),
            if (page.images.length > 1) SlidePoints(page.images, _indexUser),

            SizedBox(height: 20.sp),
            Row(children: [
              Icon(Icons.location_pin, color: Colors.red, size: 48.sp),
              Expanded(child: LabelCustom(page.address, size: 30.sp, color: const Color(0xFF0093FD), weight: FontWeight.normal))
            ], mainAxisAlignment: MainAxisAlignment.end)
          ])),
      Loading(bloc)
    ]);
  }

  void _feedback() => UtilUI.showConfirmDialog(context, '', 'Nhập nội dung phản hồi', 'Nội dung không được để trống',
    padding: EdgeInsets.all(30.sp), title: 'Phản hồi kết quả', showMsg: false, line: 0,
    inputType: TextInputType.multiline, action: TextInputAction.newline).then((value) {
      if (value != null && value is String && value.isNotEmpty) bloc!.add(SendFeedbackEvent(value, (widget as DiagnosticComparePage).ids));
    });
}

class SlidePoints extends StatelessWidget {
  final List list;
  final int now;
  const SlidePoints(this.list, this.now, {Key? key}):super(key: key);
  @override
  Widget build(BuildContext context) {
    final List<Widget> list = [];
    for(int index = 0; index < this.list.length; index ++) {
      list.add(Container(height: 8, width: now == index ? 30 : 8,
          decoration: BoxDecoration(color: now == index ? Colors.blue : const Color(0xFFCDCDCD),
              borderRadius: BorderRadius.circular(20)), margin: const EdgeInsets.symmetric(horizontal: 4)));
    }
    return Container(margin: EdgeInsets.only(top: 20.sp),
        child: Row(children: list, mainAxisAlignment: MainAxisAlignment.center), height: 8);
  }
}


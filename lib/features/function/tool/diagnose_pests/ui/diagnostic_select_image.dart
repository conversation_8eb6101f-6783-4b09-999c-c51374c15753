import 'dart:async';
import 'package:image/image.dart' as IMG;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hainong/common/base_bloc.dart';
import 'package:hainong/common/ui/loading.dart';
import 'package:hainong/common/util/util_ui.dart';
import 'package:hainong/common/ui/import_lib_base_ui.dart';
import 'package:hainong/common/ui/label_custom.dart';

class DiagnosticSelectImage extends StatefulWidget {
  static dynamic bytesOrImage;
  const DiagnosticSelectImage({Key? key}) : super (key: key);
  @override
  _DiagnosticSelectImageState createState() => _DiagnosticSelectImageState();
}

class _DiagnosticSelectImageState extends State<DiagnosticSelectImage> {
  late _CropBloc bloc;

  @override
  void dispose() {
    bloc.close();
    super.dispose();
  }

  @override
  void initState() {
    bloc = _CropBloc();
    super.initState();
    bloc.stream.listen((state) {
      if (state is LoadProvinceBaseState) UtilUI.goBack(context, true);
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    Timer(const Duration(milliseconds: 1500), () => bloc.add(CheckMemPackageEvent('')));
  }

  @override
  Widget build(BuildContext context) {
    return Stack(children: [
      WillPopScope(child: Scaffold(appBar: AppBar(backgroundColor: Colors.transparent, elevation: 0,
          actions: [
            IconButton(onPressed: _checkImage, icon: const Icon(Icons.crop, color: Colors.white))
          ]
        ), backgroundColor: Colors.black,
        body: Container(alignment: Alignment.center, //height: 200,
            decoration: BoxDecoration(
                image: DecorationImage(image: Image.memory(DiagnosticSelectImage.bytesOrImage).image, fit: BoxFit.scaleDown)
            ),
            child: GestureDetector(child: Stack(children: [
              Image.memory(DiagnosticSelectImage.bytesOrImage, fit: BoxFit.scaleDown, key: bloc.containerKey, opacity: const AlwaysStoppedAnimation(0)),

              BlocBuilder(bloc: bloc, buildWhen: (_,n) => n is CheckMemPackageState,
              builder: (_,__) {
                if (bloc.borders.isEmpty || bloc.borders['left'] == null) return const SizedBox();
                return Positioned(
                    left: bloc.borders['left'], top: bloc.borders['top'],
                    child: Container(
                        width: bloc.borders['width'].abs(), height: bloc.borders['height'].abs(),
                        decoration: BoxDecoration(border: Border.all(color: Colors.red, width: 2.8)),
                        child: Column(children: [
                          Flexible(child: Container(color: Colors.black45, padding: const EdgeInsets.all(5),
                              child: LabelCustom(bloc.getInfo(), size: 12, weight: FontWeight.normal)
                          ))
                        ], crossAxisAlignment: CrossAxisAlignment.start, mainAxisSize: MainAxisSize.min)
                    ));
              }),
            ]),
              onHorizontalDragDown: _onDragDown,
              onHorizontalDragUpdate: _onDragUpdate,
              onHorizontalDragEnd: _onDragEnd,
            )
        )
      ), onWillPop: () async {
        return true;
      }),
      Loading(bloc)
    ]);
  }

  void _onDragDown(DragDownDetails detail) {
    bloc.borders.update('start_local', (v) => detail.localPosition);
    bloc.borders.update('end_local', (v) => detail.localPosition);
    bloc.borders.update('cur_local', (v) => detail.localPosition);
    bloc.borders.update('left', (v) => null);
    bloc.borders.update('top', (v) => null);
    bloc.borders.update('width', (v) => 0.0);
    bloc.borders.update('height', (v) => 0.0);
  }

  void _onDragUpdate(DragUpdateDetails detail) {
    bloc.borders.update('end_local', (v) => detail.localPosition);

    if (((detail.localPosition.dx - (bloc.borders['cur_local'] as Offset).dx).abs() > 10.0) ||
        ((detail.localPosition.dy - (bloc.borders['cur_local'] as Offset).dy).abs() > 10.0)) {
      bloc.borders.update('cur_local', (v) => detail.localPosition);
      _calculate();
      setState(() {});
    }
  }

  /// stop draw and solve over border
  void _onDragEnd(DragEndDetails detail) {
    bool hasCalculate = false;
    Offset end = bloc.borders['end_local'] as Offset;

    if (end.dy > bloc.borders['image_height_scale']) {
      bloc.borders.update('end_local', (value) => Offset(end.dx, bloc.borders['image_height_scale']));
      hasCalculate = true;
    }
    if (end.dy < 0) {
      bloc.borders.update('end_local', (value) => Offset(end.dx, 0));
      hasCalculate = true;
    }

    end = bloc.borders['end_local'] as Offset;

    if (end.dx > bloc.borders['image_width_scale']) {
      bloc.borders.update('end_local', (value) => Offset(bloc.borders['image_width_scale'], end.dy));
      hasCalculate = true;
    }
    if (end.dx < 0) {
      bloc.borders.update('end_local', (value) => Offset(0, end.dy));
      hasCalculate = true;
    }

    if (hasCalculate) _calculate();
    setState(() {});
  }

  void _calculate() {
    double dx = (bloc.borders['end_local'] as Offset).dx - (bloc.borders['start_local'] as Offset).dx,
           dy = (bloc.borders['end_local'] as Offset).dy - (bloc.borders['start_local'] as Offset).dy;
    double? left, top;
    if (dx > 0) {
      if (dy > 0) { //1
        left = (bloc.borders['start_local'] as Offset).dx;
        top = (bloc.borders['start_local'] as Offset).dy;
      } else { //2
        left = (bloc.borders['start_local'] as Offset).dx;
        top = (bloc.borders['end_local'] as Offset).dy;
      }
    } else {
      if (dy > 0) { //3
        left = (bloc.borders['end_local'] as Offset).dx;
        top = (bloc.borders['start_local'] as Offset).dy;
      } else { //4
        left = (bloc.borders['end_local'] as Offset).dx;
        top = (bloc.borders['end_local'] as Offset).dy;
      }
    }

    bloc.borders.update('left', (v) => left);
    bloc.borders.update('top', (v) => top);
    bloc.borders.update('width', (v) => dx);
    bloc.borders.update('height', (v) => dy);
  }

  void _checkImage() {
    if ((bloc.borders['width']~/bloc.borders['scale']).abs() < 448 || (bloc.borders['height']~/bloc.borders['scale']).abs() < 448) {
      UtilUI.showCustomDialog(context, 'Ảnh không hợp lệ. Kích thước ảnh sau khi cắt nhỏ hơn tiêu chuẩn (448x448)');
      return;
    }
    bloc.add(LoadingEvent(true));
    Timer(const Duration(seconds: 1), () => bloc.add(LoadProvinceBaseEvent()));
  }
}

class _CropBloc extends BaseBloc {
  String info = '';
  final containerKey = GlobalKey();
  final Map<String, dynamic> borders = {};

  @override
  Future<void> close() async {
    borders.clear();
    super.close();
  }

  _CropBloc() : super(init: const BaseState(isShowLoading: true)) {
    on<CheckMemPackageEvent>((event, emit) async {
      _init();
      if (borders.isEmpty || !borders.containsKey('height')) {
        Timer(const Duration(seconds: 1), () => add(CheckMemPackageEvent('')));
        return;
      }
      emit(CheckMemPackageState(null));
    });
    on<LoadProvinceBaseEvent>((event, emit) async {
      emit(const BaseState(isShowLoading: true));
      IMG.Image img = IMG.copyCrop(borders['image'],
          x: borders['left']~/borders['scale'], y: borders['top']~/borders['scale'],
          width: (borders['width']~/borders['scale']).abs(), height: (borders['height']~/borders['scale']).abs());
      DiagnosticSelectImage.bytesOrImage = img;
      emit(LoadProvinceBaseState(true));
    });
  }

  void _init() {
    RenderObject? pos = containerKey.currentContext!.findRenderObject();
    if (pos == null) return;

    borders.putIfAbsent('image_width_scale', () => pos.paintBounds.width);
    borders.putIfAbsent('image_height_scale', () => pos.paintBounds.height);

    Offset start = const Offset(0.0, 0.0);
    Offset end = Offset(pos.paintBounds.width, pos.paintBounds.height);
    borders.putIfAbsent('start_local', () => start);
    borders.putIfAbsent('end_local', () => end);
    borders.putIfAbsent('cur_local', () => end);
    borders.putIfAbsent('left', () => 0.0);
    borders.putIfAbsent('top', () => 0.0);
    borders.putIfAbsent('width', () => pos.paintBounds.width);
    borders.putIfAbsent('height', () => pos.paintBounds.height);

    IMG.Image? img = IMG.decodeImage(DiagnosticSelectImage.bytesOrImage);
    if (img == null) return;

    borders.putIfAbsent('image_width', () => img.width);
    borders.putIfAbsent('image_height', () => img.height);

    borders.putIfAbsent('scale', () => pos.paintBounds.height/img.height);
    borders.putIfAbsent('image', () => img);
  }

  String getInfo() => borders.isNotEmpty && borders.containsKey('height') && borders.containsKey('scale') ?
      'Dài: ${(borders['height']~/borders['scale']).abs()}\nRộng: ${(borders['width']~/borders['scale']).abs()}' : '';
}
import 'package:carousel_slider/carousel_slider.dart' as slide;
import 'package:hainong/common/count_down_bloc.dart';
import 'package:hainong/common/ui/button_image_widget.dart';
import 'package:hainong/common/ui/star_rate.dart';
import 'package:hainong/features/function/tool/map_task/map_task_page.dart';
import 'package:hainong/features/function/tool/map_task/models/map_deep_link_model.dart';
import 'package:hainong/features/function/tool/map_task/models/map_enum.dart';
import 'package:hainong/features/main/bloc/main_bloc.dart';
import 'package:hainong/features/product/product_model.dart';
import 'package:hainong/features/product/ui/product_detail_page.dart';
import 'package:hainong/features/rating/rating_bloc.dart';
import 'package:hainong/features/shop/shop_model.dart';
import 'diagnostic_result_base_page.dart';
import 'diagnostic_result_ui.dart';

class DiagnosticResultExtFertilizerPage extends BasePage {
  final int id;
  final String name;
  DiagnosticResultExtFertilizerPage(this.id, this.name, {Key? key}) : super(key: key, pageState: _DiaResultExtFertilizerPageState());
}
class _DiaResultExtFertilizerPageState extends BasePageState {
  final Map _fertilizer = {};
  final slide.CarouselSliderController _ctr = slide.CarouselSliderController();

  @override
  void dispose() {
    _fertilizer.clear();
    super.dispose();
  }

  @override
  void initState() {
    bloc = DiagnosePestsBloc(const DiagnosePestsState(), type: 'result_ext_fertilizer');
    super.initState();
    bloc!.stream.listen((state) {
      if (state is LoadSymptomMalnutritionState && isResponseNotError(state.resp)) {
        setState(() => _fertilizer.addAll(state.resp.data));
      } else if (state is CountNotificationMainState) { // check for show error
        isResponseNotError(state.response, passString: true);
      } else if (state is PostRatingState && isResponseNotError(state.response)) {
        bloc!.add(LoadSymptomMalnutritionEvent((widget as DiagnosticResultExtFertilizerPage).id));
        UtilUI.showCustomDialog(context, 'Đã gửi đánh giá thành công');
      }
    });
    bloc!.add(LoadSymptomMalnutritionEvent((widget as DiagnosticResultExtFertilizerPage).id));
  }

  @override
  Widget build(BuildContext context, {Color color = Colors.white}) => Scaffold(appBar: AppBar(titleSpacing: 0, centerTitle: true,
      title: UtilUI.createLabel((widget as DiagnosticResultExtFertilizerPage).name), elevation: 0), backgroundColor: color,
    body: Column(children: [
      Expanded(child: ListView(padding: EdgeInsets.zero, children: [
        const _Title('Hình ảnh sản phẩm'),
        Util.checkKeyFromJson(_fertilizer, 'images') && _fertilizer['images'].isNotEmpty ? Stack(children: [
          Container(height: 0.28.sh + 80.sp, child: slide.CarouselSlider.builder(carouselController: _ctr,
              itemCount: _fertilizer['images'].length, options: slide.CarouselOptions(viewportFraction: 1),
              itemBuilder: (context, index, realIndex) => ImageNetworkAsset(path: _fertilizer['images'][index]['name']??'',
                  height: 0.28.sh, width: 0.5.sw)), padding: EdgeInsets.symmetric(vertical: 40.sp)),
          Container(child: ButtonImageWidget(60, () => _ctr.previousPage(),
              Transform.rotate(angle: -3.14, child: Icon(Icons.arrow_forward_ios, size: 120.sp, color: const Color(0xFFEFEFEF)))),
                alignment: Alignment.centerLeft, padding: EdgeInsets.only(left: 40.sp)),
          Container(alignment: Alignment.centerRight, child: ButtonImageWidget(60, () => _ctr.nextPage(),
              Icon(Icons.arrow_forward_ios, size: 120.sp, color: const Color(0xFFEFEFEF))), padding: EdgeInsets.only(right: 40.sp)),
        ], alignment: Alignment.center) : Padding(padding: EdgeInsets.all(40.sp), child: const DataUpdating()),

        Padding(child: Row(children: [
          ButtonImageWidget(10, _gotoProduct, Padding(child: Row(children: [
            Image.asset('assets/images/v11/ic_cart_ref_v11.png', width: 48.sp,
                color: _fertilizer['product_id'] == null ? Colors.white : null),
            LabelCustom('  Mua ngay  ', weight: FontWeight.w600, size: 40.sp),
            Icon(Icons.arrow_forward_ios, color: Colors.white, size: 48.sp)
          ]), padding: EdgeInsets.symmetric(vertical: 40.sp, horizontal: 20.sp)),
              color: Color(_fertilizer['product_id'] == null ? 0xFFD9D9D9 : 0xFF1DA644), elevation: 5),
          SizedBox(width: 40.sp),
          Flexible(child: ButtonImageWidget(10, _gotoAgency, Padding(child: Row(children: [
            Image.asset('assets/images/v11/ic_loc_ref_v11.png', width: 48.sp,
                color: _fertilizer['agency_id'] == null ? Colors.white : null),
            Flexible(child: Padding(padding: EdgeInsets.symmetric(horizontal: 20.sp),
                child: LabelCustom('Khám phá cửa hàng', weight: FontWeight.w600, size: 40.sp, align: TextAlign.center))),
            Icon(Icons.arrow_forward_ios, color: Colors.white, size: 48.sp)
          ], mainAxisSize: MainAxisSize.min), padding: EdgeInsets.symmetric(vertical: 40.sp, horizontal: 20.sp)),
              color: Color(_fertilizer['agency_id'] == null ? 0xFFD9D9D9 : 0xFF1DA644), elevation: 5))
        ], mainAxisAlignment: MainAxisAlignment.center, mainAxisSize: MainAxisSize.min),
            padding: EdgeInsets.fromLTRB(40.sp, 0, 40.sp, 40.sp)),

        const _Title('Hướng dẫn sử dụng'),
        Padding(padding: EdgeInsets.all(40.sp), child: (_fertilizer['instructions_for_use']??'') != '' ? MyHtml(_fertilizer['instructions_for_use']??'') : const DataUpdating()),
        const _Title('Cảnh báo an toàn'),
        Padding(padding: EdgeInsets.all(40.sp), child: (_fertilizer['safety_warning']??'') != '' ? MyHtml(_fertilizer['safety_warning']??'') : const DataUpdating())
      ])),

      ButtonImageWidget(10, _showRating, Container(padding: EdgeInsets.all(40.sp),
          decoration: BoxDecoration(color: const Color(0x5981C5FF),
              borderRadius: const BorderRadius.vertical(top: Radius.circular(10)),
              border: Border.all(color: const Color(0xFF81C5FF), width: 1)),
          child: Column(children: [
            LabelCustom('Hãy đánh giá chất lượng sản phẩm',
                align: TextAlign.justify, color: Colors.black, weight: FontWeight.w600, size: 48.sp),
            Padding(padding: EdgeInsets.symmetric(vertical: 20.sp), child: StarRate(rate: _fertilizer['rate']??0, size: 120.sp)),
          ])
      ))
    ]));

  void _showRating() {
    if (Util.checkKeyFromJson(_fertilizer, 'comment')) {
      UtilUI.showCustomDialog(context, 'Bạn đã gửi đánh giá sản phẩm này rồi');
      return;
    }

    final CountDownBloc count = CountDownBloc();
    UtilUI.showCustomDialog(context, '', title: 'Đánh giá sản phẩm', isActionCancel: true,
        lblOK: 'GỬI ĐÁNH GIÁ', extend: Padding(padding: EdgeInsets.symmetric(vertical: 20.sp), child: BlocBuilder(
          builder: (context, state) => StarRate(rate: (state as CountDownState).value, size: 120.sp, hasFunction: true,
            onClick: (int point) => count.add(CountDownEvent(value: point != (count.state as CountDownState).value ? point : 0))),
          bloc: count))).then((value) {
            if (value == true) {
              if ((count.state as CountDownState).value == 0) {
                UtilUI.showCustomDialog(context, 'Hãy chọn số sao để gửi đánh giá').whenComplete(_showRating);
                return;
              }
              bloc!.add(PostRatingEvent((count.state as CountDownState).value, '', _fertilizer['classable_type']??'', _fertilizer['classable_id']??-1, -1));
            }
          }).whenComplete(() => count.close());
  }

  void _gotoProduct() {
    if (_fertilizer['product_id'] == null) return;
    UtilUI.goToNextPage(context, ProductDetailPage(ProductModel(id: _fertilizer['product_id']), ShopModel(), hasReload: true));
  }

  void _gotoAgency() {
    if (_fertilizer['agency_id'] == null) return;
    final data = MapDeepLinkModel(id: _fertilizer['agency_id'].toString(), lat: (_fertilizer['agency_lat']??10.7546181).toString(), lng: (_fertilizer['agency_lng']??106.3655758).toString(), menu: MapMenuEnum.model, tab: MapModelEnum.store);
    UtilUI.goToNextPage(context, MapTaskPage(deepLink: data));
  }
}

class _Title extends StatelessWidget {
  final String title;
  const _Title(this.title, {Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) => Container(color: const Color(0xFFEFEFEF),
    padding: EdgeInsets.all(40.sp),
    child: LabelCustom(title, color: Colors.black, size: 48.sp, align: TextAlign.center)
  );
}
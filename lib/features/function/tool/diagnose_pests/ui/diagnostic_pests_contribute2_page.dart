import 'package:geolocator/geolocator.dart';
import 'package:hainong/common/ui/button_image_widget.dart';
import 'package:hainong/common/ui/import_lib_ui.dart';
import 'package:hainong/common/ui/label_custom.dart';
import 'package:hainong/common/ui/title_red_require.dart';
import 'package:hainong/common/models/file_byte.dart';
import 'package:hainong/common/models/item_list_model.dart';
import 'package:hainong/features/main/bloc/main_bloc.dart';
import 'diagnostic_pests_image_item.dart';
import '../diagnose_pests_bloc.dart';
import '../model/plant_model.dart';

class DiagnosticPetsContribute2Page extends BasePage {
  final int? rate;
  final String tree;
  final List<FileByte> images;
  final Function funHideRating;
  final dynamic trainIds;
  DiagnosticPetsContribute2Page(List<PlantModel> catalogues, this.images,
    Position loc, this.rate, this.trainIds, this.funHideRating, {Key? key, this.tree = ''})
  : super(key: key, pageState: _DiagnosticPetsContribute2State(catalogues, loc));
}

class _DiagnosticPetsContribute2State extends BasePageState {
  final List<PlantModel> catalogues;
  final List<ItemModel> _diagnosis = [];
  final ItemModel _locationProvinces = ItemModel();
  final ItemModel _locationDistrict = ItemModel();
  final TextEditingController _ctrTreeName = TextEditingController();
  final TextEditingController _ctrName = TextEditingController();
  final TextEditingController _ctrDescription = TextEditingController();
  final FocusNode _focusTreeName = FocusNode();
  final FocusNode _focusName = FocusNode();
  final FocusNode _focusDescription = FocusNode();
  String _lat = '', _lng = '';
  String _address = '';

  _DiagnosticPetsContribute2State(this.catalogues, Position loc) {
    _lat = loc.latitude.toString();
    _lng = loc.longitude.toString();
    for(var tree in catalogues) {
      for(var ele in tree.diagnostics) {
        _diagnosis.add(ItemModel(id: ele.name, name: ele.name + ' (${tree.name})'));
      }
    }
  }

  @override
  void dispose() {
    _diagnosis.clear();
    _ctrTreeName.dispose();
    _ctrName.dispose();
    _ctrDescription.dispose();
    _focusTreeName.dispose();
    _focusName.dispose();
    _focusDescription.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    bloc = DiagnosePestsBloc(const DiagnosePestsState(), type: 'contribute2');
    bloc!.stream.listen((state) {
      if (state is CreateDiagnostisPestSuccessState && isResponseNotError(state.resp, passString: true)) {
        (widget as DiagnosticPetsContribute2Page).funHideRating();
        UtilUI.showCustomDialog(context, MultiLanguage.get('msg_thank_feedback_pets'),
            title: MultiLanguage.get('lbl_success')).whenComplete(() => Navigator.of(context).pop());
      } else if (state is GetLocationState) {
        final json = state.response.data;
        if (Util.checkKeyFromJson(json, 'province_id')) {
          _setLocation(ItemModel(id: json['province_id'].toString(), name: json['province_name']), true);
        }
        if (Util.checkKeyFromJson(json, 'district_id')) {
          _setLocation(ItemModel(id: json['district_id'].toString(), name: json['district_name']), false);
        }
        if (Util.checkKeyFromJson(json, 'address_full')) setState(() => _address = json['address_full']);
      }
    });

    bloc!.add(GetLocationEvent(_lat.toString(), _lng.toString()));
    final page = widget as DiagnosticPetsContribute2Page;
    if (page.tree.isNotEmpty) _ctrTreeName.text = page.tree;
  }

  @override
  Widget build(BuildContext context, {Color color = Colors.white}) {
    final page = widget as DiagnosticPetsContribute2Page;
    final padding = EdgeInsets.only(top: 40.sp, bottom: 20.sp);
    return Stack(children: [
      Container(padding: EdgeInsets.all(40.sp),
        decoration: const BoxDecoration(color: StyleCustom.primaryColor,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20))),
        child: Row(children: [
          Expanded(child: LabelCustom('Đóng góp sâu bệnh', size: 48.sp, align: TextAlign.center)),
          ButtonImageWidget(20, () => UtilUI.goBack(context, false), Padding(padding: EdgeInsets.all(10.sp),
            child: Icon(Icons.close, color: Colors.white, size: 56.sp)))
        ])),

      Container(padding: EdgeInsets.all(40.sp), margin: EdgeInsets.only(top: 160.sp),
        child: Column(children: [
          page.images.length == 1 ? Row(children: [
            DiagnosticPestsImageItem2Page(page.images[0]),
            SizedBox(width: 20.sp),
            Expanded(child: AddressAndDate(_address))
          ], crossAxisAlignment: CrossAxisAlignment.end) :
          SizedBox(height: 240.sp, child: ListView.separated(scrollDirection: Axis.horizontal,
              padding: EdgeInsets.only(bottom: 20.sp), itemCount: page.images.length,
              separatorBuilder: (_,__) => SizedBox(width: 40.sp),
              itemBuilder: (context, index) => DiagnosticPestsImageItem2Page(page.images[index]))),
          if (page.images.length != 1) AddressAndDate(_address),

          TitleRedRequire('Tên cây trồng', padding: padding),
          catalogues.isNotEmpty ? Row(children: [
            Expanded(child: UtilUI.createTextField(context, _ctrTreeName, _focusTreeName, _focusName, '',
                fillColor: const Color(0xFFF1F8FE), sizeBorder: 10.sp, borderColor: Colors.transparent)),
            SizedBox(width: 32.sp),
            ButtonImageWidget(16.sp, _selectTree, Container(padding: EdgeInsets.all(20.sp),
                child: Icon(Icons.arrow_drop_down, color: Colors.white, size: 84.sp)), color: const Color(0xFF1DA644))
          ]) : UtilUI.createTextField(context, _ctrTreeName, _focusTreeName, _focusName, '',
              fillColor: const Color(0xFFF1F8FE), sizeBorder: 10.sp, borderColor: Colors.transparent),

          TitleRedRequire('Vấn đề cây gặp phải', padding: padding),
          _diagnosis.isNotEmpty ? Row(children: [
            Expanded(child: UtilUI.createTextField(context, _ctrName, _focusName, _focusDescription, '',
                fillColor: const Color(0xFFF1F8FE), sizeBorder: 10.sp, borderColor: Colors.transparent)),
            SizedBox(width: 32.sp),
            ButtonImageWidget(16.sp, _selectStatus, Container(padding: EdgeInsets.all(20.sp),
                child: Icon(Icons.arrow_drop_down, color: Colors.white, size: 84.sp)), color: const Color(0xFF1DA644))
          ]) : UtilUI.createTextField(context, _ctrName, _focusName, _focusDescription, '',
              fillColor: const Color(0xFFF1F8FE), sizeBorder: 10.sp, borderColor: Colors.transparent),

          TitleRedRequire('Nội dung góp ý', padding: padding),
          UtilUI.createTextField(context, _ctrDescription, _focusDescription, null, '',
              padding: EdgeInsets.all(30.sp),
              inputAction: TextInputAction.newline, inputType: TextInputType.multiline, maxLines: 5,
              fillColor: const Color(0xFFF1F8FE), sizeBorder: 10.sp, borderColor: Colors.transparent),

          SizedBox(height: 40.sp),
          Center(child: ButtonImageWidget(50, _contribute, Padding(padding: EdgeInsets.all(40.sp),
              child: LabelCustom('Gửi đóng góp', size: 52.sp, weight: FontWeight.w600)), color: const Color(0xFF1DA644)))
        ], mainAxisSize: MainAxisSize.min, crossAxisAlignment: CrossAxisAlignment.start)),

      BlocBuilder<BaseBloc, BaseState>(bloc: bloc, builder: (context, state) =>
        state.isShowLoading ? Container(alignment: Alignment.center, child: const
          CircularProgressIndicator(valueColor: AlwaysStoppedAnimation<Color>(StyleCustom.primaryColor)),
            color: Colors.transparent, width: 1.sw, height: 0.6.sh) : const SizedBox())
    ]);
  }

  void _selectTree() => UtilUI.showOptionDialog(context, 'Chọn cây trồng', catalogues, '').then((value) {
    if (value != null) {
      _ctrTreeName.text = value.name;
      _ctrName.text = '';
    }
  });

  void _selectStatus() {
    final list = _diagnosis.where((ele) => ele.name.toLowerCase().contains(_ctrTreeName.text.toLowerCase())).toList();
    UtilUI.showOptionDialog(context, 'Chọn tình trạng bệnh', list.isEmpty ? _diagnosis : list, '').then((value) {
      if (value != null) _ctrName.text = value.id;
    });
  }

  void _setLocation(ItemModel value, bool isProvince) {
    if (isProvince) {
      if (_locationProvinces.id == value.id) return;
      _locationProvinces.id = value.id;
      _locationProvinces.name = value.name;
    } else {
      if (_locationDistrict.id == value.id) return;
      _locationDistrict.id = value.id;
      _locationDistrict.name = value.name;
    }
  }

  void _contribute() {
    if (_ctrTreeName.text.isEmpty) {
      UtilUI.showCustomDialog(context, 'Nhập tên cây trồng').then((value) => _focusTreeName.requestFocus());
      return;
    }
    if (_ctrName.text.isEmpty) {
      UtilUI.showCustomDialog(context, 'Nhập vấn đề cây gặp phải').then((value) => _focusName.requestFocus());
      return;
    }
    if (_ctrDescription.text.isEmpty) {
      UtilUI.showCustomDialog(context, 'Nhập nội dung góp ý').then((value) => _focusDescription.requestFocus());
      return;
    }

    final page = widget as DiagnosticPetsContribute2Page;
    bloc!.add(CreateDiagnostisPestEvent(_locationProvinces.id, _locationDistrict.id,
        _address, _ctrTreeName.text, _ctrName.text, _ctrDescription.text,
        page.images, lat: _lat, lng: _lng, rate: page.rate, trainIds: page.trainIds));
  }
}

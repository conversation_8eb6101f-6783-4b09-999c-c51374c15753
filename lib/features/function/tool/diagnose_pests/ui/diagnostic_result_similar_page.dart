import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:hainong/common/models/file_byte.dart';
import 'package:hainong/common/ui/button_image_widget.dart';
import 'diagnostic_rate.dart';
import 'diagnostic_result2_page.dart';
import 'diagnostic_result_base_page.dart';

class DiaResultSimilarPage extends BasePage {
  DiaResultSimilarPage(DiagnosticModel result, List<FileByte> images,
      List<PlantModel> catalogues, Position loc, {Key? key})
        : super(pageState: _DiaResult2PageState(result, images, catalogues, loc), key: key);
}
class _DiaResult2PageState extends DiaResultBasePageState {
  _DiaResult2PageState(result, images, catalogues, loc) : super(result, '', images, catalogues, loc);

  @override
  String getTitle() => 'Kết quả tương tự';

  @override
  Widget createUI() {
    return Column(children: [
      Container(decoration: BoxDecoration(color: const Color(0xFFE6FFEA), borderRadius: BorderRadius.circular(10)),
        child: Row(children: [
          Icon(Icons.info_outline, color: const Color(0xFF1DA644), size: 64.sp), SizedBox(width: 40.sp),
          Expanded(child: LabelCustom('Dữ liệu cây trồng đang được cập nhật, vui lòng kiểm tra cây trồng tương tự dưới đây',
            size: 40.sp, color: Colors.black, weight: FontWeight.normal))
        ]), padding: EdgeInsets.all(40.sp), margin: EdgeInsets.fromLTRB(40.sp, 40.sp, 40.sp, 0)
      ),

      Expanded(child: AlignedGridView.count(shrinkWrap: true, padding: EdgeInsets.all(40.sp), crossAxisCount: 2,
        mainAxisSpacing: 40.sp, crossAxisSpacing: 40.sp, itemCount: result.trees.length,
        itemBuilder: (context, index) {
          String image = result.trees[index]['image']??'';
          if (image.isEmpty && result.trees[index]['images'].isNotEmpty) image = result.trees[index]['images'][0]['name'];
          return ButtonImageWidget(0, () => _gotoTreeDetail(index), Column(children: [
            ClipRRect(borderRadius: BorderRadius.circular(10),
              child: ImageNetworkAsset(path: image, width: 0.5.sw, height: 0.25.sw)),
            SizedBox(height: 20.sp),
            LabelCustom(result.trees[index]['name'], color: Colors.black, size: 40.sp, weight: FontWeight.w500)
          ], crossAxisAlignment: CrossAxisAlignment.start, mainAxisSize: MainAxisSize.min));
        })),

      //if (hasRating == null) DiagnosticRate(contribute)
      DiagnosticRate(contribute)
    ]);
  }

  void _gotoTreeDetail(int index) => UtilUI.goToNextPage(context, DiaResult2Page(result, images, catalogues, loc, similar: index));
}
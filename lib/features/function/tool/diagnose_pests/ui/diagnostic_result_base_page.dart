import 'package:geolocator/geolocator.dart';
import 'package:hainong/common/models/file_byte.dart';
import 'package:hainong/common/ui/import_lib_ui.dart';
import 'package:hainong/common/ui/loading.dart';
import 'package:hainong/features/main/bloc/main_bloc.dart';
import 'diagnostic_pests_contribute2_page.dart';
import '../diagnose_pests_bloc.dart';
import '../model/diagnostic_model.dart';
import '../model/plant_model.dart';

export '../diagnose_pests_bloc.dart';
export '../model/diagnostic_model.dart';
export '../model/plant_model.dart';
export 'package:hainong/common/ui/import_lib_ui.dart';
export 'package:hainong/common/ui/image_network_asset.dart';
export 'package:hainong/common/ui/label_custom.dart';
export 'package:geolocator/geolocator.dart';

abstract class DiaResultBasePageState extends BasePageState {
  final List<PlantModel> catalogues;
  final DiagnosticModel result;
  final String shareContent;
  final List<FileByte> images;
  final Position loc;
  bool? hasRating;
  DiaResultBasePageState(this.result, this.shareContent, this.images, this.catalogues, this.loc, {String type = ''}) {
    bloc = DiagnosePestsBloc(const DiagnosePestsState(), type: type);
  }

  @override
  void initState() {
    super.initState();
    bloc!.stream.listen(listenBloc);
    bloc!.add(CountNotificationMainEvent());
  }

  @override
  Widget build(BuildContext context, {Color color = Colors.white}) => WillPopScope(
      onWillPop: () async {
        UtilUI.goBack(context, true);
        return false;
      },
      child: Scaffold(appBar: AppBar(titleSpacing: 0, centerTitle: true,
          title: UtilUI.createLabel(getTitle()),
          actions: [
            buttonShare(),
            IconButton(onPressed: _feedback, icon: Icon(Icons.feedback, color: Colors.white, size: 56.sp))
          ]), backgroundColor: color,
          body: SafeArea(child: Stack(children: [createUI(), Loading(bloc)]))));

  Widget buttonShare() => const SizedBox();

  String getTitle();

  void listenBloc(state) {
    if (state is CreatePostDiagnosePestsState && isResponseNotError(state.response)) {
      UtilUI.showCustomDialog(context, 'Kết quả chẩn đoán đã được đăng lên tường thành công', title: 'Thông báo');
    } else if (state is SendFeedbackState && isResponseNotError(state.resp, passString: true)) {
      UtilUI.showCustomDialog(context, 'Đã gửi phản hồi thành công', title: 'Thông báo');
    } else if (state is CountNotificationMainState) { // check for show error
      isResponseNotError(state.response, passString: true);
    }
  }

  void _feedback() => UtilUI.showConfirmDialog(context, '', 'Nhập nội dung phản hồi',
    'Nội dung không được để trống', padding: EdgeInsets.all(30.sp),
    title: 'Phản hồi kết quả', showMsg: false, line: 0, inputType: TextInputType.multiline, action: TextInputAction.newline).then((value) {
      if (value != null && value is String && value.isNotEmpty) bloc!.add(SendFeedbackEvent(value, result.ids));
    });

  void contribute(int? rate, {bool hasBack = true}) {
    if (hasBack) UtilUI.goBack(context, false);
    showDialog(context: context, builder: (context) => AlertDialog(scrollable: true,
      shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(20))),
      contentPadding: EdgeInsets.zero, insetPadding: EdgeInsets.all(40.sp),
      content: DiagnosticPetsContribute2Page(catalogues, images, loc, rate, result.ids, _hideRating, tree: Util.getValueFromJson(result.tree, 'name', '')))
    );
  }

  void _hideRating() => setState(() => hasRating = true);
}
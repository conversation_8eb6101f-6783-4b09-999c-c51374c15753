import 'dart:io';
import 'dart:ui';
import 'package:flutter/services.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:hainong/common/ui/crop_image.dart';
import 'package:geolocator/geolocator.dart';
import 'package:hainong/common/models/file_byte.dart';
import 'package:hainong/common/models/item_list_model.dart';
import 'package:hainong/common/ui/ads.dart';
import 'package:hainong/common/ui/banner_2nong.dart';
import 'package:hainong/common/ui/button_image_widget.dart';
import 'package:hainong/common/ui/image_network_asset.dart';
import 'package:hainong/common/ui/label_custom.dart';
import 'package:hainong/common/ui/permission_image_page_state.dart';
import 'package:hainong/common/ui/title_helper.dart';
import 'package:hainong/common/ui/button_custom.dart';
import 'package:hainong/common/ui/loading_percent.dart';
import 'package:hainong/common/ui/import_lib_ui.dart';
import 'package:hainong/features/product/ui/image_item_page.dart';
import '../diagnose_pests_bloc.dart';
import '../model/plant_model.dart';
import 'diagnostic_note.dart';
import 'diagnostic_result2_page.dart';
import 'diagnostic_result_page.dart';
import 'diagnostic_result_similar_page.dart';
import 'diagnostic_pests_contribute_page.dart';
import 'diagnostic_history_page.dart';
import 'diagnostic_select_image.dart';

class DiagnosePestsPage extends BasePage {
  DiagnosePestsPage({Key? key}) : super(key: key, pageState: _DiagnosePestsPageState());
}

class _DiagnosePestsPageState extends PermissionImagePageState {
  final List<ItemModel> _imageTypes = [];
  final List<PlantModel> _catalogues = [PlantModel()];
  ScrollController? _scroller;
  int _indexCatalogue = 0, _indexPest = -1, _indexResult = -1;
  String _guidCamera = '';
  bool _popupCamera = true, _isOpenCam = false;
  List? _results;
  Position? _loc;

  _DiagnosePestsPageState() {
    bloc = DiagnosePestsBloc(const DiagnosePestsState(), type: 'diagnostic');
  }

  @override
  void dispose() {
    if (DiagnosticSelectImage.bytesOrImage != null) DiagnosticSelectImage.bytesOrImage = null;
    if (Constants().isLogin) Constants().indexPage = null;
    _imageTypes.clear();
    _catalogues.clear();
    _results?.clear();
    //_ctrDes.dispose();
    _scroller?.dispose();
    super.dispose();
  }

  @override
  loadFiles(List<File> files) async {
    continueLoading = true;
    showLoadingPermission();
    bool hasAdd = false;
    dynamic temp, name, size, error = '';
    for (int i = 0; i < files.length && images!.length < 5; i++) {
        temp = files[i].readAsBytesSync();
        //name = files[i].path;
        name = files[i].path.split('/');
        if (name != null && name.isNotEmpty) name = name[name.length - 1];
        if (Util.isImage(files[i].path)) {
          size = await _checkSize(temp, name: name);
          if (size is Map) {
            images!.add(FileByte(size['bytes']??temp, files[i].path));
            images![images!.length - 1].w = size['w'].toDouble();
            images![images!.length - 1].h = size['h'].toDouble();
            _detectImage(images!.length - 1);
            hasAdd = true;
          } else error += '$name $size\n';
        }
    }

    /// ------------- hide detecting image ---------------
    ///hasAdd ? bloc!.add(ShowImageDiagnosePestsEvent(isShowLoading: true)) : showLoadingPermission(value: false);
    hasAdd ? bloc!.add(ShowImageDiagnosePestsEvent()) : showLoadingPermission(value: false);
    if (error.isNotEmpty) UtilUI.showCustomDialog(context, 'Ảnh không hợp lệ\n${error}Kích thước ảnh nhỏ hơn tiêu chuẩn (448x448)', alignMessageText: TextAlign.left);
  }

  @override
  void showLoadingPermission({bool value = true}) => bloc!.add(LoadingEvent(value));

  @override
  void initState() {
    maxSelect = 5;
    multiSelect = true;
    setOnlyImage();
    images = [];
    super.initState();
    _initImageTypes();
    bloc!.stream.listen((state) async {
      if (state is UploadFileDiagnosePestsState && isResponseNotError(state.response)) {
        _results = state.response.data.ids;
        String content = MultiLanguage.get('lbl_result');
        if ((state.response.data.summaries.isNotEmpty || state.response.data.nuti_summaries.isNotEmpty) && state.response.data.tree.isNotEmpty) {
          bool hasCreate = false;
          state.response.data.summaries.forEach((ele) {
            final temp = ele.suggest.toLowerCase();
            final bool hasNotPercent = ele.suggest_en.toLowerCase().contains('unknow') ||
                temp.contains('không nhận ra bệnh') || temp.contains('không xác định');
            if (hasNotPercent) {
              content += '</br>  - ${ele.suggest}';
            } else {
              hasCreate = true;
              content += '</br>  - <a href="${ele.suggest}@${ele.id}">${ele.suggest}</a> (${ele.percent}%)';
            }
          });
          Map<String, dynamic> tree = state.response.data.tree;
          _indexResult = -1;
          if (Util.checkKeyFromJson(tree, 'id')) {
            for(int i = _catalogues.length - 1; i > -1; i--) {
              if (_catalogues[i].id == (tree['id']??-1).toString()) {
                _indexResult = i;
                break;
              }
            }
          }
          UtilUI.goToNextPage(context, DiaResultPage(state.response.data, hasCreate ? content : '', images!, _catalogues, _loc!), funCallback: (value) => _clearImages());
        } else if (state.response.data.tree.isNotEmpty) {
          UtilUI.goToNextPage(context, DiaResult2Page(state.response.data, images!, _catalogues, _loc!), funCallback: (value) => _clearImages());
        } else if (state.response.data.trees != null && state.response.data.trees.isNotEmpty) {
          UtilUI.goToNextPage(context, DiaResultSimilarPage(state.response.data, images!, _catalogues, _loc!), funCallback: (value) => _clearImages());
        } else {
          UtilUI.showCustomDialog(context, 'Chưa hỗ trợ nhận dạng bệnh trên loại cây này. Bạn có thể thử lại bằng tính năng nhận dạng sâu bệnh nâng cao trên cây Lúa, Cà phê, Hồ tiêu, Sầu riêng');
        }
      } else if (state is CreatePostDiagnosePestsState) {
        _handleResponse(state.response, _handleCreatePost);
      } else if (state is LoadCatalogueState) {
        if (_catalogues.length > 1) _catalogues.removeRange(1, _catalogues.length);
        _catalogues.addAll(state.list as List<PlantModel>);
      } else if (state is LoadOptionState) {
        _guidCamera = state.value;
        _popupCamera = state.popup;
      } else if (state is DetectState && state.stopDetect && _isOpenCam) {
        if (images!.last.detections != null) {
          bool? value = await UtilUI.showCustomDialog(context, 'Hình ảnh không phù hợp để nhận dạng sâu bệnh. Bạn cần chụp lại cận vết bệnh để có kết quả chính xác nhất.'
              '\nBạn vẫn muốn giữ nguyên hay chụp lại ảnh phù hợp hơn?', isActionCancel: true, lblCancel: 'TIẾP TỤC', lblOK: 'CHỤP LẠI');
          if (value == null || value == true) {
            images!.removeLast();
            checkPermissions(_imageTypes[0]);
            bloc!.add(ShowImageDiagnosePestsEvent());
            return;
          }
          _editImage(state.index);
        } else _editImage(state.index);
      }
    });
    bloc!.add(LoadCatalogueEvent());
    bloc!.add(LoadOptionEvent());
  }

  @override
  Widget build(BuildContext context, {Color color = Colors.white}) => Stack(children: [
    Scaffold(backgroundColor: color, appBar: AppBar(elevation: 0, centerTitle: true,
      title: const Padding(padding: EdgeInsets.only(right: 48), child:
        TitleHelper('Chẩn đoán cây trồng', url: 'https://help.hainong.vn/muc/7'),
      /*actions: [
        SizedBox(width: 80, child: IconButton(onPressed: () {
          UtilUI.showOptionDialog(context, 'Chọn model', [
            ItemModel(id: 'yolo8m', name: 'yolo8m'),
            ItemModel(id: 'yolo11s', name: 'yolo11s'),
            ItemModel(id: 'yolo11m', name: 'yolo11m'),
          ], _model).then((value) {
            if (value != null && value.id != _model) {
              _model = value.id;
              images!.clear();
              rootBundle.load('assets/models/$_model.tflite').then((value) {
                _yoloModel = value.buffer.asUint8List();
                setState(() {});
              });
            }
          });
        }, icon: LabelCustom(_model)))
      ]*/
    )),
      body: Column(children: [
        Expanded(child: ListView(padding: EdgeInsets.zero, children: [
          const Ads('pest'),

          SizedBox(height: 40.sp),
          BlocBuilder(bloc: bloc,
              buildWhen: (_,n) => n is ChangeCatalogueState || n is LoadCatalogueState,
              builder: (_,__) {
                final List<Widget> list = [
                  ButtonImageWidget(10, () => _changeCatalogue(0), Column(children: [
                    Padding(padding: EdgeInsets.symmetric(vertical: 20.sp), child: ImageNetworkAsset(width: 0.18.sw - 40.sp, height: 0.18.sw - 40.sp,
                        asset: 'assets/images/v7/ic_ai.png', color: Color(0 == _indexCatalogue ? 0xFF1AAD80 : 0xFFACACAC),
                        fit: BoxFit.scaleDown, opacity: 0 == _indexCatalogue ? 1.0 : 0.5)),
                    SizedBox(height: 20.sp),
                    LabelCustom('Tự động', size: 42.sp, color: Color(0 == _indexCatalogue ? 0xFF1AAD80 : 0xFFACACAC), weight: FontWeight.normal),
                    if (0 == _indexCatalogue) Container(decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        border: Border.all(color: const Color(0xFF1AAD80), width: 4.sp)),
                        width: 1.sw/12 , alignment: Alignment.center, margin: const EdgeInsets.only(top: 2))
                  ]))
                ];

                for(int i = 1; i < _catalogues.length; i++) {
                  list.add(SizedBox(width: 40.sp));
                  list.add(ButtonImageWidget(10, () => _changeCatalogue(i), Column(children: [
                    ImageNetworkAsset(path: _catalogues[i].icon, width: 0.18.sw, height: 0.18.sw,
                        fit: BoxFit.scaleDown, opacity: i == _indexCatalogue ? 1.0 : 0.5),
                    SizedBox(height: 20.sp),
                    LabelCustom(_catalogues[i].name, size: 42.sp, color: Color(i == _indexCatalogue ? 0xFF1AAD80 : 0xFFACACAC), weight: FontWeight.normal),
                    if (i == _indexCatalogue) Container(decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        border: Border.all(color: const Color(0xFF1AAD80), width: 4.sp)),
                        width: 1.sw/12 , alignment: Alignment.center, margin: const EdgeInsets.only(top: 2))
                  ])));
                }

                if (_catalogues.length < 4) return Center(child: Row(children: list, mainAxisSize: MainAxisSize.min));

                _scroller ??= ScrollController();
                return Row(children: [
                  ButtonImageWidget(25, () => _scrollNextPre(isNext: false),
                      Icon(Icons.arrow_left, size: 86.sp, color: const Color(0xFF1AAD80))),
                  Expanded(child: SingleChildScrollView(scrollDirection: Axis.horizontal,
                      controller: _scroller, child: Row(children: list))),
                  ButtonImageWidget(25, _scrollNextPre, Icon(Icons.arrow_right, size: 86.sp, color: const Color(0xFF1AAD80))),
                ]);
              }),

          BlocBuilder(bloc: bloc, buildWhen: (_,n) => n is ShowImageDiagnosePestsState,
            builder: (_,__) => images!.isNotEmpty ? Container(padding: EdgeInsets.symmetric(horizontal: 40.sp),
                height: 0.26.sh, child: imageUI(_editImage, _deleteImage, padding: 0)) : const SizedBox()),

          Stack(children: [
            BlocBuilder(bloc: bloc, buildWhen: (_,n) => n is ShowImageDiagnosePestsState || n is ChangeCatalogueState,
                builder: (_,__) => _addMorePhoto(isAddMore: images!.isEmpty)),
            ButtonImageWidget(100, _checkCountImages,
              Padding(padding: EdgeInsets.symmetric(horizontal: 40.sp, vertical: 16.sp), child: Row(children: [
                LabelCustom('Chụp ảnh', size: 48.sp, weight: FontWeight.w400),
                const SizedBox(width: 5),
                Image.asset('assets/images/ic_camera.png', width: 48.sp, height: 48.sp, color: Colors.white)
              ], mainAxisSize: MainAxisSize.min)), color: StyleCustom.primaryColor)
          ], alignment: Alignment.bottomCenter),

          DiagnosticNote('<ul><li>Để kết quả chẩn đoán sâu bệnh chính xác, vui lòng chọn đúng loại cây cần chẩn đoán.</li>'
            '<li>Hiện tại AI chỉ chẩn đoán bệnh trên cây Lúa, Cà phê, Hồ tiêu, Sầu riêng, các loại cây khác đang trong quá trình phát triển.</li></ul>',
            title: ' Lưu ý:', icon: Icons.info, size: 36.sp)
        ])),

        Divider(height: 16.sp, color: const Color(0xFFF5F5F5), thickness: 16.sp),

        Padding(padding: EdgeInsets.all(40.sp), child: IntrinsicHeight(child: Row(children: [
          Expanded(child: ButtonImageWidget(16.sp, _history,
            Container(padding: EdgeInsets.all(32.sp), child: Column(children: [
              Icon(Icons.history_sharp, size: 44.sp, color: const Color(0xFF2ECF4D)),
              SizedBox(height: 24.sp),
              LabelCustom('Lịch sử chẩn đoán', size: 42.sp, color: const Color(0xFF4D4D4D), align: TextAlign.center)
            ]),
          ), color: const Color(0xFFF3FCF9))),
          SizedBox(width: 40.sp),
          Expanded(child: ButtonImageWidget(16.sp, _contributePets, Container(padding: EdgeInsets.all(32.sp),
            child: Column(children: [
              Image.asset('assets/images/v5/ic_contribute.png', width: 44.sp, height: 44.sp, fit: BoxFit.scaleDown),
              SizedBox(height: 24.sp),
              LabelCustom('Đóng góp dữ liệu', size: 42.sp, color: const Color(0xFF4D4D4D), align: TextAlign.center)
            ]),
          ), color: const Color(0xFFF3FCF9)))
        ]))),

        BlocBuilder(bloc: bloc, buildWhen: (_,n) => n is ShowImageDiagnosePestsState || n is DetectState,
          builder: (_,s) {
            if (images!.isEmpty || (bloc as DiagnosePestsBloc).countDetecting != null) return SizedBox(height: 20.sp);
            return Column(children: [
              Container(width: 1.sw, padding: EdgeInsets.symmetric(horizontal: 40.sp),
                  child: ButtonCustom(_diagnostic, MultiLanguage.get('btn_diagnose_pests'),
                      padding: EdgeInsets.all(40.sp), size: 48.sp,
                      color: const Color(0xFF0E986F), radius: 16.sp, elevation: 0)),
              Container(margin: EdgeInsets.fromLTRB(40.sp, 20.sp, 40.sp, 40.sp),
                  padding: EdgeInsets.all(20.sp),
                  decoration: BoxDecoration(color: const Color(0xFFF3FCF9), borderRadius: BorderRadius.circular(8.sp)),
                  child: Text(MultiLanguage.get('msg_ai'),
                      style: TextStyle(color: const Color(0xFFA08805), fontSize: 36.sp), textAlign: TextAlign.center))
            ]);
          })
      ]),
      /*floatingActionButton: FloatingActionButton.large(onPressed: () {
        setState(() {
          final ctr = bloc as DiagnosePestsBloc;
          ctr.isAPI = ctr.isAPI == null ? true : null;
          for(int i = images!.length - 1; i > -1; i--) {
            _detectImage(i);
          }
        });
      }, child: Text((bloc as DiagnosePestsBloc).isAPI == null ? 'Detect\nLocal' : 'Detect\nAPI', textAlign: TextAlign.center))*/
    ),
    const Banner2Nong('pest'),
    BlocBuilder(bloc: bloc, buildWhen: (_,n) => n is DetectState || n is ShowPercentState || n is BaseState,
      builder: (_,state) {
        if ((bloc as DiagnosePestsBloc).countDetecting != null) {
          return const SizedBox();
          /*return Scaffold(backgroundColor: Colors.black26, body: Center(child: Row(children: [
            const CircularProgressIndicator(valueColor: AlwaysStoppedAnimation<Color>(StyleCustom.primaryColor)),
            Text('  Đang kiểm tra ảnh ...', textAlign: TextAlign.center, style: TextStyle(color: Colors.white, fontSize: 48.sp))
          ], mainAxisAlignment: MainAxisAlignment.center, crossAxisAlignment: CrossAxisAlignment.center)));*/
        }
        if (state is ShowPercentState && state.value) return LoadingPercent(images!.length * 5);
        if (state is BaseState && state.isShowLoading) {
          return Container(alignment: Alignment.center, child: const
            CircularProgressIndicator(valueColor: AlwaysStoppedAnimation<Color>(StyleCustom.primaryColor)),
              color: Colors.black12, width: 1.sw, height: 1.sh);
        }
        return const SizedBox();
      })
  ], alignment: Alignment.bottomRight);

  @override
  Widget imageUI(Function funAdd, Function funDelete, {double? padding}) => images!.length > 1 ?
    ListView.separated(padding: EdgeInsets.only(top: 40.sp), itemCount: images!.length, scrollDirection: Axis.horizontal,
      physics: const BouncingScrollPhysics(), addRepaintBoundaries: false,
      separatorBuilder: (context, index) => SizedBox(width: 20.sp),
      itemBuilder: (context, index) => ImageItemPests(bloc as DiagnosePestsBloc, images![index], index, funAdd, funDelete)) :
    Container(padding: EdgeInsets.only(top: 40.sp), alignment: Alignment.center,
      child: ImageItemPests(bloc as DiagnosePestsBloc, images![0], 0, funAdd, funDelete));

  @override
  void openCameraGallery() {
    _isOpenCam = checkPermission == languageKey.lblCamera;
    _isOpenCam && _guidCamera.isNotEmpty && _popupCamera ? showDialog(barrierDismissible: false, context: context,
        builder: (context) => Dialog(shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.all(Radius.circular(30.sp))),
            child: Column(mainAxisSize: MainAxisSize.min, children: [
              Container(width: 1.sw, decoration: BoxDecoration(color: const Color(0xFFF2F2F2),
                  borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(30.sp), topRight: Radius.circular(30.sp))),
                  child: Padding(padding: EdgeInsets.all(40.sp), child: Stack(children: [
                    Align(alignment: Alignment.topRight, child: GestureDetector(
                        onTap: () => Navigator.of(context).pop(false),
                        child: const Icon(Icons.close, color: Color(0xFF626262)))),
                    Center(child: LabelCustom('Lưu ý', color: const Color(0xFF191919), size: 60.sp))
                  ]))),
              Flexible(child: SingleChildScrollView(child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 20.sp, vertical: 40.sp),
                  child: Html(data: _guidCamera, style: {
                    'html, body, p': Style(margin: Margins.zero, padding: HtmlPaddings.zero, fontSize: FontSize(42.sp),
                        color: Colors.black)},
                    extensions: [
                      TagExtension(
                        tagsToExtend: {"img"},
                        builder: (ext) {
                          final src = ext.attributes['src'] ?? '';
                          return Image.network(src, width: 1.sw, fit: BoxFit.fitWidth,
                              errorBuilder: (_,__,___) => Image.asset('assets/images/ic_default.png',
                                  width: 1.sw, fit: BoxFit.fitWidth)
                          );
                        },
                      ),
                    ],
                    //'img': Style(margin: EdgeInsets.zero, padding: EdgeInsets.zero, width: 1.sw, height: 0.5.sw),
                  )))),
              Padding(padding: EdgeInsets.all(20.sp),
                  child: BlocBuilder(bloc: bloc, buildWhen: (oldS, newS) => newS is CheckPopupCameraState,
                      builder: (context, state) => Row(children: [
                        ButtonImageWidget(0, _checkPopupCamera,
                            Icon(_popupCamera ? Icons.check_box_outline_blank : Icons.check_box, size: 48.sp, color: _popupCamera ? Colors.black54 : StyleCustom.primaryColor)),
                        LabelCustom(' Không hiện lưu ý vào lần sau', size: 42.sp, color: Colors.blue, weight: FontWeight.normal)
                      ]))),
              Row(crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center, children: [
                    ElevatedButton(style: ElevatedButton.styleFrom(backgroundColor: StyleCustom.buttonColor),
                        child: LabelCustom(MultiLanguage.get(languageKey.btnOK)), onPressed: _saveCheckPopupCamera)
                  ]),
              SizedBox(height: 40.sp)
            ]))).whenComplete(() => super.openCameraGallery()) : super.openCameraGallery();
  }

  Widget _addMorePhoto({bool isAddMore = true}) => ButtonImageWidget(8.sp,
      () => _checkCountImages(isCamera: false),
      Container(alignment: Alignment.center, height: isAddMore ? 0.4.sh : 0.2.sh, width: 1.sw,
        margin: EdgeInsets.all(40.sp),
        decoration: BoxDecoration(
          image: DecorationImage(fit: BoxFit.fill,
              image: Image.asset('assets/images/v2/ic_background_${isAddMore ? 'camera' : 'addmore'}.png').image),
        ),
        child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset('assets/images/v2/ic_add_photo.png',
                  width: isAddMore ? 200.sp : 80.sp, height: isAddMore ? 200.sp : 80.sp),
              SizedBox(height: isAddMore ? 70.sp : 10.sp),
              Padding(padding: EdgeInsets.symmetric(horizontal: 80.sp), child: Wrap(children: [
                LabelCustom('Chọn hình để nhận dạng và chẩn đoán ',
                    color: const Color(0xFF2C2C2C), size: 46.sp, weight: FontWeight.normal, align: TextAlign.center),
                LabelCustom(
                    (_indexCatalogue > 0 ? 'nâng cao cho cây ' : 'tự động'),
                    color: const Color(0xFF2C2C2C), size: 46.sp, weight: FontWeight.normal, align: TextAlign.center),
                LabelCustom(
                    (_indexCatalogue > -1 ? _catalogues[_indexCatalogue].name.toLowerCase() : ''),
                    color: Colors.orange, size: 46.sp, align: TextAlign.center),
              ], alignment: WrapAlignment.center)),
              SizedBox(height: isAddMore ? 40.sp : 20.sp),
              if (isAddMore) Padding(padding: EdgeInsets.symmetric(horizontal: 80.sp),
                  child: Text('Cần hình ảnh cận cảnh và sắc nét để tăng độ chính xác (Cách điểm cần nhận dạng 20-30cm)',
                  style: TextStyle(color: const Color(0xFF2C2C2C), fontSize: 40.sp), textAlign: TextAlign.center))
            ]),
      ));

  void _initImageTypes() {
    multiSelect = true;
    setOnlyImage();
    _imageTypes.add(ItemModel(
        id: languageKey.lblCamera,
        name: MultiLanguage.get(languageKey.lblCamera)));
    _imageTypes.add(ItemModel(
        id: languageKey.lblGallery,
        name: MultiLanguage.get(languageKey.lblGallery)));
  }

  void _checkCountImages({bool isCamera = true}) {
    if (images!.length == 5) {
      UtilUI.showCustomDialog(context, 'Không thể thêm ảnh được nữa.\nSố lượng ảnh chẩn đoán không được lớn hơn 5');
      return;
    }
    isCamera ? checkPermissions(_imageTypes[0]) : selectImage(_imageTypes);
  }

  void _deleteImage(int index) {
    if ((bloc as DiagnosePestsBloc).countDetecting != null) return;
    images!.removeAt(index);
    if (images!.isEmpty && _indexCatalogue > -1 &&
        _indexPest > -1) _catalogues[_indexCatalogue].diagnostics[_indexPest].selected = false;
    bloc!.add(ShowImageDiagnosePestsEvent());
  }

  int? _indexTemp;
  void _editImage(int index) async {
    /*DiagnosticSelectImage.bytesOrImage = Uint8List.fromList(images![index].bytes);
    UtilUI.goToNextPage(context, const DiagnosticSelectImage(), funCallback: (value) {
      if (value == true) {
        final bytes = IMG.encodePng(DiagnosticSelectImage.bytesOrImage);
        images![index].bytes = bytes.toList();
        bloc!.add(ShowImageDiagnosePestsEvent());
        DiagnosticSelectImage.bytesOrImage = null;
      }
    });
    return;*/

    _isOpenCam = false;
    var dir = Directory.systemTemp.createTempSync().path;
    //String name = images![index].name.split('/').last;
    //var temp = File("$dir/$name");
    //final file = File(images![index].name);
    //temp.writeAsBytesSync(file.readAsBytesSync());
    var temp = File("$dir/temp_photo.png");
    temp.writeAsBytesSync(images![index].bytes);
    _indexTemp = index;
    //UtilUI.goToNextPage(context, CropImagePage(temp, orgImage: images![index], isAPI: (bloc as DiagnosePestsBloc).isAPI), funCallback: _cropImageCallback);
    UtilUI.goToNextPage(context, CropImagePage(temp, orgImage: images![index], isAPI: true), funCallback: _cropImageCallback);

    /*var dir = Directory.systemTemp.createTempSync().path;
    var temp = File("$dir/temp_photo.png");
    temp.writeAsBytesSync(images![index].bytes);
    CroppedFile? croppedFile = await ImageCropper().cropImage(
      sourcePath: temp.path,
      aspectRatioPresets: [
        CropAspectRatioPreset.square,
        CropAspectRatioPreset.ratio3x2,
        CropAspectRatioPreset.original,
        CropAspectRatioPreset.ratio4x3,
        CropAspectRatioPreset.ratio16x9
      ],
      uiSettings: [
        AndroidUiSettings(
            toolbarTitle: 'Cắt ảnh',
            toolbarColor: StyleCustom.primaryColor,
            toolbarWidgetColor: Colors.white,
            initAspectRatio: CropAspectRatioPreset.original,
            lockAspectRatio: false),
        IOSUiSettings(title: 'Cắt ảnh')
      ],
    );

    temp.delete();
    if (croppedFile != null) {
      croppedFile.readAsBytes().then((value) async {
        if (await _checkSize(value)) return;
        images![index].bytes = value.toList();
        bloc!.add(ShowImageDiagnosePestsEvent());
      });
    }*/
  }

  void _cropImageCallback(temp) async {
    /*if (image == null) return;
    showLoadingPermission();
    final temp = (await image.toByteData(format: ui.ImageByteFormat.png))!.buffer.asUint8List();
    if (await _checkSize(temp)) {
      showLoadingPermission(value: false);
      return;
    }*/

    /*var dir = Directory.systemTemp.createTempSync().path;
    String name = images![_indexTemp!].name.split('/').last;
    var file = File("$dir/$name");
    file.writeAsBytesSync(temp, flush: true);
    images![_indexTemp!].name = name;*/

    images![_indexTemp!].bytes = temp;
    bloc!.add(ShowImageDiagnosePestsEvent());
    _detectImage(_indexTemp!);
    _indexTemp = null;
  }

  void _clearImages() {
    images!.clear();
    bloc!.add(ShowImageDiagnosePestsEvent());
    _changeCatalogue(0);
  }

  Future<dynamic> _checkSize(Uint8List bytes, {String? name}) async {
    var decodedImage = await decodeImageFromList(bytes);
    if (decodedImage.width < 448 || decodedImage.height < 448) {
      name = (name != null && name.isNotEmpty) ? ' ($name)' : '';
      return '(${decodedImage.width}x${decodedImage.height})';
    }
    if (_isOpenCam) {
      bytes = (await decodedImage.toByteData(format: ImageByteFormat.png))!.buffer.asUint8List();
      return {'w': decodedImage.width, 'h': decodedImage.height, 'bytes': bytes};
    }
    return {'w': decodedImage.width, 'h': decodedImage.height};
  }

  _handleResponse(base, Function funHandleDetail) {
    if (base.checkTimeout()) UtilUI.showDialogTimeout(context);
    else if (base.checkOK()) funHandleDetail(base);
  }

  _handleCreatePost(base) {
    UtilUI.showCustomDialog(context, MultiLanguage.get('msg_create_post_success'), title: MultiLanguage.get('ttl_alert'));
    //.then((value) => _showDialogRating());
  }

  void _changeCatalogue(int index) {
    if (_indexCatalogue == index) return;
    /*if (index > -1) {
      switch(_catalogues[index].name.toLowerCase()) {
        case 'sầu riêng':
        case 'cam':
        case 'chanh dây':
          UtilUI.showCustomDialog(context, "Tính năng AI trên cây ${_catalogues[index].name} đang được phát triển, cần đóng góp thêm dữ liệu", lblOK: "Tiếp tục");
      }
    }*/
    _indexCatalogue = index;
    if (!bloc!.isClosed) bloc!.add(ChangeCatalogueEvent());
    _changePest(-1);
  }

  void _changePest(int index) {
    int temp = _indexResult;
    if (_indexCatalogue > -1 && index > -1) temp = _indexCatalogue;
    if (temp > -1 && index > -1) {
      if (_indexPest == index && _indexPest > -1) {
        bool value = _catalogues[temp].diagnostics[_indexPest].selected;
        _catalogues[temp].diagnostics[_indexPest].selected = !value;
        if (value) index = -1;
      } else {
        if (_indexPest > -1) _catalogues[temp].diagnostics[_indexPest].selected = false;
        _catalogues[temp].diagnostics[index].selected = true;
      }
      Util.trackActivities('pest_diagnosis', path: 'Diagnose Pests Screen -> Check for ${_catalogues[temp].diagnostics[index].name}');
    }
    _indexPest = index;
    if (!bloc!.isClosed) bloc!.add(ChangePestEvent());
  }

  void _diagnostic({bool pass = false}) {
    if (!pass) {
      for (var item in images!) {
        if (item.detections != null) {
          UtilUI.showCustomDialog(context, 'Có một vài ảnh có nhiều đối tượng không chuẩn xác có thể ảnh hưởng đến quá trình chẩn đoán.\n'
              'Bạn có muốn tiếp tục không?',
            alignMessageText: TextAlign.left, isActionCancel: true).then((value) {
              if (value == true) _diagnostic(pass: true);
          });
          return;
        }
      }
    }

    if (constants.isLogin) {
      _results?.clear();
      bloc!.add(ShowPercentEvent(true));
      _indexPest = -1;
      _determinePosition().then((value) {
        _loc = value;
        bloc!.add(UploadFileDiagnosePestsEvent(
            images!,
            _indexCatalogue < 0 ? '' : _catalogues[_indexCatalogue].id,
            value.latitude.toString(),
            value.longitude.toString()));
      }).catchError((e) {
        bloc!.add(ShowPercentEvent(false));
        UtilUI.showCustomDialog(context, e);
      });
      Util.trackActivities('pest_diagnosis', path: 'Diagnose Pests Screen -> Touch Diagnose Button');
    } else UtilUI.showDialogTimeout(context, message: languageKey.msgLoginOrCreate);
  }

  void _history() => constants.isLogin ? UtilUI.goToNextPage(context, DiagnosisHistoryPage()) :
      UtilUI.showDialogTimeout(context, message: languageKey.msgLoginOrCreate);

  void _contributePets() async {
    if (await UtilUI().alertVerifyPhone(context)) return;
    constants.isLogin ? UtilUI.goToNextPage(context, DiagnosticPetsContributePage(_catalogues))
        : UtilUI.showDialogTimeout(context, message: languageKey.msgLoginOrCreate);
  }

  Future<Position> _determinePosition() async {
    bool serviceEnabled;
    LocationPermission permission;

    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) return Future.error(MultiLanguage.get('msg_gps_disable'));

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.deniedForever) return Future.error(MultiLanguage.get('msg_gps_deny_forever'));

    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission != LocationPermission.whileInUse &&
          permission != LocationPermission.always) return Future.error(MultiLanguage.get('msg_gps_denied'));
    }

    return await Geolocator.getCurrentPosition();
  }

  /*void _showDialogRating() => _createDialogRating().then((value) {
    if (value!) {
      int index = _indexResult;
      if (_indexCatalogue > -1) index = _indexCatalogue;
      bloc!.add(PostRatingEvent(_point, _ctrDes.text, _results, _indexPest > -1 ? _catalogues[index].diagnostics[_indexPest].id : ''));
    }
  }).whenComplete(_clearImages);

  Future<bool?> _createDialogRating() {
    images?.clear();
    _point = 0;
    _ctrDes.text = '';
    bloc!.add(ShowImageDiagnosePestsEvent());

    List<Widget> actions = [];
    actions.add(_addAction(MultiLanguage.get(languageKey.btnCancel), false,
        color: Colors.grey));
    actions.add(_addAction(MultiLanguage.get(languageKey.btnOK), true));

    return showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
            title: Text(MultiLanguage.get('ttl_rating')),
            actions: actions,
            content: SingleChildScrollView(
                child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                  LabelCustom(MultiLanguage.get('msg_rating'), size: 40.sp, color: Colors.black, weight: FontWeight.normal),
                  Padding(padding: EdgeInsets.only(top: 20.sp, bottom: 20.sp),
                      child: BlocBuilder(bloc: bloc, buildWhen: (state1, state2) => state2 is ChangePointState,
                          builder: (context, state) => StarRate(
                              rate: _point,
                              size: 80.sp,
                              color: StyleCustom.buttonColor,
                              onClick: _clickStart,
                              hasFunction: true))),
                  TextFieldCustom(_ctrDes, null, null, MultiLanguage.get('lbl_des'), maxLine: 2,
                      padding: EdgeInsets.all(20.sp), type: TextInputType.multiline, inputAction: TextInputAction.newline),
                  BlocBuilder(bloc: bloc, buildWhen: (state1, state2) => state2 is ChangePestState,
                      builder: (context, state) {
                        int index = _indexResult;
                        if (_indexCatalogue > -1) index = _indexCatalogue;
                        if (index > -1) {
                          final List<Widget> list = [Padding(padding: EdgeInsets.only(top: 40.sp, bottom: 20.sp),
                              child: LabelCustom(MultiLanguage.get('lbl_guess_pest'),
                                  size: 40.sp, color: Colors.black, weight: FontWeight.normal))];
                          final pests = _catalogues[index].diagnostics;
                          for (int i = 0; i < pests.length - 1; i += 2) {
                            list.add(Padding(padding: EdgeInsets.only(bottom: 32.sp),
                                child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Expanded(
                                          child: CheckboxCustom(
                                              i,
                                              pests[i],
                                              _changePest,
                                              pests[i].selected &&
                                                  i == _indexPest)),
                                      SizedBox(width: 10.sp),
                                      Expanded(
                                          child: CheckboxCustom(
                                              i + 1,
                                              pests[i + 1],
                                              _changePest,
                                              pests[i + 1].selected &&
                                                  i + 1 == _indexPest))
                                    ])));}
                          return Column(children: list, crossAxisAlignment: CrossAxisAlignment.start);
                        }
                        return const SizedBox();
                      })
                ]))));
  }

  Widget _addAction(buttonName, value, {color = StyleCustom.buttonColor, hasPoint = true}) =>
      ElevatedButton(child: Text(buttonName),
          style: ElevatedButton.styleFrom(
              side: const BorderSide(color: Colors.transparent),
              primary: color, elevation: 4,
              textStyle: const TextStyle(color: Colors.white)),
          onPressed: () {
            /*if (value) {
              if (_point == 0 && hasPoint) UtilUI.showCustomDialog(context, MultiLanguage.get('msg_point'));
              else Navigator.of(context).pop(value);
            } else*/
            Navigator.of(context).pop(value);
          });

  void _clickStart(int index) {
    _point = index;
    bloc!.add(ChangePointEvent(index));
    Util.trackActivities('pest_diagnosis', path: 'Diagnose Pests Screen -> Vote $index start for result');
  }*/

  /*void _showDialogDiagnostic(List<Diagnostic> predicts, String content, bool createPost) =>
      _createDialogDiagnostic(predicts, content, createPost).then((value) {
        if (value != null && value && createPost) {
          bloc!.add(CreatePostDiagnosePestsEvent(images!, content));
          Util.trackActivities('pest_diagnosis', path: 'Diagnose Pests Screen -> Create post from diagnose result');
          return;
        }
        //_showDialogRating();
      });*/

  /*Future<bool?> _createDialogDiagnostic(
      List<Diagnostic> predicts, String content, bool createPost) {
    List<Widget> actions = [];
    if (createPost) actions.add(_addAction(MultiLanguage.get(languageKey.btnCancel), false, color: Colors.grey));
    actions.add(_addAction(
        createPost ? MultiLanguage.get(languageKey.btnOK) : 'Đóng', true,
        hasPoint: false));
    return showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
            title: Text(MultiLanguage.get('ttl_diagnose_result')),
            actions: actions,
            content: SingleChildScrollView(
                child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                  Html(
                      data: content,
                      style: {"body": Style(fontSize: FontSize(42.sp))},
                      onLinkTap: (url, render, map, ele) => _launchUrl(url!)),
                  OutlinedButton(
                      style: OutlinedButton.styleFrom(
                          side: const BorderSide(
                        color: Colors.transparent,
                      )),
                      onPressed: () {
                        UtilUI.goToNextPage(
                          context,
                          DiagnosisPestDetailPage(predicts),
                        );
                        Util.trackActivities('pest_diagnosis', path: 'Diagnose Pests Screen -> Show detail diagnose result');
                      },
                      child: Row(children: [
                        Expanded(
                            child: LabelCustom(
                                MultiLanguage.get('msg_detail_diagnostic'),
                                size: 36.sp,
                                color: Colors.blue,
                                weight: FontWeight.normal)),
                        Icon(Icons.remove_red_eye_outlined, size: 48.sp)
                      ])),
                  if (createPost)
                    UtilUI.createLabel(
                        MultiLanguage.get('msg_create_post_question'),
                        fontSize: 40.sp,
                        color: Colors.black,
                        fontWeight: FontWeight.normal,
                        line: 2)
                ]))));
  }*/

  /*void _launchUrl(String url) {
    UtilUI.goToNextPage(context, PestsHandbookListPage(url));
    Util.trackActivities('pest_diagnosis', path: 'Diagnose Pests Screen -> Open Pests Hand book List Page');
  }*/

  void _checkPopupCamera() {
    _popupCamera = !_popupCamera;
    bloc!.add(CheckPopupCameraEvent());
  }

  void _saveCheckPopupCamera() {
    Navigator.of(context).pop(true);
    bloc!.add(CheckPopupCameraEvent(value: _popupCamera, isSave: true));
  }

  void _scrollNextPre({bool isNext = true}) {
    if (isNext && _scroller!.position.pixels == _scroller!.position.maxScrollExtent) return;
    if (!isNext && _scroller!.position.pixels == 0) return;
    _scroller!.jumpTo(_scroller!.position.pixels + (isNext ? 0.32.sw : -0.32.sw));
  }

  void _detectImage(int index) => bloc!.add(DetectEvent(index, images![index]));
}

import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:hainong/common/models/file_byte.dart';
import 'package:hainong/common/ui/shadow_decoration.dart';
import 'diagnostic_rate.dart';
import 'diagnostic_result_base_page.dart';

class DiaResult2Page extends BasePage {
  final int? similar;
  DiaResult2Page(DiagnosticModel result, List<FileByte> images,
      List<PlantModel> catalogues, Position loc, {Key? key, this.similar})
        : super(pageState: _DiaResult2PageState(result, images, catalogues, loc), key: key);
}
class _DiaResult2PageState extends DiaResultBasePageState {
  _DiaResult2PageState(result, images, catalogues, loc) : super(result, '', images, catalogues, loc);

  @override
  String getTitle() => (widget as DiaResult2Page).similar != null ? '<PERSON><PERSON><PERSON> quả tương tự' : '<PERSON>ế<PERSON> quả chẩn đo<PERSON>';

  @override
  Widget createUI() {
    dynamic tree;
    if (result.tree.isNotEmpty) {
      tree = _getTree(result.tree);
    } else if (result.trees != null && result.trees.isNotEmpty) {
      tree = _getTree(result.trees[(widget as DiaResult2Page).similar]);
    }
    if (tree == null) return const SizedBox();
    return Column(children: [
      Padding(padding: EdgeInsets.all(40.sp), child: Row(children: [
        LabelCustom('Nhận dạng cây: ', color: const Color(0xFF1AAD80), size: 48.sp),
        LabelCustom(tree[0], size: 48.sp, color: const Color(0xFF292929))
      ])),

      Padding(child: ClipRRect(borderRadius: BorderRadius.circular(10), child:
        tree[2].length == 1 ? ImageNetworkAsset(path: tree[2][0], height: 0.2.sh + 80.sp, width: 1.sw) :
        CarouselSlider.builder(itemCount: tree[2].length,
          options: CarouselOptions(viewportFraction: 1, autoPlay: tree[2].length > 1),
          itemBuilder: (context, index, realIndex) => ImageNetworkAsset(path: tree[2][index], height: 0.2.sh, width: 1.sw)
        )), padding: EdgeInsets.symmetric(horizontal: 40.sp)),

      Expanded(child: Container(margin: EdgeInsets.all(40.sp), padding: EdgeInsets.all(40.sp), decoration: ShadowDecoration(size: 10),
        child: SingleChildScrollView(child: Html(data: tree[0].isNotEmpty ? ('<b>' + tree[0] + ':</b> ' + tree[1]) : tree[1],
          style: {'html, body, p, div': Style(margin: Margins.zero, padding: HtmlPaddings.zero,
            fontSize: FontSize(42.sp), color: const Color(0xFF292929))},
          onLinkTap: (link,_,__) {
            if (link != null) launchUrl(Uri.parse(link), mode: LaunchMode.externalApplication);
          },
          onAnchorTap: (link,_,__) {
            if (link != null) launchUrl(Uri.parse(link), mode: LaunchMode.externalApplication);
          }
          )))),

      //if (hasRating == null) DiagnosticRate(contribute)
      DiagnosticRate(contribute)
    ]);
  }

  dynamic _getTree(Map<String, dynamic> tree) {
    String name = '', desLong = '';
    List<String> images = [];
    if (Util.checkKeyFromJson(tree, 'images')) {
      tree['images'].forEach((ele) => images.add(ele['name']));
    }
    if (images.isEmpty) {
      String temp = tree['image'] ?? '';
      if (temp.contains('no_image.png')) temp = '';
      images.add(temp);
    }
    name = tree['name']??'';
    desLong = tree['description']??'';
    return [name, desLong, images];
  }
}
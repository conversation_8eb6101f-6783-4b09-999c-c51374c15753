import 'dart:typed_data';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:hainong/common/models/file_byte.dart';
import 'package:hainong/common/ui/avatar_circle_widget.dart';
import 'package:hainong/common/ui/box_dec_custom.dart';
import 'package:hainong/common/ui/button_image_widget.dart';
import 'package:hainong/common/ui/shadow_decoration.dart';
import 'package:hainong/features/function/support/pests_handbook/ui/pests_handbook_list_page.dart';
import 'package:hainong/features/main/bloc/main_bloc.dart';
import 'diagnostic_compare_page.dart';
import 'diagnostic_result_base_page.dart';
import 'diagnostic_result_ext_nutrition_page.dart';
import 'diagnostic_result_ext_page.dart';

class DiaResultPage extends BasePage {
  DiaResultPage(DiagnosticModel result, String shareContent, List<FileByte> images, List<PlantModel> catalogues,
    Position loc, {Key? key}) : super(pageState: _DiaResultPageState(result, shareContent, images, catalogues, loc), key: key);
}
class _DiaResultPageState extends DiaResultBasePageState {
  String _address = '';
  int? _indexImg;
  _DiaResultPageState(result, shareContent, images, catalogues, loc) : super(result, shareContent, images, catalogues, loc, type: 'result_ext');

  @override
  String getTitle() => 'Kết quả chẩn đoán';

  @override
  void listenBloc(state) {
    if (state is GetLocationState) {
      final json = state.response.data;
      if (Util.checkKeyFromJson(json, 'address_full')) _address = json['address_full'];
    } else super.listenBloc(state);
  }

  @override
  void initState() {
    super.initState();
    bloc!.add(GetLocationEvent(loc.latitude.toString(), loc.longitude.toString()));
  }

  @override
  Widget createUI() {
    String name = '';
    //List<String> images = [];
    if (result.tree.isNotEmpty) {
      /*if (Util.checkKeyFromJson(result.tree, 'images')) {
        result.tree['images'].forEach((ele) => images.add(ele['name']));
      }
      if (images.isEmpty) {
        String temp = result.tree['image'] ?? '';
        if (temp.contains('no_image.png')) temp = '';
        images.add(temp);
      }*/
      name = '  ' + (result.tree['name']??'');
    }
    return ListView(padding: EdgeInsets.symmetric(vertical: 40.sp), children: [
      Padding(padding: EdgeInsets.symmetric(horizontal: 40.sp),
        child: Row(mainAxisAlignment: MainAxisAlignment.end, children: [
          ImageNetworkAsset(path: result.tree['icon'] ?? '', width: 120.sp, height: 120.sp),
          LabelCustom(name, size: 48.sp, color: const Color(0xFF1865B1))
        ])),

      Padding(padding: EdgeInsets.fromLTRB(40.sp, 40.sp, 40.sp, images.length > 1 ? 0 : 40.sp),
        child: Stack(alignment: Alignment.topRight, children: [
          ClipRRect(borderRadius: BorderRadius.circular(20),
            child: images.length == 1 ? Image.memory(Uint8List.fromList(images[0].bytes), fit: BoxFit.cover, height: 0.24.sh, width: 1.sw - 80.sp) :
              CarouselSlider.builder(itemCount: images.length,
                options: CarouselOptions(viewportFraction: 1,
                    onPageChanged: (index,_) {
                      _indexImg = index;
                      bloc!.add(ChangePestEvent());
                    }),
                  itemBuilder: (_,i,___) => Image.memory(Uint8List.fromList(images[i].bytes), fit: BoxFit.cover, height: 0.24.sh, width: 1.sw - 80.sp))),
          Container(padding: EdgeInsets.all(40.sp), child: ButtonImageWidget(10, () {
            UtilUI.goToNextPage(context, DiagnosticComparePage(_address, shareContent, result.images, images, result.ids));
          }, Container(padding: EdgeInsets.all(20.sp), child: Row(mainAxisSize: MainAxisSize.min, children: [
              Image.asset('assets/images/compare.png', width: 60.sp, color: Colors.white),
              LabelCustom('  So sánh', align: TextAlign.center, size: 40.sp)
          ])), color: const Color(0xFF56A554)))
        ])),
      if (images.length > 1) Padding(padding: EdgeInsets.only(bottom: 40.sp), child: BlocBuilder(bloc: bloc,
        buildWhen: (o,n) => n is ChangePestState, builder: (_,__) => SlidePoints(images, _indexImg??0))),

      Padding(padding: EdgeInsets.only(left: 40.sp, bottom: 40.sp), child: Row(children: [
        LabelCustom('Thiếu dinh dưỡng: ', color: const Color(0xFF1AAD80), size: 48.sp),
        if (result.nuti_summaries.isEmpty) LabelCustom('(Không phát hiện)', color: Colors.black, size: 42.sp, weight: FontWeight.normal)
      ])),
      if (result.nuti_summaries.isNotEmpty) _Item(result.diagnostics, result.nuti_summaries, EdgeInsets.fromLTRB(40.sp,0,40.sp,40.sp), _gotoDetail, isPest: false),

      Divider(height: 16.sp, color: const Color(0xFFF5F5F5), thickness: 16.sp),

      Padding(padding: EdgeInsets.all(40.sp), child:
        LabelCustom('Chẩn đoán bệnh:', color: const Color(0xFF1AAD80), size: 48.sp)),
        /*Column(children: [
              ListView.builder(padding: EdgeInsets.only(top: 40.sp), shrinkWrap: true, itemBuilder: (context, index) {
                return result.diagnostics[index].message.isNotEmpty ? Text('* ' + result.diagnostics[index].message,
                    style: TextStyle(color: Colors.black, fontSize: 48.sp)) : const SizedBox();
              }, itemCount: result.diagnostics.length, physics: const NeverScrollableScrollPhysics())
        ]),*/
      if (result.summaries.isNotEmpty) _Item(result.diagnostics, result.summaries, EdgeInsets.symmetric(horizontal: 40.sp), _gotoDetail),

      if (result.summaries.isNotEmpty) _RelatePestCatalogue(result.tree),
    ]);
  }

  @override
  Widget buttonShare() {
    if (shareContent.isEmpty) return super.buttonShare();
    return IconButton(onPressed: () => bloc!.add(CreatePostDiagnosePestsEvent(images, shareContent)),
        icon: Icon(Icons.share, color: Colors.white, size: 56.sp));
  }

  void _gotoDetail(int index, bool isPest) => UtilUI.goToNextPage(context, isPest ? DiagnosticResultExtPage(index, result, shareContent, images, loc) :
    DiagnosticResultExtNutritionPage(index, result, shareContent, images, loc));
}

class _Item extends StatelessWidget {
  final List<Summary> summaries;
  final List<Diagnostic> diagnostics;
  final EdgeInsets padding;
  final bool isPest;
  final Function funDetail;
  const _Item(this.diagnostics, this.summaries, this.padding, this.funDetail, {this.isPest = true, Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) => ListView.separated(separatorBuilder: (context, index) => SizedBox(height: 40.sp),
      padding: padding, shrinkWrap: true, itemBuilder: (context, index) {
        String image = _getImage(diagnostics, summaries[index].suggest.toLowerCase(), isPest);
        return Container(padding: EdgeInsets.all(40.sp), decoration: ShadowDecoration(size: 10), child: Row(children: [
          AvatarCircleWidget(link: image, size: 120.sp, assetsImageReplace: 'assets/images/ic_default.png'),
          SizedBox(width: 20.sp),
          Expanded(child: LabelCustom(summaries[index].suggest + ' ('+Util.doubleToString(summaries[index].percent, digit: 1)+'%)', color: Colors.black, size: 48.sp, weight: FontWeight.normal)),
          if (!summaries[index].suggest_en.toLowerCase().contains('unknow'))
          ButtonImageWidget(5, () => funDetail(index, isPest), Row(children: [
            LabelCustom('Chi tiết', color: const Color(0xFF1DA644), size: 42.sp),
            Icon(Icons.arrow_forward_ios, color: const Color(0xFF1DA644), size: 42.sp)
          ]))
        ]));
      }, itemCount: summaries.length, physics: const NeverScrollableScrollPhysics());

  String _getImage(List<Diagnostic> diagnostics, String tree, bool isPest) {
    double max = 0;
    String image = '';
    for (var item in diagnostics) {
      for (var summary in (isPest ? item.predicts : item.nuti_predicts)) {
        if (summary.suggest.toLowerCase() == tree && max < summary.percent) {
          max = summary.percent;
          image = item.image;
        }
      }
    }
    return max > 0 ? image : '';
  }
}

class _RelatePestCatalogue extends StatefulWidget {
  final Map tree;
  const _RelatePestCatalogue(this.tree, {Key? key}) : super(key: key);
  @override
  _RelatePestCatalogueState createState() => _RelatePestCatalogueState();
}

class _RelatePestCatalogueState extends State<_RelatePestCatalogue> {
  final bloc = DiagnosePestsBloc(const DiagnosePestsState(), type: 'result_ext_relate_pest_catalogue');
  ScrollController? ctr;
  int? index;

  @override
  void dispose() {
    bloc.close();
    ctr?.dispose();
    ctr?.removeListener(_listenScroll);
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    bloc.add(LoadCatalogueEvent(id: widget.tree['id']??-1));
  }

  @override
  Widget build(BuildContext context) => Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Padding(padding: EdgeInsets.only(left: 40.sp, top: 40.sp), child: LabelCustom('Các bệnh liên quan:', color: const Color(0xFF1AAD80), size: 48.sp)),
      BlocBuilder(bloc: bloc, buildWhen: (_,n) => n is LoadCatalogueState, builder: (_,state) {
        if (state is LoadCatalogueState) {
          final List<Widget> list = [];
          for (Map ele in state.list) {
            if (ele['name_en'] == 'Healthy' || ele['name_en'] == 'Unknow') continue;
            list.add(GestureDetector(onTap: () => _gotoDetail(ele),
              child: Container(width: 0.25.sw - 50.sp, margin: EdgeInsets.only(right: 40.sp),
                child: Column(children: [
                  Container(padding: EdgeInsets.all(40.sp), decoration: ShadowDecoration(size: 10),
                    child: AvatarCircleWidget(link: ele['image']??'', size: 0.25.sw - 130.sp, assetsImageReplace: 'assets/images/ic_default.png')),
                  SizedBox(height: 20.sp),
                  LabelCustom(ele['name_vi']??'', color: Colors.black, size: 48.sp, weight: FontWeight.normal, align: TextAlign.center)
                ]))));
          }

          if (list.length < 5) {
            return SingleChildScrollView(padding: EdgeInsets.all(40.sp), scrollDirection: Axis.horizontal,
                      child: Row(crossAxisAlignment: CrossAxisAlignment.start, children: list));
          }

          index = 0;
          ctr = ScrollController();
          ctr!.addListener(_listenScroll);
          return Column(children: [
            SingleChildScrollView(padding: EdgeInsets.all(40.sp), scrollDirection: Axis.horizontal,
                controller: ctr, child: Row(crossAxisAlignment: CrossAxisAlignment.start, children: list)),
            BlocBuilder(bloc: bloc, buildWhen: (_,n) => n is ChangeCatalogueState,
              builder: (_,state) => SlidePoints(const [0,1], index!))
          ]);
        }
        return const SizedBox();
      })
    ]);

  void _listenScroll() {
    index = ctr!.position.pixels > 0.25.sw ? 1 : 0;
    bloc.add(ChangeCatalogueEvent());
  }

  void _gotoDetail(Map item) {
    UtilUI.goToNextPage(context, PestsHandbookListPage(item['name_vi']??'', catId: (item['id']??'').toString(), catName: item['name_vi']??''));
  }
}
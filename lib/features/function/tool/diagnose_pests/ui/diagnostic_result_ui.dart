import 'package:flutter_html/flutter_html.dart';
import 'package:hainong/common/ui/button_image_widget.dart';
import 'package:hainong/common/ui/shadow_decoration.dart';
import 'package:url_launcher/url_launcher.dart';
import 'diagnostic_result_base_page.dart';
import 'diagnostic_result_ext_fertilizer_page.dart';
import 'diagnostic_result_ext_medicine_page.dart';

class MyHtml extends StatelessWidget {
  final String data;
  const MyHtml(this.data, {Key? key}):super(key:key);
  @override
  Widget build(BuildContext context) => Html(data: data, style: {
    'html,body,p,div': Style(padding: HtmlPaddings.zero, margin: Margins.zero, fontSize: FontSize(20))
  },
      onLinkTap: (url,_,__) => launchUrl(Uri.parse(url!), mode: LaunchMode.externalApplication),
      onAnchorTap: (url,_,__) => launchUrl(Uri.parse(url!), mode: LaunchMode.externalApplication));
}

class ShowHtml extends StatelessWidget {
  final BaseBloc? bloc;
  String data, type;
  ShowHtml(this.bloc, this.data, this.type, {Key? key}):super(key:key);
  @override
  Widget build(BuildContext context) => Padding(padding: EdgeInsets.fromLTRB(40.sp, 20.sp, 40.sp, 40.sp),
      child: BlocBuilder(bloc: bloc, builder: (_,state) {
        if (state is LoadSymptomMalnutritionState) data = state.resp.data[type];
        if (data.isEmpty) return const DataUpdating();
        return data.length > 320 ? ExpandHtml(bloc, MyHtml(data), type) : MyHtml(data);
      }, buildWhen: (oldS, newS) => (newS is LoadSymptomMalnutritionState && newS.resp.data['error'] == null)));
}

class ExpandHtml extends StatelessWidget {
  final BaseBloc? bloc;
  final Widget html;
  String expType;
  ExpandHtml(this.bloc, this.html, this.expType, {Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) => BlocBuilder(bloc: bloc, builder: (_, state) {
    final isExp = bloc!.data[expType].contains('_on');
    return Column(children: [
      isExp ? html: SizedBox(height: 320.sp, child: html),
      ButtonImageWidget(0, () => bloc!.add(ExpandEvent(bloc!.data[expType])),
          Row(children: [
            LabelCustom(isExp?'Thu gon':'Xem thêm', color: const Color(0xFF2BCE85), size: 36.sp, weight: FontWeight.normal),
            Icon(isExp?Icons.keyboard_arrow_up:Icons.keyboard_arrow_down, color: const Color(0xFF2BCE85), size: 64.sp)
          ], mainAxisAlignment: MainAxisAlignment.end))
    ], mainAxisSize: MainAxisSize.min, crossAxisAlignment: CrossAxisAlignment.end);
  }, buildWhen: (oldExpS, newExpS) => newExpS is ExpandState && newExpS.type == expType);
}

class ExpandItemList extends StatelessWidget {
  final BaseBloc? bloc;
  final List medicines;
  final bool? hasImage;
  final int maxExp;
  String expType;
  ExpandItemList(this.bloc, this.medicines, this.expType, {this.hasImage, this.maxExp = 3, Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) => BlocBuilder(bloc: bloc, builder: (_, state) {
    final isNotExp = bloc!.data[expType].contains('_off');
    int len = medicines.length;
    if (medicines.length > maxExp && isNotExp) len = maxExp;

    final temp = ListView.separated(physics: const NeverScrollableScrollPhysics(), shrinkWrap: true,
      padding: EdgeInsets.symmetric(vertical: 40.sp), separatorBuilder: (_,__) => SizedBox(height: 40.sp),
      itemCount: len, itemBuilder: (_, index) {
        String? link;
        if (Util.checkKeyFromJson(medicines[index], 'images') && medicines[index]['images'].isNotEmpty) link = medicines[index]['images'][0]['name'];
        return ButtonImageWidget(60, () => _gotoDetail(context, index),
          Container(padding: EdgeInsets.all(40.sp), decoration: ShadowDecoration(size: 10, bgColor: const Color(0xFFFFFFFF)),
            child: Row(children: [
              if (hasImage == true) Padding(padding: EdgeInsets.only(right: 40.sp),
                child: ImageNetworkAsset(path: link??'', width: 128.sp, height: 128.sp)),
              Expanded(child: LabelCustom(medicines[index]['title']??'', color: Colors.black)),
              Icon(Icons.arrow_forward_ios, size: 64.sp, color: Colors.black)
            ])));
      });

    return medicines.length > maxExp ? Column(children: [
      temp,
      ButtonImageWidget(0, () => bloc!.add(ExpandEvent(bloc!.data[expType])),
          Row(children: [
            LabelCustom(isNotExp?'Xem thêm':'Thu gon', color: const Color(0xFF2BCE85), size: 36.sp, weight: FontWeight.normal),
            Icon(isNotExp?Icons.keyboard_arrow_down:Icons.keyboard_arrow_up, color: const Color(0xFF2BCE85), size: 64.sp)
          ], mainAxisSize: MainAxisSize.min))
    ], mainAxisSize: MainAxisSize.min, crossAxisAlignment: CrossAxisAlignment.end) : temp;
  }, buildWhen: (oldExpS, newExpS) => newExpS is ExpandState && newExpS.type == expType);

  void _gotoDetail(BuildContext context, int index) {
    final page = hasImage == true ? DiagnosticResultExtFertilizerPage(medicines[index]['id']??-1, medicines[index]['title']??'') :
                 DiagnosticResultExtMedicinePage(medicines[index]['id']??-1, medicines[index]['title']??'');
    UtilUI.goToNextPage(context, page);
  }
}

class ResultTabItem extends StatelessWidget {
  final bool active;
  final String title;
  final int index;
  final Function change;
  const ResultTabItem(this.title, this.index, this.active, this.change, {Key? key}) : super(key:key);
  @override
  Widget build(BuildContext context) => Expanded(child: Container(decoration: BoxDecoration(border:
  Border(bottom: BorderSide(color: Color(active ? 0xFF1DA644 : 0xFFCDCDCD), width: 10.sp)), color: const Color(0xFFFAFAFA)),
      child: ButtonImageWidget(5.sp, () => change(index), Padding(padding: EdgeInsets.only(top: 40.sp, bottom: 20.sp),
          child: LabelCustom(title, color: Color(active ? 0xFF1DA644 : 0xFF000000), align: TextAlign.center,
              weight: FontWeight.w500, size: 48.sp)))));
}

class DataUpdating extends StatelessWidget {
  const DataUpdating({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) => LabelCustom('Dữ liệu đang cập nhật ...', size: 40.sp, color: const Color(0xFFCDCDCD), weight: FontWeight.normal);
}
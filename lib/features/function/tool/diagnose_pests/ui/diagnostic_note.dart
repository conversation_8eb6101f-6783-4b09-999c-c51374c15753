import 'package:flutter_html/flutter_html.dart';
import 'package:hainong/common/ui/import_lib_base_ui.dart';
import 'package:hainong/common/ui/label_custom.dart';

class DiagnosticNote extends StatelessWidget {
  final String data, title;
  final IconData icon;
  final double? size;
  final EdgeInsets? margin;
  const DiagnosticNote(this.data, {this.title = ' Chú ý:', this.icon = Icons.info_outline, this.size, this.margin, Key? key}):super(key:key);
  @override
  Widget build(BuildContext context) => Container(padding: EdgeInsets.all(24.sp), margin: margin??EdgeInsets.all(40.sp),
      decoration: BoxDecoration(color: const Color(0xFFFF3E3E).withOpacity(0.28), borderRadius: BorderRadius.circular(10)),
      child: Column(children: [
        Row(children: [
          Icon(icon, color: Colors.orange, size: size??64.sp),
          LabelCustom(title, color: Colors.orange, size: 36.sp)
        ]),
        Padding(padding: EdgeInsets.only(left: 32.sp, top: 10.sp), child: (Html(data: data,
            style: {'html, body, p, ul, li': Style(margin: Margins.zero, padding: HtmlPaddings.zero,
                color: Colors.black, fontSize: FontSize(12))})))
      ]));
}
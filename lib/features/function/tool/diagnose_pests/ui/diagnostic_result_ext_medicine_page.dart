import 'package:hainong/common/ui/button_image_widget.dart';
import 'package:hainong/features/function/tool/diagnose_pests/ui/diagnostic_note.dart';
import 'package:hainong/features/function/tool/diagnose_pests/ui/diagnostic_result_ui.dart';
import 'package:hainong/features/main/bloc/main_bloc.dart';

import 'diagnostic_result_base_page.dart';

class DiagnosticResultExtMedicinePage extends BasePage {
  final int id;
  final String name;
  DiagnosticResultExtMedicinePage(this.id, this.name, {Key? key}) : super(key: key, pageState: _DiaResultExtMedicinePageState());
}
class _DiaResultExtMedicinePageState extends BasePageState {
  final List _medicines = [], _warnings = [];
  String _expValue = 'medicine_off';

  @override
  void dispose() {
    _medicines.clear();
    _warnings.clear();
    super.dispose();
  }

  @override
  void initState() {
    bloc = DiagnosePestsBloc(const DiagnosePestsState(), type: 'result_ext_medicine');
    super.initState();
    bloc!.stream.listen((state) {
      if (state is LoadSymptomMalnutritionState && isResponseNotError(state.resp)) {
        _medicines.addAll(state.resp.data);
      } else if (state is LoadTreatmentState && isResponseNotError(state.resp)) {
        setState(() => _warnings.addAll(state.resp.data));
      } else if (state is CountNotificationMainState) { // check for show error
        isResponseNotError(state.response, passString: true);
      }
    });
    bloc!.add(LoadSymptomMalnutritionEvent((widget as DiagnosticResultExtMedicinePage).id));
  }

  @override
  Widget build(BuildContext context, {Color color = Colors.white}) => Scaffold(appBar: AppBar(titleSpacing: 0, centerTitle: true,
      title: UtilUI.createLabel((widget as DiagnosticResultExtMedicinePage).name), elevation: 5), backgroundColor: color,
    body: ListView(padding: EdgeInsets.all(40.sp), children: [
      LabelCustom('Danh mục thuốc chứa hoạt chất', size: 54.sp, color: Colors.black),
      BlocBuilder(bloc: bloc, buildWhen: (oldS, newS) => newS is ExpandState,
        builder: (_,state) {
          if (_medicines.isEmpty) return Container(alignment: Alignment.centerLeft, padding: EdgeInsets.symmetric(vertical: 40.sp), child: const DataUpdating());

          if (state is ExpandState) _expValue = state.value;
          bool isNotExp = _expValue.contains('_off');
          int len = _medicines.length;
          if (_medicines.length > 5 && isNotExp) len = 5;
          final temp = ListView.separated(separatorBuilder: (_,__) => SizedBox(height: 40.sp), itemCount: len,
              shrinkWrap: true, physics: const NeverScrollableScrollPhysics(), padding: EdgeInsets.only(top: 40.sp, bottom: _medicines.length > 5 ? 0 : 40.sp),
              itemBuilder: (_,index) => Row(children: [
                ImageNetworkAsset(path: _medicines[index]['image']??'', width: 64.sp, height: 64.sp),
                Expanded(child: LabelCustom('  ' + (_medicines[index]['title']??''), size: 40.sp, color: Colors.black, weight: FontWeight.w300))
              ]));
          return _medicines.length > 5 ? Column(children: [
            temp,
            ButtonImageWidget(0, () => bloc!.add(ExpandEvent(_expValue)),
                Row(children: [
                  LabelCustom(isNotExp?'Xem thêm':'Thu gon', color: const Color(0xFF2BCE85), size: 36.sp, weight: FontWeight.normal),
                  Icon(isNotExp?Icons.keyboard_arrow_down:Icons.keyboard_arrow_up, color: const Color(0xFF2BCE85), size: 64.sp)
                ], mainAxisSize: MainAxisSize.min)),
          ], crossAxisAlignment: CrossAxisAlignment.end) : temp;
        }),

      DiagnosticNote('Hãy kiểm tra kỹ nhãn hiệu và thành phần hoạt chất được ghi trên hộp đựng.'
        '<br><br>Luôn tuân thủ các biện pháp phòng ngừa rủi ro khi tiếp xúc với thuốc bảo vệ thực vật nguy hiểm.',
        margin: EdgeInsets.symmetric(vertical: 40.sp)),

      LabelCustom('Cảnh báo an toàn ', size: 54.sp, color: Colors.black),
      _warnings.isEmpty ? Container(alignment: Alignment.centerLeft, padding: EdgeInsets.symmetric(vertical: 40.sp), child: const DataUpdating()) :
      ListView.separated(separatorBuilder: (_,__) => SizedBox(height: 40.sp), itemCount: _warnings.length,
        shrinkWrap: true, physics: const NeverScrollableScrollPhysics(), padding: EdgeInsets.symmetric(vertical: 40.sp),
        itemBuilder: (_,index) => Row(children: [
          ImageNetworkAsset(path: _warnings[index]['icon']??'', width: 120.sp, height: 120.sp),
          Expanded(child: LabelCustom('     ' + (_warnings[index]['title']??''), size: 40.sp, color: Colors.black, weight: FontWeight.w400))
        ])),
    ]));
}
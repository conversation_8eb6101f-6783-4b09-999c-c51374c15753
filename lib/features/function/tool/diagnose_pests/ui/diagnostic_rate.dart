import 'package:shared_preferences/shared_preferences.dart';
import 'package:hainong/common/ui/button_image_widget.dart';
import 'package:hainong/common/ui/import_lib_base_ui.dart';
import 'package:hainong/common/ui/label_custom.dart';
import 'package:hainong/common/ui/star_rate.dart';

class DiagnosticRate extends StatefulWidget {
  final Function funContribute;
  const DiagnosticRate(this.funContribute, {Key? key}) : super(key: key);

  @override
  _DiagnosticRateState createState() => _DiagnosticRateState();
}

class _DiagnosticRateState extends State<DiagnosticRate> {
  String name = '';
  int point = 0;

  @override
  void initState() {
    super.initState();
    SharedPreferences.getInstance().then((pref) => setState(() => name = pref.getString('name')??''));
  }

  @override
  Widget build(BuildContext context) {
    return Container(padding: EdgeInsets.all(40.sp),
        decoration: BoxDecoration(color: const Color(0x5981C5FF),
            borderRadius: const BorderRadius.vertical(top: Radius.circular(10)),
            border: Border.all(color: const Color(0xFF81C5FF), width: 1)),
        child: Column(children: [
          LabelCustom(name + ' ơi, hãy đánh giá và đóng góp hình ảnh để nhận điểm thưởng nhé!',
              align: TextAlign.justify, color: Colors.black, weight: FontWeight.w600, size: 60.sp),
          Padding(padding: EdgeInsets.symmetric(vertical: 20.sp), child: StarRate(rate: point, size: 120.sp, onClick: _changeStar, hasFunction: true)),
          ButtonImageWidget(20, _contribute,
              Padding(padding: EdgeInsets.symmetric(vertical: 25.sp, horizontal: 50.sp),
                  child: LabelCustom('Đóng góp', weight: FontWeight.w600, size: 60.sp)), color: Color(point > 0 ? 0xFF1DA644 : 0xFFCDCDCD))
        ])
    );
  }

  void _changeStar(int index) {
    if (point != index) setState(() => point = index);
    else setState(() => point = 0);
  }

  void _contribute() {
    if (point == 0) return;
    widget.funContribute(point, hasBack: false);
  }
}
import 'package:hainong/common/models/file_byte.dart';
import 'diagnostic_note.dart';
import 'diagnostic_result_ui.dart';
import 'diagnostic_result_base_page.dart';

class DiagnosticResultExtPage extends BasePage {
  final int index;
  DiagnosticResultExtPage(this.index, DiagnosticModel result, String shareContent, List<FileByte> images, loc,
      {Key? key}) : super(key: key, pageState: _DiaResultExtPageState(result, shareContent, images, loc));
}
class _DiaResultExtPageState extends DiaResultBasePageState {
  int _tabIndex = 1;
  String _symptom = '', _cause = '', _prevention = '';
  final List _treatments = [];
  _DiaResultExtPageState(result, shareContent, images, loc) : super(result, shareContent, images, [], loc, type: 'result_ext');

  @override
  String getTitle() => 'Kết quả chẩn đo<PERSON>';

  @override
  void listenBloc(state) {
    if (state is LoadSymptomMalnutritionState && isResponseNotError(state.resp)) {
      _symptom = state.resp.data['symptom'];
      _cause = state.resp.data['cause'];
      bloc!.data.putIfAbsent('symptom', () => 'symptom_off');
      bloc!.data.putIfAbsent('cause', () => 'cause_off');
    } else if (state is LoadTreatmentState && isResponseNotError(state.resp)) {
      _treatments.addAll(state.resp.data);
      for (var ele in _treatments) {
        bloc!.data.putIfAbsent(ele['id'].toString(), () => ele['id'].toString() + '_off');
      }
    }  else if (state is LoadPreventionState && isResponseNotError(state.resp, passString: true)) {
      _prevention = state.resp.data;
    } else super.listenBloc(state);
  }

  @override
  void initState() {
    super.initState();
    bloc!.add(LoadSymptomMalnutritionEvent(result.summaries[(widget as DiagnosticResultExtPage).index].id));
    bloc!.data = {};
  }

  @override
  Widget createUI() {
    final summary = result.summaries[(widget as DiagnosticResultExtPage).index];
    return ListView(padding: EdgeInsets.zero, children: [
      Padding(padding: EdgeInsets.all(40.sp), child: Row(children: [
        Expanded(child: Column(children: [
          LabelCustom(summary.suggest, size: 48.sp, weight: FontWeight.w500, color: const Color(0xFF002500)),
          SizedBox(height: 10.sp),
          LabelCustom(summary.suggest_en, size: 40.sp, weight: FontWeight.w500, color: const Color(0xFFCDCDCD)),
        ], crossAxisAlignment: CrossAxisAlignment.start)),
        ImageNetworkAsset(path: result.tree['icon'] ?? '', width: 120.sp, height: 120.sp),
        LabelCustom('  ' + (result.tree['name']??''), size: 48.sp, color: const Color(0xFF1865B1))
      ], mainAxisAlignment: MainAxisAlignment.end)),

      Padding(padding: EdgeInsets.symmetric(horizontal: 40.sp), child: LabelCustom('1. Nguyên nhân', size: 54.sp, color: Colors.black)),
      ShowHtml(bloc, _cause, 'cause'),

      Padding(padding: EdgeInsets.symmetric(horizontal: 40.sp), child: LabelCustom('2. Triệu chứng', size: 54.sp, color: Colors.black)),
      ShowHtml(bloc, _symptom, 'symptom'),

      BlocBuilder(bloc: bloc, builder: (_,__) => Column(children: [
        Row(children: [
          ResultTabItem('Biện pháp\nphòng ngừa', 0, _tabIndex == 0, _changeTab),
          ResultTabItem('Biện pháp\nđiều trị', 1, _tabIndex == 1, _changeTab)
        ]),
        _tabIndex == 1 ? (_treatments.isEmpty ? Container(alignment: Alignment.centerLeft, padding: EdgeInsets.all(40.sp),
          child: const DataUpdating()) : ListView.builder(physics: const NeverScrollableScrollPhysics(), shrinkWrap: true,
          itemCount: _treatments.length, itemBuilder: (_,index) {
            final treatment = _treatments[index];
            return Column(children: [
              Row(children: [
                ImageNetworkAsset(path: treatment['image']??'', width: 100.sp, height: 100.sp),
                LabelCustom('  ' + (treatment['name']??''), size: 45.sp, color: Colors.black)
              ]),
              if (treatment['object_results'] != null)
                ExpandItemList(bloc, treatment['object_results'], treatment['id'].toString())
            ]);
          }, padding: EdgeInsets.all(40.sp))) : Column(children: [
            Container(padding: EdgeInsets.fromLTRB(40.sp, 40.sp, 40.sp, 0), alignment: Alignment.centerLeft,
                child: _prevention.isEmpty ? const DataUpdating() : MyHtml(_prevention)),
            const DiagnosticNote('Nên ưu tiên sử dụng canh tác kỹ thuật và sinh học để phòng trừ bệnh hại ở giai đoạn bệnh chưa phát triển'
              '<br><br>Nếu sâu bệnh hại đã phát triển vượt ngưỡng cho phép thì nên sử dụng biện pháp hóa học hoặc sinh học.')
          ])
      ]), buildWhen: (oldS, newS) => newS is ChangeCatalogueState || newS is LoadTreatmentState),
    ]);
  }

  @override
  Widget buttonShare() {
    if (shareContent.isEmpty) return super.buttonShare();
    return IconButton(onPressed: () => bloc!.add(CreatePostDiagnosePestsEvent(images, shareContent)),
        icon: Icon(Icons.share, color: Colors.white, size: 56.sp));
  }

  void _changeTab(int index) {
    if (_tabIndex == index) return;
    setState(() => _tabIndex = index);
    //bloc!.add(ChangeCatalogueEvent());
  }
}
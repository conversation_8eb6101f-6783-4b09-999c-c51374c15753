import 'dart:typed_data';
import 'package:hainong/common/models/file_byte.dart';
import 'package:hainong/common/ui/import_lib_base_ui.dart';
import 'package:hainong/common/ui/label_custom.dart';
import 'package:hainong/common/util/util.dart';

class DiagnosticPestsImageItemPage extends StatelessWidget {
  final FileByte file;
  final Function delete;
  const DiagnosticPestsImageItemPage(this.file, this.delete, {Key? key}):super(key:key);

  @override
  Widget build(BuildContext context) => Container(alignment: Alignment.topRight,
    width: 240.sp, margin: EdgeInsets.only(right: 20.sp),
    decoration: BoxDecoration(image: DecorationImage(image:
      Image.memory(Uint8List.fromList(file.bytes)).image, fit: BoxFit.cover)),
    child: SizedBox(height: 24, width: 24, child: IconButton(onPressed: () => delete(),
      icon: const Icon(Icons.close, color: Colors.white,size: 20), padding: EdgeInsets.zero)));
}

class DiagnosticPestsImageItem2Page extends StatelessWidget {
  final FileByte file;
  const DiagnosticPestsImageItem2Page(this.file, {Key? key}) : super(key:key);
  @override
  Widget build(BuildContext context) => ClipRRect(borderRadius: BorderRadius.circular(10),
    child: Image.memory(Uint8List.fromList(file.bytes), width: 0.2.sw, height: 0.2.sw, fit: BoxFit.cover));
}

class AddressAndDate extends StatelessWidget {
  final String address;
  const AddressAndDate(this.address, {Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) => Column(children: [
    LabelCustom(Util.dateToString(DateTime.now(), pattern: 'HH:mm a; dd/MM/yyyy'), size: 24.sp, color: Colors.red, weight: FontWeight.normal),
    LabelCustom(address, size: 24.sp, color: const Color(0xFF0093FD), weight: FontWeight.normal)
  ], crossAxisAlignment: CrossAxisAlignment.start);
}

class AHalfButton extends StatelessWidget {
  final Color color;
  final bool radiusLeft;
  const AHalfButton(this.color, {this.radiusLeft = true, Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) => Container(decoration: BoxDecoration(
    color: color, borderRadius: BorderRadius.horizontal(
          left: radiusLeft ? const Radius.circular(50) : Radius.zero,
          right: radiusLeft ? Radius.zero : const Radius.circular(50)),
  ), padding: EdgeInsets.symmetric(vertical: 60.sp), width: 0.25.sw);
}

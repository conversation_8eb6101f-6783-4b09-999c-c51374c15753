import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hainong/features/function/tool/map_task/models/map_enum.dart';

/// Model layer configuration
class ModelLayerModel {
  final MapModelEnum id;
  final String title;
  final IconData icon;
  final Color color;
  final String description;

  const ModelLayerModel({required this.id, required this.title, required this.icon, required this.color, required this.description});
}

class ModelLayersConfig {
  static const List<ModelLayerModel> modelLayers = [
    ModelLayerModel(
      id: MapModelEnum.demonstration,
      title: '<PERSON>ô hình',
      icon: Icons.other_houses,
      color: Colors.orange,
      description: '<PERSON>ô hình trình diễn'
    ),
    ModelLayerModel(
      id: MapModelEnum.store,
      title: 'Cửa hàng',
      icon: Icons.store,
      color: Colors.green,
      description: '<PERSON>ác cửa hàng nông sản'
    ),
    ModelLayerModel(
      id: MapModelEnum.storage,
      title: '<PERSON><PERSON> bãi',
      icon: Icons.warehouse,
      color: Colors.blue,
      description: '<PERSON><PERSON><PERSON> kho bãi lưu trữ'
    ),
  ];

  static ModelLayerModel getLayerById(MapModelEnum id) => modelLayers.firstWhere((layer) => layer.id == id, orElse: () => modelLayers.first);
}

class ModelDropdownWidget extends StatefulWidget {
  final MapModelEnum selectedModelLayer;
  final Function(MapModelEnum) onModelLayerChanged;

  const ModelDropdownWidget({
    super.key,
    required this.selectedModelLayer,
    required this.onModelLayerChanged,
  });

  @override
  State<ModelDropdownWidget> createState() => _ModelDropdownWidgetState();
}

class _ModelDropdownWidgetState extends State<ModelDropdownWidget> {
  bool _isModelDropdownExpanded = false;
  late ModelLayerModel _currentLayer;

  @override
  void initState() {
    super.initState();
    _currentLayer = ModelLayersConfig.getLayerById(widget.selectedModelLayer);
  }

  @override
  void didUpdateWidget(ModelDropdownWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.selectedModelLayer != widget.selectedModelLayer) {
      _currentLayer = ModelLayersConfig.getLayerById(widget.selectedModelLayer);
    }
  }

  void _onLayerTap(MapModelEnum layerId) {
    setState(() {
      _currentLayer = ModelLayersConfig.getLayerById(layerId);
    });
    widget.onModelLayerChanged(layerId);
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: 20.sp,
      left: 20.sp,
      child: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16.sp),
                boxShadow: [BoxShadow(color: Colors.grey.withOpacity(0.5), spreadRadius: 1.0, blurRadius: 5.0, offset: const Offset(0, 1))],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  GestureDetector(
                    onTap: () => setState(() => _isModelDropdownExpanded = !_isModelDropdownExpanded),
                    child: Container(
                      width: 0.36.sw,
                      padding: EdgeInsets.all(16.sp),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12.sp),
                        boxShadow: [BoxShadow(color: Colors.black.withOpacity(0.12), spreadRadius: 0, blurRadius: 8, offset: const Offset(0, 2))],
                        border: Border.all(color: Colors.blue[200]!, width: 1.5),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              Icon(_currentLayer.icon, color: _currentLayer.color, size: 62.sp),
                              SizedBox(width: 36.sp),
                              Text(_currentLayer.title, style: TextStyle(color: _currentLayer.color, fontWeight: FontWeight.w600, fontSize: 38.sp)),
                              SizedBox(width: 16.sp),
                            ],
                          ),
                          Icon(
                            _isModelDropdownExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                            color: _currentLayer.color,
                            size: 52.sp,
                          ),
                        ],
                      ),
                    ),
                  ),
                  if (_isModelDropdownExpanded) ...[
                    Container(
                      width: 0.36.sw,
                      padding: EdgeInsets.symmetric(vertical: 12.sp),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ...ModelLayersConfig.modelLayers.map((layer) {
                            return Container(
                              padding: EdgeInsets.all(16.sp),
                              child: GestureDetector(
                                onTap: () {
                                  setState(() => _isModelDropdownExpanded = false);
                                  _onLayerTap(layer.id);
                                },
                                child: Container(
                                  width: double.infinity,
                                  color: Colors.white,
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Icon(layer.icon, color: widget.selectedModelLayer == layer.id ? layer.color : Colors.black87, size: 62.sp),
                                          SizedBox(width: 36.sp),
                                          Text(layer.title,
                                              style: TextStyle(color: widget.selectedModelLayer == layer.id ? layer.color : Colors.black87, fontSize: 38.sp, fontWeight: FontWeight.w600)),
                                        ],
                                      ),
                                      if (widget.selectedModelLayer == layer.id) Icon(Icons.check_circle, size: 52.sp, color: layer.color),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          }),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

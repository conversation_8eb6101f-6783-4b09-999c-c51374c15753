import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hainong/features/function/tool/map_task/components/map_weather/weather_constants.dart';
import 'package:hainong/features/function/tool/map_task/components/map_weather/weather_map_controller.dart';
import 'package:hainong/features/function/tool/map_task/map_constants.dart';
import 'package:hainong/features/function/tool/map_task/models/map_enum.dart';
import 'package:hainong/features/function/tool/map_task/models/map_item_menu_model.dart';
import 'package:hainong/features/function/tool/map_task/utils/map_utils.dart';

List<Widget> buildWeatherControlButtons(WeatherMapController weatherController, Function() toggleTimelineAndScale, Function() toggleWeatherIconsVisibility) {
  bool shouldShowCloudButton = weatherController.selectedWeatherLayer != WeatherConstants.wind;

  return [
    SizedBox(height: 20.sp),
    if (shouldShowCloudButton) ...[
      buildWeatherControlButton(
        icon: Icons.wb_sunny,
        isActive: weatherController.weatherIconsVisible,
        onTap: toggleWeatherIconsVisibility,
      ),
      SizedBox(height: 20.sp),
    ],
    buildWeatherControlButton(
      icon: Icons.timeline,
      isActive: (weatherController.timelineVisible || weatherController.verticalScaleVisible),
      onTap: toggleTimelineAndScale,
    ),
  ];
}

// Main menu list widget that appears by default on first map entry
class MapMenuListOverlay extends StatefulWidget {
  final List<MenuItemMap>? menuList;
  final MapMenuEnum selectMenuId;
  final Function(int) onMenuSelected;
  final VoidCallback onShowMapPressed;

  const MapMenuListOverlay({
    super.key,
    this.menuList,
    required this.selectMenuId,
    required this.onMenuSelected,
    required this.onShowMapPressed,
  });

  static void resetState() {
    _MapMenuListOverlayState._hasShownOnce = false;
  }

  @override
  State<MapMenuListOverlay> createState() => _MapMenuListOverlayState();
}

class _MapMenuListOverlayState extends State<MapMenuListOverlay> with SingleTickerProviderStateMixin {
  static bool _hasShownOnce = false;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(duration: const Duration(milliseconds: 300), vsync: this);

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(CurvedAnimation(parent: _animationController, curve: Curves.easeInOut));

    _slideAnimation = Tween<Offset>(begin: const Offset(1.0, 0.0), end: Offset.zero).animate(CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic));

    // Show animation on first load
    if (!_hasShownOnce) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _animationController.forward();
        _hasShownOnce = true;
      });
    } else {
      _animationController.value = 1.0;
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Color _getMenuColor(MapMenuEnum menu) {
    switch (menu) {
      case MapMenuEnum.pet:
        return Colors.green;
      case MapMenuEnum.nutrition:
        return Colors.orange;
      case MapMenuEnum.weather:
        return Colors.blue;
      case MapMenuEnum.model:
        return Colors.yellow.shade700;
      default:
        return Colors.black87;
    }
  }

  void _hideMenuList() {
    _animationController.reverse().then((_) {
      widget.onShowMapPressed();
    });
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: Align(
              alignment: Alignment.topRight,
              child: Stack(
                children: [
                  Container(
                    margin: EdgeInsets.only(top: 16.sp, right: 16.sp),
                    width: 0.32.sw,
                    padding: EdgeInsets.zero,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(20.sp),
                      boxShadow: [BoxShadow(color: Colors.grey.withOpacity(0.4), spreadRadius: 1, blurRadius: 2, offset: const Offset(0, 1))],
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        ...widget.menuList?.map((item) {
                              final menu = MapConstants.indexEnumMenuMap(item.id);
                              final isSelected = widget.selectMenuId == menu;
                              return GestureDetector(
                                onTap: () {
                                  widget.onMenuSelected(item.id);
                                  _hideMenuList();
                                },
                                child: Container(
                                  padding: EdgeInsets.symmetric(vertical: 16.sp, horizontal: 32.sp),
                                  decoration: BoxDecoration(
                                    color: isSelected ? _getMenuColor(menu).withOpacity(0.1) : Colors.transparent,
                                    borderRadius: item.id == 1
                                        ? BorderRadius.only(topLeft: Radius.circular(20.sp), topRight: Radius.circular(20.sp))
                                        : item.id == 4
                                            ? BorderRadius.only(bottomLeft: Radius.circular(20.sp), bottomRight: Radius.circular(20.sp))
                                            : null,
                                  ),
                                  child: SizedBox(
                                    height: 92.sp,
                                    child: Column(
                                      children: [
                                        Expanded(
                                          child: Row(
                                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                            children: [
                                              SizedBox(width: 20.sp),
                                              Expanded(
                                                child: Text(
                                                  item.name,
                                                  style: TextStyle(
                                                      fontSize: 38.sp, color: isSelected ? _getMenuColor(menu) : Colors.black87, fontWeight: isSelected ? FontWeight.bold : FontWeight.normal),
                                                ),
                                              ),
                                              SizedBox(
                                                width: 62.w,
                                                height: 62.w,
                                                child: ColorFiltered(
                                                    colorFilter: isSelected ? ColorFilter.mode(_getMenuColor(menu), BlendMode.srcATop) : const ColorFilter.mode(Colors.transparent, BlendMode.multiply),
                                                    child: Image.asset(item.image, fit: BoxFit.cover)),
                                              ),
                                            ],
                                          ),
                                        ),
                                        if (item.id != 4) const Divider(color: Colors.grey, height: 2, thickness: 0),
                                      ],
                                    ),
                                  ),
                                ),
                              );
                            }).toList() ??
                            [],
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

class PopupMapMenuButton extends StatefulWidget {
  final List<MenuItemMap>? menuList;
  final MapMenuEnum selectMenuId;
  final Function(int) onCallBack;
  final bool isVisible;

  const PopupMapMenuButton({
    this.selectMenuId = MapMenuEnum.pet,
    this.menuList,
    super.key,
    required this.onCallBack,
    this.isVisible = true,
  });

  static void resetState() {
    // Reset state for both widgets
    MapMenuListOverlay.resetState();
  }

  @override
  State<PopupMapMenuButton> createState() => _PopupMapMenuButtonState();
}

class _PopupMapMenuButtonState extends State<PopupMapMenuButton> {
  final GlobalKey<DropdownButton2State> _dropdownKey = GlobalKey<DropdownButton2State>();

  Color _getMenuColor(MapMenuEnum menu) {
    switch (menu) {
      case MapMenuEnum.pet:
        return Colors.green;
      case MapMenuEnum.nutrition:
        return Colors.orange;
      case MapMenuEnum.weather:
        return Colors.blue;
      case MapMenuEnum.model:
        return Colors.yellow.shade700;
      default:
        return Colors.black87;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isVisible) {
      return const SizedBox.shrink();
    }

    return DropdownButtonHideUnderline(
      child: DropdownButton2(
        key: _dropdownKey,
        alignment: Alignment.topRight,
        onMenuStateChange: (isOpen) {
          setState(() {});
        },
        customButton: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [BoxShadow(color: Colors.grey.withOpacity(0.5), spreadRadius: 1, blurRadius: 5, offset: const Offset(0, 1))],
          ),
          padding: const EdgeInsets.all(12),
          child: const Icon(Icons.menu),
        ),
        dropdownStyleData: DropdownStyleData(
          offset: const Offset(0, -6),
          direction: DropdownDirection.left,
          width: 0.34.sw,
          isOverButton: false,
          padding: EdgeInsets.zero,
          decoration:
              BoxDecoration(borderRadius: BorderRadius.circular(20.sp), boxShadow: [BoxShadow(color: Colors.grey.withOpacity(0.5), spreadRadius: 1, blurRadius: 5, offset: const Offset(0, 1))]),
        ),
        items: widget.menuList?.map((item) {
          final menu = MapConstants.indexEnumMenuMap(item.id);
          return DropdownMenuItem<String>(
            value: item.name,
            child: SizedBox(
              child: Column(
                children: [
                  SizedBox(height: 30.sp),
                  Expanded(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        SizedBox(width: 20.sp),
                        Expanded(
                          child: Text(
                            item.name,
                            style: TextStyle(
                              fontSize: 38.sp,
                              color: widget.selectMenuId == menu ? _getMenuColor(menu) : Colors.black87,
                              fontWeight: widget.selectMenuId == menu ? FontWeight.bold : FontWeight.normal,
                            ),
                          ),
                        ),
                        SizedBox(
                          width: 72.w,
                          height: 72.w,
                          child: ColorFiltered(
                            colorFilter: widget.selectMenuId == menu ? ColorFilter.mode(_getMenuColor(menu), BlendMode.srcATop) : const ColorFilter.mode(Colors.transparent, BlendMode.multiply),
                            child: Image.asset(item.image),
                          ),
                        )
                      ],
                    ),
                  ),
                  SizedBox(height: 30.sp),
                  if (item.id != 4) const Divider(color: Colors.grey, height: 2, thickness: 0),
                ],
              ),
            ),
          );
        }).toList(),
        onChanged: (value) {
          if (widget.menuList != null) {
            final selectedItem = widget.menuList!.firstWhere((item) => item.name == value);
            widget.onCallBack(selectedItem.id);
          }
        },
        iconStyleData: IconStyleData(icon: const Icon(Icons.menu), iconEnabledColor: Colors.green, iconDisabledColor: Colors.black87, iconSize: 24.sp),
      ),
    );
  }
}

// New comprehensive popup menu panel widget with updated flow
class PopupMapMenuPanel extends StatefulWidget {
  final MapMenuEnum selectMenu;
  final bool isAtCurrentLocation;
  final bool Function() isShowMenuModelOption;
  final VoidCallback selectOptionMap;
  final VoidCallback selectPositionMap;
  final Function(int) selectMenuMap;
  final List<Widget> Function()? buildWeatherControlButtons;
  final dynamic weatherController;

  const PopupMapMenuPanel({
    super.key,
    required this.selectMenu,
    required this.isAtCurrentLocation,
    required this.isShowMenuModelOption,
    required this.selectOptionMap,
    required this.selectPositionMap,
    required this.selectMenuMap,
    this.buildWeatherControlButtons,
    this.weatherController,
  });

  @override
  State<PopupMapMenuPanel> createState() => _PopupMapMenuPanelState();
}

class _PopupMapMenuPanelState extends State<PopupMapMenuPanel> {
  bool _showMenuList = true;
  bool _showPopupMenu = false;

  void _onShowMapPressed() {
    setState(() {
      _showMenuList = false;
      _showPopupMenu = true;
    });
  }

  void _onMenuSelected(int menuId) {
    widget.selectMenuMap(menuId);
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        if (_showMenuList) MapMenuListOverlay(menuList: MapConstants.menuMapOption(), selectMenuId: widget.selectMenu, onMenuSelected: _onMenuSelected, onShowMapPressed: _onShowMapPressed),
        if (_showPopupMenu)
          Align(
            alignment: Alignment.topRight,
            child: Container(
              margin: EdgeInsets.only(top: 12.sp, right: 10.sp, left: 6.sp, bottom: 6.sp),
              padding: EdgeInsets.all(16.sp),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  PopupMapMenuButton(
                    selectMenuId: widget.selectMenu,
                    onCallBack: widget.selectMenuMap,
                    menuList: MapConstants.menuMapOption(),
                    isVisible: true,
                  ),
                  if (widget.isShowMenuModelOption()) ...[
                    SizedBox(height: 20.sp),
                    _buildOptionButton(),
                  ],
                  SizedBox(height: 20.sp),
                  _buildCameraButton(),
                  // if (widget.selectMenu == MapMenuEnum.weather && widget.buildWeatherControlButtons != null)
                  //   AnimatedBuilder(
                  //     animation: widget.weatherController,
                  //     builder: (context, child) {
                  //       return Column(children: widget.buildWeatherControlButtons!());
                  //     },
                  //   ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildOptionButton() {
    return GestureDetector(
      onTap: widget.selectOptionMap,
      child: Container(
        decoration: BoxDecoration(
            color: Colors.white, borderRadius: BorderRadius.circular(12), boxShadow: [BoxShadow(color: Colors.grey.withOpacity(0.5), spreadRadius: 1, blurRadius: 5, offset: const Offset(0, 1))]),
        padding: const EdgeInsets.all(12),
        child: Icon(MapUtils.iconOptionMap(widget.selectMenu), color: Colors.black),
      ),
    );
  }

  Widget _buildCameraButton() {
    return GestureDetector(
      onTap: widget.selectPositionMap,
      child: Container(
        decoration: BoxDecoration(
            color: Colors.white, borderRadius: BorderRadius.circular(12), boxShadow: [BoxShadow(color: Colors.grey.withOpacity(0.5), spreadRadius: 1, blurRadius: 5, offset: const Offset(0, 1))]),
        padding: const EdgeInsets.all(12),
        child: Icon(
          widget.isAtCurrentLocation ? Icons.home : Icons.near_me_outlined,
          color: widget.isAtCurrentLocation ? Colors.blue : Colors.black,
        ),
      ),
    );
  }
}

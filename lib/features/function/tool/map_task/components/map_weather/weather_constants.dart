import 'package:flutter/material.dart';
import 'package:hainong/features/function/tool/map_task/source/colors/map_weather_color.dart';

class WeatherConstants {
  // Weather Layer Types
  static const String temperature = 'temperature';
  static const String precipitation = 'precipitation';
  static const String wind = 'wind';
  static const String pressure = 'pressure';
  static const String humidity = 'humidity';
  static const String cloud = 'cloud';

  static const double weatherPanelWidth = 320;
  static const double timelineHeight = 300;
  static const double verticalScaleWidth = 40;
  static const double verticalScaleHeight = 600;

  static const Duration panelAnimationDuration = Duration(milliseconds: 400);
  static const Duration loadingAnimationDuration = Duration(milliseconds: 150);
  static const Duration timelineAnimationDuration = Duration(milliseconds: 300);
  static const Duration playTimerDuration = Duration(milliseconds: 3000);

  static const int totalTimelineDays = 5;
  static const int timeStepsPerDay = 8;
  static const int totalTimelineSteps = totalTimelineDays * timeStepsPerDay; // 40 steps

  static const Map<String, String> weatherLayerMapping = {
    temperature: 'temperature',
    precipitation: 'rainfall',
    wind: 'weather_points',
    pressure: 'weather_points',
    humidity: 'weather_points',
    cloud: 'weather_points'
  };

  static String getApiLayerName(String uiLayerId) => weatherLayerMapping[uiLayerId] ?? 'temperature';

  static String generateTimeStep(DateTime date, int hourIndex) {
    final day = date.day.toString().padLeft(2, '0');
    final month = date.month.toString().padLeft(2, '0');
    final year = date.year.toString().substring(2);
    final hour = (hourIndex * 3 + 1).toString().padLeft(2, '0');
    return "${day}-${month}-${year}_$hour";
  }

  static String getTimeStepFromIndex(int timelineIndex) {
    final now = DateTime.now();
    final dayDiff = timelineIndex ~/ timeStepsPerDay;
    final hourIndex = timelineIndex % timeStepsPerDay;
    final date = DateTime(now.year, now.month, now.day + dayDiff);
    return generateTimeStep(date, hourIndex);
  }

  static int getIndexFromTimeStep(String timeStep) {
    try {
      final parts = timeStep.split('_');
      if (parts.length != 2) return 0;

      final datePart = parts[0];
      final hourPart = parts[1];

      final dateParts = datePart.split('-');
      if (dateParts.length != 3) return 0;

      final day = int.parse(dateParts[0]);
      final month = int.parse(dateParts[1]);
      final year = int.parse("20${dateParts[2]}");
      final hour = int.parse(hourPart);

      final date = DateTime(year, month, day);
      final now = DateTime.now();
      final nowStart = DateTime(now.year, now.month, now.day);
      final dayDiff = date.difference(nowStart).inDays;

      final hourIndex = (hour - 1) ~/ 3;
      return dayDiff * timeStepsPerDay + hourIndex;
    } catch (e) {
      debugPrint("WeatherConstants: Error parsing time step $timeStep: $e");
      return 0;
    }
  }

  static List<String> getTimeStepsForWeatherType(String weatherType) {
    final timeSteps = <String>[];
    final now = DateTime.now();

    for (int dayDiff = 0; dayDiff <= 4; dayDiff++) {
      final date = DateTime(now.year, now.month, now.day + dayDiff);
      for (int hourIndex = 0; hourIndex <= 7; hourIndex++) {
        timeSteps.add(generateTimeStep(date, hourIndex));
      }
    }

    return timeSteps;
  }

  static String parseTimeStepToReadable(String timeStep) {
    try {
      final parts = timeStep.split('_');
      if (parts.length != 2) return timeStep;

      final datePart = parts[0];
      final hourPart = parts[1];

      final dateParts = datePart.split('-');
      if (dateParts.length != 3) return timeStep;

      final day = dateParts[0];
      final month = dateParts[1];
      final year = "20${dateParts[2]}";
      final hour = hourPart;

      return 'Ngày $day/$month/$year, Giờ $hour:00';
    } catch (e) {
      debugPrint("WeatherConstants: Error parsing time step $timeStep: $e");
      return timeStep;
    }
  }

  static int getHourFromTimeStep(String timeStep) {
    try {
      final parts = timeStep.split('_');
      if (parts.length == 2) {
        return int.parse(parts[1]);
      }
    } catch (e) {
      debugPrint("WeatherConstants: Error parsing hour from $timeStep: $e");
    }
    return 1;
  }

  static int getDayFromTimeStep(String timeStep) {
    try {
      final parts = timeStep.split('_');
      if (parts.length == 2) {
        final dateParts = parts[0].split('-');
        if (dateParts.length == 3) {
          return int.parse(dateParts[0]);
        }
      }
    } catch (e) {
      debugPrint("WeatherConstants: Error parsing day from $timeStep: $e");
    }
    return 1;
  }
}

class WeatherLayerModel {
  final String id;
  final String title;
  final IconData icon;
  final Color color;
  final String description;
  final String unit;

  const WeatherLayerModel({required this.id, required this.title, required this.icon, required this.color, required this.description, required this.unit});
}

class WeatherLayersConfig {
  static const List<WeatherLayerModel> weatherLayers = [
    WeatherLayerModel(id: WeatherConstants.temperature, title: 'Nhiệt độ', icon: Icons.thermostat_rounded, color: MapWeatherColor.temperatureAccent, description: 'Thông tin nhiệt độ', unit: '°C'),
    WeatherLayerModel(
        id: WeatherConstants.precipitation, title: 'Lượng mưa', icon: Icons.water_drop_rounded, color: MapWeatherColor.precipitationAccent, description: 'Thông tin lượng mưa', unit: 'mm'),
    WeatherLayerModel(id: WeatherConstants.wind, title: 'Gió', icon: Icons.air, color: MapWeatherColor.windAccent, description: 'Thông tin gió và hướng gió', unit: 'm/s'),
  ];

  static WeatherLayerModel getLayerById(String id) => weatherLayers.firstWhere((layer) => layer.id == id, orElse: () => weatherLayers.first);

  static LinearGradient getLayerGradient(String layerId) => MapWeatherColor.getLayerGradient(layerId);

  static String getMinValue(String layerId) => MapWeatherColor.getMinValue(layerId);

  static String getMaxValue(String layerId) => MapWeatherColor.getMaxValue(layerId);
}

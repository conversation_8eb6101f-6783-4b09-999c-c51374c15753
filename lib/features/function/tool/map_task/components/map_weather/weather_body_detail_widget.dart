import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hainong/common/ui/label_custom.dart';
import 'package:hainong/features/function/tool/map_task/models/map_address_model.dart';
import 'package:hainong/features/function/tool/map_task/models/map_response_model.dart';
import 'package:hainong/features/function/tool/map_task/utils/map_utils.dart';
import 'package:trackasia_gl/trackasia_gl.dart';

/// Reusable weather info row widget
class WeatherInfoRow extends StatelessWidget {
  final String label;
  final String value;
  final Color labelColor;
  final Color valueColor;

  const WeatherInfoRow({
    Key? key,
    required this.label,
    required this.value,
    this.labelColor = Colors.black87,
    this.valueColor = Colors.black87,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: 20.sp),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          LabelCustom(label, color: labelColor),
          LabelCustom(value, color: valueColor),
        ],
      ),
    );
  }
}

/// Reusable weather image widget
class WeatherImageWidget extends StatelessWidget {
  final String? imageUrl;
  final double size;

  const WeatherImageWidget({
    Key? key,
    required this.imageUrl,
    this.size = 0.36,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (imageUrl == null || imageUrl!.isEmpty) {
      return const SizedBox.shrink();
    }

    return SizedBox(
      width: size.sw,
      height: size.sw,
      child: Image.asset(
        "assets/images/v9/map/weather/$imageUrl.png",
        fit: BoxFit.fill,
        errorBuilder: (context, error, stackTrace) => Container(
          width: size.sw,
          height: size.sw,
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: BorderRadius.circular(8.sp),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.image_not_supported, size: 48.sp, color: Colors.grey[400]),
              SizedBox(height: 8.sp),
              Text(
                "Không tìm thấy hình ảnh",
                style: TextStyle(fontSize: 12.sp, color: Colors.grey[600]),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Reusable coordinate display widget
class CoordinateWidget extends StatelessWidget {
  final LatLng point;

  const CoordinateWidget({Key? key, required this.point}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: 20.sp, right: 20.sp, top: 20.sp),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Image.asset("assets/images/v9/map/ic_map_rate.png", width: 28, height: 28),
          SizedBox(width: 20.sp),
          Text(
            "Tọa độ: Lat: ${point.latitude.toStringAsFixed(4)} Lng: ${point.longitude.toStringAsFixed(4)}",
            style: const TextStyle(fontSize: 14),
          ),
        ],
      ),
    );
  }
}

Widget addressWeatherWidget(ValueNotifier<MapAddressModel> addressMap, {bool isAddressFull = true}) {
  return ValueListenableBuilder<MapAddressModel>(
    valueListenable: addressMap,
    builder: (context, address, child) {
      final addressText = isAddressFull ? address.address_full ?? "" : '${address.district_name}, ${address.province_name}';
      final displayAddress = addressText.isEmpty ? "Không xác định địa chỉ" : addressText;
      return Padding(
        padding: EdgeInsets.symmetric(horizontal: 20.sp, vertical: 20.sp),
        child: Text(
          displayAddress,
          style: TextStyle(
            fontSize: 16,
            color: addressText.isEmpty ? Colors.grey[600] : Colors.black87,
            fontStyle: addressText.isEmpty ? FontStyle.italic : FontStyle.normal,
          ),
        ),
      );
    },
  );
}

Widget tabBodyOverviewWeatherWidget(
  BuildContext context,
  MapGeoJsonModel data,
  LatLng point,
  ValueNotifier<bool> isPlay,
  Function(bool isPlay, String url) callBackAudio, {
  ValueNotifier<String?>? externalAudioLink,
  Function(LatLng point)? onRetryAudioLink,
  ValueNotifier<bool>? isLoadingAudio,
}) {
  final imageUrl = data.data['icon'] ?? data.data['weather_status_icon'];
  final dataAudioLink = data.data['audio_link'];

  return ValueListenableBuilder<String?>(
    valueListenable: externalAudioLink ?? ValueNotifier<String?>(null),
    builder: (context, externalAudioValue, child) {
      final audioLink = externalAudioValue?.isNotEmpty == true ? externalAudioValue : dataAudioLink;
      final hasAudio = audioLink != null && audioLink.toString().isNotEmpty;

      return SingleChildScrollView(
          child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Divider(height: 0.2, color: Colors.grey.withOpacity(0.4)),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 20.sp),
          child: GestureDetector(
            onTap: () {
              if (hasAudio) {
                callBackAudio(!isPlay.value, audioLink.toString());
              } else if (onRetryAudioLink != null) {
                // Defensive check to prevent multiple retry calls
                if (isLoadingAudio?.value != true) {
                  onRetryAudioLink(point);
                }
              }
            },
            child: ValueListenableBuilder<bool>(
              valueListenable: isPlay,
              builder: (context, playValue, child) {
                return ValueListenableBuilder<bool>(
                  valueListenable: isLoadingAudio ?? ValueNotifier(false),
                  builder: (context, loadingValue, child) {
                    final isLoading = loadingValue;
                    return Row(
                      children: [
                        IconButton(
                          icon: hasAudio
                              ? (playValue
                                  ? const Stack(
                                      children: [SizedBox(width: 24, height: 24, child: CircularProgressIndicator(color: Colors.blue, strokeWidth: 4.0)), Icon(Icons.play_arrow, color: Colors.red)])
                                  : const Icon(Icons.mic_none, size: 32, color: Colors.blue))
                              : (isLoading
                                  ? const SizedBox(width: 24, height: 24, child: CircularProgressIndicator(color: Colors.orange, strokeWidth: 3.0))
                                  : const Icon(Icons.refresh, size: 24, color: Colors.orange)),
                          onPressed: () {
                            if (hasAudio) {
                              callBackAudio(!playValue, audioLink);
                            } else if (!isLoading && onRetryAudioLink != null) {
                              // Double check to prevent race condition
                              if (isLoadingAudio?.value != true) {
                                onRetryAudioLink(point);
                              }
                            }
                          },
                        ),
                        Text(hasAudio ? (playValue ? "Dừng nghe" : "Nhấn để nghe") : (isLoading ? "Đang tải audio..." : "Nhấn để tải audio"),
                            style: TextStyle(fontSize: 16, color: hasAudio ? Colors.blue : Colors.orange))
                      ],
                    );
                  },
                );
              },
            ),
          ),
        ),
        Divider(height: 0.2, color: Colors.grey.withOpacity(0.4)),
        CoordinateWidget(point: point),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 80.sp, vertical: 20.sp),
          child: Column(
            children: [
              WeatherImageWidget(imageUrl: imageUrl),
              SizedBox(height: 20.sp),
              WeatherInfoRow(label: 'Hiện tại: ', value: '${data.data['weather_status'] ?? "N/A"}', labelColor: Colors.green, valueColor: Colors.green),
              WeatherInfoRow(label: 'Khả năng mưa: ', value: '${data.data['pop'] ?? data.data['percent_rain'] ?? "N/A"}%'),
              WeatherInfoRow(label: 'Nhiệt độ: ', value: '${MapUtils.convertMapKeyToDouble(data.data, "temp").round()}°'),
              WeatherInfoRow(
                label: 'Nhiệt độ giao động: ',
                value: '${MapUtils.convertMapKeyToDouble(data.data, "temp_min").round()}°-${MapUtils.convertMapKeyToDouble(data.data, "temp_max").round()}°',
              ),
              WeatherInfoRow(label: 'Chỉ số bức xạ: ', value: '${MapUtils.convertMapKeyToDouble(data.data, "uv").round()}'),
              WeatherInfoRow(label: 'Sức gió: ', value: '${MapUtils.convertMapKeyToDouble(data.data, "wind_speed").round()} km/h'),
              WeatherInfoRow(label: 'Độ ẩm: ', value: '${MapUtils.convertMapKeyToDouble(data.data, "humidity").round()} %'),
            ],
          ),
        ),
      ]));
    },
  );
}

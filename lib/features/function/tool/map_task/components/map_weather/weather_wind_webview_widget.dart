import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:webview_flutter/webview_flutter.dart';

class WeatherWindWebViewWidget extends StatefulWidget {
  final String currentTimestamp;
  final int currentTimeIndex;
  final bool isVisible;
  final VoidCallback? onWebViewReady;
  final Function(double, double, double)? onCameraChanged;
  final double? syncZoom;
  final double? syncLng;
  final double? syncLat;
  final bool needsForceSync;
  final bool? isPlaying;
  final bool? weatherIconsVisible;

  const WeatherWindWebViewWidget({
    Key? key,
    required this.currentTimestamp,
    required this.currentTimeIndex,
    required this.isVisible,
    this.onWebViewReady,
    this.onCameraChanged,
    this.syncZoom,
    this.syncLng,
    this.syncLat,
    this.needsForceSync = false,
    this.isPlaying,
    this.weatherIconsVisible,
  }) : super(key: key);

  @override
  WeatherWindWebViewWidgetState createState() => WeatherWindWebViewWidgetState();
  

  static void setWeatherIconsVisibilityFromKey(GlobalKey<WeatherWindWebViewWidgetState> key, bool visible) {
    key.currentState?.setWeatherIconsVisibility(visible);
  }
}

class WeatherWindWebViewWidgetState extends State<WeatherWindWebViewWidget> {
  final Completer<WebViewController> _controller = Completer<WebViewController>();
  bool _isLoading = true;
  String? _htmlContent;

  @override
  void initState() {
    super.initState();
    _loadHtmlContent();
  }

  @override
  void didUpdateWidget(WeatherWindWebViewWidget oldWidget) {
    super.didUpdateWidget(oldWidget);


    if (widget.currentTimestamp != oldWidget.currentTimestamp) {

      _syncTimelineWithWebView();
    }


    if (widget.currentTimeIndex != oldWidget.currentTimeIndex) {

      _syncTimelineWithWebView();
    }


    if (widget.isPlaying != oldWidget.isPlaying) {

      _syncPlayStateWithWebView();
    }


    if (widget.weatherIconsVisible != oldWidget.weatherIconsVisible) {

      _syncWeatherIconsVisibilityWithWebView();
    }


    if (widget.syncZoom != null &&
        widget.syncLng != null &&
        widget.syncLat != null &&
        (widget.syncZoom != oldWidget.syncZoom || widget.syncLng != oldWidget.syncLng || widget.syncLat != oldWidget.syncLat || widget.needsForceSync != oldWidget.needsForceSync)) {


      // If force sync is needed, try multiple times with delays
      if (widget.needsForceSync) {
        _syncCameraWithWebView();

        // Retry after delays để đảm bảo WebView ready
        Future.delayed(const Duration(milliseconds: 300), () {
          if (mounted) _syncCameraWithWebView();
        });

        Future.delayed(const Duration(milliseconds: 800), () {
          if (mounted) _syncCameraWithWebView();
        });
      } else {
        _syncCameraWithWebView();
      }
    }
  }

  void _syncTimelineWithWebView() async {
    if (!mounted || _isLoading) return;



    try {
      final WebViewController controller = await _controller.future;

      // Simplified JavaScript - chỉ cập nhật wind data
      final jsCode = '''
        try {
          console.log('Flutter: Updating wind data to timestamp ${widget.currentTimestamp}');
          
          if (typeof weatherMap !== 'undefined' && weatherMap && weatherMap.loadWeatherData) {
            // Set wind as current weather type
            weatherMap.currentWeatherType = 'wind';
            
            // Update current timestamp
            weatherMap.currentTimestamp = '${widget.currentTimestamp}';
            
            // Load wind data for the new timestamp
            weatherMap.loadWeatherData('wind', '${widget.currentTimestamp}');
            
            // Update hidden dropdown (for internal state)
            const selectElement = document.getElementById('timestamp-select');
            if (selectElement) {
              selectElement.value = '${widget.currentTimestamp}';
            }
            
            console.log('Flutter: Wind data updated successfully to ${widget.currentTimestamp}');
          } else {
            console.log('Flutter: weatherMap not ready, retrying...');
            
            // Retry mechanism
            setTimeout(function() {
              if (typeof weatherMap !== 'undefined' && weatherMap && weatherMap.loadWeatherData) {
                weatherMap.currentWeatherType = 'wind';
                weatherMap.currentTimestamp = '${widget.currentTimestamp}';
                weatherMap.loadWeatherData('wind', '${widget.currentTimestamp}');
                
                const selectElement = document.getElementById('timestamp-select');
                if (selectElement) {
                  selectElement.value = '${widget.currentTimestamp}';
                }
                
                console.log('Flutter: Wind data updated on retry to ${widget.currentTimestamp}');
              }
            }, 1000);
          }
        } catch (error) {
          console.error('Flutter: Error updating wind data:', error);
        }
      ''';

      await controller.runJavascript(jsCode);
    } catch (e) {
      debugPrint('Flutter: Error syncing timeline with WebView: $e');
    }
  }

  void _syncPlayStateWithWebView() async {
    if (!mounted || _isLoading) return;

    try {
      final WebViewController controller = await _controller.future;

      final jsCode = '''
        try {
          console.log('WebView: Syncing play state - isPlaying: ${widget.isPlaying}');
          
          if (typeof weatherMap !== 'undefined' && weatherMap) {
            if (${widget.isPlaying == true}) {
              // Start WebView animation
              weatherMap.runAnimation();
              console.log('WebView: Animation started');
            } else {
              // Stop WebView animation
              weatherMap.stopAnimation();
              console.log('WebView: Animation stopped');
            }
          }
        } catch (error) {
          console.error('WebView: Error syncing play state:', error);
        }
      ''';

      await controller.runJavascript(jsCode);
    } catch (e) {
      debugPrint('Flutter: Error syncing play state: $e');
    }
  }

  void _syncWeatherIconsVisibilityWithWebView() async {
    if (!mounted || _isLoading) return;

    try {
      final WebViewController controller = await _controller.future;

      final jsCode = '''
        try {
          console.log('WebView: Syncing weather icons visibility - visible: ${widget.weatherIconsVisible}');
          
          if (typeof setWeatherIconsVisibility !== 'undefined') {
            setWeatherIconsVisibility(${widget.weatherIconsVisible == true});
            console.log('WebView: Weather icons visibility updated to ${widget.weatherIconsVisible}');
          } else {
            console.log('WebView: setWeatherIconsVisibility function not found, retrying...');
            
            // Retry mechanism
            setTimeout(function() {
              if (typeof setWeatherIconsVisibility !== 'undefined') {
                setWeatherIconsVisibility(${widget.weatherIconsVisible == true});
                console.log('WebView: Weather icons visibility updated on retry to ${widget.weatherIconsVisible}');
              }
            }, 1000);
          }
        } catch (error) {
          console.error('WebView: Error syncing weather icons visibility:', error);
        }
      ''';

      await controller.runJavascript(jsCode);
    } catch (e) {

    }
  }

  // Public method để gọi từ bên ngoài
  void setWeatherIconsVisibility(bool visible) async {
    if (!mounted || _isLoading) return;

    try {
      final WebViewController controller = await _controller.future;

      final jsCode = '''
        try {
          console.log('WebView: Setting weather icons visibility - visible: $visible');
          
          if (typeof setWeatherIconsVisibility !== 'undefined') {
            setWeatherIconsVisibility($visible);
            console.log('WebView: Weather icons visibility set to $visible');
          } else {
            console.log('WebView: setWeatherIconsVisibility function not found');
          }
        } catch (error) {
          console.error('WebView: Error setting weather icons visibility:', error);
        }
      ''';

      await controller.runJavascript(jsCode);
    } catch (e) {

    }
  }

  void _syncCameraWithWebView() async {
    if (!mounted || widget.syncZoom == null || widget.syncLng == null || widget.syncLat == null) return;



    try {
      final WebViewController controller = await _controller.future;

      // Enhanced JavaScript with better retry logic
      final jsCode = '''
        try {
          console.log('WebView: Attempting camera sync - zoom: ${widget.syncZoom}, lng: ${widget.syncLng}, lat: ${widget.syncLat}');
          
          function attemptSync(retryCount = 0) {
            if (typeof window.syncCameraFromFlutter === 'function' && typeof weatherMap !== 'undefined' && weatherMap && weatherMap.map) {
              console.log('WebView: Executing camera sync...');
              window.syncCameraFromFlutter(${widget.syncZoom}, ${widget.syncLng}, ${widget.syncLat});
              return true;
            } else {
              if (retryCount < 5) {
                console.log('WebView: Camera sync not ready, retry ' + (retryCount + 1) + '/5 in ' + (500 * (retryCount + 1)) + 'ms');
                setTimeout(() => attemptSync(retryCount + 1), 500 * (retryCount + 1));
              } else {
                console.error('WebView: Failed to sync camera after 5 attempts');
              }
              return false;
            }
          }
          
          attemptSync();
          
        } catch (error) {
          console.error('WebView: Error in camera sync:', error);
        }
      ''';

      await controller.runJavascript(jsCode);
    } catch (e) {
      debugPrint('WebView: Error syncing camera: $e');
      // Retry after delay if failed
      Future.delayed(const Duration(milliseconds: 1000), () {
        if (mounted) _syncCameraWithWebView();
      });
    }
  }

  Future<void> _loadHtmlContent() async {
    try {
      final String htmlContent = await rootBundle.loadString('assets/html/weather_wind_map.html');
      setState(() => _htmlContent = htmlContent);
    } catch (e) {
      debugPrint('WebView: Error loading HTML content: $e');
      setState(() => _isLoading = false);
    }
  }

  void _initializeWindMode() async {
    if (!mounted) return;

    try {
      final WebViewController controller = await _controller.future;
      final zoom = widget.syncZoom ?? 5.0;
      final lng = widget.syncLng ?? 106.0;
      final lat = widget.syncLat ?? 16.0;
      final jsCode = '''
        try {
          console.log('Flutter: Initializing wind mode with camera - zoom: $zoom, lng: $lng, lat: $lat, timestamp: ${widget.currentTimestamp}');
          
          // Wait for weatherMap to be ready
          if (typeof weatherMap !== 'undefined' && weatherMap) {
            // Force wind mode
            weatherMap.currentWeatherType = 'wind';
            
            // Set current timestamp from Flutter
            weatherMap.currentTimestamp = '${widget.currentTimestamp}';
            
            // Set wind radio as checked (even though it's hidden)
            const windRadio = document.getElementById('map-type-wind');
            if (windRadio) {
              windRadio.checked = true;
            }
            
            // Update legend visibility to show only wind
            if (weatherMap.updateLegendVisibility) {
              weatherMap.updateLegendVisibility();
            }
            
            // Set camera position từ TrackAsiaMap
            if (weatherMap.map) {
              weatherMap.map.setCenter([$lng, $lat]);
              weatherMap.map.setZoom($zoom);
            }
            
            // Update timestamp select element
            const selectElement = document.getElementById('timestamp-select');
            if (selectElement) {
              selectElement.value = '${widget.currentTimestamp}';
            }
            
            // Load wind data for current timestamp
            weatherMap.loadWeatherData('wind', '${widget.currentTimestamp}');
            
            // Start animation if playing state is true
            if (${widget.isPlaying == true}) {
              setTimeout(() => {
                weatherMap.runAnimation();
                console.log('Flutter: Auto-started animation after init');
              }, 500);
            }
            
            console.log('Flutter: Wind mode initialized successfully with camera sync and timestamp ${widget.currentTimestamp}');
          } else {
            // Retry if not ready
            setTimeout(() => {
              if (typeof weatherMap !== 'undefined' && weatherMap) {
                weatherMap.currentWeatherType = 'wind';
                weatherMap.currentTimestamp = '${widget.currentTimestamp}';
                
                const windRadio = document.getElementById('map-type-wind');
                if (windRadio) windRadio.checked = true;
                if (weatherMap.updateLegendVisibility) weatherMap.updateLegendVisibility();
                
                // Set camera position on retry
                if (weatherMap.map) {
                  weatherMap.map.setCenter([$lng, $lat]);
                  weatherMap.map.setZoom($zoom);
                }
                
                const selectElement = document.getElementById('timestamp-select');
                if (selectElement) {
                  selectElement.value = '${widget.currentTimestamp}';
                }
                
                weatherMap.loadWeatherData('wind', '${widget.currentTimestamp}');
                
                // Start animation if playing state is true
                if (${widget.isPlaying == true}) {
                  setTimeout(() => {
                    weatherMap.runAnimation();
                    console.log('Flutter: Auto-started animation after retry init');
                  }, 500);
                }
                
                console.log('Flutter: Wind mode initialized on retry with camera sync and timestamp ${widget.currentTimestamp}');
              }
            }, 1500);
          }
        } catch (error) {
          console.error('Flutter: Error initializing wind mode:', error);
        }
      ''';

      await controller.runJavascript(jsCode);
    } catch (e) {
      debugPrint('Flutter: Error initializing wind mode: $e');
    }
  }

  void _handleWebViewMessage(String message) {
    try {


      if (message.startsWith('locationClicked:')) {
        final locationName = message.split(':')[1];

        // Handle location click
        _showSnackBar('Đã click vào: $locationName');
      } else if (message.startsWith('test:')) {

        _showSnackBar('Test message: ${message.split(':')[1]}');
      } else if (message.startsWith('ready:')) {

        // WebView is ready, sync timeline
        Future.delayed(const Duration(milliseconds: 500), () {
          _syncTimelineWithWebView();
        });
      } else if (message.startsWith('zoomChanged:') || message.startsWith('cameraChanged:')) {
        // Handle camera changes from WebView
        final parts = message.split(':');
        if (parts.length >= 4) {
          try {
            final zoom = double.parse(parts[1]);
            final lng = double.parse(parts[2]);
            final lat = double.parse(parts[3]);



            // Notify parent about camera change
            widget.onCameraChanged?.call(zoom, lng, lat);
          } catch (e) {
            debugPrint('Flutter: Error handling camera change: $e');
          }
        }
      }
    } catch (e) {
      debugPrint('Flutter: Error handling WebView message: $e');
    }
  }

  void _showSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  JavascriptChannel _flutterBridgeJavascriptChannel() {
    return JavascriptChannel(
      name: 'FlutterBridge',
      onMessageReceived: (JavascriptMessage message) {
        _handleWebViewMessage(message.message);
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isVisible) {
      return const SizedBox.shrink();
    }

    return SizedBox(
      width: double.infinity,
      height: double.infinity,
      child: Stack(
        children: [
          // WebView
          if (_htmlContent != null)
            WebView(
              initialUrl: 'about:blank',
              javascriptMode: JavascriptMode.unrestricted,
              onWebViewCreated: (WebViewController webViewController) async {
                _controller.complete(webViewController);
                await webViewController.loadHtmlString(_htmlContent!);
                Future.delayed(const Duration(milliseconds: 1000), () {
                  _initializeWindMode();
                });
              },
              onPageStarted: (String url) {
                setState(() => _isLoading = true);
              },
              onPageFinished: (String url) {
                setState(() => _isLoading = false);
                widget.onWebViewReady?.call();
                Future.delayed(const Duration(milliseconds: 2000), () {
                  _syncTimelineWithWebView();
                });
              },
              javascriptChannels: <JavascriptChannel>{
                _flutterBridgeJavascriptChannel(),
              },
              backgroundColor: const Color(0x00000000),
            ),
        ],
      ),
    );
  }
}

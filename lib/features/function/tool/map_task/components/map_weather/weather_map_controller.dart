import 'dart:async';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:hainong/features/function/tool/map_task/components/map_weather/weather_body_detail_widget.dart';
import 'package:hainong/features/function/tool/map_task/components/map_weather/weather_constants.dart';
import 'package:hainong/features/function/tool/map_task/map_constants.dart';
import 'package:hainong/features/function/tool/map_task/map_task_bloc.dart';
import 'package:hainong/features/function/tool/map_task/models/map_address_model.dart';
import 'package:hainong/features/function/tool/map_task/models/map_response_model.dart';
import 'package:hainong/features/function/tool/map_task/source/trackasia_weather_map_source.dart';
import 'package:hainong/features/function/tool/map_task/utils/map_audio_util.dart';
import 'package:hainong/features/function/tool/map_task/utils/map_dialog_utils.dart';
import 'package:hainong/features/function/tool/map_task/utils/map_utils.dart';
import 'package:trackasia_gl/trackasia_gl.dart';

class WeatherMapController with ChangeNotifier {
  String _selectedWeatherLayer = WeatherConstants.temperature;
  bool _showWeatherPanel = false;
  final bool _isWeatherLoading = false;
  bool _isTimelineActive = false;
  bool _isPlaying = false;
  bool _weatherIconsVisible = true;
  int _currentTimeIndex = 0;

  bool _showWindWebView = false;
  bool _showWindPanel = true;
  double _lastKnownZoom = 5.0;
  double _lastKnownLng = 106.0;
  double _lastKnownLat = 16.0;
  bool _isSyncingCamera = false;
  bool _needsWebViewSync = false;

  Timer? _playTimer;
  late List<DateTime> _timelineHours;
  AnimationController? _weatherPanelAnimationController;
  Animation<Offset>? _weatherSlideAnimation;

  TrackAsiaMapController? _mapController;
  MapTaskBloc? _bloc;
  BuildContext? _context;
  ValueNotifier<bool>? _isPlay;
  ValueNotifier<MapAddressModel>? _addressMap;
  final ValueNotifier<String?> _externalAudioLink = ValueNotifier<String?>(null);
  final ValueNotifier<bool> _isLoadingAudio = ValueNotifier<bool>(false);

  // Weather data state
  final ValueNotifier<MapGeoJsonModel?> _weatherData = ValueNotifier<MapGeoJsonModel?>(null);
  final ValueNotifier<MapGeoJsonModel?> _currentWeatherData = ValueNotifier<MapGeoJsonModel?>(null);
  final ValueNotifier<LatLng?> _weatherPoint = ValueNotifier<LatLng?>(null);
  final ValueNotifier<LatLng?> _currentWeatherPoint = ValueNotifier<LatLng?>(null);
  final ValueNotifier<MapAddressModel?> _addressModel = ValueNotifier<MapAddressModel?>(null);

  // Getters
  String get selectedWeatherLayer => _selectedWeatherLayer;
  bool get showWeatherPanel => _showWeatherPanel;
  bool get isWeatherLoading => _isWeatherLoading;
  bool get isTimelineActive => _isTimelineActive;
  bool get isPlaying => _isPlaying;
  bool get weatherIconsVisible => _weatherIconsVisible;
  int get currentTimeIndex => _currentTimeIndex;
  List<DateTime> get timelineHours => _timelineHours;
  Animation<Offset>? get weatherSlideAnimation => _weatherSlideAnimation;
  String? get externalAudioLink => _externalAudioLink.value;
  ValueNotifier<bool> get isLoadingAudio => _isLoadingAudio;
  ValueNotifier<MapGeoJsonModel?> get currentWeatherData => _currentWeatherData;
  ValueNotifier<LatLng?> get currentWeatherPoint => _currentWeatherPoint;
  bool get showWindWebView => _showWindWebView;
  bool get showWindPanel => _showWindPanel;
  double get lastKnownZoom => _lastKnownZoom;
  double get lastKnownLng => _lastKnownLng;
  double get lastKnownLat => _lastKnownLat;
  bool get needsWebViewSync => _needsWebViewSync;

  //================[INITIALIZATION]================//

  void initialize({
    required TickerProvider tickerProvider,
    required BuildContext context,
    MapTaskBloc? bloc,
    ValueNotifier<MapAddressModel>? addressMap,
    ValueNotifier<bool>? isPlay,
    dynamic clusterSource,
  }) {
    _context = context;
    _bloc = bloc;
    _isPlay = isPlay;
    _addressMap = addressMap;
    _initializeWeatherTimeline();
    _initializeAnimationControllers(tickerProvider);
  }

  void setMapController(TrackAsiaMapController? controller) {
    _mapController = controller;
    if (_mapController != null) {
      TrackAsiaWeatherMapSource.addSourceWeatherMap(_mapController, MapConstants.sourceIdWeatherMap);
      if (_showWeatherPanel) {
        final apiLayerName = WeatherConstants.getApiLayerName(_selectedWeatherLayer);
        TrackAsiaWeatherMapSource.setLayerWeatherMap(
          _mapController,
          MapConstants.sourceIdWeatherMap,
          apiLayerName,
          TrackAsiaWeatherMapSource.getTimeStepFromIndex(_currentTimeIndex),
          timelineIndex: _currentTimeIndex,
        );
      }
    }
  }

  void _initializeWeatherTimeline() {
    _timelineHours = [];
    final now = DateTime.now();
    for (int i = 0; i < WeatherConstants.totalTimelineSteps; i++) {
      final dayDiff = i ~/ WeatherConstants.timeStepsPerDay;
      final hourIndex = i % WeatherConstants.timeStepsPerDay;
      final hour = hourIndex * 3 + 1;
      final targetTime = DateTime(now.year, now.month, now.day + dayDiff, hour, 0, 0, 0);
      _timelineHours.add(targetTime);
    }
  }

  void _initializeAnimationControllers(TickerProvider tickerProvider) {
    _weatherPanelAnimationController = AnimationController(
      duration: WeatherConstants.panelAnimationDuration,
      vsync: tickerProvider,
    );
    _weatherSlideAnimation = Tween<Offset>(
      begin: const Offset(-1, 0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _weatherPanelAnimationController!,
      curve: Curves.easeInOutCubic,
    ));
  }

  //================[WEATHER PANEL MANAGEMENT]================//

  void initializeWeatherMap(Function() onCallBack) async {
    _selectedWeatherLayer = WeatherConstants.temperature;
    _showWeatherPanel = true;
    _isTimelineActive = true;
    _isPlaying = false;
    _currentTimeIndex = 0;
    _weatherIconsVisible = true;
    TrackAsiaWeatherMapSource.isVisible = _weatherIconsVisible;
    _showWindWebView = (_selectedWeatherLayer == WeatherConstants.wind);
    _showWindPanel = true;
    _weatherPanelAnimationController?.forward();
    if (_mapController != null) {
      await TrackAsiaWeatherMapSource.addSourceWeatherMap(_mapController, MapConstants.sourceIdWeatherMap);
      TrackAsiaWeatherMapSource.isVisible = _weatherIconsVisible;
      final apiLayerName = WeatherConstants.getApiLayerName(_selectedWeatherLayer);
      await TrackAsiaWeatherMapSource.setLayerWeatherMap(
        _mapController,
        MapConstants.sourceIdWeatherMap,
        apiLayerName,
        TrackAsiaWeatherMapSource.getTimeStepFromIndex(_currentTimeIndex),
        timelineIndex: _currentTimeIndex,
      );
      await TrackAsiaWeatherMapSource.updateWeatherLayerVisibility(_mapController, apiLayerName, TrackAsiaWeatherMapSource.getTimeStepFromIndex(_currentTimeIndex));
      await _loadCurrentWeatherData();

      debugPrint('WeatherMapController: Initialized with clouds ${_weatherIconsVisible ? "visible" : "hidden"}');
    }

    notifyListeners();
  }

  void updateWeatherLayer(String newLayer) async {
    if (_selectedWeatherLayer == newLayer) return;
    _selectedWeatherLayer = newLayer;

    if (newLayer == WeatherConstants.wind) {
      _showWindWebView = true;
      _needsWebViewSync = true;

      await _forceSyncCameraToWebView();

      if (_mapController != null) {
        final apiLayerNames = ['temperature', 'rainfall', 'weather_points'];
        for (final layerName in apiLayerNames) {
          try {
            await _mapController!.setLayerVisibility("${MapConstants.keyLayerWeatherMap}_$layerName", false);
          } catch (e) {
            debugPrint('WeatherMapController: Error hiding layer $layerName: $e');
          }
        }
      }
    } else {
      _showWindWebView = false;

      if (_mapController != null) {
        final apiLayerName = WeatherConstants.getApiLayerName(newLayer);
        final apiLayerNames = ['temperature', 'rainfall', 'weather_points'];
        for (final layerName in apiLayerNames) {
          try {
            await _mapController!.setLayerVisibility("${MapConstants.keyLayerWeatherMap}_$layerName", false);
          } catch (e) {
            debugPrint('WeatherMapController: Error hiding layer $layerName: $e');
          }
        }

        try {
          await _mapController!.setLayerVisibility("${MapConstants.keyLayerWeatherMap}_$apiLayerName", true);
          await TrackAsiaWeatherMapSource.updateWeatherForTimeStepInstant(
            _mapController,
            MapConstants.sourceIdWeatherMap,
            apiLayerName,
            _currentTimeIndex,
          );
        } catch (e) {
          debugPrint('WeatherMapController: Error showing layer: $e');
        }

        if (_weatherIconsVisible) {
          try {
            await _mapController!.setLayerVisibility("${MapConstants.keyLayerWeatherMap}_weather_points", true);
          } catch (e) {
            debugPrint('WeatherMapController: Error showing weather icons: $e');
          }
        }
      }
    }

    notifyListeners();

    HapticFeedback.mediumImpact();
  }

  void toggleWeatherPanel() {
    if (_selectedWeatherLayer == WeatherConstants.wind && _showWeatherPanel) {
      _showWindPanel = !_showWindPanel;
      _isTimelineActive = _showWindPanel;
      _showWindWebView = true;

      if (_showWindPanel) {
        _weatherPanelAnimationController?.forward();
      } else {
        _weatherPanelAnimationController?.reverse();
        _isPlaying = false;
        stopTimeline();
      }
    } else {
      _showWeatherPanel = !_showWeatherPanel;
      if (_showWeatherPanel) {
        _weatherPanelAnimationController?.forward();
        _isTimelineActive = true;
        _showWindPanel = true;
        if (_selectedWeatherLayer == WeatherConstants.wind) {
          _showWindWebView = true;
        }
      } else {
        _weatherPanelAnimationController?.reverse();
        _isTimelineActive = false;
        _isPlaying = false;
        _showWindPanel = false;
        _showWindWebView = false;
        stopTimeline();
      }
    }

    notifyListeners();
    HapticFeedback.lightImpact();
  }

  String getCurrentTimeStepForWebView() {
    return TrackAsiaWeatherMapSource.getTimeStepFromIndex(_currentTimeIndex);
  }

  int getCurrentTimeIndexForWebView() {
    return _currentTimeIndex;
  }

  //================[CAMERA SYNC METHODS]================//

  void updateCameraFromWebView(double zoom, double lng, double lat) {
    if (_isSyncingCamera) return;
    _lastKnownZoom = zoom;
    _lastKnownLng = lng;
    _lastKnownLat = lat;
    if (_mapController != null && _selectedWeatherLayer != WeatherConstants.wind) {
      _syncCameraToTrackAsiaMap();
    }
  }

  Future<void> syncCameraToWebView() async {
    if (_mapController == null || _isSyncingCamera) return;

    try {
      final cameraPosition = _mapController!.cameraPosition;
      if (cameraPosition != null) {
        _lastKnownZoom = cameraPosition.zoom;
        _lastKnownLng = cameraPosition.target.longitude;
        _lastKnownLat = cameraPosition.target.latitude;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('WeatherMapController: Error in camera sync: $e');
    }
  }

  Future<void> _forceSyncCameraToWebView() async {
    if (_mapController == null || _isSyncingCamera) return;

    try {
      final cameraPosition = _mapController!.cameraPosition;
      if (cameraPosition != null) {
        _lastKnownZoom = cameraPosition.zoom;
        _lastKnownLng = cameraPosition.target.longitude;
        _lastKnownLat = cameraPosition.target.latitude;
        _needsWebViewSync = true;
        notifyListeners();
        Future.delayed(const Duration(milliseconds: 500), () {
          if (_needsWebViewSync) {
            notifyListeners();
          }
        });

        Future.delayed(const Duration(milliseconds: 1500), () {
          if (_needsWebViewSync) {
            notifyListeners();
          }
        });
      }
    } catch (e) {
      debugPrint('WeatherMapController: Error in camera sync: $e');
    }
  }

  void clearWebViewSyncFlag() {
    _needsWebViewSync = false;
  }

  void _syncCameraToTrackAsiaMap() async {
    if (_mapController == null || _isSyncingCamera) return;

    try {
      _isSyncingCamera = true;

      await _mapController!.animateCamera(
        CameraUpdate.newLatLngZoom(
          LatLng(_lastKnownLat, _lastKnownLng),
          _lastKnownZoom,
        ),
      );
    } catch (e) {
      debugPrint('WeatherMapController: Error in camera sync: $e');
    } finally {
      Future.delayed(const Duration(milliseconds: 1000), () {
        _isSyncingCamera = false;
      });
    }
  }

  Function(bool)? _onWeatherIconsVisibilityChanged;

  void setWeatherIconsVisibilityCallback(Function(bool)? callback) {
    _onWeatherIconsVisibilityChanged = callback;
  }

  void toggleWeatherIconsVisibility([bool? forceValue]) {
    _weatherIconsVisible = forceValue ?? !_weatherIconsVisible;
    TrackAsiaWeatherMapSource.isVisible = _weatherIconsVisible;
    TrackAsiaWeatherMapSource.updateWeatherLayerVisibility(_mapController, TrackAsiaWeatherMapSource.currentWeatherType, TrackAsiaWeatherMapSource.currentTimestamp);
    if (_onWeatherIconsVisibilityChanged != null) {
      _onWeatherIconsVisibilityChanged!(_weatherIconsVisible);
    }
    notifyListeners();
    if (forceValue == null) {
      HapticFeedback.lightImpact();
    }
  }

  bool _timelineVisible = true;
  bool _verticalScaleVisible = true;

  bool get timelineVisible => _timelineVisible;
  bool get verticalScaleVisible => _verticalScaleVisible;

  void toggleTimelineVisibility() {
    _timelineVisible = !_timelineVisible;

    notifyListeners();
  }

  void toggleVerticalScaleVisibility() {
    _verticalScaleVisible = !_verticalScaleVisible;

    notifyListeners();
  }

  //================[TIMELINE MANAGEMENT]================//

  void toggleWeatherTimeline() {
    _isTimelineActive = !_isTimelineActive;
    if (!_isTimelineActive) {
      _isPlaying = false;
      stopTimeline();
    }
    notifyListeners();
  }

  void togglePlayPause(bool shouldPlay) {
    _isPlaying = shouldPlay;
    if (_isPlaying) {
      startTimeline();
    } else {
      stopTimeline();
    }
    notifyListeners();
    HapticFeedback.mediumImpact();
  }

  void startTimeline() {
    _playTimer = Timer.periodic(WeatherConstants.playTimerDuration, (timer) {
      _currentTimeIndex = (_currentTimeIndex + 1) % _timelineHours.length;
      if (_selectedWeatherLayer == WeatherConstants.wind) {
        notifyListeners();
      } else {
        final apiLayerName = WeatherConstants.getApiLayerName(_selectedWeatherLayer);
        TrackAsiaWeatherMapSource.updateWeatherForTimeStepSmooth(
          _mapController,
          MapConstants.sourceIdWeatherMap,
          apiLayerName,
          _currentTimeIndex,
        );
      }
      notifyListeners();
    });
  }

  void stopTimeline() => _playTimer?.cancel();

  void onTimelineSeek(double value) {
    _currentTimeIndex = (value * (_timelineHours.length - 1)).round();
    if (_selectedWeatherLayer == WeatherConstants.wind) {
      // Just notify listeners to update WebView with new timestamp
      notifyListeners();
      HapticFeedback.selectionClick();
      return;
    }

    // For other layers, update TrackAsia layers
    if (_mapController == null) return;
    try {
      final apiLayerName = WeatherConstants.getApiLayerName(_selectedWeatherLayer);
      TrackAsiaWeatherMapSource.updateWeatherForTimeStepInstant(_mapController, MapConstants.sourceIdWeatherMap, apiLayerName, _currentTimeIndex);
      notifyListeners();
      HapticFeedback.selectionClick();
    } catch (e) {
      debugPrint('WeatherMapController: Error in camera sync: $e');
    }
  }

  void resetTimeline() {
    stopTimeline();
    _currentTimeIndex = 0;
    _isPlaying = false;

    // For wind layer, only update the timeline index (WebView will handle the rest)
    if (_selectedWeatherLayer == WeatherConstants.wind) {
      // Just notify listeners to update WebView with new timestamp
      notifyListeners();
      HapticFeedback.mediumImpact();
      return;
    }

    // For other layers, update TrackAsia layers
    if (_mapController == null) return;
    try {
      final apiLayerName = WeatherConstants.getApiLayerName(_selectedWeatherLayer);
      TrackAsiaWeatherMapSource.updateWeatherForTimeStepInstant(_mapController, MapConstants.sourceIdWeatherMap, apiLayerName, _currentTimeIndex);
      notifyListeners();
      HapticFeedback.mediumImpact();
    } catch (e) {
      debugPrint('WeatherMapController: Error in camera sync: $e');
    }
  }

  Future<void> handleWeatherFeatureTap(Point<double> point) async {
    if (_mapController == null) return;

    // Cancel any ongoing operations before starting new request
    cancelAudioLoading();

    final latLng = await _mapController!.toLatLng(point);
    _bloc?.add(LoadDetailWeatherEvent(latLng, isShowDetail: true));
  }

  void handleWeatherAudio(bool isRun, String url) {
    if (_isPlay != null) {
      _isPlay!.value = isRun;
    }
    MapAudioUtil.handleWeatherAudio(isRun, url);
  }

  //================[DATA MANAGEMENT]================//

  void updateExternalAudioLink(String? audioLink) {
    _externalAudioLink.value = audioLink;
    if (audioLink != null && audioLink.isNotEmpty) {
      _isLoadingAudio.value = false;
    }
    notifyListeners();
  }

  void clearExternalAudioLink() {
    _externalAudioLink.value = null;
    notifyListeners();
  }

  void setCurrentWeatherData(MapGeoJsonModel data, LatLng point) {
    _currentWeatherData.value = data;
    _currentWeatherPoint.value = point;
    notifyListeners();
  }

  void showWeatherDetailBottomSheet(MapGeoJsonModel data, LatLng point, Function() onComplete) {
    // Clear external audio link only (loading state is already set properly in map_task_page)
    clearExternalAudioLink();

    _weatherData.value = data;
    _weatherPoint.value = point;
    if (_context != null && _addressMap != null) {
      final addressWidget = addressWeatherWidget(_addressMap!);
      final contentWidget = tabBodyOverviewWeatherWidget(
        _context!,
        data,
        point,
        _isPlay!,
        handleWeatherAudio,
        externalAudioLink: _externalAudioLink,
        onRetryAudioLink: handleReloadAudio,
        isLoadingAudio: _isLoadingAudio,
      );
      MapDialogUtils.showWeatherInfoBottomSheet(_context!, addressWidget, contentWidget).then((_) {
        handleWeatherAudio(false, '');
        cancelAudioLoading();
        clearExternalAudioLink();
        onComplete();
      });
    } else {
      onComplete();
    }

    notifyListeners();
  }

  void removeWeatherSource() {
    if (_mapController == null) return;
    try {
      TrackAsiaWeatherMapSource.removeSourceWeatherMap(_mapController, MapConstants.sourceIdWeatherMap, MapConstants.keyLayerWeatherMap);
    } catch (e) {
      debugPrint('WeatherMapController: Error in camera sync: $e');
    }
  }

  void updateCurrentLocationAddress(MapAddressModel address) => _addressModel.value = address;

  //================[LOCATION MARKER MANAGEMENT]================//

  Future<void> _loadCurrentWeatherData() async {
    final position = await MapUtils.getCurrentPositionMap();
    _bloc?.add(LoadCurrentDetailWeatherEvent(LatLng(position.latitude, position.longitude)));
  }

  void addCurrentLocationMarker(Function() onTap) async {
    if (_mapController == null) return;

    try {
      await _removeCurrentLocationMarker();
      await _mapController!.addGeoJsonSource("current_location_source", {
        "type": "FeatureCollection",
        "features": [
          {
            "type": "Feature",
            "geometry": {
              "type": "Point",
              "coordinates": [_currentWeatherPoint.value!.longitude, _currentWeatherPoint.value!.latitude]
            },
            "properties": {}
          }
        ]
      });

      await _mapController!.addSymbolLayer(
        "current_location_source",
        "current_location_icon",
        const SymbolLayerProperties(
          iconImage: "assets/images/v9/map/ic_map_location.png",
          iconSize: 0.6,
          iconAllowOverlap: true,
        ),
      );

      _mapController!.onSymbolTapped.add((symbol) {
        if (symbol.options.geometry == _currentWeatherPoint.value) {
          onTap();
        }
      });
    } catch (e) {
      debugPrint('WeatherMapController: Error in camera sync: $e');
    }
  }

  void handleReloadAudio(LatLng point) {
    // Cancel any ongoing audio loading first
    cancelAudioLoading();
    _isLoadingAudio.value = true;
    _bloc?.add(LoadAudioLinkEvent(point));
    Timer(const Duration(seconds: 15), () {
      if (_isLoadingAudio.value) {
        _isLoadingAudio.value = false;
      }
    });
  }

  void handleAudioLoadingSuccess() => _isLoadingAudio.value = false;

  void handleAudioLoadingFailed() => _isLoadingAudio.value = false;

  void handleLoadingStateChange(bool isLoading) => _isLoadingAudio.value = isLoading;

  /// Cancel all ongoing audio loading requests
  void cancelAudioLoading() {
    // Cancel at bloc level first
    if (_bloc is MapTaskBloc) {
      (_bloc as MapTaskBloc).cancelCurrentRequest();
      (_bloc as MapTaskBloc).cancelAudioLoading();
    }

    // Reset local state
    _isLoadingAudio.value = false;
    clearExternalAudioLink();

    debugPrint('WeatherMapController: Audio loading cancelled completely');
  }

  Future<void> _removeCurrentLocationMarker() async {
    if (_mapController == null) return;

    try {
      final layerIds = (await _mapController!.getLayerIds()).cast<String>();
      final sourceIds = (await _mapController!.getSourceIds()).cast<String>();

      if (layerIds.contains("current_location_icon")) {
        await _mapController!.removeLayer("current_location_icon");
      }
      await Future.delayed(const Duration(milliseconds: 50));
      if (sourceIds.contains("current_location_source")) {
        await _mapController!.removeSource("current_location_source");
      }
    } catch (e) {
      debugPrint('WeatherMapController: Error in camera sync: $e');
    }
  }

  //================[CLEANUP]================//

  void resetWeatherState() {
    // Cancel any ongoing operations first
    cancelAudioLoading();

    // Reset UI state variables
    _showWeatherPanel = false;
    _isTimelineActive = false;
    _isPlaying = false;
    _currentTimeIndex = 0;
    _externalAudioLink.value = null;
    _isLoadingAudio.value = false;
    _weatherData.value = null;
    _weatherPoint.value = null;
    _showWindWebView = false;
    _showWindPanel = false;
    _needsWebViewSync = false;
    _isSyncingCamera = false;
    stopTimeline();
    _weatherPanelAnimationController?.reset();
    if (_mapController != null) {
      _removeCurrentLocationMarker();
      _cleanupWeatherLayers();
    }

    notifyListeners();
  }

  Future<void> _cleanupWeatherLayers() async {
    if (_mapController == null) return;

    try {
      await TrackAsiaWeatherMapSource.removeAllLayerWeatherMap(_mapController);
      await Future.delayed(const Duration(milliseconds: 50));
      TrackAsiaWeatherMapSource.removeSourceWeatherMap(_mapController, MapConstants.sourceIdWeatherMap, MapConstants.keyLayerWeatherMap);
      await _mapController?.clearSymbols();

      debugPrint("Weather map layers and sources successfully removed");
    } catch (e) {
      debugPrint("Error cleaning up weather layers: $e");
    }
  }

  @override
  void dispose() {
    stopTimeline();
    _mapController = null;
    _weatherPanelAnimationController?.dispose();
    _weatherData.dispose();
    _currentWeatherData.dispose();
    _weatherPoint.dispose();
    _currentWeatherPoint.dispose();
    _addressModel.dispose();
    _externalAudioLink.dispose();
    _isLoadingAudio.dispose();
    super.dispose();
  }
}

Widget buildWeatherControlButton({required IconData icon, required bool isActive, required VoidCallback onTap}) {
  return GestureDetector(
    onTap: onTap,
    child: Container(
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(12), boxShadow: [BoxShadow(color: Colors.grey.withOpacity(0.5), spreadRadius: 1, blurRadius: 5, offset: const Offset(0, 1))]),
      padding: const EdgeInsets.all(12),
      child: Icon(icon, color: isActive ? Colors.blue : Colors.black),
    ),
  );
}

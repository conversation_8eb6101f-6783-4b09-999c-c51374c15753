import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hainong/features/function/tool/map_task/map_constants.dart';
import 'package:hainong/features/function/tool/map_task/source/colors/map_weather_color.dart';
import 'package:hainong/features/function/tool/map_task/utils/map_button_utils.dart';
import 'package:hainong/features/function/tool/map_task/utils/map_dialog_utils.dart';

class WeatherTimelineControls {
  static Widget buildTimelinePopup({
    required Widget timelineContent,
    required VoidCallback onDragStart,
    required Function(double) onDragUpdate,
    required VoidCallback onDragEnd,
    required double height,
  }) {
    return GestureDetector(
      onPanStart: (_) => onDragStart(),
      onPanUpdate: (details) => onDragUpdate(details.delta.dy),
      onPanEnd: (_) => onDragEnd(),
      child: ClipRRect(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(32.sp),
          topRight: Radius.circular(32.sp),
        ),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: MapWeatherColor.popupBlurSigma, sigmaY: MapWeatherColor.popupBlurSigma),
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOutCubic,
            height: height,
            decoration: _getTimelinePopupDecoration(),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildDragHandle(),
                SizedBox(height: 10.sp),
                Expanded(child: Padding(padding: EdgeInsets.fromLTRB(20.sp, 0, 20.sp, 0), child: timelineContent)),
              ],
            ),
          ),
        ),
      ),
    );
  }

  static Widget buildTimelineHeader({
    required bool isPlaying,
    required VoidCallback onTogglePlayPause,
    required VoidCallback onResetTimeline,
    required Widget timeDisplay,
    required Color activeColor,
    required double buttonSize,
    Color? disabledColor,
  }) {
    return Row(
      children: [
        MapButtonUtils.buildTimelineButton(
          icon: isPlaying ? Icons.pause_rounded : Icons.play_arrow_rounded,
          onPressed: onTogglePlayPause,
          isActive: isPlaying,
          activeColor: activeColor,
          size: buttonSize,
          disabledColor: disabledColor ?? Colors.grey,
        ),
        SizedBox(width: 20.sp),
        MapButtonUtils.buildTimelineButton(
          icon: Icons.replay_rounded,
          onPressed: onResetTimeline,
          isActive: false,
          activeColor: activeColor,
          size: buttonSize,
          disabledColor: disabledColor ?? Colors.grey,
        ),
        SizedBox(width: 20.sp),
        Expanded(child: timeDisplay),
      ],
    );
  }

  static Widget buildTimeDisplay({
    required String formattedTime,
    required Color textColor,
    required double fontSize,
    FontWeight? fontWeight,
  }) {
    return Text(
      formattedTime,
      style: TextStyle(
        fontSize: fontSize.sp,
        fontWeight: fontWeight ?? FontWeight.normal,
        color: textColor,
      ),
    );
  }

  static Widget buildDayLabels({
    required List<String> weekdayNames,
    required int currentDayIndex,
    required Color activeColor,
    required int totalDays,
  }) {
    return SizedBox(
      height: 56.sp,
      child: Row(
        children: List.generate(totalDays, (dayIndex) {
          final isCurrentDay = currentDayIndex == dayIndex;
          return Expanded(
            child: Container(
              margin: EdgeInsets.only(right: dayIndex < totalDays - 1 ? 2.sp : 0),
              decoration: BoxDecoration(
                color: isCurrentDay ? activeColor.withOpacity(0.1) : Colors.transparent,
                borderRadius: BorderRadius.circular(36.sp),
                border: isCurrentDay ? Border.all(color: activeColor, width: 1) : null,
              ),
              child: Center(
                child: Text(
                  weekdayNames[dayIndex],
                  style: TextStyle(
                    fontSize: MapConstants.defaultContentFontSize.sp,
                    fontWeight: isCurrentDay ? FontWeight.bold : FontWeight.w500,
                    color: isCurrentDay ? activeColor : MapWeatherColor.mediumGrey,
                  ),
                ),
              ),
            ),
          );
        }),
      ),
    );
  }

  static Widget buildTimelineSlider({
    required BuildContext context,
    required double value,
    required Function(double) onChanged,
    required Function(double)? onChangeStart,
    required Function(double) onChangeEnd,
    required Color activeColor,
    required int totalDays,
  }) {
    return Stack(
      children: [
        _buildSliderTrack(),
        ..._buildDaySeparators(context, totalDays),
        _buildSlider(context, value, onChanged, onChangeStart, onChangeEnd, activeColor),
      ],
    );
  }

  static List<String> getWeekdayNamesStartingFromToday(DateTime startDate, int totalDays) {
    const allWeekdays = ['Chủ nhật', 'Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7'];
    final startWeekday = startDate.weekday % 7;
    List<String> weekdayNames = [];
    for (int i = 0; i < totalDays; i++) {
      final weekdayIndex = (startWeekday + i) % 7;
      weekdayNames.add(allWeekdays[weekdayIndex]);
    }
    return weekdayNames;
  }

  static String formatDateTime(DateTime dateTime) {
    const months = ['Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6', 'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'];
    final dayName = _getDayName(dateTime);
    return '$dayName, ${dateTime.day} ${months[dateTime.month - 1]} - ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  static BoxDecoration _getTimelinePopupDecoration() {
    return MapDialogUtils.getPopupBackground(borderRadius: 32).copyWith(
      borderRadius: BorderRadius.only(topLeft: Radius.circular(32.sp), topRight: Radius.circular(32.sp)),
    );
  }

  static Widget _buildDragHandle() {
    return Container(
      margin: EdgeInsets.only(top: 8.sp),
      child: Container(
        width: 80.sp,
        height: 10.sp,
        decoration: BoxDecoration(color: MapWeatherColor.getDragHandleColor(), borderRadius: BorderRadius.circular(24.sp)),
      ),
    );
  }

  static Widget _buildSliderTrack() {
    return Positioned(
      left: 8,
      right: 8,
      top: 38.sp,
      child: Container(
        height: 18.sp,
        decoration: BoxDecoration(
          color: Colors.grey.withOpacity(0.2),
          borderRadius: BorderRadius.circular(7.sp),
        ),
      ),
    );
  }

  static List<Widget> _buildDaySeparators(BuildContext context, int totalDays) {
    return List.generate(totalDays - 1, (index) {
      final separatorIndex = index + 1;
      final position = separatorIndex / totalDays;
      final availableWidth = MediaQuery.of(context).size.width - 80.sp;
      return Positioned(
        left: position * availableWidth,
        top: 34.sp,
        child: Container(
          width: 28.sp,
          height: 28.sp,
          decoration: BoxDecoration(color: Colors.grey.withOpacity(0.7), borderRadius: BorderRadius.circular(32.sp)),
        ),
      );
    });
  }

  static Widget _buildSlider(BuildContext context, double value, Function(double) onChanged, Function(double)? onChangeStart, Function(double) onChangeEnd, Color activeColor) {
    return Positioned.fill(
      child: Container(
        padding: EdgeInsets.zero,
        child: SliderTheme(
          data: SliderTheme.of(context).copyWith(
            activeTrackColor: activeColor,
            inactiveTrackColor: Colors.transparent,
            thumbColor: activeColor,
            overlayColor: activeColor.withOpacity(0.2),
            thumbShape: RoundSliderThumbShape(enabledThumbRadius: 20.sp),
            trackHeight: 16.sp,
            overlayShape: SliderComponentShape.noThumb,
            showValueIndicator: ShowValueIndicator.never,
            tickMarkShape: const RoundSliderTickMarkShape(tickMarkRadius: 0),
            activeTickMarkColor: Colors.transparent,
            inactiveTickMarkColor: Colors.transparent,
          ),
          child: Slider(value: value, min: 0.0, max: 1.0, divisions: null, onChanged: onChanged, onChangeStart: onChangeStart, onChangeEnd: onChangeEnd),
        ),
      ),
    );
  }

  static String _getDayName(DateTime dateTime) {
    const allWeekdays = ['Chủ nhật', 'Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7'];
    final weekdayIndex = dateTime.weekday % 7;
    return allWeekdays[weekdayIndex];
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hainong/features/function/tool/map_task/components/map_weather/weather_constants.dart';

/// Weather dropdown widget similar to model dropdown
class WeatherDropdownWidget extends StatefulWidget {
  final String selectedWeatherLayer;
  final Function(String) onLayerChanged;
  final bool autoOpen;

  const WeatherDropdownWidget({super.key, required this.selectedWeatherLayer, required this.onLayerChanged, this.autoOpen = false});

  @override
  State<WeatherDropdownWidget> createState() => _WeatherDropdownWidgetState();
}

class _WeatherDropdownWidgetState extends State<WeatherDropdownWidget> {
  bool _isExpanded = false;
  static final ValueNotifier<bool> _autoOpenNotifier = ValueNotifier<bool>(false);

  @override
  void initState() {
    super.initState();
    if (widget.autoOpen) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) setState(() => _isExpanded = true);
      });
    }
    _autoOpenNotifier.addListener(_handleAutoOpen);
  }

  @override
  void didUpdateWidget(WeatherDropdownWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.autoOpen && !oldWidget.autoOpen) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) setState(() => _isExpanded = true);
      });
    }
  }

  @override
  void dispose() {
    _autoOpenNotifier.removeListener(_handleAutoOpen);
    super.dispose();
  }

  void _handleAutoOpen() {
    if (_autoOpenNotifier.value && mounted) {
      setState(() => _isExpanded = true);
      _autoOpenNotifier.value = false;
    }
  }

  void _onLayerTap(String layerId) {
    setState(() => _isExpanded = false);
    widget.onLayerChanged(layerId);
  }

  @override
  Widget build(BuildContext context) {
    final currentLayer = WeatherLayersConfig.getLayerById(widget.selectedWeatherLayer);

    return Positioned(
      top: 20.sp,
      left: 20.sp,
      child: SafeArea(
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12.sp),
            boxShadow: [BoxShadow(color: Colors.grey.withOpacity(0.5), spreadRadius: 1.0, blurRadius: 5.0, offset: const Offset(0, 1))],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              // Dropdown header
              GestureDetector(
                onTap: () => setState(() => _isExpanded = !_isExpanded),
                child: Container(
                  width: 0.36.sw,
                  padding: EdgeInsets.all(16.sp),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12.sp),
                    border: Border.all(color: Colors.blue[200]!, width: 1.5),
                    boxShadow: [BoxShadow(color: Colors.black.withOpacity(0.12), spreadRadius: 0, blurRadius: 8, offset: const Offset(0, 2))],
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Row(
                          children: [
                            Icon(currentLayer.icon, color: currentLayer.color, size: 62.sp),
                            SizedBox(width: 12.sp),
                            Flexible(child: Text(currentLayer.title, style: TextStyle(color: currentLayer.color, fontWeight: FontWeight.w600, fontSize: 38.sp), overflow: TextOverflow.ellipsis)),
                          ],
                        ),
                      ),
                      Icon(_isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down, color: currentLayer.color, size: 52.sp),
                    ],
                  ),
                ),
              ),
              // Dropdown items
              if (_isExpanded) ...[
                Container(
                  width: 0.36.sw,
                  padding: EdgeInsets.symmetric(vertical: 12.sp),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: WeatherLayersConfig.weatherLayers.map((layer) {
                      final isSelected = widget.selectedWeatherLayer == layer.id;
                      return Container(
                        padding: EdgeInsets.all(16.sp),
                        child: GestureDetector(
                          onTap: () => _onLayerTap(layer.id),
                          child: Container(
                            width: double.infinity,
                            color: Colors.transparent,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Expanded(
                                  child: Row(
                                    children: [
                                      Icon(layer.icon, color: layer.color, size: 62.sp),
                                      SizedBox(width: 16.sp),
                                      Flexible(child: Text(layer.title, style: TextStyle(color: Colors.black87, fontSize: 38.sp, fontWeight: FontWeight.w600), overflow: TextOverflow.ellipsis)),
                                    ],
                                  ),
                                ),
                                if (isSelected) Icon(Icons.check_circle, size: 52.sp, color: layer.color),
                              ],
                            ),
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

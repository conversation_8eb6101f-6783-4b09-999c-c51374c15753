import 'package:flutter/material.dart';
import 'package:hainong/features/function/tool/map_task/components/map_nutrition/nutrition_tree_change_widget.dart';
import 'package:hainong/features/function/tool/map_task/components/map_nutrition/nutrition_tree_color_widget.dart';
import 'package:hainong/features/function/tool/map_task/components/map_nutrition/nutrition_tree_select_widget.dart';
import 'package:hainong/features/function/tool/map_task/map_constants.dart';
import 'package:hainong/features/function/tool/map_task/models/map_item_menu_model.dart';

class LayerTreeValueWidget extends StatelessWidget {
  final List<ColorItemMap> colorTreeValueList;
  final MenuItemMap? treeNutritionType;
  final MenuItemMap? treeType;
  final Function(MenuItemMap) onTreeTypeSelected;
  final Function(MenuItemMap, {bool isUpdate}) onNutritionTypeSelected;
  final ValueNotifier<bool> showTreeSelectionPopup;
  final bool Function() isShowLevelTree;

  const LayerTreeValueWidget({
    Key? key,
    required this.colorTreeValueList,
    required this.treeNutritionType,
    required this.treeType,
    required this.onTreeTypeSelected,
    required this.onNutritionTypeSelected,
    required this.showTreeSelectionPopup,
    required this.isShowLevelTree,
  }) : super(key: key);

  bool _needsTreeSelection() {
    if (treeNutritionType == null) return false;
    String nutritionType = treeNutritionType!.value.toLowerCase();
    return ["n", "p", "k", "npk", "ph", "ec", "cec", "om"].contains(nutritionType);
  }

  bool _shouldShowColorWidget() => !(_needsTreeSelection() && treeType == null);

  @override
  Widget build(BuildContext context) {
    return Stack(children: [
      if (_shouldShowColorWidget())
        Align(
            alignment: Alignment.topLeft,
            child: LayerNutritionTreeColorWidget(
              colorTreeValueList,
              treeNutritionType,
              treeType: treeType,
              onTreeSelect: () => showTreeSelectionPopup.value = true,
            )),
      if (isShowLevelTree())
        Align(
            alignment: Alignment.bottomLeft,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                LayerNutritionTreeChangeWidget(
                  selectTreeLayer: treeNutritionType,
                  menuList: MapConstants.menuNutritionalTypeBottomModel(),
                  onCallBack: (value) => onNutritionTypeSelected(value, isUpdate: true),
                ),
                LayerNutritionTreeSelectWidget(treeType: treeType, onTreeSelected: onTreeTypeSelected),
              ],
            )),
    ]);
  }
}

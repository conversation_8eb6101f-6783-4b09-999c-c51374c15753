import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hainong/features/function/tool/map_task/map_constants.dart';
import 'package:hainong/features/function/tool/map_task/models/map_item_menu_model.dart';
import 'package:hainong/features/function/tool/map_task/utils/map_utils.dart';

class LayerNutritionTreeColorWidget extends StatefulWidget {
  const LayerNutritionTreeColorWidget(this._treeValueList, this.type, {this.treeType, this.onPopupToggle, this.onTreeSelect, super.key});
  final dynamic type;
  final dynamic treeType;
  final List<ColorItemMap> _treeValueList;
  final VoidCallback? onPopupToggle;
  final VoidCallback? onTreeSelect;

  @override
  State<LayerNutritionTreeColorWidget> createState() => _LayerNutritionTreeColorWidgetState();
}

class _LayerNutritionTreeColorWidgetState extends State<LayerNutritionTreeColorWidget> {
  bool _showColorCodes = false;
  bool _isPopupVisible = false;

  String _getTreeImage() {
    if (widget.treeType?.value == null) return '';

    if (widget.treeType!.value == 'lua') {
      return MapConstants.menuTreeRiceItem().image;
    }

    final fruitList = MapConstants.menuFruitItem();
    final fruitItem = fruitList.firstWhere(
      (item) => item.value == widget.treeType!.value,
      orElse: () => MenuItemMap(image: ''),
    );

    return fruitItem.image ?? '';
  }

  List<ColorItemMap> _getNutrientColors(String nutrientType) {
    if (widget.treeType?.value == null) return [];
    return MapUtils.menuLayerColor(nutrientType, widget.treeType!.value);
  }

  bool _isNPKMode() {
    return widget.type?.value == 'npk';
  }

  bool _hasDataToShow() {
    if (_isNPKMode()) {
      return widget.treeType?.value != null;
    } else {
      return widget._treeValueList.isNotEmpty;
    }
  }

  Widget _buildNutrientSection(String nutrientType, String title) {
    final colors = _getNutrientColors(nutrientType);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(vertical: 8.sp, horizontal: 12.sp),
          margin: EdgeInsets.only(bottom: 8.sp),
          decoration: BoxDecoration(
            color: _getNutrientHeaderColor(nutrientType),
            borderRadius: BorderRadius.circular(8.sp),
            border: Border.all(color: _getNutrientBorderColor(nutrientType), width: 1),
          ),
          child: Row(
            children: [
              Container(
                padding: EdgeInsets.all(4.sp),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(6.sp),
                ),
                child: Image.asset(
                  _getNutrientImagePath(nutrientType),
                  width: 20.sp,
                  height: 20.sp,
                  errorBuilder: (context, error, stackTrace) {
                    return Icon(
                      Icons.science,
                      color: _getNutrientIconColor(nutrientType),
                      size: 16.sp,
                    );
                  },
                ),
              ),
              SizedBox(width: 8.sp),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    color: _getNutrientTextColor(nutrientType),
                    fontSize: 26.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              GestureDetector(
                onTap: () {
                  setState(() {
                    _showColorCodes = !_showColorCodes;
                  });
                },
                child: Container(
                  padding: EdgeInsets.all(4.sp),
                  decoration: BoxDecoration(
                    color: _showColorCodes ? Colors.blue[100] : Colors.grey[200],
                    borderRadius: BorderRadius.circular(4.sp),
                    border: Border.all(
                      color: _showColorCodes ? Colors.blue[300]! : Colors.grey[400]!,
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        _showColorCodes ? Icons.code_off : Icons.code,
                        size: 16.sp,
                        color: _showColorCodes ? Colors.blue[700] : Colors.grey[600],
                      ),
                      SizedBox(width: 2.sp),
                      Text(
                        'HEX',
                        style: TextStyle(
                          fontSize: 8.sp,
                          fontWeight: FontWeight.w600,
                          color: _showColorCodes ? Colors.blue[700] : Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),

        // Bảng màu hoặc thông báo không có dữ liệu
        if (colors.isEmpty) ...[
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(16.sp),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12.sp),
              border: Border.all(color: Colors.grey[200]!, width: 1),
              color: Colors.grey[50],
            ),
            child: Center(
              child: Text(
                'Không có dữ liệu cho loại dinh dưỡng này',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 18.sp,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
          ),
        ] else ...[
          // Bảng màu nhỏ gọn cho loại dinh dưỡng này
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12.sp),
              border: Border.all(color: Colors.grey[200]!, width: 1),
              color: Colors.grey[50],
            ),
            child: Container(
              padding: EdgeInsets.all(8.sp),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Dải màu liền mạch bên trái
                  Container(
                    width: 32.w,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(6.sp),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 2,
                          offset: const Offset(0, 1),
                        ),
                      ],
                    ),
                    child: Column(
                      children: colors.asMap().entries.map((entry) {
                        int index = entry.key;
                        ColorItemMap item = entry.value;
                        bool isFirst = index == 0;
                        bool isLast = index == colors.length - 1;

                        return Container(
                          width: 28.w,
                          height: 32.sp,
                          decoration: BoxDecoration(
                            color: MapUtils.hexToColor(item.color),
                            borderRadius: BorderRadius.only(
                              topLeft: isFirst ? Radius.circular(6.sp) : Radius.zero,
                              topRight: isFirst ? Radius.circular(6.sp) : Radius.zero,
                              bottomLeft: isLast ? Radius.circular(6.sp) : Radius.zero,
                              bottomRight: isLast ? Radius.circular(6.sp) : Radius.zero,
                            ),
                            border: Border(
                              bottom: !isLast
                                  ? BorderSide(
                                      color: Colors.white.withOpacity(0.3),
                                      width: 0.5,
                                    )
                                  : BorderSide.none,
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  ),

                  SizedBox(width: 8.sp),

                  // Danh sách hàm lượng bên phải
                  Expanded(
                    child: Column(
                      children: colors.asMap().entries.map((entry) {
                        int index = entry.key;
                        ColorItemMap item = entry.value;

                        return Container(
                          height: 32.sp,
                          margin: EdgeInsets.only(bottom: index < colors.length - 1 ? 1.sp : 0),
                          padding: EdgeInsets.symmetric(horizontal: 8.sp, vertical: 4.sp),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(6.sp),
                            border: Border.all(color: Colors.grey[200]!, width: 0.5),
                          ),
                          child: Row(
                            children: [
                              // Cột mã màu hex (nếu được bật)
                              if (_showColorCodes) ...[
                                SizedBox(
                                  width: 90.w,
                                  child: Container(
                                    padding: EdgeInsets.symmetric(horizontal: 4.sp, vertical: 2.sp),
                                    decoration: BoxDecoration(
                                      color: Colors.grey[100],
                                      borderRadius: BorderRadius.circular(3.sp),
                                      border: Border.all(color: Colors.grey[300]!, width: 0.5),
                                    ),
                                    child: Text(
                                      item.color,
                                      style: TextStyle(
                                        color: Colors.grey[700],
                                        fontSize: 16.sp,
                                        fontWeight: FontWeight.w500,
                                        fontFamily: 'monospace',
                                      ),
                                      textAlign: TextAlign.center,
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ),
                                SizedBox(width: 6.sp),
                              ],
                              // Chấm màu indicator nhỏ
                              Container(
                                width: 6.sp,
                                height: 6.sp,
                                decoration: BoxDecoration(
                                  color: MapUtils.hexToColor(item.color),
                                  shape: BoxShape.circle,
                                  border: Border.all(color: Colors.white, width: 1),
                                ),
                              ),
                              SizedBox(width: 6.sp),
                              // Text hàm lượng
                              Expanded(
                                child: Text(
                                  item.value2.isNotEmpty ? '${item.value} / ${item.value2}' : item.value,
                                  style: TextStyle(
                                    color: _getNutrientTextColor(nutrientType),
                                    fontSize: 22.sp,
                                    fontWeight: FontWeight.w500,
                                    letterSpacing: 0.1,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],

        SizedBox(height: 12.sp),
      ],
    );
  }

  // Lấy màu header cho từng loại dinh dưỡng
  Color _getNutrientHeaderColor(String nutrientType) {
    switch (nutrientType) {
      case 'n':
        return Colors.green[50]!;
      case 'p':
        return Colors.orange[50]!;
      case 'k':
        return Colors.red[50]!;
      case 'cec':
        return Colors.purple[50]!;
      default:
        return Colors.blue[50]!;
    }
  }

  Color _getNutrientBorderColor(String nutrientType) {
    switch (nutrientType) {
      case 'n':
        return Colors.green[200]!;
      case 'p':
        return Colors.orange[200]!;
      case 'k':
        return Colors.red[200]!;
      case 'cec':
        return Colors.purple[200]!;
      default:
        return Colors.blue[200]!;
    }
  }

  Color _getNutrientTextColor(String nutrientType) {
    switch (nutrientType) {
      case 'n':
        return Colors.green[800]!;
      case 'p':
        return Colors.orange[800]!;
      case 'k':
        return Colors.red[800]!;
      case 'cec':
        return Colors.purple[800]!;
      default:
        return Colors.blue[800]!;
    }
  }

  Color _getNutrientIconColor(String nutrientType) {
    switch (nutrientType) {
      case 'n':
        return Colors.green[700]!;
      case 'p':
        return Colors.orange[700]!;
      case 'k':
        return Colors.red[700]!;
      case 'cec':
        return Colors.purple[700]!;
      default:
        return Colors.blue[700]!;
    }
  }

  String _getNutrientImagePath(String nutrientType) {
    switch (nutrientType) {
      case 'n':
        return 'assets/images/v9/map/ic_map_dam.png';
      case 'p':
        return 'assets/images/v9/map/ic_map_lan.png';
      case 'k':
        return 'assets/images/v9/map/ic_map_kali.png';
      case 'cec':
        return 'assets/images/v9/map/ic_map_cec.png';
      default:
        return 'assets/images/v9/map/ic_map_npk.png';
    }
  }

  void _togglePopup() {
    setState(() {
      _isPopupVisible = !_isPopupVisible;
    });
    if (_isPopupVisible) {
      widget.onPopupToggle?.call();
    }
  }

  void closePopup() {
    if (_isPopupVisible) {
      setState(() {
        _isPopupVisible = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return !_hasDataToShow()
        ? const SizedBox()
        : Padding(
            padding: EdgeInsets.only(left: 12.sp, top: 12.sp),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                GestureDetector(
                  onTap: _togglePopup,
                  child: Container(
                    height: 86.h,
                    width: 0.38.sw,
                    margin: EdgeInsets.only(bottom: 2.sp, top: 10.sp, left: 10.sp),
                    padding: EdgeInsets.symmetric(horizontal: 20.sp, vertical: 12.sp),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12.sp),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.12),
                          spreadRadius: 0,
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        )
                      ],
                      border: Border.all(color: Colors.blue[100]!, width: 1.5),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        GestureDetector(
                          onTap: widget.onTreeSelect,
                          child: Container(
                            padding: EdgeInsets.all(6.sp),
                            decoration: BoxDecoration(
                              color: Colors.green[50],
                              borderRadius: BorderRadius.circular(8.sp),
                              border: Border.all(color: Colors.green[200]!, width: 1),
                            ),
                            child: _getTreeImage().isNotEmpty
                                ? Image.asset(
                                    _getTreeImage(),
                                    width: 46.sp,
                                    height: 46.sp,
                                    errorBuilder: (context, error, stackTrace) {
                                      return Icon(Icons.eco, color: Colors.green[700], size: 40.sp);
                                    },
                                  )
                                : Icon(Icons.eco, color: Colors.green[700], size: 40.sp),
                          ),
                        ),
                        SizedBox(width: 10.sp),
                        Text(
                          'Dinh dưỡng',
                          style: TextStyle(
                            color: Colors.blue[800],
                            fontSize: 36.sp,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                        SizedBox(width: 20.sp),
                        Icon(
                          _isPopupVisible ? Icons.expand_less : Icons.expand_more,
                          color: Colors.blue[700],
                          size: 42.sp,
                        ),
                      ],
                    ),
                  ),
                ),

                if (_isPopupVisible) ...[
                  SizedBox(height: 2.sp),
                  Container(
                    width: 0.38.sw,
                    margin: EdgeInsets.only(left: 10.sp),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16.sp),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.15),
                          spreadRadius: 0,
                          blurRadius: 12,
                          offset: const Offset(0, 4),
                        )
                      ],
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Header với tiêu đề
                        Container(
                          width: double.infinity,
                          padding: EdgeInsets.all(16.sp),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                Colors.blue[50]!,
                                Colors.white,
                              ],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(16.sp),
                              topRight: Radius.circular(16.sp),
                            ),
                            border: Border(
                              bottom: BorderSide(
                                color: Colors.grey[200]!,
                                width: 1,
                              ),
                            ),
                          ),
                          child: Row(
                            children: [
                              SizedBox(width: 16.sp),
                              Expanded(
                                child: Text(
                                  MapConstants.getDisplayText(widget.type?.value ?? ''),
                                  style: TextStyle(color: Colors.black87, fontSize: 32.sp, fontWeight: FontWeight.bold),
                                ),
                              ),
                              GestureDetector(
                                onTap: _togglePopup,
                                child: Container(
                                  padding: EdgeInsets.all(4.sp),
                                  decoration: BoxDecoration(color: Colors.grey[100], borderRadius: BorderRadius.circular(6.sp)),
                                  child: Icon(
                                    Icons.close,
                                    color: Colors.grey[600],
                                    size: 36.sp,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),

                        ConstrainedBox(
                          constraints: BoxConstraints(
                            maxHeight: 0.6.sh,
                          ),
                          child: SingleChildScrollView(
                            child: Padding(
                              padding: EdgeInsets.all(16.sp),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  if (widget.type!.value != 'ph' && widget.type!.value != 'ec' && widget.type!.value != 'om')
                                    Container(
                                      width: double.infinity,
                                      padding: EdgeInsets.symmetric(vertical: 8.sp, horizontal: 12.sp),
                                      margin: EdgeInsets.only(bottom: 12.sp),
                                      decoration: BoxDecoration(
                                        color: Colors.orange[50],
                                        borderRadius: BorderRadius.circular(8.sp),
                                        border: Border.all(color: Colors.orange[200]!, width: 1),
                                      ),
                                      child: Row(
                                        children: [
                                          Icon(
                                            Icons.info_outline,
                                            color: Colors.orange[700],
                                            size: 28.sp,
                                          ),
                                          SizedBox(width: 8.sp),
                                          Expanded(
                                            child: RichText(
                                              text: TextSpan(
                                                text: 'Đơn vị: ',
                                                style: TextStyle(
                                                  color: Colors.orange[800],
                                                  fontSize: 24.sp,
                                                  fontWeight: FontWeight.w500,
                                                ),
                                                children: [
                                                  TextSpan(
                                                    text: 'mg/kg, %, meq/100g',
                                                    style: TextStyle(
                                                      color: Colors.orange[700],
                                                      fontSize: 24.sp,
                                                      fontWeight: FontWeight.w600,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  if (_isNPKMode()) ...[
                                    _buildNutrientSection('n', 'Hàm lượng Đạm (N)'),
                                    _buildNutrientSection('p', 'Hàm lượng Lân (P)'),
                                    _buildNutrientSection('k', 'Hàm lượng Kali (K)'),
                                  ] else ...[
                                    Container(
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(12.sp),
                                        border: Border.all(color: Colors.grey[200]!, width: 1),
                                        color: Colors.grey[50],
                                      ),
                                      child: Column(
                                        children: [
                                          Container(
                                            padding: EdgeInsets.symmetric(vertical: 10.sp, horizontal: 12.sp),
                                            decoration: BoxDecoration(
                                              color: Colors.grey[100],
                                              borderRadius: BorderRadius.only(
                                                topLeft: Radius.circular(12.sp),
                                                topRight: Radius.circular(12.sp),
                                              ),
                                            ),
                                            child: Row(
                                              children: [
                                                SizedBox(
                                                  width: 48.w,
                                                  child: Text(
                                                    'Màu',
                                                    style: TextStyle(
                                                      fontSize: 24.sp,
                                                      fontWeight: FontWeight.w700,
                                                      color: Colors.grey[700],
                                                    ),
                                                    textAlign: TextAlign.center,
                                                  ),
                                                ),
                                                SizedBox(width: 24.sp),
                                                if (_showColorCodes) ...[
                                                  SizedBox(
                                                    width: 100.w,
                                                    child: Text(
                                                      'Mã màu',
                                                      style: TextStyle(
                                                        fontSize: 24.sp,
                                                        fontWeight: FontWeight.w700,
                                                        color: Colors.grey[700],
                                                      ),
                                                      textAlign: TextAlign.center,
                                                    ),
                                                  ),
                                                  SizedBox(width: 16.sp),
                                                ],
                                                Expanded(
                                                  child: Text(
                                                    'Hàm lượng',
                                                    style: TextStyle(
                                                      fontSize: 24.sp,
                                                      fontWeight: FontWeight.w700,
                                                      color: Colors.grey[700],
                                                    ),
                                                  ),
                                                ),
                                                GestureDetector(
                                                  onTap: () {
                                                    setState(() {
                                                      _showColorCodes = !_showColorCodes;
                                                    });
                                                  },
                                                  child: Container(
                                                    padding: EdgeInsets.all(6.sp),
                                                    decoration: BoxDecoration(
                                                      color: _showColorCodes ? Colors.blue[100] : Colors.grey[200],
                                                      borderRadius: BorderRadius.circular(6.sp),
                                                      border: Border.all(
                                                        color: _showColorCodes ? Colors.blue[300]! : Colors.grey[400]!,
                                                        width: 1,
                                                      ),
                                                    ),
                                                    child: Row(
                                                      mainAxisSize: MainAxisSize.min,
                                                      children: [
                                                        Icon(
                                                          _showColorCodes ? Icons.code_off : Icons.code,
                                                          size: 24.sp,
                                                          color: _showColorCodes ? Colors.blue[700] : Colors.grey[600],
                                                        ),
                                                        SizedBox(width: 4.sp),
                                                        Text(
                                                          'HEX',
                                                          style: TextStyle(
                                                            fontSize: 10.sp,
                                                            fontWeight: FontWeight.w600,
                                                            color: _showColorCodes ? Colors.blue[700] : Colors.grey[600],
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),

                                          Container(
                                            padding: EdgeInsets.all(8.sp),
                                            child: Row(
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              children: [
                                                Container(
                                                  width: 62.w,
                                                  decoration: BoxDecoration(
                                                    borderRadius: BorderRadius.circular(8.sp),
                                                    boxShadow: [
                                                      BoxShadow(
                                                        color: Colors.black.withOpacity(0.1),
                                                        blurRadius: 4,
                                                        offset: const Offset(0, 2),
                                                      ),
                                                    ],
                                                  ),
                                                  child: Column(
                                                    children: widget._treeValueList.asMap().entries.map((entry) {
                                                      int index = entry.key;
                                                      ColorItemMap item = entry.value;
                                                      bool isFirst = index == 0;
                                                      bool isLast = index == widget._treeValueList.length - 1;

                                                      return Container(
                                                        width: 32.w,
                                                        height: 52.sp,
                                                        decoration: BoxDecoration(
                                                          color: MapUtils.hexToColor(item.color),
                                                          borderRadius: BorderRadius.only(
                                                            topLeft: isFirst ? Radius.circular(8.sp) : Radius.zero,
                                                            topRight: isFirst ? Radius.circular(8.sp) : Radius.zero,
                                                            bottomLeft: isLast ? Radius.circular(8.sp) : Radius.zero,
                                                            bottomRight: isLast ? Radius.circular(8.sp) : Radius.zero,
                                                          ),
                                                          border: Border(
                                                            bottom: !isLast
                                                                ? BorderSide(
                                                                    color: Colors.white.withOpacity(0.3),
                                                                    width: 0.5,
                                                                  )
                                                                : BorderSide.none,
                                                          ),
                                                        ),
                                                      );
                                                    }).toList(),
                                                  ),
                                                ),

                                                SizedBox(width: 10.sp),

                                                // Danh sách hàm lượng bên phải
                                                Expanded(
                                                  child: Column(
                                                    children: widget._treeValueList.asMap().entries.map((entry) {
                                                      int index = entry.key;
                                                      ColorItemMap item = entry.value;

                                                      return Container(
                                                        height: 52.sp,
                                                        margin: EdgeInsets.only(bottom: index < widget._treeValueList.length - 1 ? 2.sp : 0),
                                                        padding: EdgeInsets.symmetric(horizontal: 12.sp, vertical: 8.sp),
                                                        decoration: BoxDecoration(
                                                          color: Colors.white,
                                                          borderRadius: BorderRadius.circular(8.sp),
                                                          border: Border.all(color: Colors.grey[200]!, width: 0.5),
                                                          boxShadow: [
                                                            BoxShadow(
                                                              color: Colors.black.withOpacity(0.02),
                                                              blurRadius: 2,
                                                              offset: const Offset(0, 1),
                                                            ),
                                                          ],
                                                        ),
                                                        child: Row(
                                                          children: [
                                                            if (_showColorCodes) ...[
                                                              SizedBox(
                                                                width: 132.w,
                                                                child: Container(
                                                                  padding: EdgeInsets.symmetric(horizontal: 6.sp, vertical: 4.sp),
                                                                  decoration: BoxDecoration(
                                                                    color: Colors.grey[100],
                                                                    borderRadius: BorderRadius.circular(4.sp),
                                                                    border: Border.all(color: Colors.grey[300]!, width: 0.5),
                                                                  ),
                                                                  child: Text(
                                                                    item.color,
                                                                    style: TextStyle(
                                                                      color: Colors.grey[700],
                                                                      fontSize: 20.sp,
                                                                      fontWeight: FontWeight.w500,
                                                                      fontFamily: 'monospace',
                                                                    ),
                                                                    textAlign: TextAlign.center,
                                                                    maxLines: 1,
                                                                    overflow: TextOverflow.ellipsis,
                                                                  ),
                                                                ),
                                                              ),
                                                              SizedBox(width: 16.sp),
                                                            ],
                                                            // Chấm màu indicator
                                                            Container(
                                                              width: 10.sp,
                                                              height: 10.sp,
                                                              decoration: BoxDecoration(
                                                                color: MapUtils.hexToColor(item.color),
                                                                shape: BoxShape.circle,
                                                                border: Border.all(color: Colors.white, width: 1.5),
                                                                boxShadow: [
                                                                  BoxShadow(
                                                                    color: Colors.black.withOpacity(0.2),
                                                                    blurRadius: 2,
                                                                    offset: const Offset(0, 1),
                                                                  ),
                                                                ],
                                                              ),
                                                            ),
                                                            SizedBox(width: 10.sp),
                                                            // Text hàm lượng
                                                            Expanded(
                                                              child: Text(
                                                                item.value2.isNotEmpty ? '${item.value} / ${item.value2}' : item.value,
                                                                style: TextStyle(
                                                                  color: Colors.blue[700],
                                                                  fontSize: 24.sp,
                                                                  fontWeight: FontWeight.w600,
                                                                  letterSpacing: 0.2,
                                                                ),
                                                                maxLines: 1,
                                                                overflow: TextOverflow.ellipsis,
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      );
                                                    }).toList(),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          );
  }
}

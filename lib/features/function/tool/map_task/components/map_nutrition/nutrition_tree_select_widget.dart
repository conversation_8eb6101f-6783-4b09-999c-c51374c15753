import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hainong/common/ui/button_image_widget.dart';
import 'package:hainong/features/function/tool/map_task/map_constants.dart';
import 'package:hainong/features/function/tool/map_task/models/map_item_menu_model.dart';
import 'package:hainong/features/function/tool/map_task/utils/map_utils.dart';

class LayerNutritionTreeSelectWidget extends StatefulWidget {
  const LayerNutritionTreeSelectWidget({
    Key? key,
    required this.treeType,
    required this.onTreeSelected,
  }) : super(key: key);

  final MenuItemMap? treeType;
  final Function(MenuItemMap) onTreeSelected;

  @override
  State<LayerNutritionTreeSelectWidget> createState() => _LayerNutritionTreeSelectWidgetState();
}

class _LayerNutritionTreeSelectWidgetState extends State<LayerNutritionTreeSelectWidget> {
  bool _showTreeSelection = false;

  void _toggleTreeSelection() {
    setState(() {
      _showTreeSelection = !_showTreeSelection;
    });
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Popup chọn loại cây
          if (_showTreeSelection)
            Container(
              margin: EdgeInsets.only(top: 10.sp, left: 10.sp, bottom: 2.sp),
              width: 0.36.sw,
              decoration: BoxDecoration(
                gradient: LinearGradient(colors: [
                  Colors.white.withOpacity(0.6),
                  Colors.blue[50]!,
                ], begin: Alignment.topLeft, end: Alignment.bottomRight),
                borderRadius: BorderRadius.circular(16.sp),
                boxShadow: [
                  BoxShadow(color: Colors.blue.withOpacity(0.2), spreadRadius: 0, blurRadius: 20, offset: const Offset(0, 8)),
                  BoxShadow(color: Colors.black.withOpacity(0.1), spreadRadius: 0, blurRadius: 10, offset: const Offset(0, 4)),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Header
                  Container(
                    padding: EdgeInsets.all(16.sp),
                    decoration: BoxDecoration(
                      color: Colors.green[50],
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(16.sp),
                        topRight: Radius.circular(16.sp),
                      ),
                      border: Border(bottom: BorderSide(color: Colors.grey[200]!, width: 1)),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.eco,
                          color: Colors.green[700],
                          size: 46.sp,
                        ),
                        SizedBox(width: 12.sp),
                        Expanded(
                          child: Padding(
                            padding: EdgeInsets.all(8.sp),
                            child: Text('Chọn loại cây', style: TextStyle(color: Colors.black87, fontSize: 36.sp, fontWeight: FontWeight.bold)),
                          ),
                        ),
                        GestureDetector(
                          onTap: _toggleTreeSelection,
                          child: Icon(Icons.close, color: Colors.grey[600], size: 36.sp),
                        ),
                      ],
                    ),
                  ),
      
                  // Nội dung
                  Container(
                    constraints: BoxConstraints(
                      maxHeight: 600.h,
                    ),
                    child: SingleChildScrollView(
                      physics: BouncingScrollPhysics(),
                      child: Padding(
                        padding: EdgeInsets.all(16.sp),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Cây lương thực
                            _buildSectionTitle('Cây lương thực'),
                            SizedBox(height: 12.sp),
                            _buildTreeItem(MapConstants.menuTreeRiceItem()),
                            SizedBox(height: 20.sp),
                            _buildSectionTitle('Cây ăn quả'),
                            SizedBox(height: 12.sp),
                            ...MapConstants.menuFruitItem().map((item) => Padding(padding: EdgeInsets.only(bottom: 8.sp), child: _buildTreeItem(item))),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          Container(
            height: 90.h,
            width: 90.h,
            margin: EdgeInsets.only(left: 10.sp, bottom: 20.sp),
            child: ButtonImageWidget(
              10.sp,
              _toggleTreeSelection,
              Container(
                padding: EdgeInsets.all(4.sp),
                child: widget.treeType != null
                    ? Image.asset(widget.treeType!.image, width: 86.w, height: 86.w)
                    : Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.eco, color: Colors.green[700], size: 46.sp),
                          SizedBox(height: 8.sp),
                          Text(
                            'Chọn cây',
                            style: TextStyle(color: Colors.green[700], fontSize: 24.sp, fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
              ),
              color: Colors.white.withOpacity(0.6),
              elevation: 4.0,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: TextStyle(color: Colors.black87, fontSize: 36.sp, fontWeight: FontWeight.bold),
    );
  }

  Widget _buildTreeItem(MenuItemMap item) {
    final isSelected = widget.treeType?.id == item.id;

    return GestureDetector(
      onTap: () {
        widget.onTreeSelected(item);
        _toggleTreeSelection();
      },
      child: Container(
        padding: EdgeInsets.all(12.sp),
        decoration: BoxDecoration(
          color: isSelected ? Colors.green[50] : Colors.white,
          borderRadius: BorderRadius.circular(12.sp),
          border: Border.all(color: isSelected ? Colors.green[300]! : Colors.grey[200]!, width: 1),
        ),
        child: Row(
          children: [
            Container(
              padding: EdgeInsets.all(8.sp),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8.sp),
              ),
              child: Image.asset(item.image, width: 46.sp, height: 46.sp),
            ),
            SizedBox(width: 12.sp),
            Expanded(
              child: Text(
                item.name,
                style: TextStyle(
                  fontSize: 36.sp,
                  fontWeight: isSelected ? FontWeight.w700 : FontWeight.w600,
                  letterSpacing: 0.2,
                  color: isSelected ? Colors.green[700] : Colors.black87,
                ),
              ),
            ),
            if (isSelected)
              Container(
                width: 42.sp,
                height: 42.sp,
                padding: EdgeInsets.all(4.sp),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.green[400]!, Colors.green[600]!],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(24.sp),
                  boxShadow: [
                    BoxShadow(color: Colors.green.withOpacity(0.3), blurRadius: 3, offset: const Offset(0, 1)),
                  ],
                ),
                child: Icon(
                  Icons.check_rounded,
                  color: Colors.white,
                  size: 32.sp,
                ),
              ),
          ],
        ),
      ),
    );
  }
}

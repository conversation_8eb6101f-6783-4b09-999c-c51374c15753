import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hainong/features/function/tool/map_task/map_constants.dart';
import 'package:hainong/features/function/tool/map_task/models/map_item_menu_model.dart';

class LayerNutritionTreeChangeWidget extends StatefulWidget {
  final List<MenuItemMap>? menuList;
  final MenuItemMap? selectTreeLayer;
  final Function(MenuItemMap) onCallBack;
  final VoidCallback? onDropdownToggle;
  const LayerNutritionTreeChangeWidget({this.selectTreeLayer, this.menuList, this.onDropdownToggle, super.key, required this.onCallBack});

  @override
  State<LayerNutritionTreeChangeWidget> createState() => _LayerNutritionTreeChangeWidgetState();
}

class _LayerNutritionTreeChangeWidgetState extends State<LayerNutritionTreeChangeWidget> with TickerProviderStateMixin {
  bool _isDropdownOpen = false;
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(duration: const Duration(milliseconds: 200), vsync: this);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _toggleDropdown() {
    if (_isDropdownOpen) {
      _closeDropdown();
    } else {
      _openDropdown();
    }
  }

  void _openDropdown() {
    _animationController.forward();
    setState(() => _isDropdownOpen = true);
    widget.onDropdownToggle?.call();
  }

  void _closeDropdown() {
    _animationController.reverse();
    setState(() => _isDropdownOpen = false);
  }

  void closeDropdown() {
    if (_isDropdownOpen) _closeDropdown();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Padding(
        padding: EdgeInsets.only(left: 16.sp, bottom: 20.sp),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (_isDropdownOpen)
              Container(
                margin: EdgeInsets.only(bottom: 2.sp),
                child: AnimatedScale(
                  scale: _isDropdownOpen ? 1.0 : 0.0,
                  duration: const Duration(milliseconds: 400),
                  curve: Curves.easeInOut,
                  alignment: Alignment.bottomCenter,
                  child: AnimatedOpacity(
                    opacity: _isDropdownOpen ? 1.0 : 0.0,
                    duration: const Duration(milliseconds: 400),
                    child: _buildDropdownContent(),
                  ),
                ),
              ),
            GestureDetector(
              onTap: _toggleDropdown,
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Colors.white,
                      Colors.blue[50]!.withOpacity(0.8),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(16.sp),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.blue.withOpacity(0.15),
                      spreadRadius: 0,
                      blurRadius: 12,
                      offset: const Offset(0, 4),
                    ),
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      spreadRadius: 0,
                      blurRadius: 6,
                      offset: const Offset(0, 2),
                    ),
                  ],
                  border: Border.all(
                    color: Colors.blue[100]!.withOpacity(0.5),
                    width: 1.5,
                  ),
                ),
                padding: EdgeInsets.symmetric(vertical: 16.sp, horizontal: 20.sp),
                child: SizedBox(
                  width: 0.415.sw,
                  height: 60.h,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                        padding: EdgeInsets.all(4.sp),
                        child: Image.asset(
                          _getImageForValue(widget.selectTreeLayer?.value ?? ''),
                          width: 64.sp,
                          height: 64.sp,
                          errorBuilder: (context, error, stackTrace) {
                            return Icon(
                              Icons.science_rounded,
                              color: Colors.white,
                              size: 20.sp,
                            );
                          },
                        ),
                      ),
                      SizedBox(width: 28.sp),
                      Expanded(
                        child: Text(
                          MapConstants.getDisplayText(widget.selectTreeLayer?.value ?? ''),
                          style: TextStyle(
                            color: Colors.blue[800],
                            fontSize: 32.sp,
                            fontWeight: FontWeight.w700,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      Container(
                        padding: EdgeInsets.only(right: 8.sp),
                        decoration: BoxDecoration(
                          color: Colors.blue[100]!.withOpacity(0.6),
                          borderRadius: BorderRadius.circular(6.sp),
                        ),
                        child: AnimatedRotation(
                          turns: _isDropdownOpen ? 0.5 : 0,
                          duration: const Duration(milliseconds: 200),
                          child: Icon(
                            Icons.keyboard_arrow_down_rounded,
                            color: Colors.blue[700],
                            size: 46.sp,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDropdownContent() {
    return Material(
      color: Colors.transparent,
      elevation: 8,
      borderRadius: BorderRadius.circular(16.sp),
      child: Container(
        width: 0.46.sw,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Colors.white.withOpacity(0.6),
              Colors.blue[50]!,
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(16.sp),
          boxShadow: [
            BoxShadow(
              color: Colors.blue.withOpacity(0.2),
              spreadRadius: 0,
              blurRadius: 20,
              offset: const Offset(0, 8),
            ),
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              spreadRadius: 0,
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.all(16.sp),
              decoration: BoxDecoration(
                color: Colors.green[50],
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16.sp),
                  topRight: Radius.circular(16.sp),
                ),
                border: Border(
                  bottom: BorderSide(
                    color: Colors.grey[200]!,
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.science_rounded,
                    color: Colors.green[700],
                    size: 46.sp,
                  ),
                  SizedBox(width: 12.sp),
                  Expanded(
                    child: Padding(
                      padding: EdgeInsets.all(8.sp),
                      child: Text(
                        'Chọn dinh dưỡng',
                        style: TextStyle(
                          color: Colors.black87,
                          fontSize: 36.sp,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                  GestureDetector(
                    onTap: _closeDropdown,
                    child: Icon(
                      Icons.close,
                      color: Colors.grey[600],
                      size: 36.sp,
                    ),
                  ),
                ],
              ),
            ),
            Container(
              constraints: BoxConstraints(
                maxHeight: 600.h,
              ),
              child: SingleChildScrollView(
                physics: const BouncingScrollPhysics(),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: widget.menuList?.map((item) {
                        final isSelected = widget.selectTreeLayer?.id == item.id;
                        return GestureDetector(
                          behavior: HitTestBehavior.translucent,
                          onTap: () {
                            widget.onCallBack(item);
                            _closeDropdown();
                          },
                          child: Container(
                            width: double.infinity,
                            padding: EdgeInsets.symmetric(horizontal: 16.sp, vertical: 18.sp),
                            margin: EdgeInsets.symmetric(horizontal: 8.sp, vertical: 8.sp),
                            decoration: BoxDecoration(
                              gradient: isSelected
                                  ? LinearGradient(
                                      colors: [
                                        Colors.blue[100]!.withOpacity(0.7),
                                        Colors.blue[50]!.withOpacity(0.5),
                                      ],
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                    )
                                  : null,
                              color: isSelected ? null : Colors.white,
                              borderRadius: BorderRadius.circular(12.sp),
                              border: isSelected
                                  ? Border.all(
                                      color: Colors.blue[300]!.withOpacity(0.8),
                                      width: 1,
                                    )
                                  : Border.all(
                                      color: Colors.grey[300]!,
                                      width: 0.5,
                                    ),
                              boxShadow: isSelected
                                  ? [
                                      BoxShadow(
                                        color: Colors.blue.withOpacity(0.1),
                                        blurRadius: 4,
                                        offset: const Offset(0, 1),
                                      ),
                                    ]
                                  : null,
                            ),
                            child: Row(
                              children: [
                                Container(
                                  padding: EdgeInsets.all(4.sp),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(8.sp),
                                  ),
                                  child: Image.asset(
                                    _getImageForValue(item.value),
                                    width: 48.sp,
                                    height: 48.sp,
                                    errorBuilder: (context, error, stackTrace) {
                                      return Icon(
                                        Icons.science_rounded,
                                        color: Colors.white,
                                        size: 42.sp,
                                      );
                                    },
                                  ),
                                ),
                                SizedBox(width: 24.sp),
                                Expanded(
                                  child: Text(
                                    MapConstants.getDisplayText(item.value),
                                    style: TextStyle(
                                      color: isSelected ? Colors.blue[800] : Colors.grey[700],
                                      fontSize: 32.sp,
                                      fontWeight: isSelected ? FontWeight.w700 : FontWeight.w600,
                                      letterSpacing: 0.2,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                                if (isSelected)
                                  Container(
                                    width: 42.sp,
                                    height: 42.sp,
                                    padding: EdgeInsets.all(4.sp),
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                        colors: [
                                          Colors.green[400]!,
                                          Colors.green[600]!,
                                        ],
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight,
                                      ),
                                      borderRadius: BorderRadius.circular(24.sp),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.green.withOpacity(0.3),
                                          blurRadius: 3,
                                          offset: const Offset(0, 1),
                                        ),
                                      ],
                                    ),
                                    child: Icon(
                                      Icons.check_rounded,
                                      color: Colors.white,
                                      size: 32.sp,
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        );
                      }).toList() ??
                      [],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getImageForValue(String value) {
    final nutritionList = MapConstants.menuNutritionalTypeModel();
    final nutritionItem = nutritionList.firstWhere(
      (item) => item.value == value,
      orElse: () => MenuItemMap(image: "assets/images/v9/map/ic_map_npk.png"),
    );
    return nutritionItem.image;
  }
}

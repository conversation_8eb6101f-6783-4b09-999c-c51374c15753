import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hainong/features/function/tool/map_task/map_constants.dart';
import 'package:hainong/features/function/tool/map_task/models/map_item_menu_model.dart';

class LayerNutritionTreeMenuWidget extends StatelessWidget {
  final List<MenuItemMap> treeNutritionList;
  final MenuItemMap? selectedNutritionType;
  final Function(MenuItemMap) onNutritionTypeSelected;
  final List<MenuItemMap> treeTypeList;
  final MenuItemMap? selectedTreeType;
  final Function(MenuItemMap) onTreeTypeSelected;

  const LayerNutritionTreeMenuWidget({
    super.key,
    required this.treeNutritionList,
    required this.selectedNutritionType,
    required this.onNutritionTypeSelected,
    required this.treeTypeList,
    required this.selectedTreeType,
    required this.onTreeTypeSelected,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      physics: const BouncingScrollPhysics(),
      child: Container(
        padding: EdgeInsets.only(left: 20.sp, right: 20.sp, top: 20.sp, bottom: 0.sp),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 24.sp),
            _buildSectionHeader("Kiểu dinh dưỡng", Icons.grain),
            SizedBox(height: 24.sp),
            _buildNutritionTypeGrid(),
            if (_shouldShowTreeTypes()) ...[
              SizedBox(height: 24.sp),
              _buildSectionHeader("Cây lương thực", Icons.room_service_outlined),
              SizedBox(height: 24.sp),
              _buildFoodCropGrid(),
              SizedBox(height: 24.sp),
              _buildSectionHeader("Cây ăn quả", Icons.eco),
              SizedBox(height: 24.sp),
              _buildFruitTreeGrid(),
            ],
          ],
        ),
      ),
    );
  }

  bool _shouldShowTreeTypes() {
    if (selectedNutritionType == null) return false;
    final List<String> nutritionTypesForTrees = ['n', 'p', 'k', 'npk', 'ph', 'ec', 'cec', 'om'];
    final String nutritionValue = selectedNutritionType!.value.toLowerCase();
    return nutritionTypesForTrees.contains(nutritionValue);
  }

  Widget _buildSectionHeader(String title, IconData icon) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 8.sp),
      child: Row(
        children: [
          Icon(icon, color: const Color(0xFF4CAF50), size: 42.sp),
          SizedBox(width: 12.sp),
          Text(title, style: TextStyle(color: Colors.black87, fontSize: 42.sp, fontWeight: FontWeight.w600)),
        ],
      ),
    );
  }

  Widget _buildNutritionTypeGrid() {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: EdgeInsets.symmetric(horizontal: 4.sp, vertical: 8.sp),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(crossAxisCount: 4, childAspectRatio: 0.85, crossAxisSpacing: 24.sp, mainAxisSpacing: 24.sp),
      itemCount: treeNutritionList.length,
      itemBuilder: (context, index) => _buildItemCard(treeNutritionList[index], selectedNutritionType?.id, onNutritionTypeSelected, isNutritionType: true),
    );
  }

  Widget _buildFoodCropGrid() {
    MenuItemMap? riceItem = _findRiceItem();
    riceItem ??= MapConstants.menuTreeRiceItem();
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: EdgeInsets.symmetric(horizontal: 4.sp, vertical: 8.sp),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(crossAxisCount: 4, childAspectRatio: 0.85, crossAxisSpacing: 24.sp, mainAxisSpacing: 24.sp),
      itemCount: 1,
      itemBuilder: (context, index) => _buildItemCard(riceItem!, selectedTreeType?.id, onTreeTypeSelected, isNutritionType: false),
    );
  }

  Widget _buildFruitTreeGrid() {
    List<MenuItemMap> fruitItems = treeTypeList.where((item) => !item.value.contains('rice') && !item.name.toLowerCase().contains('lúa')).toList();
    if (fruitItems.isEmpty) {
      return Center(
        child: Padding(padding: EdgeInsets.all(16.sp), child: Text("Không có dữ liệu cây ăn quả", style: TextStyle(color: Colors.grey, fontSize: 32.sp))),
      );
    }

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: EdgeInsets.symmetric(horizontal: 4.sp, vertical: 8.sp),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(crossAxisCount: 4, childAspectRatio: 0.85, crossAxisSpacing: 24.sp, mainAxisSpacing: 24.sp),
      itemCount: fruitItems.length,
      itemBuilder: (context, index) => _buildItemCard(fruitItems[index], selectedTreeType?.id, onTreeTypeSelected, isNutritionType: false // Đánh dấu đây là cây trồng
          ),
    );
  }

  MenuItemMap? _findRiceItem() {
    for (var item in treeTypeList) {
      if (item.value.contains('rice') || item.name.toLowerCase().contains('lúa')) {
        return item;
      }
    }
    return null;
  }

  Widget _buildItemCard(MenuItemMap item, int? selectedId, Function(MenuItemMap) onSelected, {required bool isNutritionType}) {
    final bool isSelected = item.id == selectedId;
    final Color selectedBorderColor = isNutritionType
        ? const Color(0xFF2196F3) // Màu xanh dương cho dinh dưỡng
        : const Color(0xFF4CAF50); // Màu xanh lá cho cây trồng
    final Color selectedTextColor = isNutritionType
        ? const Color(0xFF2196F3) // Màu xanh dương cho dinh dưỡng
        : const Color(0xFF4CAF50); // Màu xanh lá cho cây trồng
    return GestureDetector(
      onTap: () => onSelected(item),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(32.sp),
          boxShadow: [BoxShadow(color: Colors.black.withOpacity(0.06), blurRadius: 4, spreadRadius: 0, offset: const Offset(0, 1))],
          border: Border.all(
            color: isSelected ? selectedBorderColor : Colors.grey.withOpacity(0.15),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            Expanded(
              flex: 4,
              child: Container(
                padding: EdgeInsets.all(8.sp),
                margin: EdgeInsets.only(top: 6.sp),
                width: double.infinity,
                child: ClipRRect(borderRadius: BorderRadius.circular(8.sp), child: Image.asset(item.image, fit: BoxFit.fill)),
              ),
            ),
            Expanded(
              flex: 1,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 4.sp),
                alignment: Alignment.center,
                child: Text(
                  item.name,
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    color: isSelected ? selectedTextColor : Colors.black87,
                    fontSize: 38.sp,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

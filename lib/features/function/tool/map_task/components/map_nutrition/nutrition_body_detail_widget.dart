import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hainong/common/constants.dart';
import 'package:hainong/common/multi_language.dart';
import 'package:hainong/common/util/util_ui.dart';
import 'package:hainong/features/function/tool/map_task/components/map_nutrition/nutrition_body_detail_info_update_page.dart';
import 'package:hainong/features/function/tool/map_task/models/map_address_model.dart';
import 'package:hainong/features/function/tool/map_task/models/map_item_menu_model.dart';
import 'package:hainong/features/function/tool/map_task/models/map_response_model.dart';
import 'package:trackasia_gl/trackasia_gl.dart';

bool _hasNutritionData(dynamic data) {
  return data != null && data.toString().isNotEmpty && data.toString() != "N/A" && data.toString() != "";
}

Widget addressNutritionWidget(ValueNotifier<MapAddressModel> addressMap, MenuItemMap? treeType) {
  return ValueListenableBuilder<MapAddressModel>(
      valueListenable: addressMap,
      builder: (context, address, child) {
        return Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
          Expanded(child: Padding(padding: EdgeInsets.symmetric(horizontal: 20.sp), child: Text(address.address_full ?? "", style: const TextStyle(fontSize: 14)))),
          if (treeType != null)
            Container(
                padding: EdgeInsets.all(10.sp),
                decoration: BoxDecoration(border: Border.all(color: Colors.blue), borderRadius: BorderRadius.all(Radius.circular(25.sp))),
                child: Image.asset(treeType.image, width: 100.w, height: 100.w))
        ]);
      });
}

Widget tabBarNutritionWidget(
    BuildContext context, ValueNotifier<MapAddressModel> _addressMap, TabController _tabBodyController, MenuItemMap? _treeType, MenuItemMap? _treeNutritionType, MapGeoJsonModel data, LatLng point,
    {bool isRecommend = true}) {
  return Container(
    color: Colors.transparent,
    child: Column(
      children: [
        TabBar(
            padding: EdgeInsets.zero,
            isScrollable: true,
            controller: _tabBodyController,
            labelColor: Colors.blue,
            indicatorColor: Colors.blue,
            unselectedLabelColor: Colors.black,
            indicatorWeight: 1,
            indicatorSize: TabBarIndicatorSize.tab,
            labelPadding: EdgeInsets.symmetric(horizontal: 100.sp),
            indicatorPadding: EdgeInsets.zero,
            unselectedLabelStyle: const TextStyle(color: Colors.black87),
            indicator: UnderlineTabIndicator(borderSide: const BorderSide(width: 4.0, color: Colors.blue), insets: EdgeInsets.symmetric(horizontal: 40.sp)),
            tabs: isRecommend ? [const Tab(text: "Tổng quan"), const Tab(text: "Khuyến cáo")] : [const Tab(text: "Tổng quan")]),
        Expanded(
            child: TabBarView(
                controller: _tabBodyController,
                children: isRecommend
                    ? [tabBodyOverviewNutritionWidget(context, _addressMap, _treeType, _treeNutritionType, data, point), tabBodyAdviseNutritionWidget(data, _treeType, _treeNutritionType)]
                    : [tabBodyOverviewNutritionWidget(context, _addressMap, _treeType, _treeNutritionType, data, point)])),
      ],
    ),
  );
}

Widget tabBodyOverviewNutritionWidget(BuildContext context, ValueNotifier<MapAddressModel> _addressMap, MenuItemMap? _treeType, MenuItemMap? _treeNutritionType, MapGeoJsonModel data, LatLng point) {
  return ValueListenableBuilder<MapAddressModel>(
      valueListenable: _addressMap,
      builder: (context, address, child) {
        return SingleChildScrollView(
            child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          const Divider(height: 0.2, color: Colors.grey),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 20.sp, vertical: 20.sp),
            child: Row(
              children: [
                Image.asset("assets/images/v9/map/ic_map_ecological.png", width: 32, height: 32),
                SizedBox(width: 20.sp),
                Expanded(
                  child: Text("Vùng sinh thái: ${data.data['overall']["region_name"]}", style: const TextStyle(fontSize: 16)),
                )
              ],
            ),
          ),
          Divider(height: 0.2, color: Colors.grey.withOpacity(0.4)),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 20.sp, vertical: 30.sp),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Image.asset(
                    _treeNutritionType?.value == "ph"
                        ? "assets/images/v9/map/ic_map_ph.png"
                        : _treeNutritionType?.value == "ec"
                            ? "assets/images/v9/map/ic_map_ec.png"
                            : _treeNutritionType?.value == "cec"
                                ? "assets/images/v9/map/ic_map_cec.png"
                                : _treeNutritionType?.value == "om"
                                    ? "assets/images/v9/map/ic_map_om.png"
                                    : "assets/images/v9/map/ic_map_mass.png",
                    width: 42,
                    height: 42),
                SizedBox(width: 20.sp),
                Expanded(
                  child: Row(
                    children: [
                      Text(_treeNutritionType?.value == "ph" ? "Độ" : "Hàm lượng ", style: const TextStyle(fontSize: 16), maxLines: 1),
                      SizedBox(width: 20.sp),
                      Expanded(
                        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                          if (_hasNutritionData(data.data["overall"]["ph"]))
                            Wrap(alignment: WrapAlignment.start, children: [
                              const Text('<PH>: ', style: TextStyle(fontSize: 14, color: Colors.red)),
                              Text("${data.data["overall"]["ph"]}", style: const TextStyle(fontSize: 14, color: Colors.grey)),
                              const Text(" %", style: TextStyle(fontSize: 14))
                            ]),
                          if (_hasNutritionData(data.data["overall"]["ec"]))
                            Wrap(alignment: WrapAlignment.start, children: [
                              const Text('<EC>: ', style: TextStyle(fontSize: 14, color: Colors.red)),
                              Text("${data.data["overall"]["ec"]}", style: const TextStyle(fontSize: 14, color: Colors.grey)),
                              const Text(" %", style: TextStyle(fontSize: 14))
                            ]),
                          if (_hasNutritionData(data.data["overall"]["cec"]))
                            Wrap(alignment: WrapAlignment.start, children: [
                              const Text('<CEC>: ', style: TextStyle(fontSize: 14, color: Colors.red)),
                              Text("${data.data["overall"]["cec"]}", style: const TextStyle(fontSize: 14, color: Colors.grey)),
                              const Text(" meq/100g", style: TextStyle(fontSize: 14))
                            ]),
                          if (_hasNutritionData(data.data["overall"]["om"]))
                            Wrap(alignment: WrapAlignment.start, children: [
                              const Text('<OM>: ', style: TextStyle(fontSize: 14, color: Colors.red)),
                              Text("${data.data["overall"]["om"]}", style: const TextStyle(fontSize: 14, color: Colors.grey)),
                              const Text(" %", style: TextStyle(fontSize: 14))
                            ]),
                          if (_hasNutritionData(data.data["overall"]["n"]))
                            Wrap(alignment: WrapAlignment.start, children: [
                              const Text('<Nito tổng số>: ', style: TextStyle(fontSize: 14, color: Colors.red)),
                              Text("${data.data["overall"]["n"]}", style: const TextStyle(fontSize: 14, color: Colors.grey)),
                              const Text(" %", style: TextStyle(fontSize: 14))
                            ]),
                          if (_hasNutritionData(data.data["overall"]["p"]))
                            Wrap(alignment: WrapAlignment.start, children: [
                              const Text('<Phospho dễ tiêu>: ', style: TextStyle(fontSize: 14, color: Colors.red)),
                              Text("${data.data["overall"]["p"]}", style: const TextStyle(fontSize: 14, color: Colors.grey)),
                              const Text(" mg/kg", style: TextStyle(fontSize: 14))
                            ]),
                          if (_hasNutritionData(data.data["overall"]["k"]))
                            Wrap(alignment: WrapAlignment.start, children: [
                              const Text('<Kali trao đổi>: ', style: TextStyle(fontSize: 14, color: Colors.red)),
                              Text("${data.data["overall"]["k"]}", style: const TextStyle(fontSize: 14, color: Colors.grey)),
                              const Text(" meq/100g", style: TextStyle(fontSize: 14))
                            ]),
                        ]),
                      )
                    ],
                  ),
                )
              ],
            ),
          ),
          Divider(height: 0.2, color: Colors.grey.withOpacity(0.4)),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 20.sp, vertical: 30.sp),
            child: Row(
              children: [
                Image.asset("assets/images/v9/map/ic_map_rate.png", width: 32, height: 32),
                SizedBox(width: 20.sp),
                Text("Tọa độ: Lat: ${point.latitude.toStringAsFixed(4)} Lng: ${point.longitude.toStringAsFixed(4)}", style: const TextStyle(fontSize: 16))
              ],
            ),
          ),
          Divider(height: 0.2, color: Colors.grey.withOpacity(0.4)),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 20.sp, vertical: 30.sp),
            child: GestureDetector(
              onTap: () {
                if (!Constants().isLogin) {
                  UtilUI.showCustomDialog(context, MultiLanguage.get('msg_login_create_account'));
                } else {
                  UtilUI.goToNextPage(context, MapNutritionInfoUpdatePage(addressMap: address, point: point));
                }
              },
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Image.asset("assets/images/v9/map/ic_map_location_plus.png", width: 32, height: 32),
                  SizedBox(width: 20.sp),
                  const Text("Thêm thông tin vị trí", style: TextStyle(fontSize: 16, color: Colors.blue))
                ],
              ),
            ),
          ),
        ]));
      });
}

Widget tabBodyAdviseNutritionWidget(MapGeoJsonModel data, MenuItemMap? _treeType, MenuItemMap? _treeNutritionType) {
  return SingleChildScrollView(
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
    const Divider(height: 0.2, color: Colors.grey),
    Padding(
      padding: EdgeInsets.only(left: 10.sp, right: 10.sp, top: 30.sp),
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Image.asset("assets/images/v9/map/ic_map_mass.png", width: 32, height: 32),
              SizedBox(width: 20.sp),
              Padding(padding: EdgeInsets.symmetric(vertical: 20.sp), child: const Text("Công thức khuyến cáo", style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold), maxLines: 1)),
            ],
          ),
        ],
      ),
    ),
    Padding(
      padding: EdgeInsets.symmetric(horizontal: 30.sp),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (_hasNutritionData(data.data["recommendation"]["n"])) ...[
            Padding(padding: EdgeInsets.symmetric(vertical: 30.sp), child: Divider(height: 0.2, color: Colors.grey.withOpacity(0.4))),
            Row(children: [
              SizedBox(width: 85.sp),
              const Text("Nitơ (N): ", style: TextStyle(fontSize: 16), maxLines: 1),
              Text("${data.data["recommendation"]["n"]}", style: const TextStyle(fontSize: 16, color: Colors.red), maxLines: 1),
              Text(_treeNutritionType?.value == 'npk' ? " Kg/cây/năm" : " Kg/hecta", style: const TextStyle(fontSize: 16, color: Colors.grey), maxLines: 1),
            ]),
          ],
          if (_hasNutritionData(data.data["recommendation"]["p"])) ...[
            Padding(padding: EdgeInsets.symmetric(vertical: 30.sp), child: Divider(height: 0.2, color: Colors.grey.withOpacity(0.4))),
            Row(children: [
              SizedBox(width: 85.sp),
              const Text("Phospho (P205): ", style: TextStyle(fontSize: 16), maxLines: 1),
              Text("${data.data["recommendation"]["p"]}", style: const TextStyle(fontSize: 16, color: Colors.red), maxLines: 1),
              const Text(" Kg/cây/năm", style: TextStyle(fontSize: 16, color: Colors.grey), maxLines: 1),
            ]),
          ],
          if (_hasNutritionData(data.data["recommendation"]["k"])) ...[
            Padding(padding: EdgeInsets.symmetric(vertical: 30.sp), child: Divider(height: 0.2, color: Colors.grey.withOpacity(0.4))),
            Row(children: [
              SizedBox(width: 85.sp),
              const Text("Kali (K20): ", style: TextStyle(fontSize: 16), maxLines: 1),
              Text("${data.data["recommendation"]["k"]}", style: const TextStyle(fontSize: 16, color: Colors.red), maxLines: 1),
              const Text(" Kg/cây/năm", style: TextStyle(fontSize: 16, color: Colors.grey), maxLines: 1),
            ]),
          ],
          Padding(padding: EdgeInsets.symmetric(vertical: 30.sp), child: Divider(height: 0.2, color: Colors.grey.withOpacity(0.4))),
          Container(
              padding: EdgeInsets.symmetric(horizontal: 20.sp, vertical: 30.sp),
              decoration: BoxDecoration(borderRadius: BorderRadius.circular(20), color: Colors.red[400]?.withOpacity(0.4)),
              child: Row(
                children: [
                  const Icon(Icons.info, color: Colors.red),
                  const Text("Lưu ý: ", style: TextStyle(color: Colors.red)),
                  Expanded(
                      child: Text(
                          _treeType?.value != "lua"
                              ? '-Công thức khuyến cáo hiện đang áp dụng trên cây ăn trái giai đoạn kinh doanh. \n -Dữ liệu hiện tại đang được mô phỏng từ các điểm lấy mẫu lân cận, chỉ mang tính chất tham khảo.'
                              : "Dữ liệu hiện tại đang được mô phỏng từ các điểm lấy mẫu lân cận, chỉ mang tính chất tham khảo.",
                          style: const TextStyle(fontSize: 12),
                          maxLines: 4))
                ],
              ))
        ],
      ),
    )
  ]));
}

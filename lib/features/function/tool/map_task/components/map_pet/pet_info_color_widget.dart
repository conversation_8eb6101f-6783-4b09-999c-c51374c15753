import 'package:flutter/material.dart';
import 'package:hainong/common/ui/label_custom.dart';

class DiseaseColorLegendWidget extends StatefulWidget {
  const DiseaseColorLegendWidget({super.key});

  @override
  DiseaseColorLegendWidgetState createState() => DiseaseColorLegendWidgetState();
}

class DiseaseColorLegendWidgetState extends State<DiseaseColorLegendWidget> {
  bool isExpanded = false;

  void toggleExpanded() => setState(() => isExpanded = !isExpanded);

  @override
  Widget build(BuildContext context) {
    final List<MapCropCategory> categories = [
      MapCropCategory(name: "<PERSON><PERSON><PERSON>", color: const Color(0xFF0304af)),
      MapCropCategory(name: "Cà phê", color: const Color(0xFF006600)),
      MapCropCategory(name: "Sầu riêng", color: const Color(0xFF00ff99)),
      MapCropCategory(name: "<PERSON><PERSON> tiêu", color: const Color(0xFF00b4d8)),
    ];

    return GestureDetector(
      onTap: toggleExpanded,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        margin: const EdgeInsets.all(10),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(color: Colors.black.withOpacity(0.1), blurRadius: 4, offset: const Offset(0, 2)),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.info_outline, size: 16),
                const SizedBox(width: 8),
                const LabelCustom("Loại cây", color: Colors.black87, size: 14, weight: FontWeight.bold),
                const SizedBox(width: 4),
                Icon(isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down, size: 18),
              ],
            ),
            if (isExpanded) ...[
              const SizedBox(height: 8),
              ...categories.map((category) => Padding(
                    padding: const EdgeInsets.only(top: 8),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(width: 12, height: 12, decoration: BoxDecoration(color: category.color, shape: BoxShape.circle)),
                        const SizedBox(width: 8),
                        LabelCustom(category.name, color: Colors.black87, size: 12),
                      ],
                    ),
                  )),
            ],
          ],
        ),
      ),
    );
  }
}

class MapCropCategory {
  final String name;
  final Color color;

  MapCropCategory({required this.name, required this.color});
}

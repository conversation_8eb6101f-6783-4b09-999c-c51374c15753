import 'package:hainong/common/util/util.dart';

class MapAddressModel {
  double? latitude;
  double? longitude;
  String? address_full;
  String? district_name;
  String? province_name;
  int? districtId;
  int? provinceId;
  int? wardId;
  MapAddressModel({this.latitude = 0, this.longitude = 0, this.address_full = '', this.district_name = '', this.province_name = '', this.districtId = -1, this.provinceId = -1, this.wardId = -1});

  MapAddressModel fromJson(json, {String? keyName}) {
    provinceId = Util.getValueFromJson(json, keyName ?? 'province_id', -1);
    districtId = Util.getValueFromJson(json, keyName ?? 'district_id', -1);
    wardId = Util.getValueFromJson(json, keyName ?? 'ward_id', -1);
    address_full = Util.getValueFromJson(json, keyName ?? 'address_full', '');
    district_name = Util.getValueFromJson(json, keyName ?? 'district_name', '');
    province_name = Util.getValueFromJson(json, keyName ?? 'province_name', '');
    latitude = Util.getValueFromJson(json, keyName ?? 'latitude', 0.0);
    longitude = Util.getValueFromJson(json, keyName ?? 'longitude', 0.0);
    return this;
  }

  factory MapAddressModel.fromJson(Map<String, dynamic> json) => MapAddressModel().fromJson(json);

  /// Tạo bản sao với các thuộc tính mới
  MapAddressModel copyWith({
    double? latitude,
    double? longitude,
    String? address_full,
    String? district_name,
    String? province_name,
    int? districtId,
    int? provinceId,
    int? wardId,
  }) {
    return MapAddressModel(
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      address_full: address_full ?? this.address_full,
      district_name: district_name ?? this.district_name,
      province_name: province_name ?? this.province_name,
      districtId: districtId ?? this.districtId,
      provinceId: provinceId ?? this.provinceId,
      wardId: wardId ?? this.wardId,
    );
  }
}

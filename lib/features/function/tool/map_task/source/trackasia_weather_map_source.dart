import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:hainong/features/function/tool/map_task/map_constants.dart';
import 'package:hainong/features/function/tool/map_task/source/colors/map_weather_color.dart';
import 'package:trackasia_gl/trackasia_gl.dart';

class TrackAsiaWeatherMapSource {
  static const List<String> layers = [
    "temperature",
    "rainfall",
    "weather_points",
    "temperature-contour-layer",
    "rainfall-animation-layer",
  ];

  static const List<String> weatherIcons = [
    '01d', '01dd', '01ddd', '02d', '03d', '04d', '09d', '10d', '11d', '13d', '50d', // Day icons
    '01n', '02n', '03n', '04n', '09n', '10n', '11n', '13n', '50n', // Night icons
    '3n', '6-02', '9d', '10dd', '11dd', // Special variants
  ];

  // Time step collections
  static List<String> rainfallTimeSteps = [];
  static List<String> temperatureTimeSteps = [];
  static List<String> weatherPointsTimeSteps = [];

  // Current weather state
  static String currentWeatherType = 'temperature';
  static String currentTimestamp = '';
  static bool isVisible = false;
  static Timer? animationTimer;

  //================[INITIALIZATION]================//

  static void initializeTimeSteps() {
    rainfallTimeSteps.clear();
    temperatureTimeSteps.clear();
    weatherPointsTimeSteps.clear();

    final now = DateTime.now();
    for (int dayDiff = 0; dayDiff <= 4; dayDiff++) {
      final date = DateTime(now.year, now.month, now.day + dayDiff);
      final day = date.day.toString().padLeft(2, '0');
      final month = date.month.toString().padLeft(2, '0');
      final year = date.year.toString().substring(2);

      for (int hourIndex = 0; hourIndex <= 7; hourIndex++) {
        final hour = (hourIndex * 3 + 1).toString().padLeft(2, '0');
        final timeStep = "${day}-${month}-${year}_$hour";

        rainfallTimeSteps.add(timeStep);
        temperatureTimeSteps.add(timeStep);
        weatherPointsTimeSteps.add(timeStep);
      }
    }

    if (temperatureTimeSteps.isNotEmpty) {
      currentTimestamp = temperatureTimeSteps[0];
    }
  }

  static Future<void> preloadWeatherIcons(TrackAsiaMapController? mapController) async {
    if (mapController == null) return;

    try {
      for (final iconName in weatherIcons) {
        try {
          final iconPath = "assets/images/v9/map/weather/$iconName.png";
          await _loadWeatherIcon(mapController, iconName, iconPath);
        } catch (err) {
          debugPrint("Weather: Failed to load icon '$iconName': $err");
        }
      }
    } catch (e) {
      debugPrint("Weather: Error preloading icons: $e");
    }
  }

  static Future<void> _loadWeatherIcon(TrackAsiaMapController mapController, String iconName, String iconPath) async {
    try {
      final ByteData data = await rootBundle.load(iconPath);
      final Uint8List bytes = data.buffer.asUint8List();
      await mapController.addImage(iconName, bytes);
    } catch (e) {
      debugPrint("Weather: Failed to load icon $iconName: $e");
    }
  }

  //================[SOURCE MANAGEMENT]================//

  static Future<void> addSourceWeatherMap(TrackAsiaMapController? mapController, String sourceIdWeatherMap) async {
    if (mapController == null) return;

    final sourceIds = await mapController.getSourceIds();
    if (!sourceIds.contains(sourceIdWeatherMap)) {
      try {
        await mapController.addSource(sourceIdWeatherMap, const VectorSourceProperties(tiles: [MapConstants.weatherTileUrl], maxzoom: 11, minzoom: 3));
        isVisible = false;
        initializeTimeSteps();
        await preloadWeatherIcons(mapController);
      } catch (e) {
        debugPrint("Weather: Error adding weather source: $e");
      }
    }
  }

  static Future<void> removeSourceWeatherMap(TrackAsiaMapController? mapController, String sourceIdWeatherMap, String keyLayerWeatherMap) async {
    if (mapController == null) return;
    
    try {
      // First remove all weather layers
      await removeAllLayerWeatherMap(mapController);
      
      // Small delay to ensure layers are properly removed
      await Future.delayed(const Duration(milliseconds: 50));
      
      // Then remove the weather source
      final sources = await mapController.getSourceIds();
      if (sources.contains(sourceIdWeatherMap)) {
        await mapController.removeSource(sourceIdWeatherMap);
      }
      
      // Clear any symbols that might be related to weather
      await mapController.clearSymbols();
    } catch (e) {
      debugPrint("Error in removeSourceWeatherMap: $e");
    }
  }

  static Future<void> removeAllLayerWeatherMap(TrackAsiaMapController? mapController) async {
    if (mapController == null) return;
    
    try {
      final layerIds = await mapController.getLayerIds();
      
      // Check for weather layers using more comprehensive patterns
      final weatherLayerPatterns = [
        MapConstants.keyLayerWeatherMap,  // Matches the weather map key prefix
        'temperature',
        'rainfall',
        'weather_points',
        'weather-',
        'contour-layer',
        'animation-layer',
      ];
      
      for (var layerId in layerIds) {
        bool isWeatherLayer = false;
        // Check if this layer matches any weather layer pattern
        for (var pattern in weatherLayerPatterns) {
          if (layerId.contains(pattern)) {
            isWeatherLayer = true;
            break;
          }
        }
        
        // Remove the layer if it's a weather layer
        if (isWeatherLayer) {
          try {
            await mapController.removeLayer(layerId);
            await Future.delayed(const Duration(milliseconds: 10)); // Small delay between layer removals
            debugPrint("Removed weather layer: $layerId");
          } catch (e) {
            debugPrint("Error removing weather layer $layerId: $e");
          }
        }
      }
      
      // Also try removing all standard layers to be sure
      for (var layer in layers) {
        try {
          if (layerIds.contains(layer)) {
            await mapController.removeLayer(layer);
            await Future.delayed(const Duration(milliseconds: 10));
          }
          
          // Also try removing layer with the weather map key prefix
          final fullLayerId = "${MapConstants.keyLayerWeatherMap}_$layer";
          if (layerIds.contains(fullLayerId)) {
            await mapController.removeLayer(fullLayerId);
            await Future.delayed(const Duration(milliseconds: 10));
          }
        } catch (e) {
          // Silently continue if a specific layer wasn't found
        }
      }
    } catch (e) {
      debugPrint("Error in removeAllLayerWeatherMap: $e");
    }
  }

  //================[LAYER MANAGEMENT]================//

  static Future<void> setLayerWeatherMap(TrackAsiaMapController? mapController, String sourceIdWeatherMap, String weatherType, String timeStep, {double? fillOpacity, int? timelineIndex}) async {
    if (mapController == null) return;

    final sourceIds = await mapController.getSourceIds();
    if (!sourceIds.contains(sourceIdWeatherMap)) return;

    currentWeatherType = weatherType;
    currentTimestamp = timeStep;
    final currentLayers = await mapController.getLayerIds();

    // Add layers if they don't exist
    if (!currentLayers.contains('temperature')) {
      await addTemperatureLayer(mapController, sourceIdWeatherMap, timeStep, fillOpacity: fillOpacity, timelineIndex: timelineIndex);
    }
    if (!currentLayers.contains('rainfall')) {
      await addRainfallLayer(mapController, sourceIdWeatherMap, timeStep, fillOpacity: fillOpacity, timelineIndex: timelineIndex);
    }
    if (!currentLayers.contains('weather_points')) {
      await addWeatherPointsLayer(mapController, sourceIdWeatherMap, timeStep, fillOpacity: fillOpacity, timelineIndex: timelineIndex);
    }

    await updateWeatherLayerVisibility(mapController, weatherType, timeStep);
  }

  static Future<void> addTemperatureLayer(TrackAsiaMapController? mapController, String sourceId, String timeStep, {double? fillOpacity, int? timelineIndex}) async {
    await mapController?.getLayerIds().then((value) async {
      if (!value.contains('temperature')) {
        await mapController.addLayer(
          sourceId,
          'temperature',
          FillLayerProperties(fillAntialias: true, fillColor: MapWeatherColor.temperatureFillColors, fillOpacity: fillOpacity ?? 0.7, fillOutlineColor: "rgba(255, 255, 255, 0)"),
          filter: [
            "==",
            ["get", "time_step"],
            timeStep
          ],
          sourceLayer: "temperature",
          belowLayerId: 'water',
        );
      }
    });
  }

  static Future<void> addRainfallLayer(TrackAsiaMapController? mapController, String sourceId, String timeStep, {double? fillOpacity, int? timelineIndex}) async {
    await mapController?.getLayerIds().then((value) async {
      if (!value.contains('rainfall')) {
        await mapController.addLayer(
          sourceId,
          'rainfall',
          FillLayerProperties(fillAntialias: true, fillColor: MapWeatherColor.rainfallFillColors, fillOpacity: fillOpacity ?? 0.7, fillOutlineColor: "rgba(255, 255, 255, 0)"),
          filter: [
            "==",
            ["get", "time_step"],
            timeStep
          ],
          sourceLayer: "rainfall",
          belowLayerId: 'water',
        );
      }
    });
  }

  static Future<void> addWeatherPointsLayer(TrackAsiaMapController? mapController, String sourceId, String timeStep, {double? fillOpacity, int? timelineIndex}) async {
    await mapController?.getLayerIds().then((value) async {
      if (!value.contains('weather_points')) {
        await mapController.addLayer(
          sourceId,
          'weather_points',
          const SymbolLayerProperties(iconImage: ["get", "icon"], iconSize: 1.0, iconAllowOverlap: false),
          filter: [
            "==",
            ["get", "time_step"],
            timeStep
          ],
          sourceLayer: "weather_points",
        );
      }
    });
  }

  static Future<void> updateWeatherLayerVisibility(TrackAsiaMapController? mapController, String weatherType, String timestamp) async {
    if (mapController == null) return;

    try {
      // Update layer visibility
      for (final layerId in ['temperature', 'rainfall']) {
        final isVisible = (layerId == weatherType);
        await mapController.setLayerVisibility(layerId, isVisible);
      }
      await mapController.setLayerVisibility('weather_points', TrackAsiaWeatherMapSource.isVisible);

      // Update filters
      final futures = <Future>[];
      futures.add(mapController.setFilter(weatherType, [
        '==',
        ['get', 'time_step'],
        timestamp
      ]));
      futures.add(mapController.setFilter('weather_points', [
        '==',
        ['get', 'time_step'],
        timestamp
      ]));
      await Future.wait(futures);
    } catch (e) {
      debugPrint("Weather: Error updating layer visibility: $e");
    }
  }

  static Future<void> setWeatherLayerOpacity(TrackAsiaMapController? mapController, String weatherType, double opacity) async {
    if (mapController == null) return;

    try {
      await mapController.setLayerProperties(weatherType, FillLayerProperties(fillOpacity: opacity));
    } catch (e) {
      debugPrint("Weather: Error setting opacity: $e");
    }
  }

  static Future<void> toggleWeatherVisibility(TrackAsiaMapController? mapController) async {
    isVisible = !isVisible;
    await updateWeatherLayerVisibility(mapController, currentWeatherType, currentTimestamp);
  }

  //================[ANIMATION MANAGEMENT]================//

  static void runWeatherAnimation(TrackAsiaMapController? mapController, String sourceId, {Duration interval = const Duration(seconds: 1)}) {
    stopWeatherAnimation();

    final timestamps = currentWeatherType == 'rainfall' ? rainfallTimeSteps : temperatureTimeSteps;
    int currentIndex = timestamps.indexOf(currentTimestamp);
    if (currentIndex == -1) currentIndex = 0;

    animationTimer = Timer.periodic(interval, (timer) {
      currentIndex = (currentIndex + 1) % timestamps.length;
      final nextTimestamp = timestamps[currentIndex];
      updateWeatherLayerVisibility(mapController, currentWeatherType, nextTimestamp);
      currentTimestamp = nextTimestamp;
    });
  }

  static void stopWeatherAnimation() {
    animationTimer?.cancel();
    animationTimer = null;
  }

  //================[TIMELINE UTILITIES]================//

  static String getTimeStepFromIndex(int timelineIndex) {
    if (temperatureTimeSteps.isEmpty) initializeTimeSteps();
    if (timelineIndex >= 0 && timelineIndex < temperatureTimeSteps.length) {
      return temperatureTimeSteps[timelineIndex];
    }
    return temperatureTimeSteps.isNotEmpty ? temperatureTimeSteps[0] : '';
  }

  static int getIndexFromTimeStep(String timeStep) {
    if (temperatureTimeSteps.isEmpty) initializeTimeSteps();
    final index = temperatureTimeSteps.indexOf(timeStep);
    return index >= 0 ? index : 0;
  }

  static String parseTimeStepToReadable(String timeStep) {
    try {
      final parts = timeStep.split('_');
      if (parts.length != 2) return timeStep;

      final datePart = parts[0];
      final hourPart = parts[1];
      final dateParts = datePart.split('-');
      if (dateParts.length != 3) return timeStep;

      final day = dateParts[0];
      final month = dateParts[1];
      final year = "20${dateParts[2]}";
      final hour = hourPart;

      return 'Ngày $day/$month/$year, Giờ $hour:00';
    } catch (e) {
      debugPrint("Weather: Error parsing time step $timeStep: $e");
      return timeStep;
    }
  }

  static List<String> getTimestampsByType(String weatherType) {
    switch (weatherType) {
      case 'rainfall':
        return rainfallTimeSteps;
      case 'temperature':
        return temperatureTimeSteps;
      case 'weather_points':
        return weatherPointsTimeSteps;
      default:
        return temperatureTimeSteps;
    }
  }

  //================[UPDATE METHODS]================//

  static Future<void> updateWeatherForTimeStep(TrackAsiaMapController? mapController, String sourceIdWeatherMap, String weatherType, int timelineIndex, {double? fillOpacity}) async {
    final timeStep = getTimeStepFromIndex(timelineIndex);
    await setLayerWeatherMap(mapController, sourceIdWeatherMap, weatherType, timeStep, fillOpacity: fillOpacity, timelineIndex: timelineIndex);
  }

  static Future<void> updateWeatherForTimeStepSmooth(TrackAsiaMapController? mapController, String sourceIdWeatherMap, String weatherType, int timelineIndex, {double? fillOpacity}) async {
    final timeStep = getTimeStepFromIndex(timelineIndex);
    currentWeatherType = weatherType;
    currentTimestamp = timeStep;

    try {
      await updateWeatherLayerVisibility(mapController, weatherType, timeStep);
    } catch (e) {
      debugPrint("Weather: Error in smooth update: $e");
      await updateWeatherForTimeStep(mapController, sourceIdWeatherMap, weatherType, timelineIndex, fillOpacity: fillOpacity);
    }
  }

  static Future<void> updateWeatherForTimeStepInstant(TrackAsiaMapController? mapController, String sourceIdWeatherMap, String weatherType, int timelineIndex, {double? fillOpacity}) async {
    final timeStep = getTimeStepFromIndex(timelineIndex);
    currentWeatherType = weatherType;
    currentTimestamp = timeStep;

    try {
      await updateWeatherLayerVisibility(mapController, weatherType, timeStep);
    } catch (e) {
      await updateWeatherForTimeStep(mapController, sourceIdWeatherMap, weatherType, timelineIndex, fillOpacity: fillOpacity);
    }
  }

  static void createTimeTransitionEffect(TrackAsiaMapController? mapController, String sourceIdWeatherMap, String weatherType, int fromIndex, int toIndex, {Duration? duration}) {
    const steps = 5;
    final stepDuration = (duration ?? const Duration(milliseconds: 500)).inMilliseconds ~/ steps;

    for (int i = 0; i <= steps; i++) {
      final progress = i / steps;
      final currentIndex = fromIndex + ((toIndex - fromIndex) * progress).round();
      Timer(Duration(milliseconds: stepDuration * i), () {
        updateWeatherForTimeStep(mapController, sourceIdWeatherMap, weatherType, currentIndex);
      });
    }
  }

  static Future<void> initializeWeatherMap(TrackAsiaMapController? mapController, String sourceId) async {
    if (mapController == null) return;

    initializeTimeSteps();
    isVisible = false;
    currentWeatherType = 'temperature';
    currentTimestamp = temperatureTimeSteps.isNotEmpty ? temperatureTimeSteps[0] : '';

    await addSourceWeatherMap(mapController, sourceId);
    await setLayerWeatherMap(mapController, sourceId, currentWeatherType, currentTimestamp);
  }
}

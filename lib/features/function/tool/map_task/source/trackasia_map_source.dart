import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart' show rootBundle;
import 'package:hainong/features/function/tool/map_task/map_constants.dart';
import 'package:hainong/features/function/tool/map_task/utils/map_utils.dart';
import 'package:hainong/features/function/tool/suggestion_map/UI/utils/trackasia_util.dart';
import 'package:trackasia_gl/trackasia_gl.dart';

import '../models/map_enum.dart';

class TrackAsiaMapSource {
  Future<void> addTrackasiaClusterMap({required TrackAsiaMapController? mapController, required String sourceId, required Map<String, dynamic> dataMap, required String keyChartName}) async {
    if (dataMap.isEmpty || mapController == null) return;

    final keyChartImageCircleRate = "${keyChartName}_chart_image_circle_rate";
    final keyChartCircleRate = "${keyChartName}_chart_circle_rate";
    final keyChartChildren = "${keyChartName}_chart_circle_children";
    final keyChartCircleCount = "${keyChartName}_chart_circle_count";

    dataMap["type"] = "FeatureCollection";
    await addClusteredPointSource(mapController: mapController, sourceId: sourceId, data: dataMap);
    await addClusteredPointLayers(
        mapController: mapController,
        sourceId: sourceId,
        keyChartImageCircleRate: keyChartImageCircleRate,
        keyChartCircleRate: keyChartCircleRate,
        keyChartChildren: keyChartChildren,
        keyChartCircleCount: keyChartCircleCount);
  }

  Future<void> addPetClusterMap(
      {required TrackAsiaMapController? mapController,
      required String sourceId,
      required Map<String, dynamic> dataMap,
      required String keyChartName,
      String? provinceId,
      BuildContext? context}) async {
    if (dataMap.isEmpty || mapController == null) return;

    try {
      dataMap["type"] = "FeatureCollection";

      // Use province-specific source ID if provinceId is provided
      final actualSourceId = provinceId != null ? "${sourceId}_province_$provinceId" : sourceId;

      await addClusteredPointSource(mapController: mapController, sourceId: actualSourceId, data: dataMap, maxZoom: 10);

      // Generate layer keys with province suffix if needed
      final provinceSuffix = provinceId != null ? "_province_$provinceId" : "";
      final keyChartImageCircleRate = "${keyChartName}_chart_image_circle_rate$provinceSuffix";
      final keyChartChildren = "${keyChartName}_chart_circle_children$provinceSuffix";
      final keyPetChildren = "${keyChartName}_pet_circle_children$provinceSuffix";
      final keyChartCircleCount = "${keyChartName}_chart_circle_count$provinceSuffix";

      // Add pet-specific layers with delays for province loading
      await addImageCircleRate(mapController: mapController, sourceId: actualSourceId, keyLayer: keyChartImageCircleRate);
      if (provinceId != null) await Future.delayed(const Duration(milliseconds: 30));

      await addPetCircleColor(mapController: mapController, sourceId: actualSourceId, keyLayer: keyChartChildren);
      if (provinceId != null) await Future.delayed(const Duration(milliseconds: 30));

      await addIconPetChildren(mapController: mapController, sourceId: actualSourceId, keyLayer: keyPetChildren);
      if (provinceId != null) await Future.delayed(const Duration(milliseconds: 30));

      await addCircleCountOptimized(mapController: mapController, sourceId: actualSourceId, keyLayer: keyChartCircleCount);
    } catch (e) {
      final location = provinceId != null ? "province $provinceId" : "filtered data";
      logDebug("Error adding pet map for $location: $e");
    }
  }

  Future<void> addWeatherClusterMap({required TrackAsiaMapController? mapController, required String sourceId, required Map<String, dynamic> dataMap, required String keyChartName}) async {
    if (dataMap.isEmpty || mapController == null) return;

    final keyImage = "${keyChartName}_image";
    final keyCircle = "${keyChartName}_circle";
    final keyCircleCount = "${keyChartName}_circle_count";
    final keyCircleColor = "${keyChartName}_circle_color";

    await addClusteredPointSource(mapController: mapController, sourceId: sourceId, data: dataMap, maxZoom: 11);
    await addClusteredWeatherLayers(
      mapController: mapController,
      sourceId: sourceId,
      keyChartImage: keyImage,
      keyChartCircle: keyCircle,
      keyChartCircleCount: keyCircleCount,
      keyCircleColor: keyCircleColor,
    );
  }

  Future<void> addModelClusterMap(
      {required TrackAsiaMapController? mapController,
      required MapModelEnum model,
      required String sourceId,
      required Map<String, dynamic> dataMap,
      required String keyChartName,
      required String image}) async {
    if (dataMap.isEmpty || mapController == null) return;

    final keyImage = "${keyChartName}_image";
    final keyText = "${keyChartName}_text";
    final keyCircle = "${keyChartName}_circle";
    final keyCircleCount = "${keyChartName}_circle_count";

    await addClusteredPointSource(mapController: mapController, sourceId: sourceId, data: dataMap, maxZoom: 10);
    await addClusteredModelLayers(
        mapController: mapController, model: model, sourceId: sourceId, keyImage: keyImage, keyText: keyText, keyCircle: keyCircle, keyCircleCount: keyCircleCount, image: image);
  }

  // Source management
  Future<void> addClusteredPointSource({required TrackAsiaMapController? mapController, required String sourceId, required Map<String, dynamic>? data, double? maxZoom}) async {
    if (mapController == null || data == null) return;

    final sourceIds = await mapController.getSourceIds();
    final exists = sourceIds.contains(sourceId);

    if (exists) {
      await mapController.setGeoJsonSource(sourceId, data);
    } else {
      await mapController.addSource(sourceId, GeojsonSourceProperties(data: data, cluster: true, clusterMaxZoom: maxZoom ?? 10));
    }
  }

  // Layer creation methods
  Future<void> addClusteredPointLayers(
      {required TrackAsiaMapController? mapController,
      required String sourceId,
      required String keyChartImageCircleRate,
      required String keyChartCircleRate,
      required String keyChartChildren,
      required String keyChartCircleCount}) async {
    await addImageCircleRate(mapController: mapController, sourceId: sourceId, keyLayer: keyChartImageCircleRate);
    await addChartCircleRate(mapController: mapController, sourceId: sourceId, keyLayer: keyChartCircleRate, keyImage: keyChartImageCircleRate);
    await addChartChildren(mapController: mapController, sourceId: sourceId, keyLayer: keyChartChildren);
    await addCircleCount(mapController: mapController, sourceId: sourceId, keyLayer: keyChartCircleCount);
  }

  Future<void> addClusteredWeatherLayers(
      {required TrackAsiaMapController? mapController,
      required String sourceId,
      required String keyChartImage,
      required String keyChartCircle,
      required String keyCircleColor,
      required String keyChartCircleCount}) async {
    await addWeatherCircle(mapController: mapController, sourceId: sourceId, keyLayer: keyChartCircle, keyImage: keyChartCircle);
    await addImageWeather(mapController: mapController, sourceId: sourceId, keyLayer: keyChartImage);
  }

  Future<void> addClusteredModelLayers(
      {required TrackAsiaMapController? mapController,
      required MapModelEnum model,
      required String sourceId,
      required String keyImage,
      required String keyText,
      required String keyCircle,
      required String keyCircleCount,
      required String image}) async {
    await addImageModel(mapController: mapController, sourceId: sourceId, keyLayer: keyImage, image: image);
    await addModelCount(mapController: mapController, sourceId: sourceId, keyLayer: keyCircleCount);
  }

  // Individual layer methods
  Future<void> addImageCircleRate({required TrackAsiaMapController? mapController, required String sourceId, required String keyLayer}) async {
    if (mapController == null) return;

    try {
      await removeLayer(mapController: mapController, keyLayer: keyLayer);
      await mapController.addLayer(
        sourceId,
        keyLayer,
        const SymbolLayerProperties(
          iconImage: [
            'case',
            [
              '>=',
              ['get', 'point_count'],
              10000
            ],
            'cluster_10000',
            [
              '>=',
              ['get', 'point_count'],
              5000
            ],
            'cluster_5000',
            [
              '>=',
              ['get', 'point_count'],
              2500
            ],
            'cluster_2500',
            [
              '>=',
              ['get', 'point_count'],
              2000
            ],
            'cluster_2000',
            [
              '>=',
              ['get', 'point_count'],
              1000
            ],
            'cluster_1000',
            [
              '>=',
              ['get', 'point_count'],
              500
            ],
            'cluster_500',
            [
              '>=',
              ['get', 'point_count'],
              200
            ],
            'cluster_200',
            [
              '>=',
              ['get', 'point_count'],
              100
            ],
            'cluster_100',
            [
              '>=',
              ['get', 'point_count'],
              50
            ],
            'cluster_50',
            [
              '>=',
              ['get', 'point_count'],
              10
            ],
            'cluster_10',
            'cluster_1'
          ],
          iconSize: [
            'interpolate',
            ['linear'],
            ['get', 'point_count'],
            1,
            1.0,
            10,
            1.2,
            50,
            1.3,
            100,
            1.4,
            500,
            1.6,
            1000,
            1.8,
            5000,
            2.0,
            10000,
            2.2
          ],
          iconAllowOverlap: true,
          symbolSortKey: 10,
          iconIgnorePlacement: true,
        ),
        filter: [Expressions.has, 'point_count'],
      );
    } catch (e, stackTrace) {
      logDebug("Error adding image circle rate layer: $e");
    }
  }

  Future<void> addImageWeather({required TrackAsiaMapController? mapController, required String sourceId, required String keyLayer}) async {
    if (mapController == null) return;

    await removeLayer(mapController: mapController, keyLayer: keyLayer);
    await mapController.addLayer(
        sourceId,
        keyLayer,
        SymbolLayerProperties(
          iconImage: ['get', 'icon'],
          iconSize: Platform.isIOS ? 0.8 : 0.4,
          iconAllowOverlap: true,
        ));
  }

  Future<void> addImageModel({required TrackAsiaMapController? mapController, required String sourceId, required String keyLayer, required String image}) async {
    if (mapController == null) return;

    await removeLayer(mapController: mapController, keyLayer: keyLayer);
    await mapController.addLayer(
        sourceId,
        keyLayer,
        SymbolLayerProperties(
          iconImage: image,
          iconSize: 1,
          iconAllowOverlap: true,
        ));
  }

  Future<void> addChartCircleRate({required TrackAsiaMapController? mapController, required String sourceId, required String keyLayer, required String keyImage}) async {
    if (mapController == null) return;

    const pointKey = "point_count";

    try {
      await removeLayer(mapController: mapController, keyLayer: keyLayer);
      await mapController.addSymbolLayer(
          sourceId,
          keyLayer,
          SymbolLayerProperties(
            textHaloWidth: 1,
            textSize: 6,
            iconImage: keyImage,
            iconSize: [
              'interpolate',
              ['linear'],
              ['get', pointKey],
              1,
              0.7,
              10,
              0.8,
              50,
              0.9,
              100,
              1.0,
              200,
              1.1,
              500,
              1.2,
              1000,
              1.3,
              2000,
              1.4,
              5000,
              1.5,
              10000,
              1.6
            ],
            iconAllowOverlap: true,
            symbolSortKey: 1,
            iconIgnorePlacement: true,
          ),
          filter: [Expressions.has, pointKey]);
    } catch (e) {
      logDebug("Error adding chart circle rate layer: $e");
    }
  }

  Future<void> addWeatherCircle({required TrackAsiaMapController? mapController, required String sourceId, required String keyLayer, required String keyImage}) async {
    if (mapController == null) return;

    await removeLayer(mapController: mapController, keyLayer: keyLayer);
    await mapController.addLayer(
        sourceId,
        keyLayer,
        SymbolLayerProperties(
          iconImage: [
            'step',
            ['get', 'point_count'],
            '01dd',
            1000,
            '01dd',
            5000,
            '01dd',
          ],
          iconSize: Platform.isIOS ? 0.6 : 0.32,
          iconAllowOverlap: true,
        ),
        filter: [Expressions.has, "point_count"]);
  }

  Future<void> addCircleCount({required TrackAsiaMapController? mapController, required String sourceId, required String keyLayer, int? chunkIndex}) async {
    if (mapController == null) return;

    const pointKey = "point_count";
    await removeLayer(mapController: mapController, keyLayer: keyLayer);
    await mapController.addSymbolLayer(sourceId, keyLayer, const SymbolLayerProperties(textField: pointKey, textSize: 12), filter: [Expressions.has, pointKey]);
  }

  Future<void> addModelCount({required TrackAsiaMapController? mapController, required String sourceId, required String keyLayer}) async {
    if (mapController == null) return;

    const pointAbbreviated = "point_count_abbreviated";
    await removeLayer(mapController: mapController, keyLayer: keyLayer);
    await mapController.addLayer(
        sourceId,
        keyLayer,
        const SymbolLayerProperties(
            textField: [Expressions.get, pointAbbreviated],
            textSize: 12.0,
            textColor: '#000000',
            textHaloColor: '#00FF00',
            textHaloWidth: 10.0,
            textPadding: 2.0,
            textHaloBlur: 1.0,
            textAllowOverlap: true,
            textIgnorePlacement: true));
  }

  Future<void> addChartChildren({required TrackAsiaMapController? mapController, required String sourceId, required String keyLayer}) async {
    if (mapController == null) return;

    const pointKey = "point_count";
    await removeLayer(mapController: mapController, keyLayer: keyLayer);
    await mapController.addCircleLayer(
        sourceId,
        keyLayer,
        const CircleLayerProperties(circleColor: [
          'case',
          ['has', 'mag'],
          ['get', 'mag'],
          '#00CC33'
        ], circleRadius: 10),
        filter: [
          "!",
          [Expressions.has, pointKey]
        ]);
  }

  Future<void> addPetCircleColor({required TrackAsiaMapController? mapController, required String sourceId, required String keyLayer}) async {
    if (mapController == null) return;

    const pointKey = "point_count";

    try {
      await removeLayer(mapController: mapController, keyLayer: keyLayer);
      await mapController.addCircleLayer(
          sourceId,
          keyLayer,
          const CircleLayerProperties(circleStrokeColor: [
            'case',
            ['has', 'mag'],
            ['get', 'mag'],
            '#00CC33'
          ], circleColor: 'rgba(0, 0, 0, 0)', circleStrokeWidth: 2, circleRadius: 16),
          filter: [
            "!",
            [Expressions.has, pointKey]
          ]);
    } catch (e) {
      // logDebug("Error adding pet circle color layer: $e");
    }
  }

  Future<void> addIconPetChildren({required TrackAsiaMapController? mapController, required String sourceId, required String keyLayer}) async {
    if (mapController == null) return;

    const pointKey = "point_count";

    try {
      await removeLayer(mapController: mapController, keyLayer: keyLayer);
      await mapController.addLayer(
          sourceId,
          keyLayer,
          SymbolLayerProperties(
            iconImage: ['get', 'category_id'],
            iconSize: Platform.isIOS ? 0.8 : 0.8,
            iconAllowOverlap: true,
          ),
          filter: [
            "!",
            [Expressions.has, pointKey]
          ]);
    } catch (e) {
      // logDebug("Error adding pet children layer: $e");
    }
  }

  Future<void> addCircleCountOptimized({required TrackAsiaMapController? mapController, required String sourceId, required String keyLayer}) async {
    if (mapController == null) return;

    const pointKey = "point_count";

    try {
      await removeLayer(mapController: mapController, keyLayer: keyLayer);

      const textFieldExpression = [
        "case",
        [
          ">=",
          ["get", pointKey],
          1000
        ],
        [
          "concat",
          [
            "to-string",
            [
              "/",
              [
                "round",
                [
                  "*",
                  [
                    "/",
                    ["get", pointKey],
                    1000
                  ],
                  10
                ]
              ],
              10
            ]
          ],
          "k"
        ],
        [
          "to-string",
          ["get", pointKey]
        ]
      ];

      await mapController.addSymbolLayer(
          sourceId,
          keyLayer,
          const SymbolLayerProperties(
            textField: textFieldExpression,
            textSize: 12,
            textColor: '#000000',
            textHaloColor: '#FFFFFF',
            textHaloWidth: 1.5,
            textAllowOverlap: true,
            iconAllowOverlap: true,
            symbolSortKey: 10,
          ),
          filter: [Expressions.has, pointKey]);
    } catch (e) {
      logDebug("Error adding optimized circle count layer: $e");
    }
  }

  // Utility methods
  static Future<void> removeSource({required TrackAsiaMapController? mapController, required String sourceId}) async {
    if (mapController == null) return;

    final sourceIds = await mapController.getSourceIds();
    if (sourceIds.contains(sourceId)) {
      await mapController.removeSource(sourceId);
    }
  }

  static Future<void> removeLayer({required TrackAsiaMapController? mapController, required String keyLayer, List<dynamic>? layerIds}) async {
    if (mapController == null) return;

    List<dynamic> _layerIds = layerIds ?? await mapController.getLayerIds();
    if (_layerIds.contains(keyLayer)) {
      await mapController.removeLayer(keyLayer);
    }
  }

  static Future<void> removeSourcePetMap(TrackAsiaMapController? mapController, String sourceId, String baseLayerId) async {
    if (mapController == null) return;

    try {
      final layerIds = await mapController.getLayerIds();
      for (var layerId in layerIds) {
        if (layerId.startsWith("disease_layer_") || layerId.contains(baseLayerId)) {
          try {
            await mapController.removeLayer(layerId);
          } catch (e) {
            logDebug("Error removing layer $layerId: $e");
          }
        }
      }

      try {
        await mapController.removeSource(sourceId);
      } catch (e) {
        logDebug("Error removing source $sourceId: $e");
      }
    } catch (e) {
      logDebug("Error in removeSourcePetMap: $e");
    }
  }

  static void removeSourceWeatherMap(TrackAsiaMapController? mapController, String sourceId, String layerId) {
    removeSource(mapController: mapController, sourceId: sourceId);
    mapController?.getLayerIds().then((value) {
      removeLayer(layerIds: value, mapController: mapController, keyLayer: "${layerId}_image");
      removeLayer(layerIds: value, mapController: mapController, keyLayer: "${layerId}_circle");
      removeLayer(layerIds: value, mapController: mapController, keyLayer: "${layerId}_circle_count");
      removeLayer(layerIds: value, mapController: mapController, keyLayer: "${layerId}_circle_color");
    });
  }

  static Future<void> removeSourceModelMap(TrackAsiaMapController? mapController, String sourceId, String layerId) async {
    if (mapController == null) return;
    
    try {
      // First remove all associated layers
      final layerIds = await mapController.getLayerIds();
      final layersToRemove = [
        "${layerId}_image",
        "${layerId}_circle", 
        "${layerId}_circle_count",
        // Additional potential layer names that might be created
        "${layerId}_symbol",
        "${layerId}_line",
        "${layerId}_fill"
      ];
      
      for (final layerName in layersToRemove) {
        try {
          if (layerIds.contains(layerName)) {
            await mapController.removeLayer(layerName);
            // Small delay to ensure layer is properly removed
            await Future.delayed(const Duration(milliseconds: 10));
          }
        } catch (e) {
          logDebug("Error removing model layer $layerName: $e");
        }
      }
      
      // Wait to ensure all layers are properly removed
      await Future.delayed(const Duration(milliseconds: 50));
      
      // Then remove the source
      final sources = await mapController.getSourceIds();
      if (sources.contains(sourceId)) {
        await mapController.removeSource(sourceId);
      }
      
      // Clear any related symbols
      await mapController.clearSymbols();
    } catch (e) {
      logDebug("Error in removeSourceModelMap: $e");
    }
  }

  static Future<void> addLayerWithParams(TrackAsiaMapController? mapController, String sourceId, String layerId, List<dynamic>? filter, List<dynamic> fillColor, {double? fillOpacity}) async {
    await addLayer(mapController: mapController, sourceId: sourceId, layerId: layerId, filter: filter, fillColor: fillColor, fillOpacity: fillOpacity);
  }

  static Future<void> addLayer(
      {required mapController, required String sourceId, required String layerId, required List<dynamic>? filter, required List<dynamic> fillColor, double? fillOpacity}) async {
    mapController?.getLayerIds().then((value) async {
      if (!value.contains(layerId)) {
        await mapController.addLayer(sourceId, layerId, FillLayerProperties(fillColor: fillColor, fillOpacity: fillOpacity ?? 1), filter: filter, belowLayerId: 'waterway', sourceLayer: "soil");
      }
    });
  }

  // Static utility methods for loading icons
  static Future<void> loadIcons(TrackAsiaMapController mapController) async {
    try {
      for (String path in MapConstants.imageIconMapPaths) {
        final ByteData byteData = await rootBundle.load(path);
        final Uint8List bytes = byteData.buffer.asUint8List();
        final String imageName = path.split('/').last.split('.').first;
        await mapController.addImage(imageName, bytes);
      }
    } catch (e) {
      logDebug("Error loading icons: $e");
    }
  }

  static Future<void> loadDonutChartIcons(TrackAsiaMapController mapController) async {
    try {
      final clusterLevels = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 15, 20, 25, 30, 40, 50, 75, 100, 150, 200, 300, 500, 750, 1000, 1500, 2000, 2500, 3000, 5000, 7500, 10000];
      final futures = <Future>[];
      for (var level in clusterLevels) {
        final future = _createAndLoadClusterIcon(mapController, level);
        futures.add(future);
      }

      await Future.wait(futures);
    } catch (e) {
      debugPrint("Error loading donut chart icons: $e");
    }
  }

  static Future<void> _createAndLoadClusterIcon(TrackAsiaMapController mapController, int level) async {
    try {
      final imageBytes = await TrackasiaUtils.createDonutChartPng(level);
      if (imageBytes != null) await mapController.addImage('cluster_$level', imageBytes);
    } catch (e) {
      debugPrint("Error loading cluster icon $level: $e");
    }
  }

  static Future<void> removePetLayerMap(TrackAsiaMapController? mapController, String baseSourceId, String baseLayerId) async {
    if (mapController == null) return;

    try {
      final layers = await mapController.getLayerIds();
      final sources = await mapController.getSourceIds();
      for (var layerId in List.from(layers)) {
        try {
          if (layerId.toString().contains(baseLayerId) ||
              layerId.toString().contains("_pet_circle_") ||
              layerId.toString().contains("_chart_image_circle_rate") ||
              layerId.toString().contains("_chart_circle_children") ||
              layerId.toString().contains("_chart_circle_count") ||
              layerId.toString().contains("_disease_layer_") ||
              layerId.toString().contains("province_") ||
              (layerId.toString().startsWith("pet_map") && layerId.toString().contains("_province_"))) {
            await mapController.removeLayer(layerId);
            await Future.delayed(const Duration(milliseconds: 10));
          }
        } catch (e) {
          logDebug("Error removing layer $layerId: $e");
        }
      }

      await Future.delayed(const Duration(milliseconds: 50));
      for (var sourceId in List.from(sources)) {
        try {
          if (sourceId.toString() == baseSourceId || sourceId.toString().startsWith("${baseSourceId}_province_") || sourceId.toString().contains("pet_map")) {
            await mapController.removeSource(sourceId);
            await Future.delayed(const Duration(milliseconds: 10));
          }
        } catch (e) {
          logDebug("Error removing source $sourceId: $e");
        }
      }

      await mapController.clearSymbols();
    } catch (e) {
      logDebug("Error in removePetLayerMap: $e");
    }
  }
}

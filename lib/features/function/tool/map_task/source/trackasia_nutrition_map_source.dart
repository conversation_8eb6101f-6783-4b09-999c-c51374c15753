import 'dart:developer';

import 'package:hainong/common/constants.dart';
import 'package:hainong/features/function/tool/map_task/source/trackasia_map_source.dart';
import 'package:trackasia_gl/trackasia_gl.dart';

class TrackAsiaNutritionMapSource {
  static final layers = [
    "lua-n-layer",
    "lua-p-layer",
    "lua-k-layer",
    "saurieng-n-layer",
    "saurieng-p-layer",
    "saurieng-k-layer",
    "nhan-n-layer",
    "nhan-p-layer",
    "nhan-k-layer",
    "thanhlong-n-layer",
    "thanhlong-p-layer",
    "thanhlong-k-layer",
    "xoai-n-layer",
    "xoai-p-layer",
    "xoai-k-layer",
    "cam-n-layer",
    "cam-p-layer",
    "cam-k-layer",
    "buoi-n-layer",
    "buoi-p-layer",
    "buoi-k-layer",
    "ec-layer",
    "cec-layer",
    "ph-layer",
    "om-layer",
  ];

  static Future<void> removeAllLayerNutritionMap(TrackAsiaMapController? mapController) async {
    if (mapController == null) return;
    
    try {
      // First remove all nutrition layers
      final layerIds = await mapController.getLayerIds();
      for (var layer in layers) {
        try {
          if (layerIds.contains(layer)) {
            await TrackAsiaMapSource.removeLayer(layerIds: layerIds, mapController: mapController, keyLayer: layer);
            // Small delay to ensure layer is properly removed
            await Future.delayed(const Duration(milliseconds: 10));
          }
        } catch (e) {
          log("Error removing nutrition layer $layer: $e");
        }
      }
      
      // Wait to ensure all layers are properly removed
      await Future.delayed(const Duration(milliseconds: 50));
      
      // Then remove the nutrition source
      final sources = await mapController.getSourceIds();
      if (sources.contains('nutrition-map')) {
        await mapController.removeSource('nutrition-map');
      }
      
      // Clear any related symbols
      await mapController.clearSymbols();
    } catch (e) {
      log("Error in removeAllLayerNutritionMap: $e");
    }
  }

  static void addSourceNutritionMap(TrackAsiaMapController? mapController, String sourceIdNutritionMap) async {
    mapController?.getSourceIds().then((value) {
      if (!value.contains(sourceIdNutritionMap)) {
        return mapController.addSource(sourceIdNutritionMap, VectorSourceProperties(url: "${Constants().mapUrl}/api/soil_geos/tile_conf.json"));
      }
    });
  }

  static void setLayerNutritionMap(TrackAsiaMapController? mapController, String sourceIdNutritionMap, String nutritionType, String treeType) async {
    await removeAllLayerNutritionMap(mapController);
    switch (nutritionType) {
      case "ec":
        await addECLayers(mapController, sourceIdNutritionMap);
        break;
      case "cec":
        await addCECLayers(mapController, sourceIdNutritionMap);
        break;
      case "ph":
        await addPHLayers(mapController, sourceIdNutritionMap);
        break;
      case "om":
        await addOMLayers(mapController, sourceIdNutritionMap);
        break;
      case "npk":
        switch (treeType) {
          case "lua":
            await addLuaNLayers(mapController, sourceIdNutritionMap, fillOpacity: 0.4);
            await addLuaPLayers(mapController, sourceIdNutritionMap, fillOpacity: 0.4);
            await addLuaKLayers(mapController, sourceIdNutritionMap, fillOpacity: 0.4);
            break;
          case "sau_rieng":
            await addSauriengNLayers(mapController, sourceIdNutritionMap, fillOpacity: 0.4);
            await addSauriengPLayers(mapController, sourceIdNutritionMap, fillOpacity: 0.4);
            await addSauriengKLayers(mapController, sourceIdNutritionMap, fillOpacity: 0.4);
            break;
          case "nhan":
            await addNhanNLayers(mapController, sourceIdNutritionMap, fillOpacity: 0.4);
            await addNhanPLayers(mapController, sourceIdNutritionMap, fillOpacity: 0.4);
            await addNhanKLayers(mapController, sourceIdNutritionMap, fillOpacity: 0.4);
            break;
          case "thanh_long":
            await addThanhLongNLayers(mapController, sourceIdNutritionMap, fillOpacity: 0.4);
            await addThanhLongPLayers(mapController, sourceIdNutritionMap, fillOpacity: 0.4);
            await addThanhLongKLayers(mapController, sourceIdNutritionMap, fillOpacity: 0.4);
            break;
          case "xoai":
            await addXoaiNLayers(mapController, sourceIdNutritionMap, fillOpacity: 0.4);
            await addXoaiPLayers(mapController, sourceIdNutritionMap, fillOpacity: 0.4);
            await addXoaiKLayers(mapController, sourceIdNutritionMap, fillOpacity: 0.4);
            break;
          case "cam":
            await addCamNLayers(mapController, sourceIdNutritionMap, fillOpacity: 0.4);
            await addCamPLayers(mapController, sourceIdNutritionMap, fillOpacity: 0.4);
            await addCamKLayers(mapController, sourceIdNutritionMap, fillOpacity: 0.4);
            break;
          case "buoi":
            await addBuoiNLayers(mapController, sourceIdNutritionMap, fillOpacity: 0.4);
            await addBuoiPLayers(mapController, sourceIdNutritionMap, fillOpacity: 0.4);
            await addBuoiKLayers(mapController, sourceIdNutritionMap, fillOpacity: 0.4);
            break;
        }
        break;
      case "n":
        switch (treeType) {
          case "lua":
            await addLuaNLayers(mapController, sourceIdNutritionMap);
            break;
          case "sau_rieng":
            await addSauriengNLayers(mapController, sourceIdNutritionMap);
            break;
          case "nhan":
            await addNhanNLayers(mapController, sourceIdNutritionMap);
            break;
          case "thanh_long":
            await addThanhLongNLayers(mapController, sourceIdNutritionMap);
            break;
          case "xoai":
            await addXoaiNLayers(mapController, sourceIdNutritionMap);
            break;
          case "cam":
            await addCamNLayers(mapController, sourceIdNutritionMap);
            break;
          case "buoi":
            await addBuoiNLayers(mapController, sourceIdNutritionMap);
            break;
        }
        break;
      case "p":
        switch (treeType) {
          case "lua":
            await addLuaPLayers(mapController, sourceIdNutritionMap);
            break;
          case "sau_rieng":
            await addSauriengPLayers(mapController, sourceIdNutritionMap);
            break;
          case "nhan":
            await addNhanPLayers(mapController, sourceIdNutritionMap);
            break;
          case "thanh_long":
            await addThanhLongPLayers(mapController, sourceIdNutritionMap);
            break;
          case "xoai":
            await addXoaiPLayers(mapController, sourceIdNutritionMap);
            break;
          case "cam":
            await addCamPLayers(mapController, sourceIdNutritionMap);
            break;
          case "buoi":
            await addBuoiPLayers(mapController, sourceIdNutritionMap);
            break;
        }
        break;
      case "k":
        switch (treeType) {
          case "lua":
            await addLuaKLayers(mapController, sourceIdNutritionMap);
            break;
          case "sau_rieng":
            await addSauriengKLayers(mapController, sourceIdNutritionMap);
            break;
          case "nhan":
            await addNhanKLayers(mapController, sourceIdNutritionMap);
            break;
          case "thanh_long":
            await addThanhLongKLayers(mapController, sourceIdNutritionMap);
            break;
          case "xoai":
            await addXoaiKLayers(mapController, sourceIdNutritionMap);
            break;
          case "cam":
            await addCamKLayers(mapController, sourceIdNutritionMap);
            break;
          case "buoi":
            await addBuoiKLayers(mapController, sourceIdNutritionMap);
            break;
        }
        break;
      default:
        break;
    }
  }

  // Lúa (Rice)
  static Future<void> addLuaNLayers(TrackAsiaMapController? mapController, String sourceId, {double? fillOpacity}) async {
    await TrackAsiaMapSource.addLayerWithParams(
        mapController,
        sourceId,
        'lua-n-layer',
        [
          '==',
          ['get', 'layer'],
          'lua'
        ],
        [
          "match",
          ["get", "n"],
          ["> 0,45"],
          "#004D00", // Dark green
          ["0,361-0,45", "0.361-0.45"],
          "#006600", // Deep green
          ["0,271-0,36", "0.271-0.36"],
          "#1A991A", // Green
          ["0,181-0,27", "0.181-0.27"],
          "#4DB34D", // Medium green
          ["0,091-0,18", "0.091-0.18"],
          "#80C980", // Light green
          ["< 0,091", "< 0.091"],
          "#B3E0B3", // Pale green
          "transparent"
        ],
        fillOpacity: fillOpacity);
  }

  static Future<void> addLuaPLayers(TrackAsiaMapController? mapController, String sourceId, {double? fillOpacity}) async {
    await TrackAsiaMapSource.addLayerWithParams(
        mapController,
        sourceId,
        'lua-p-layer',
        [
          '==',
          ['get', 'layer'],
          'lua'
        ],
        [
          "match",
          ["get", "p"],
          ["> 26.25"],
          "#BC6F00", // Dark yellow
          ["> 18,75", "21.1-26.25"],
          "#D18C00", // Deep yellow
          ["15,1-18,75", "15.76-21"],
          "#E6A900", // Yellow
          ["11,26-15,0", "10.51-15.7"],
          "#FBC600", // Bright yellow
          ["7,6-11,25", "5.25-10.5"],
          "#FFE066", // Light yellow
          ["3,76-7,5", "< 5.25"],
          "#FFF4B2", // Pale yellow
          ["< 3,76"],
          "#FFFBDB", // Very pale yellow
          "transparent"
        ],
        fillOpacity: fillOpacity);
  }

  static Future<void> addLuaKLayers(TrackAsiaMapController? mapController, String sourceId, {double? fillOpacity}) async {
    await TrackAsiaMapSource.addLayerWithParams(
        mapController,
        sourceId,
        'lua-k-layer',
        [
          '==',
          ['get', 'layer'],
          'lua'
        ],
        [
          "match",
          ["get", "k"],
          ["> 14,62", "> 14.62"],
          "#8B0000", // Dark red
          ["9,94-14,62", "9.94-14.62"],
          "#A11E1E", // Deep red
          ["6,61-9,94", "6.61-9.94"],
          "#B73C3C", // Red
          ["4,56-6,61", "4.56-6.61"],
          "#CD5A5A", // Medium red
          ["1,0-4,56", "1.0-4.56"],
          "#E37878", // Light red
          ["> 0,375", "> 0.45"],
          "#F9A6A6", // Pale red
          ["0,31-0,375", "0.361-0.45"],
          "#FBBBBB", // Very pale red
          ["0,226-0,3", "0.271-0.36"],
          "#FCC0C0", // Extremely pale red
          ["0,151-0,225", "0.181-0.27"],
          "#FDC8C8", // Near-white red
          ["0,076-0,15", "0.091-0.18"],
          "#FDD0D0", // Very near-white red
          ["< 1"],
          "#FEE0E0", // Almost white red
          "transparent"
        ],
        fillOpacity: fillOpacity);
  }

// Sầu riêng (Durian)
  static Future<void> addSauriengNLayers(TrackAsiaMapController? mapController, String sourceId, {double? fillOpacity}) async {
    await TrackAsiaMapSource.addLayerWithParams(
        mapController,
        sourceId,
        'saurieng-n-layer',
        [
          '==',
          ['get', 'layer'],
          'sau_rieng'
        ],
        [
          "match",
          ["get", "n"],
          ["> 0,45"],
          "#1B3C00", // Dark forest green
          ["0,361-0,45", "0.361-0.45"],
          "#2E5C00", // Deep forest green
          ["0,271-0,36", "0.271-0.36"],
          "#4A7A00", // Forest green
          ["0,181-0,27", "0.181-0.27"],
          "#6B9A00", // Medium green
          ["0,091-0,18", "0.091-0.18"],
          "#A3C664", // Light green
          ["< 0,091", "< 0.091"],
          "#C7D98B", // Pale green
          "transparent"
        ],
        fillOpacity: fillOpacity);
  }

  static Future<void> addSauriengPLayers(TrackAsiaMapController? mapController, String sourceId, {double? fillOpacity}) async {
    await TrackAsiaMapSource.addLayerWithParams(
        mapController,
        sourceId,
        'saurieng-p-layer',
        [
          '==',
          ['get', 'layer'],
          'sau_rieng'
        ],
        [
          "match",
          ["get", "p"],
          ["> 26.25"],
          "#8B6F00", // Dark golden yellow
          ["> 18,75", "21.1-26.25"],
          "#A88C00", // Deep golden yellow
          ["15,1-18,75", "15.76-21"],
          "#C5A900", // Golden yellow
          ["11,26-15,0", "10.51-15.7"],
          "#E2C600", // Bright yellow
          ["7,6-11,25", "5.25-10.5"],
          "#FFE066", // Light yellow
          ["3,76-7,5", "< 5.25"],
          "#FFF4B2", // Pale yellow
          ["< 3,76"],
          "#FFFBDB", // Very pale yellow
          "transparent"
        ],
        fillOpacity: fillOpacity);
  }

  static Future<void> addSauriengKLayers(TrackAsiaMapController? mapController, String sourceId, {double? fillOpacity}) async {
    await TrackAsiaMapSource.addLayerWithParams(
        mapController,
        sourceId,
        'saurieng-k-layer',
        [
          '==',
          ['get', 'layer'],
          'sau_rieng'
        ],
        [
          "match",
          ["get", "k"],
          ["> 14,62", "> 14.62"],
          "#B71C1C", // Dark red
          ["9,94-14,62", "9.94-14.62"],
          "#C62828", // Deep red
          ["6,61-9,94", "6.61-9.94"],
          "#D32F2F", // Red
          ["4,56-6,61", "4.56-6.61"],
          "#E53935", // Medium red
          ["1,0-4,56", "1.0-4.56"],
          "#F44336", // Light red
          ["< 1"],
          "#FF5722", // Red-orange
          ["> 0,375", "> 0.45"],
          "#FF6F00", // Orange
          ["0,31-0,375", "0.361-0.45"],
          "#FF8F00", // Deep orange
          ["0,226-0,3", "0.271-0.36"],
          "#FFA000", // Amber
          ["0,151-0,225", "0.181-0.27"],
          "#FFB300", // Light amber
          ["0,076-0,15", "0.091-0.18"],
          "#FFC107", // Yellow
          "transparent"
        ],
        fillOpacity: fillOpacity);
  }

// Xoài (Mango)
  static Future<void> addXoaiNLayers(TrackAsiaMapController? mapController, String sourceId, {double? fillOpacity}) async {
    await TrackAsiaMapSource.addLayerWithParams(
        mapController,
        sourceId,
        'xoai-n-layer',
        [
          '==',
          ['get', 'layer'],
          'xoai'
        ],
        [
          "match",
          ["get", "n"],
          ["> 0,45"],
          "#1A4D00", // Dark vibrant green
          ["0,361-0,45", "0.361-0.45"],
          "#2E6600", // Deep vibrant green
          ["0,271-0,36", "0.271-0.36"],
          "#4A8000", // Vibrant green
          ["0,181-0,27", "0.181-0.27"],
          "#669A00", // Medium green
          ["0,091-0,18", "0.091-0.18"],
          "#99CC66", // Light green
          ["< 0,091", "< 0.091"],
          "#CCE699", // Pale green
          "transparent"
        ],
        fillOpacity: fillOpacity);
  }

  static Future<void> addXoaiPLayers(TrackAsiaMapController? mapController, String sourceId, {double? fillOpacity}) async {
    await TrackAsiaMapSource.addLayerWithParams(
        mapController,
        sourceId,
        'xoai-p-layer',
        [
          '==',
          ['get', 'layer'],
          'xoai'
        ],
        [
          "match",
          ["get", "p"],
          ["> 26.25"],
          "#C94F00", // Dark yellow-orange
          ["> 18,75", "21.1-26.25"],
          "#D97100", // Deep yellow-orange
          ["15,1-18,75", "15.76-21"],
          "#EB8B1A", // Yellow-orange
          ["11,26-15,0", "10.51-15.7"],
          "#FBC600", // Bright yellow-orange
          ["7,6-11,25", "5.25-10.5"],
          "#FFA533", // Light yellow-orange
          ["3,76-7,5", "< 5.25"],
          "#FFBF66", // Pale yellow-orange
          ["< 3,76"],
          "#FFD9B2", // Very pale yellow-orange
          "transparent"
        ],
        fillOpacity: fillOpacity);
  }

  static Future<void> addXoaiKLayers(TrackAsiaMapController? mapController, String sourceId, {double? fillOpacity}) async {
    await TrackAsiaMapSource.addLayerWithParams(
        mapController,
        sourceId,
        'xoai-k-layer',
        [
          '==',
          ['get', 'layer'],
          'xoai'
        ],
        [
          "match",
          ["get", "k"],
          ["> 14,62", "> 14.62"],
          "#A63D00", // Dark coral red
          ["9,94-14,62", "9.94-14.62"],
          "#B35100", // Deep coral red
          ["6,61-9,94", "6.61-9.94"],
          "#C06600", // Coral red
          ["4,56-6,61", "4.56-6.61"],
          "#CD7A1A", // Medium coral red
          ["1,0-4,56", "1.0-4.56"],
          "#DB8F33", // Light coral red
          ["< 1"],
          "#E8A34D", // Pale coral red
          ["> 0,375", "> 0.45"],
          "#F5B866", // Very pale coral red
          ["0,31-0,375", "0.361-0.45"],
          "#FFCC80", // Extremely pale coral red
          ["0,226-0,3", "0.271-0.36"],
          "#FFD999", // Near-white coral red
          ["0,151-0,225", "0.181-0.27"],
          "#FFE6B2", // Very near-white coral red
          ["0,076-0,15", "0.091-0.18"],
          "#FFF2CC", // Almost white coral red
          "transparent"
        ],
        fillOpacity: fillOpacity);
  }

// Bưởi (Pomelo)
  static Future<void> addBuoiNLayers(TrackAsiaMapController? mapController, String sourceId, {double? fillOpacity}) async {
    await TrackAsiaMapSource.addLayerWithParams(
        mapController,
        sourceId,
        'buoi-n-layer',
        [
          '==',
          ['get', 'layer'],
          'buoi'
        ],
        [
          "match",
          ["get", "n"],
          ["> 0,45"],
          "#1A4D33", // Dark pale green
          ["0,361-0,45", "0.361-0.45"],
          "#2E6647", // Deep pale green
          ["0,271-0,36", "0.271-0.36"],
          "#4A805A", // Pale green
          ["0,181-0,27", "0.181-0.27"],
          "#669A6E", // Medium pale green
          ["0,091-0,18", "0.091-0.18"],
          "#99CC99", // Light pale green
          ["< 0,091", "< 0.091"],
          "#CCE6CC", // Very pale green
          "transparent"
        ],
        fillOpacity: fillOpacity);
  }

  static Future<void> addBuoiPLayers(TrackAsiaMapController? mapController, String sourceId, {double? fillOpacity}) async {
    await TrackAsiaMapSource.addLayerWithParams(
        mapController,
        sourceId,
        'buoi-p-layer',
        [
          '==',
          ['get', 'layer'],
          'buoi'
        ],
        [
          "match",
          ["get", "p"],
          ["> 26.25"],
          "#D9A900", // Dark yellow
          ["> 18,75", "21.1-26.25"],
          "#E6B800", // Deep yellow
          ["15,1-18,75", "15.76-21"],
          "#F2C600", // Yellow
          ["11,26-15,0", "10.51-15.7"],
          "#FFD400", // Bright yellow
          ["7,6-11,25", "5.25-10.5"],
          "#FFE066", // Light yellow
          ["3,76-7,5", "< 5.25"],
          "#FFF4B2", // Pale yellow
          ["< 3,76"],
          "#FFFBDB", // Very pale yellow
          "transparent"
        ],
        fillOpacity: fillOpacity);
  }

  static Future<void> addBuoiKLayers(TrackAsiaMapController? mapController, String sourceId, {double? fillOpacity}) async {
    await TrackAsiaMapSource.addLayerWithParams(
        mapController,
        sourceId,
        'buoi-k-layer',
        [
          '==',
          ['get', 'layer'],
          'buoi'
        ],
        [
          "match",
          ["get", "k"],
          ["> 14,62", "> 14.62"],
          "#8B2A47", // Dark pinkish-red
          ["9,94-14,62", "9.94-14.62"],
          "#A14765", // Deep pinkish-red
          ["6,61-9,94", "6.61-9.94"],
          "#B76483", // Pinkish-red
          ["4,56-6,61", "4.56-6.61"],
          "#CD81A1", // Medium pinkish-red
          ["1,0-4,56", "1.0-4.56"],
          "#E39EBF", // Light pinkish-red
          ["< 1"],
          "#F9BBDD", // Pale pinkish-red
          ["> 0,375", "> 0.45"],
          "#FBDDE6", // Very pale pinkish-red
          ["0,31-0,375", "0.361-0.45"],
          "#FDC4E9", // Extremely pale pinkish-red
          ["0,226-0,3", "0.271-0.36"],
          "#FDCBEC", // Near-white pinkish-red
          ["0,151-0,225", "0.181-0.27"],
          "#FED2EF", // Very near-white pinkish-red
          ["0,076-0,15", "0.091-0.18"],
          "#FEDAEF", // Almost white pinkish-red
          "transparent"
        ],
        fillOpacity: fillOpacity);
  }

// Cam (Orange)
  static Future<void> addCamNLayers(TrackAsiaMapController? mapController, String sourceId, {double? fillOpacity}) async {
    await TrackAsiaMapSource.addLayerWithParams(
        mapController,
        sourceId,
        'cam-n-layer',
        [
          '==',
          ['get', 'layer'],
          'cam'
        ],
        [
          "match",
          ["get", "n"],
          ["> 0,45"],
          "#1A4D1A", // Dark citrus green
          ["0,361-0,45", "0.361-0.45"],
          "#2E662E", // Deep citrus green
          ["0,271-0,36", "0.271-0.36"],
          "#4A804A", // Citrus green
          ["0,181-0,27", "0.181-0.27"],
          "#669A66", // Medium green
          ["0,091-0,18", "0.091-0.18"],
          "#99CC99", // Light green
          ["< 0,091", "< 0.091"],
          "#CCE6CC", // Pale green
          "transparent"
        ],
        fillOpacity: fillOpacity);
  }

  static Future<void> addCamPLayers(TrackAsiaMapController? mapController, String sourceId, {double? fillOpacity}) async {
    await TrackAsiaMapSource.addLayerWithParams(
        mapController,
        sourceId,
        'cam-p-layer',
        [
          '==',
          ['get', 'layer'],
          'cam'
        ],
        [
          "match",
          ["get", "p"],
          ["> 26.25"],
          "#D84300", // Dark orange
          ["> 18,75", "21.1-26.25"],
          "#E65C00", // Deep orange
          ["15,1-18,75", "15.76-21"],
          "#F57500", // Orange
          ["11,26-15,0", "10.51-15.7"],
          "#FF8E00", // Bright orange
          ["7,6-11,25", "5.25-10.5"],
          "#FFA666", // Light orange
          ["3,76-7,5", "< 5.25"],
          "#FFCC99", // Pale orange
          ["< 3,76"],
          "#FFE6CC", // Very pale orange
          "transparent"
        ],
        fillOpacity: fillOpacity);
  }

  static Future<void> addCamKLayers(TrackAsiaMapController? mapController, String sourceId, {double? fillOpacity}) async {
    await TrackAsiaMapSource.addLayerWithParams(
        mapController,
        sourceId,
        'cam-k-layer',
        [
          '==',
          ['get', 'layer'],
          'cam'
        ],
        [
          "match",
          ["get", "k"],
          ["> 14,62", "> 14.62"],
          "#8B0000", // Dark red
          ["9,94-14,62", "9.94-14.62"],
          "#A11E1E", // Deep red
          ["6,61-9,94", "6.61-9.94"],
          "#B73C3C", // Red
          ["4,56-6,61", "4.56-6.61"],
          "#CD5A5A", // Medium red
          ["1,0-4,56", "1.0-4.56"],
          "#E37878", // Light red
          ["< 1"],
          "#F9A6A6", // Pale red
          ["> 0,375", "> 0.45"],
          "#FBBBBB", // Very pale red
          ["0,31-0,375", "0.361-0.45"],
          "#FCC0C0", // Extremely pale red
          ["0,226-0,3", "0.271-0.36"],
          "#FDC8C8", // Near-white red
          ["0,151-0,225", "0.181-0.27"],
          "#FDD0D0", // Very near-white red
          ["0,076-0,15", "0.091-0.18"],
          "#FEE0E0", // Almost white red
          "transparent"
        ],
        fillOpacity: fillOpacity);
  }

// Nhãn (Longan)
  static Future<void> addNhanNLayers(TrackAsiaMapController? mapController, String sourceId, {double? fillOpacity}) async {
    await TrackAsiaMapSource.addLayerWithParams(
        mapController,
        sourceId,
        'nhan-n-layer',
        [
          '==',
          ['get', 'layer'],
          'nhan'
        ],
        [
          "match",
          ["get", "n"],
          ["> 0,45"],
          "#3C4D1A", // Dark olive green
          ["0,361-0,45", "0.361-0.45"],
          "#4A662E", // Deep olive green
          ["0,271-0,36", "0.271-0.36"],
          "#5A804A", // Olive green
          ["0,181-0,27", "0.181-0.27"],
          "#6E9A66", // Medium olive green
          ["0,091-0,18", "0.091-0.18"],
          "#99CC99", // Light olive green
          ["< 0,091", "< 0.091"],
          "#CCE6CC", // Pale olive green
          "transparent"
        ],
        fillOpacity: fillOpacity);
  }

  static Future<void> addNhanPLayers(TrackAsiaMapController? mapController, String sourceId, {double? fillOpacity}) async {
    await TrackAsiaMapSource.addLayerWithParams(
        mapController,
        sourceId,
        'nhan-p-layer',
        [
          '==',
          ['get', 'layer'],
          'nhan'
        ],
        [
          "match",
          ["get", "p"],
          ["> 26.25"],
          "#8B5A00", // Dark golden brown
          ["> 18,75", "21.1-26.25"],
          "#A17100", // Deep golden brown
          ["15,1-18,75", "15.76-21"],
          "#B78800", // Golden brown
          ["11,26-15,0", "10.51-15.7"],
          "#CD9F00", // Medium golden brown
          ["7,6-11,25", "5.25-10.5"],
          "#E3B633", // Light golden brown
          ["3,76-7,5", "< 5.25"],
          "#F9CD66", // Pale golden brown
          ["< 3,76"],
          "#FFE6B2", // Very pale golden brown
          "transparent"
        ],
        fillOpacity: fillOpacity);
  }

  static Future<void> addNhanKLayers(TrackAsiaMapController? mapController, String sourceId, {double? fillOpacity}) async {
    await TrackAsiaMapSource.addLayerWithParams(
        mapController,
        sourceId,
        'nhan-k-layer',
        [
          '==',
          ['get', 'layer'],
          'nhan'
        ],
        [
          "match",
          ["get", "k"],
          ["> 14,62", "> 14.62"],
          "#4A148C", // Dark purple
          ["9,94-14,62", "9.94-14.62"],
          "#6A1B9A", // Deep purple
          ["6,61-9,94", "6.61-9.94"],
          "#7B1FA2", // Purple
          ["4,56-6,61", "4.56-6.61"],
          "#8E24AA", // Medium purple
          ["1,0-4,56", "1.0-4.56"],
          "#9C27B0", // Light purple
          ["< 1"],
          "#AB47BC", // Pale purple
          ["> 0,375", "> 0.45"],
          "#BA68C8", // Very pale purple
          ["0,31-0,375", "0.361-0.45"],
          "#CE93D8", // Purple-pink transition
          ["0,226-0,3", "0.271-0.36"],
          "#E1BEE7", // Light purple-pink
          ["0,151-0,225", "0.181-0.27"],
          "#F3E5F5", // Very pale purple-pink
          ["0,076-0,15", "0.091-0.18"],
          "#FFF8E1", // Almost white yellow
          "transparent"
        ],
        fillOpacity: fillOpacity);
  }

// Thanh long (Dragon Fruit)
  static Future<void> addThanhLongNLayers(TrackAsiaMapController? mapController, String sourceId, {double? fillOpacity}) async {
    await TrackAsiaMapSource.addLayerWithParams(
        mapController,
        sourceId,
        'thanhlong-n-layer',
        [
          '==',
          ['get', 'layer'],
          'thanh_long'
        ],
        [
          "match",
          ["get", "n"],
          ["> 0,45"],
          "#1A3C2F", // Dark cactus green
          ["0,361-0,45", "0.361-0.45"],
          "#2E5C47", // Deep cactus green
          ["0,271-0,36", "0.271-0.36"],
          "#4A7A5A", // Cactus green
          ["0,181-0,27", "0.181-0.27"],
          "#669A6E", // Medium cactus green
          ["0,091-0,18", "0.091-0.18"],
          "#99CC99", // Light cactus green
          ["< 0,091", "< 0.091"],
          "#CCE6CC", // Pale cactus green
          "transparent"
        ],
        fillOpacity: fillOpacity);
  }

  static Future<void> addThanhLongPLayers(TrackAsiaMapController? mapController, String sourceId, {double? fillOpacity}) async {
    await TrackAsiaMapSource.addLayerWithParams(
        mapController,
        sourceId,
        'thanhlong-p-layer',
        [
          '==',
          ['get', 'layer'],
          'thanh_long'
        ],
        [
          "match",
          ["get", "p"],
          ["> 26.25"],
          "#8B2A47", // Dark pinkish-yellow
          ["> 18,75", "21.1-26.25"],
          "#A14765", // Deep pinkish-yellow
          ["15,1-18,75", "15.76-21"],
          "#B76483", // Pinkish-yellow
          ["11,26-15,0", "10.51-15.7"],
          "#CD81A1", // Medium pinkish-yellow
          ["7,6-11,25", "5.25-10.5"],
          "#E39EBF", // Light pinkish-yellow
          ["3,76-7,5", "< 5.25"],
          "#F9BBDD", // Pale pinkish-yellow
          ["< 3,76"],
          "#FDDDE6", // Very pale pinkish-yellow
          "transparent"
        ],
        fillOpacity: fillOpacity);
  }

  static Future<void> addThanhLongKLayers(TrackAsiaMapController? mapController, String sourceId, {double? fillOpacity}) async {
    await TrackAsiaMapSource.addLayerWithParams(
        mapController,
        sourceId,
        'thanhlong-k-layer',
        [
          '==',
          ['get', 'layer'],
          'thanh_long'
        ],
        [
          "match",
          ["get", "k"],
          ["> 14,62", "> 14.62"],
          "#4A148C", // Dark purple-red
          ["9,94-14,62", "9.94-14.62"],
          "#5C2A9A", // Deep purple-red
          ["6,61-9,94", "6.61-9.94"],
          "#6E40A8", // Purple-red
          ["4,56-6,61", "4.56-6.61"],
          "#8056B6", // Medium purple-red
          ["1,0-4,56", "1.0-4.56"],
          "#A880D1", // Light purple-red
          ["< 1"],
          "#D1B3E6", // Pale purple-red
          ["> 0,375", "> 0.45"],
          "#E6CCF2", // Very pale purple-red
          ["0,31-0,375", "0.361-0.45"],
          "#EBD6F5", // Extremely pale purple-red
          ["0,226-0,3", "0.271-0.36"],
          "#F0E0F8", // Near-white purple-red
          ["0,151-0,225", "0.181-0.27"],
          "#F5E9FB", // Very near-white purple-red
          ["0,076-0,15", "0.091-0.18"],
          "#FAF2FD", // Almost white purple-red
          "transparent"
        ],
        fillOpacity: fillOpacity);
  }

  static Future<void> addPHLayers(TrackAsiaMapController? mapController, String sourceId, {double? fillOpacity}) async {
    await TrackAsiaMapSource.addLayerWithParams(mapController, sourceId, 'ph-layer', null, [
      "case",
      [
        "==",
        ["get", "ph"],
        "< 5,0"
      ],
      "#FFF9C4", // Light yellow
      [
        "==",
        ["get", "ph"],
        "5,0-6,0"
      ],
      "#FFEB3B", // Medium yellow
      [
        "==",
        ["get", "ph"],
        "6-7,5"
      ],
      "#FBC02D", // Dark yellow
      "transparent"
    ]);
  }

  static Future<void> addECLayers(TrackAsiaMapController? mapController, String sourceId) async {
    await TrackAsiaMapSource.addLayerWithParams(mapController, sourceId, 'ec-layer', null, [
      "case",
      [
        "==",
        ["get", "ec"],
        "< 0.8"
      ],
      "#BBDEFB", // Light blue
      [
        "==",
        ["get", "ec"],
        "0,8-1,6"
      ],
      "#42A5F5", // Medium blue
      [
        "==",
        ["get", "ec"],
        "> 1.6"
      ],
      "#1565C0", // Dark blue
      "transparent"
    ]);
  }

  static Future<void> addCECLayers(TrackAsiaMapController? mapController, String sourceId) async {
    await TrackAsiaMapSource.addLayerWithParams(mapController, sourceId, 'cec-layer', null, [
      "case",
      [
        "==",
        ["get", "cec"],
        "5-15"
      ],
      "#BBDEFB", // Light blue
      [
        "==",
        ["get", "cec"],
        "15-25"
      ],
      "#1976D2", // Dark blue
      "transparent"
    ]);
  }

  static Future<void> addOMLayers(TrackAsiaMapController? mapController, String sourceId) async {
    await TrackAsiaMapSource.addLayerWithParams(mapController, sourceId, 'om-layer', null, [
      "case",
      [
        "==",
        ["get", "om"],
        "< 4,0"
      ],
      "#FFCDD2", // Light red
      [
        "==",
        ["get", "om"],
        "4-10"
      ],
      "#EF5350", // Medium red
      [
        "==",
        ["get", "om"],
        "> 10"
      ],
      "#C62828", // Dark red
      "transparent"
    ]);
  }
}

import 'package:flutter/material.dart';

/// Weather Map Color Schemes
/// Centralizes all color definitions for weather layers and UI components
class MapWeatherColor {
  //================[GENERAL UI COLORS]================//

  /// Primary UI colors used across weather components
  static const Color primaryOrange = Color(0xFFF39C12);
  static const Color darkGrey = Color(0xFF333333);
  static const Color mediumGrey = Color(0xFF666666);
  static const Color lightGrey = Color(0xFF999999);

  //================[WEATHER LAYER COLORS]================//

  /// Temperature accent color for UI elements
  static const Color temperatureAccent = Color(0xFFFF6B35);

  /// Precipitation accent color for UI elements
  static const Color precipitationAccent = Color(0xFF2196F3);

  /// Wind accent color for UI elements
  static const Color windAccent = Color(0xFF4CAF50);

  //================[TEMPERATURE COLORS]================//

  /// Temperature layer fill colors for map display
  static const List<dynamic> temperatureFillColors = [
    "interpolate",
    ["exponential", 1.5],
    ["get", "max"],
    -5, "rgb(0, 0, 150)", // Very Cold - Dark Blue
    5, "rgb(0, 0, 200)", // Cold - Blue
    10, "rgb(53, 154, 248)", // Cool - Light Blue
    15, "#8fc3ff", // Mild Cool - Sky Blue
    20, "rgb(0, 255, 0)", // Comfortable - Green
    24, "rgb(173, 255, 47)", // Warm - Light Green
    28, "rgb(255, 255, 0)", // Hot - Yellow
    32, "rgb(255, 200, 0)", // Very Hot - Orange
    36, "rgb(255, 165, 0)", // Extremely Hot - Deep Orange
    39, "rgb(255, 100, 0)", // Scorching - Red Orange
    45, "rgb(255, 0, 0)", // Burning - Red
    50, "rgb(128, 0, 0)" // Extreme - Dark Red
  ];

  /// Temperature gradient colors for UI scale display
  static const List<Color> temperatureGradientColors = [
    Color.fromRGBO(128, 0, 0, 1), // Dark Red (Hot)
    Color.fromRGBO(255, 0, 0, 1), // Red
    Color.fromRGBO(255, 100, 0, 1), // Red Orange
    Color.fromRGBO(255, 165, 0, 1), // Orange
    Color.fromRGBO(255, 200, 0, 1), // Light Orange
    Color.fromRGBO(255, 255, 0, 1), // Yellow
    Color.fromRGBO(173, 255, 47, 1), // Yellow Green
    Color.fromRGBO(0, 255, 0, 1), // Green
    Color(0xFF8fc3ff), // Sky Blue
    Color.fromRGBO(53, 154, 248, 1), // Light Blue
    Color.fromRGBO(0, 0, 200, 1), // Blue
    Color.fromRGBO(0, 0, 150, 1), // Dark Blue (Cold)
  ];

  /// Temperature gradient stops for precise positioning
  static const List<double> temperatureGradientStops = [0.0, 0.09, 0.18, 0.27, 0.36, 0.45, 0.55, 0.64, 0.73, 0.82, 0.91, 1.0];

  //================[RAINFALL COLORS]================//

  /// Rainfall layer fill colors for map display
  static const List<dynamic> rainfallFillColors = [
    "interpolate",
    ["exponential", 1.5],
    ["get", "max"],
    0.2, "rgba(255, 255, 255, 0)", // No Rain - Transparent
    1, "rgb(195, 228, 254)", // Light Rain - Very Light Blue
    3, "rgb(90, 154, 226)", // Light-Moderate Rain - Light Blue
    7, "rgb(60, 113, 236)", // Moderate Rain - Blue
    15, "rgb(11, 66, 168)", // Heavy Rain - Dark Blue
    25, "rgb(12, 0, 150)", // Very Heavy Rain - Deep Blue
    50, "rgb(12, 0, 70)", // Extreme Rain - Very Dark Blue
    100, "rgb(96, 0, 0)" // Torrential Rain - Dark Red
  ];

  /// Rainfall gradient colors for UI scale display
  static const List<Color> rainfallGradientColors = [
    Color.fromRGBO(96, 0, 0, 1), // Dark Red (Heavy)
    Color.fromRGBO(12, 0, 70, 1), // Very Dark Blue
    Color.fromRGBO(12, 0, 150, 1), // Deep Blue
    Color.fromRGBO(11, 66, 168, 1), // Dark Blue
    Color.fromRGBO(60, 113, 236, 1), // Blue
    Color.fromRGBO(90, 154, 226, 1), // Light Blue
    Color.fromRGBO(195, 228, 254, 1), // Very Light Blue
    Color.fromRGBO(255, 255, 255, 0.3), // Almost Transparent (Light)
  ];

  /// Rainfall gradient stops for precise positioning
  static const List<double> rainfallGradientStops = [0.0, 0.14, 0.28, 0.42, 0.56, 0.70, 0.84, 1.0];

  //================[WIND COLORS]================//

  /// Wind gradient colors for UI scale display - cải thiện màu sắc
  static const List<Color> windGradientColors = [
    Color.fromRGBO(76, 175, 80, 1.0), // Xanh lá nhạt (Gió nhẹ 0-5m/s)
    Color.fromRGBO(139, 195, 74, 1.0), // Xanh lá (Gió nhẹ 5-10m/s)
    Color.fromRGBO(255, 235, 59, 1.0), // Vàng (Gió trung bình 10-15m/s)
    Color.fromRGBO(255, 193, 7, 1.0), // Cam nhạt (Gió khá mạnh 15-20m/s)
    Color.fromRGBO(255, 152, 0, 1.0), // Cam (Gió mạnh 20-25m/s)
    Color.fromRGBO(244, 67, 54, 1.0), // Đỏ (Gió rất mạnh 25-30m/s)
    Color.fromRGBO(156, 39, 176, 1.0), // Tím (Bão >30m/s)
  ];

  /// Wind gradient stops for precise positioning
  static const List<double> windGradientStops = [0.0, 0.16, 0.32, 0.48, 0.64, 0.80, 1.0];

  //================[UI PANEL COLORS]================//

  /// Weather panel background colors
  static const Color panelBackground = Colors.white;
  static const double panelOpacity = 0.4;
  static const Color panelBorder = Colors.grey;
  static const double panelBorderOpacity = 0.3;

  /// Timeline popup colors
  static const Color timelineBackground = Colors.white;
  static const double timelineBackgroundOpacity = 0.6;
  static const double timelineBackgroundOpacityEnd = 0.8;
  static const Color timelineBorder = Colors.white;
  static const double timelineBorderOpacity = 0.3;
  static const double timelineBorderWidth = 1.5;

  /// Drag handle colors
  static const Color dragHandleColor = Colors.grey;
  static const double dragHandleOpacity = 0.4;

  /// Shadow colors for UI elements
  static const Color shadowColor = Colors.black;
  static const double shadowOpacity = 0.15;
  static const Color blueShadowColor = Colors.blue;
  static const double blueShadowOpacity = 0.1;

  //================[HELPER METHODS]================//

  /// Get temperature gradient for UI components
  static LinearGradient getTemperatureGradient() {
    return const LinearGradient(begin: Alignment.topCenter, end: Alignment.bottomCenter, colors: temperatureGradientColors, stops: temperatureGradientStops);
  }

  /// Get rainfall gradient for UI components
  static LinearGradient getRainfallGradient() {
    return const LinearGradient(begin: Alignment.topCenter, end: Alignment.bottomCenter, colors: rainfallGradientColors, stops: rainfallGradientStops);
  }

  /// Get wind gradient for UI components
  static LinearGradient getWindGradient() {
    return const LinearGradient(begin: Alignment.topCenter, end: Alignment.bottomCenter, colors: windGradientColors, stops: windGradientStops);
  }

  /// Get timeline background gradient
  static LinearGradient getTimelineBackgroundGradient() {
    return LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        Colors.white.withOpacity(0.25),
        Colors.white.withOpacity(0.15),
        Colors.blue.withOpacity(0.1),
      ],
    );
  }

  /// Common backdrop filter blur values for consistency
  static const double popupBlurSigma = 12.0;
  static const double panelBlurSigma = 10.0;

  static Color getPanelBackgroundColor() => panelBackground.withOpacity(panelOpacity);

  static Color getPanelBorderColor() => panelBorder.withOpacity(panelBorderOpacity);

  /// Get timeline border color
  static Color getTimelineBorderColor() {
    return timelineBorder.withOpacity(timelineBorderOpacity);
  }

  /// Get drag handle color
  static Color getDragHandleColor() {
    return dragHandleColor.withOpacity(dragHandleOpacity);
  }

  /// Get shadow color for UI elements
  static Color getShadowColor() {
    return shadowColor.withOpacity(shadowOpacity);
  }

  /// Get blue shadow color for UI elements
  static Color getBlueShadowColor() {
    return blueShadowColor.withOpacity(blueShadowOpacity);
  }

  /// Get layer shadow color by type
  static Color getLayerShadowColor(String layerType) {
    switch (layerType.toLowerCase()) {
      case 'temperature':
        return temperatureAccent.withOpacity(0.2);
      case 'precipitation':
      case 'rainfall':
        return precipitationAccent.withOpacity(0.2);
      case 'wind':
        return windAccent.withOpacity(0.2);
      default:
        return primaryOrange.withOpacity(0.2);
    }
  }

  //================[LAYER GRADIENT METHODS]================//

  /// Get gradient by layer ID for weather constants
  static LinearGradient getLayerGradient(String layerId) {
    switch (layerId) {
      case 'temperature':
        return const LinearGradient(
          colors: [
            Color(0xFF3F51B5), // Cold
            Color(0xFF2196F3),
            Color(0xFF00BCD4),
            Color(0xFF4CAF50),
            Color(0xFFFFEB3B),
            Color(0xFFFF9800),
            Color(0xFFFF5722), // Hot
          ],
        );
      case 'precipitation':
        return const LinearGradient(
          colors: [
            Colors.white,
            Color(0xFFE3F2FD),
            Color(0xFF2196F3),
            Color(0xFF1565C0),
            Color(0xFF0D47A1),
          ],
        );
      case 'wind':
        return getWindGradient();
      default:
        return const LinearGradient(colors: [Colors.grey, Colors.blueGrey]);
    }
  }

  //================[VALUE RANGE METHODS]================//

  /// Get minimum value display for weather layer scales
  static String getMinValue(String layerId) {
    switch (layerId) {
      case 'temperature':
        return '-5°C';
      case 'precipitation':
        return '0mm';
      case 'wind':
        return '0m/s';
      default:
        return '0';
    }
  }

  /// Get maximum value display for weather layer scales
  static String getMaxValue(String layerId) {
    switch (layerId) {
      case 'temperature':
        return '50°C';
      case 'precipitation':
        return '100mm';
      case 'wind':
        return '25m/s';
      default:
        return '100';
    }
  }
}

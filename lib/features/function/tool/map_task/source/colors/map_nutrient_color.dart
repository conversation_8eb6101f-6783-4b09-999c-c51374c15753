import 'package:hainong/features/function/tool/map_task/models/map_item_menu_model.dart';

class MapNutrientColor {
// Sầu riêng (Durian)
  static List<ColorItemMap> sauriengNColor() => [
        ColorItemMap(id: 1, value: "> 0,45", value2: "", color: "#1B3C00", nutrient: "N"), // Dark forest green
        ColorItemMap(id: 2, value: "0,361-0,45", value2: "0.361-0.45", color: "#2E5C00", nutrient: "N"), // Deep forest green
        ColorItemMap(id: 3, value: "0,271-0,36", value2: "0.271-0.36", color: "#4A7A00", nutrient: "N"), // Forest green
        ColorItemMap(id: 4, value: "0,181-0,27", value2: "0.181-0.27", color: "#6B9A00", nutrient: "N"), // Medium green
        ColorItemMap(id: 5, value: "0,091-0,18", value2: "0.091-0.18", color: "#A3C664", nutrient: "N"), // Light green
        ColorItemMap(id: 6, value: "< 0,091", value2: "< 0.091", color: "#C7D98B", nutrient: "N"), // Pale green
      ];

  static List<ColorItemMap> sauriengPColor() => [
        ColorItemMap(id: 1, value: "> 26.25", value2: "", color: "#8B6F00", nutrient: "P"), // Dark golden yellow
        ColorItemMap(id: 2, value: "> 18,75", value2: "21.1-26.25", color: "#A88C00", nutrient: "P"), // Deep golden yellow
        ColorItemMap(id: 3, value: "15,1-18,75", value2: "15.76-21", color: "#C5A900", nutrient: "P"), // Golden yellow
        ColorItemMap(id: 4, value: "11,26-15,0", value2: "10.51-15.7", color: "#E2C600", nutrient: "P"), // Bright yellow
        ColorItemMap(id: 5, value: "7,6-11,25", value2: "5.25-10.5", color: "#FFE066", nutrient: "P"), // Light yellow
        ColorItemMap(id: 6, value: "3,76-7,5", value2: "< 5.25", color: "#FFF4B2", nutrient: "P"), // Pale yellow
        ColorItemMap(id: 7, value: "< 3,76", value2: "", color: "#FFFBDB", nutrient: "P"), // Very pale yellow
      ];

  static List<ColorItemMap> sauriengKColor() => [
        ColorItemMap(id: 1, value: "> 14,62", value2: "> 14.62", color: "#B71C1C", nutrient: "K"), // Dark red
        ColorItemMap(id: 2, value: "9,94-14,62", value2: "9.94-14.62", color: "#C62828", nutrient: "K"), // Deep red
        ColorItemMap(id: 3, value: "6,61-9,94", value2: "6.61-9.94", color: "#D32F2F", nutrient: "K"), // Red
        ColorItemMap(id: 4, value: "4,56-6,61", value2: "4.56-6.61", color: "#E53935", nutrient: "K"), // Medium red
        ColorItemMap(id: 5, value: "1,0-4,56", value2: "1.0-4.56", color: "#F44336", nutrient: "K"), // Light red
        ColorItemMap(id: 6, value: "< 1", value2: "", color: "#FF5722", nutrient: "K"), // Red-orange
        ColorItemMap(id: 7, value: "> 0,375", value2: "> 0.45", color: "#FF6F00", nutrient: "K"), // Orange
        ColorItemMap(id: 8, value: "0,31-0,375", value2: "0.361-0.45", color: "#FF8F00", nutrient: "K"), // Deep orange
        ColorItemMap(id: 9, value: "0,226-0,3", value2: "0.271-0.36", color: "#FFA000", nutrient: "K"), // Amber
        ColorItemMap(id: 10, value: "0,151-0,225", value2: "0.181-0.27", color: "#FFB300", nutrient: "K"), // Light amber
        ColorItemMap(id: 11, value: "0,076-0,15", value2: "0.091-0.18", color: "#FFC107", nutrient: "K"), // Yellow
      ];

  static List<ColorItemMap> camNColor() => [
        ColorItemMap(id: 1, value: "> 0,45", value2: "", color: "#1A4D1A", nutrient: "N"), // Dark citrus green
        ColorItemMap(id: 2, value: "0,361-0,45", value2: "0.361-0.45", color: "#2E662E", nutrient: "N"), // Deep citrus green
        ColorItemMap(id: 3, value: "0,271-0,36", value2: "0.271-0.36", color: "#4A804A", nutrient: "N"), // Citrus green
        ColorItemMap(id: 4, value: "0,181-0,27", value2: "0.181-0.27", color: "#669A66", nutrient: "N"), // Medium green
        ColorItemMap(id: 5, value: "0,091-0,18", value2: "0.091-0.18", color: "#99CC99", nutrient: "N"), // Light green
        ColorItemMap(id: 6, value: "< 0,091", value2: "< 0.091", color: "#CCE6CC", nutrient: "N"), // Pale green
      ];

  static List<ColorItemMap> camPColor() => [
        ColorItemMap(id: 1, value: "> 26.25", value2: "", color: "#D84300", nutrient: "P"), // Dark orange
        ColorItemMap(id: 2, value: "> 18,75", value2: "21.1-26.25", color: "#E65C00", nutrient: "P"), // Deep orange
        ColorItemMap(id: 3, value: "15,1-18,75", value2: "15.76-21", color: "#F57500", nutrient: "P"), // Orange
        ColorItemMap(id: 4, value: "11,26-15,0", value2: "10.51-15.7", color: "#FF8E00", nutrient: "P"), // Bright orange
        ColorItemMap(id: 5, value: "7,6-11,25", value2: "5.25-10.5", color: "#FFA666", nutrient: "P"), // Light orange
        ColorItemMap(id: 6, value: "3,76-7,5", value2: "< 5.25", color: "#FFCC99", nutrient: "P"), // Pale orange
        ColorItemMap(id: 7, value: "< 3,76", value2: "", color: "#FFE6CC", nutrient: "P"), // Very pale orange
      ];

  static List<ColorItemMap> camKColor() => [
        ColorItemMap(id: 1, value: "> 14,62", value2: "> 14.62", color: "#8B0000", nutrient: "K"), // Dark red
        ColorItemMap(id: 2, value: "9,94-14,62", value2: "9.94-14.62", color: "#A11E1E", nutrient: "K"), // Deep red
        ColorItemMap(id: 3, value: "6,61-9,94", value2: "6.61-9.94", color: "#B73C3C", nutrient: "K"), // Red
        ColorItemMap(id: 4, value: "4,56-6,61", value2: "4.56-6.61", color: "#CD5A5A", nutrient: "K"), // Medium red
        ColorItemMap(id: 5, value: "1,0-4,56", value2: "1.0-4.56", color: "#E37878", nutrient: "K"), // Light red
        ColorItemMap(id: 6, value: "< 1", value2: "", color: "#F9A6A6", nutrient: "K"), // Pale red
        ColorItemMap(id: 7, value: "> 0,375", value2: "> 0.45", color: "#FBBBBB", nutrient: "K"), // Very pale red
        ColorItemMap(id: 8, value: "0,31-0,375", value2: "0.361-0.45", color: "#FCC0C0", nutrient: "K"), // Extremely pale red
        ColorItemMap(id: 9, value: "0,226-0,3", value2: "0.271-0.36", color: "#FDC8C8", nutrient: "K"), // Near-white red
        ColorItemMap(id: 10, value: "0,151-0,225", value2: "0.181-0.27", color: "#FDD0D0", nutrient: "K"), // Very near-white red
        ColorItemMap(id: 11, value: "0,076-0,15", value2: "0.091-0.18", color: "#FEE0E0", nutrient: "K"), // Almost white red
      ];

// Bưởi (Pomelo)
  static List<ColorItemMap> buoiNColor() => [
        ColorItemMap(id: 1, value: "> 0,45", value2: "", color: "#1A4D33", nutrient: "N"), // Dark pale green
        ColorItemMap(id: 2, value: "0,361-0,45", value2: "0.361-0.45", color: "#2E6647", nutrient: "N"), // Deep pale green
        ColorItemMap(id: 3, value: "0,271-0,36", value2: "0.271-0.36", color: "#4A805A", nutrient: "N"), // Pale green
        ColorItemMap(id: 4, value: "0,181-0,27", value2: "0.181-0.27", color: "#669A6E", nutrient: "N"), // Medium pale green
        ColorItemMap(id: 5, value: "0,091-0,18", value2: "0.091-0.18", color: "#99CC99", nutrient: "N"), // Light pale green
        ColorItemMap(id: 6, value: "< 0,091", value2: "< 0.091", color: "#CCE6CC", nutrient: "N"), // Very pale green
      ];

  static List<ColorItemMap> buoiPColor() => [
        ColorItemMap(id: 1, value: "> 26.25", value2: "", color: "#D9A900", nutrient: "P"), // Dark yellow
        ColorItemMap(id: 2, value: "> 18,75", value2: "21.1-26.25", color: "#E6B800", nutrient: "P"), // Deep yellow
        ColorItemMap(id: 3, value: "15,1-18,75", value2: "15.76-21", color: "#F2C600", nutrient: "P"), // Yellow
        ColorItemMap(id: 4, value: "11,26-15,0", value2: "10.51-15.7", color: "#FFD400", nutrient: "P"), // Bright yellow
        ColorItemMap(id: 5, value: "7,6-11,25", value2: "5.25-10.5", color: "#FFE066", nutrient: "P"), // Light yellow
        ColorItemMap(id: 6, value: "3,76-7,5", value2: "< 5.25", color: "#FFF4B2", nutrient: "P"), // Pale yellow
        ColorItemMap(id: 7, value: "< 3,76", value2: "", color: "#FFFBDB", nutrient: "P"), // Very pale yellow
      ];

  static List<ColorItemMap> buoiKColor() => [
        ColorItemMap(id: 1, value: "> 14,62", value2: "> 14.62", color: "#8B2A47", nutrient: "K"), // Dark pinkish-red
        ColorItemMap(id: 2, value: "9,94-14,62", value2: "9.94-14.62", color: "#A14765", nutrient: "K"), // Deep pinkish-red
        ColorItemMap(id: 3, value: "6,61-9,94", value2: "6.61-9.94", color: "#B76483", nutrient: "K"), // Pinkish-red
        ColorItemMap(id: 4, value: "4,56-6,61", value2: "4.56-6.61", color: "#CD81A1", nutrient: "K"), // Medium pinkish-red
        ColorItemMap(id: 5, value: "1,0-4,56", value2: "1.0-4.56", color: "#E39EBF", nutrient: "K"), // Light pinkish-red
        ColorItemMap(id: 6, value: "< 1", value2: "", color: "#F9BBDD", nutrient: "K"), // Pale pinkish-red
        ColorItemMap(id: 7, value: "> 0,375", value2: "> 0.45", color: "#FBDDE6", nutrient: "K"), // Very pale pinkish-red
        ColorItemMap(id: 8, value: "0,31-0,375", value2: "0.361-0.45", color: "#FDC4E9", nutrient: "K"), // Extremely pale pinkish-red
        ColorItemMap(id: 9, value: "0,226-0,3", value2: "0.271-0.36", color: "#FDCBEC", nutrient: "K"), // Near-white pinkish-red
        ColorItemMap(id: 10, value: "0,151-0,225", value2: "0.181-0.27", color: "#FED2EF", nutrient: "K"), // Very near-white pinkish-red
        ColorItemMap(id: 11, value: "0,076-0,15", value2: "0.091-0.18", color: "#FEDAEF", nutrient: "K"), // Almost white pinkish-red
      ];

// Nhãn (Longan)
  static List<ColorItemMap> nhanNColor() => [
        ColorItemMap(id: 1, value: "> 0,45", value2: "", color: "#3C4D1A", nutrient: "N"), // Dark olive green
        ColorItemMap(id: 2, value: "0,361-0,45", value2: "0.361-0.45", color: "#4A662E", nutrient: "N"), // Deep olive green
        ColorItemMap(id: 3, value: "0,271-0,36", value2: "0.271-0.36", color: "#5A804A", nutrient: "N"), // Olive green
        ColorItemMap(id: 4, value: "0,181-0,27", value2: "0.181-0.27", color: "#6E9A66", nutrient: "N"), // Medium olive green
        ColorItemMap(id: 5, value: "0,091-0,18", value2: "0.091-0.18", color: "#99CC99", nutrient: "N"), // Light olive green
        ColorItemMap(id: 6, value: "< 0,091", value2: "< 0.091", color: "#CCE6CC", nutrient: "N"), // Pale olive green
      ];

  static List<ColorItemMap> nhanPColor() => [
        ColorItemMap(id: 1, value: "> 26.25", value2: "", color: "#8B5A00", nutrient: "P"), // Dark golden brown
        ColorItemMap(id: 2, value: "> 18,75", value2: "21.1-26.25", color: "#A17100", nutrient: "P"), // Deep golden brown
        ColorItemMap(id: 3, value: "15,1-18,75", value2: "15.76-21", color: "#B78800", nutrient: "P"), // Golden brown
        ColorItemMap(id: 4, value: "11,26-15,0", value2: "10.51-15.7", color: "#CD9F00", nutrient: "P"), // Medium golden brown
        ColorItemMap(id: 5, value: "7,6-11,25", value2: "5.25-10.5", color: "#E3B633", nutrient: "P"), // Light golden brown
        ColorItemMap(id: 6, value: "3,76-7,5", value2: "< 5.25", color: "#F9CD66", nutrient: "P"), // Pale golden brown
        ColorItemMap(id: 7, value: "< 3,76", value2: "", color: "#FFE6B2", nutrient: "P"), // Very pale golden brown
      ];

  static List<ColorItemMap> nhanKColor() => [
        ColorItemMap(id: 1, value: "> 14,62", value2: "> 14.62", color: "#4A148C", nutrient: "K"), // Dark purple
        ColorItemMap(id: 2, value: "9,94-14,62", value2: "9.94-14.62", color: "#6A1B9A", nutrient: "K"), // Deep purple
        ColorItemMap(id: 3, value: "6,61-9,94", value2: "6.61-9.94", color: "#7B1FA2", nutrient: "K"), // Purple
        ColorItemMap(id: 4, value: "4,56-6,61", value2: "4.56-6.61", color: "#8E24AA", nutrient: "K"), // Medium purple
        ColorItemMap(id: 5, value: "1,0-4,56", value2: "1.0-4.56", color: "#9C27B0", nutrient: "K"), // Light purple
        ColorItemMap(id: 6, value: "< 1", value2: "", color: "#AB47BC", nutrient: "K"), // Pale purple
        ColorItemMap(id: 7, value: "> 0,375", value2: "> 0.45", color: "#BA68C8", nutrient: "K"), // Very pale purple
        ColorItemMap(id: 8, value: "0,31-0,375", value2: "0.361-0.45", color: "#CE93D8", nutrient: "K"), // Purple-pink transition
        ColorItemMap(id: 9, value: "0,226-0,3", value2: "0.271-0.36", color: "#E1BEE7", nutrient: "K"), // Light purple-pink
        ColorItemMap(id: 10, value: "0,151-0,225", value2: "0.181-0.27", color: "#F3E5F5", nutrient: "K"), // Very pale purple-pink
        ColorItemMap(id: 11, value: "0,076-0,15", value2: "0.091-0.18", color: "#FFF8E1", nutrient: "K"), // Almost white yellow
      ];

// Thanh long (Dragon Fruit)
  static List<ColorItemMap> thanhLongNColor() => [
        ColorItemMap(id: 1, value: "> 0,45", value2: "", color: "#1A3C2F", nutrient: "N"), // Dark cactus green
        ColorItemMap(id: 2, value: "0,361-0,45", value2: "0.361-0.45", color: "#2E5C47", nutrient: "N"), // Deep cactus green
        ColorItemMap(id: 3, value: "0,271-0,36", value2: "0.271-0.36", color: "#4A7A5A", nutrient: "N"), // Cactus green
        ColorItemMap(id: 4, value: "0,181-0,27", value2: "0.181-0.27", color: "#669A6E", nutrient: "N"), // Medium cactus green
        ColorItemMap(id: 5, value: "0,091-0,18", value2: "0.091-0.18", color: "#99CC99", nutrient: "N"), // Light cactus green
        ColorItemMap(id: 6, value: "< 0,091", value2: "< 0.091", color: "#CCE6CC", nutrient: "N"), // Pale cactus green
      ];

  static List<ColorItemMap> thanhLongPColor() => [
        ColorItemMap(id: 1, value: "> 26.25", value2: "", color: "#8B2A47", nutrient: "P"), // Dark pinkish-yellow
        ColorItemMap(id: 2, value: "> 18,75", value2: "21.1-26.25", color: "#A14765", nutrient: "P"), // Deep pinkish-yellow
        ColorItemMap(id: 3, value: "15,1-18,75", value2: "15.76-21", color: "#B76483", nutrient: "P"), // Pinkish-yellow
        ColorItemMap(id: 4, value: "11,26-15,0", value2: "10.51-15.7", color: "#CD81A1", nutrient: "P"), // Medium pinkish-yellow
        ColorItemMap(id: 5, value: "7,6-11,25", value2: "5.25-10.5", color: "#E39EBF", nutrient: "P"), // Light pinkish-yellow
        ColorItemMap(id: 6, value: "3,76-7,5", value2: "< 5.25", color: "#F9BBDD", nutrient: "P"), // Pale pinkish-yellow
        ColorItemMap(id: 7, value: "< 3,76", value2: "", color: "#FDDDE6", nutrient: "P"), // Very pale pinkish-yellow
      ];

  static List<ColorItemMap> thanhLongKColor() => [
        ColorItemMap(id: 1, value: "> 14,62", value2: "> 14.62", color: "#4A148C", nutrient: "K"), // Dark purple-red
        ColorItemMap(id: 2, value: "9,94-14,62", value2: "9.94-14.62", color: "#5C2A9A", nutrient: "K"), // Deep purple-red
        ColorItemMap(id: 3, value: "6,61-9,94", value2: "6.61-9.94", color: "#6E40A8", nutrient: "K"), // Purple-red
        ColorItemMap(id: 4, value: "4,56-6,61", value2: "4.56-6.61", color: "#8056B6", nutrient: "K"), // Medium purple-red
        ColorItemMap(id: 5, value: "1,0-4,56", value2: "1.0-4.56", color: "#A880D1", nutrient: "K"), // Light purple-red
        ColorItemMap(id: 6, value: "< 1", value2: "", color: "#D1B3E6", nutrient: "K"), // Pale purple-red
        ColorItemMap(id: 7, value: "> 0,375", value2: "> 0.45", color: "#E6CCF2", nutrient: "K"), // Very pale purple-red
        ColorItemMap(id: 8, value: "0,31-0,375", value2: "0.361-0.45", color: "#EBD6F5", nutrient: "K"), // Extremely pale purple-red
        ColorItemMap(id: 9, value: "0,226-0,3", value2: "0.271-0.36", color: "#F0E0F8", nutrient: "K"), // Near-white purple-red
        ColorItemMap(id: 10, value: "0,151-0,225", value2: "0.181-0.27", color: "#F5E9FB", nutrient: "K"), // Very near-white purple-red
        ColorItemMap(id: 11, value: "0,076-0,15", value2: "0.091-0.18", color: "#FAF2FD", nutrient: "K"), // Almost white purple-red
      ];

  // Xoài (Mango)
  static List<ColorItemMap> xoaiNColor() => [
        ColorItemMap(id: 1, value: "> 0,45", value2: "", color: "#1A4D00", nutrient: "N"), // Dark vibrant green
        ColorItemMap(id: 2, value: "0,361-0,45", value2: "0.361-0.45", color: "#2E6600", nutrient: "N"), // Deep vibrant green
        ColorItemMap(id: 3, value: "0,271-0,36", value2: "0.271-0.36", color: "#4A8000", nutrient: "N"), // Vibrant green
        ColorItemMap(id: 4, value: "0,181-0,27", value2: "0.181-0.27", color: "#669A00", nutrient: "N"), // Medium green
        ColorItemMap(id: 5, value: "0,091-0,18", value2: "0.091-0.18", color: "#99CC66", nutrient: "N"), // Light green
        ColorItemMap(id: 6, value: "< 0,091", value2: "< 0.091", color: "#CCE699", nutrient: "N"), // Pale green
      ];

  static List<ColorItemMap> xoaiPColor() => [
        ColorItemMap(id: 1, value: "> 26.25", value2: "", color: "#C94F00", nutrient: "P"), // Dark yellow-orange
        ColorItemMap(id: 2, value: "> 18,75", value2: "21.1-26.25", color: "#D97100", nutrient: "P"), // Deep yellow-orange
        ColorItemMap(id: 3, value: "15,1-18,75", value2: "15.76-21", color: "#EB8B1A", nutrient: "P"), // Yellow-orange
        ColorItemMap(id: 4, value: "11,26-15,0", value2: "10.51-15.7", color: "#FBC600", nutrient: "P"), // Bright yellow-orange
        ColorItemMap(id: 5, value: "7,6-11,25", value2: "5.25-10.5", color: "#FFA533", nutrient: "P"), // Light yellow-orange
        ColorItemMap(id: 6, value: "3,76-7,5", value2: "< 5.25", color: "#FFBF66", nutrient: "P"), // Pale yellow-orange
        ColorItemMap(id: 7, value: "< 3,76", value2: "", color: "#FFD9B2", nutrient: "P"), // Very pale yellow-orange
      ];

  static List<ColorItemMap> xoaiKColor() => [
        ColorItemMap(id: 1, value: "> 14,62", value2: "> 14.62", color: "#A63D00", nutrient: "K"), // Dark coral red
        ColorItemMap(id: 2, value: "9,94-14,62", value2: "9.94-14.62", color: "#B35100", nutrient: "K"), // Deep coral red
        ColorItemMap(id: 3, value: "6,61-9,94", value2: "6.61-9.94", color: "#C06600", nutrient: "K"), // Coral red
        ColorItemMap(id: 4, value: "4,56-6,61", value2: "4.56-6.61", color: "#CD7A1A", nutrient: "K"), // Medium coral red
        ColorItemMap(id: 5, value: "1,0-4,56", value2: "1.0-4.56", color: "#DB8F33", nutrient: "K"), // Light coral red
        ColorItemMap(id: 6, value: "< 1", value2: "", color: "#E8A34D", nutrient: "K"), // Pale coral red
        ColorItemMap(id: 7, value: "> 0,375", value2: "> 0.45", color: "#F5B866", nutrient: "K"), // Very pale coral red
        ColorItemMap(id: 8, value: "0,31-0,375", value2: "0.361-0.45", color: "#FFCC80", nutrient: "K"), // Extremely pale coral red
        ColorItemMap(id: 9, value: "0,226-0,3", value2: "0.271-0.36", color: "#FFD999", nutrient: "K"), // Near-white coral red
        ColorItemMap(id: 10, value: "0,151-0,225", value2: "0.181-0.27", color: "#FFE6B2", nutrient: "K"), // Very near-white coral red
        ColorItemMap(id: 11, value: "0,076-0,15", value2: "0.091-0.18", color: "#FFF2CC", nutrient: "K"), // Almost white coral red
      ];

// Lúa (Rice)
  static List<ColorItemMap> luaNColor() => [
        ColorItemMap(id: 1, value: "> 0,45", value2: "", color: "#004D00", nutrient: "N"), // Dark green
        ColorItemMap(id: 2, value: "0,361-0,45", value2: "0.361-0.45", color: "#006600", nutrient: "N"), // Deep green
        ColorItemMap(id: 3, value: "0,271-0,36", value2: "0.271-0.36", color: "#1A991A", nutrient: "N"), // Green
        ColorItemMap(id: 4, value: "0,181-0,27", value2: "0.181-0.27", color: "#4DB34D", nutrient: "N"), // Medium green
        ColorItemMap(id: 5, value: "0,091-0,18", value2: "0.091-0.18", color: "#80C980", nutrient: "N"), // Light green
        ColorItemMap(id: 6, value: "< 0,091", value2: "< 0.091", color: "#B3E0B3", nutrient: "N"), // Pale green
      ];

  static List<ColorItemMap> luaPColor() => [
        ColorItemMap(id: 1, value: "> 26,25", value2: "", color: "#BC6F00", nutrient: "P"), // Dark yellow
        ColorItemMap(id: 2, value: "> 18,75", value2: "21.1-26.25", color: "#D18C00", nutrient: "P"), // Deep yellow
        ColorItemMap(id: 3, value: "15,1-18,75", value2: "15.76-21", color: "#E6A900", nutrient: "P"), // Yellow
        ColorItemMap(id: 4, value: "11,26-15,0", value2: "10.51-15.7", color: "#FBC600", nutrient: "P"), // Bright yellow
        ColorItemMap(id: 5, value: "7,6-11,25", value2: "5.25-10.5", color: "#FFE066", nutrient: "P"), // Light yellow
        ColorItemMap(id: 6, value: "3,76-7,5", value2: "< 5.25", color: "#FFF4B2", nutrient: "P"), // Pale yellow
        ColorItemMap(id: 7, value: "< 3,76", value2: "", color: "#FFFBDB", nutrient: "P"), // Very pale yellow
      ];

  static List<ColorItemMap> luaKColor() => [
        ColorItemMap(id: 1, value: "> 14,62", value2: "> 14.62", color: "#8B0000", nutrient: "K"), // Dark red
        ColorItemMap(id: 2, value: "9,94-14,62", value2: "9.94-14.62", color: "#A11E1E", nutrient: "K"), // Deep red
        ColorItemMap(id: 3, value: "6,61-9,94", value2: "6.61-9.94", color: "#B73C3C", nutrient: "K"), // Red
        ColorItemMap(id: 4, value: "4,56-6,61", value2: "4.56-6.61", color: "#CD5A5A", nutrient: "K"), // Medium red
        ColorItemMap(id: 5, value: "1,0-4,56", value2: "1.0-4.56", color: "#E37878", nutrient: "K"), // Light red
        ColorItemMap(id: 6, value: "> 0,375", value2: "> 0.45", color: "#F9A6A6", nutrient: "K"), // Pale red
        ColorItemMap(id: 7, value: "0,31-0,375", value2: "0.361-0.45", color: "#FBBBBB", nutrient: "K"), // Very pale red
        ColorItemMap(id: 8, value: "0,226-0,3", value2: "0.271-0.36", color: "#FCC0C0", nutrient: "K"), // Extremely pale red
        ColorItemMap(id: 9, value: "0,151-0,225", value2: "0.181-0.27", color: "#FDC8C8", nutrient: "K"), // Near-white red
        ColorItemMap(id: 10, value: "0,076-0,15", value2: "0.091-0.18", color: "#FDD0D0", nutrient: "K"), // Very near-white red
        ColorItemMap(id: 11, value: "< 1", value2: "", color: "#FEE0E0", nutrient: "K"), // Almost white red
      ];

  static List<ColorItemMap> pHColor() => [
        ColorItemMap(id: 1, value: "6-7,5", color: "#FBC02D", nutrient: "pH"), // Dark yellow
        ColorItemMap(id: 2, value: "5,0-6,0", color: "#FFEB3B", nutrient: "pH"), // Medium yellow
        ColorItemMap(id: 3, value: "< 5,0", color: "#FFF9C4", nutrient: "pH"), // Light yellow
      ];

  static List<ColorItemMap> eCColor() => [
        ColorItemMap(id: 1, value: "> 1,6", color: "#1565C0", nutrient: "eC"), // Dark blue
        ColorItemMap(id: 2, value: "0,8-1,6", color: "#42A5F5", nutrient: "eC"), // Medium blue
        ColorItemMap(id: 3, value: "< 0,8", color: "#BBDEFB", nutrient: "eC"), // Light blue
      ];

  static List<ColorItemMap> ceCColor() => [
        ColorItemMap(id: 1, value: "15-25", color: "#1976D2", nutrient: "ceC"), // Dark blue
        ColorItemMap(id: 2, value: "5-15", color: "#BBDEFB", nutrient: "ceC"), // Light blue
      ];

  static List<ColorItemMap> omCColor() => [
        ColorItemMap(id: 1, value: "> 10", color: "#C62828", nutrient: "omC"), // Dark red
        ColorItemMap(id: 2, value: "4-10", color: "#EF5350", nutrient: "omC"), // Medium red
        ColorItemMap(id: 3, value: "< 4,0", color: "#FFCDD2", nutrient: "omC"), // Light red
      ];
}

import 'dart:math';

import 'package:hainong/features/function/tool/map_task/components/map_model/model_body_detail_widget.dart';
import 'package:hainong/features/function/tool/map_task/components/map_model/model_dropdown_widget.dart';
import 'package:hainong/features/function/tool/map_task/components/map_model/model_update_page.dart';
import 'package:hainong/features/function/tool/map_task/components/map_nutrition/nutrition_body_detail_widget.dart';
import 'package:hainong/features/function/tool/map_task/components/map_nutrition/nutrition_tree_menu_widget.dart';
import 'package:hainong/features/function/tool/map_task/components/map_pet/pet_body_detail_widget.dart';
import 'package:hainong/features/function/tool/map_task/components/map_pet/pet_filter_page.dart';
import 'package:hainong/features/function/tool/map_task/components/map_weather/weather_map_controller.dart';
import 'package:hainong/features/function/tool/map_task/components/map_weather/weather_map_panel_widget.dart';
import 'package:hainong/features/function/tool/map_task/components/popup_map_menu_button_widget.dart';
import 'package:hainong/features/function/tool/map_task/map_constants.dart';
import 'package:hainong/features/function/tool/map_task/map_task_bloc.dart';
import 'package:hainong/features/function/tool/map_task/models/map_address_model.dart';
import 'package:hainong/features/function/tool/map_task/models/map_data_model.dart';
import 'package:hainong/features/function/tool/map_task/models/map_deep_link_model.dart';
import 'package:hainong/features/function/tool/map_task/models/map_enum.dart';
import 'package:hainong/features/function/tool/map_task/models/map_response_model.dart';
import 'package:hainong/features/function/tool/map_task/source/trackasia_nutrition_map_source.dart';
import 'package:hainong/features/function/tool/map_task/utils/map_dialog_utils.dart';
import 'package:hainong/features/function/tool/map_task/utils/map_utils.dart';
import 'package:hainong/features/post/ui/import_lib_ui_post.dart';
import 'package:textfield_tags/textfield_tags.dart';
import 'package:trackasia_gl/trackasia_gl.dart';

import 'components/map_model/model_body_detail_overview_widget.dart';
import 'components/map_nutrition/nutrition_tree_value_widget.dart';
import 'components/map_pet/pet_body_detail_image_widget.dart';
import 'components/map_pet/pet_info_color_widget.dart';
import 'models/map_item_menu_model.dart';
import 'source/trackasia_map_source.dart';
import 'utils/map_audio_util.dart';

class MapTaskPage extends BasePage {
  MapTaskPage({this.deepLink, this.openPest, Key? key}) : super(pageState: _MapTaskPageState(), key: key);
  final MapDeepLinkModel? deepLink;
  final bool? openPest;
}

class _MapTaskPageState extends BasePageState with TickerProviderStateMixin {
  //================[VARIABLES & CONTROLLERS]================//

  // Map Controllers
  TrackAsiaMapController? mapController;
  TrackAsiaMapSource clusterSource = TrackAsiaMapSource();
  late TabController _tabMenuController, _tabBodyController;

  // Weather Controller - Thêm controller mới
  late WeatherMapController _weatherController;

  // State Management
  MapMenuEnum _selectMenu = MapMenuEnum.none;
  MapModelEnum _selectTabMenuModel = MapModelEnum.demonstration;
  bool isShowMapDemoBottomSheet = false, isDeepLink = false;
  int indexPetZoom = 1;

  // Tree & Nutrition Data
  List<MenuItemMap> _treeNutritionalList = [], _treeTypeList = [];
  List<ColorItemMap> _colorTreeValueList = [];
  MenuItemMap? _treeType, _treeNutritionType;
  final tagTreeValueController = StringTagController();

  // UI State Notifiers
  final ValueNotifier<List<MenuItemMap>> _treeNutritionNotifier = ValueNotifier([]), _treeTypeNotifier = ValueNotifier([]);
  final ValueNotifier<MapAddressModel> _addressMap = ValueNotifier<MapAddressModel>(MapAddressModel());
  final ValueNotifier<bool> _expandedDescription = ValueNotifier(false), _expandedTime = ValueNotifier(false), _isPlay = ValueNotifier(false);
  final ValueNotifier<MapDataModel> _dataMap = ValueNotifier<MapDataModel>(MapDataModel());

  // Filter Data
  DateTime? _fromDate, _toDate;
  ItemModel _currentProvince = ItemModel();
  List<String> _currentKinds = [];
  String? _lastFilterHash;

  // Map Interaction
  LatLng? tappedNutritionPosition;
  Symbol? tappedNutritionSymbol;
  Symbol? tappedWeatherSymbol;
  Symbol? tappedCurrentLocationSymbol;

  // Camera Position State
  bool _isAtCurrentLocation = false;
  final LatLng _initialCameraPosition = const LatLng(15.7146441, 106.401633);
  final double _initialZoom = 5.0;

  // Deep Link & Dialog
  MapDeepLinkModel? deepLink;
  MapTaskPage? page;
  MapDataModel dataDialog = MapDataModel();
  String imageUser = "";

  // UI Keys
  final GlobalKey<DiseaseColorLegendWidgetState> _diseaseLegendKey = GlobalKey<DiseaseColorLegendWidgetState>();

  final ValueNotifier<bool> _showTreeSelectionPopup = ValueNotifier(false);
  List<ItemModel> _provinces = [];
  final Set<String> _loadedProvinces = {};
  bool _provincesLoaded = false;
  bool _isLoadingProvinces = false;
  bool _shouldCancelProvinceLoading = false;
  bool _isLoadingCurrentLocation = false; // Thêm loading state cho current location

  // Weather detail loading management
  bool _isLoadingWeatherDetail = false;
  String? _lastWeatherDetailRequestId;
  bool _isWeatherDetailBottomSheetShowing = false;

  // Weather control states

  //================[LIFECYCLE METHODS]================//

  @override
  void initState() {
    super.initState();
    PopupMapMenuButton.resetState();
    _initializeWeatherController();
    _initializeMapTask();
  }

  @override
  void dispose() {
    _weatherController.removeListener(_weatherControllerListener);
    _resetWeatherDetailState();
    if (bloc is MapTaskBloc) {
      (bloc as MapTaskBloc).cancelWeatherDetailLoading();
      (bloc as MapTaskBloc).cancelCurrentRequest();
      (bloc as MapTaskBloc).cancelProvinceLoading();
    }
    _weatherController.dispose();
    cancelProvinceLoading();
    _tabMenuController.dispose();
    _tabBodyController.dispose();
    _clearLists();
    _disposeAudio();
    super.dispose();
  }

  //================[INITIALIZATION]================//

  void _initializeWeatherController() {
    _weatherController = WeatherMapController();
  }

  void _weatherControllerListener() {
    if (mounted) {
      setState(() {});
    }
  }

  void _initializeMapTask() {
    page = widget as MapTaskPage;
    _handleDeepLink();
    _getImageUser();
    _resetWeatherDetailState();

    _initializeBloc();
    _initializeControllers();
    _loadInitialData();

    _weatherController.initialize(
      tickerProvider: this,
      context: context,
      bloc: bloc as MapTaskBloc?,
      addressMap: _addressMap,
      isPlay: _isPlay,
      clusterSource: clusterSource,
    );
    _weatherController.addListener(_weatherControllerListener);
  }

  void _handleDeepLink() {
    if (page?.deepLink != null) {
      isDeepLink = true;
      deepLink = page?.deepLink;
      _selectMenu = deepLink!.menu;
      _selectTabMenuModel = deepLink!.tab;
      bloc?.add(ChangeIndexTabEvent());
    }
  }

  void _initializeBloc() {
    bloc = MapTaskBloc();
    bloc!.stream.listen(_handleBlocStates);
  }

  void _initializeControllers() {
    _tabMenuController = TabController(length: 3, vsync: this);
    _tabBodyController = TabController(length: 2, vsync: this);
  }

  void _loadInitialData() {
    _treeNutritionalList = MapConstants.menuNutritionalTypeModel();
    _treeNutritionNotifier.value = List.from(_treeNutritionalList);
    _loadProvinces();
    if (page!.openPest == true) selectMenuMap(1);
  }

  void _loadProvinces() {
    if (!_provincesLoaded) bloc?.add(LoadProvincesEvent());
  }

  List<ItemModel> getProvincesForFilter() => (_provincesLoaded && _provinces.isNotEmpty) ? _provinces : [];

  void _fetchPetMapWithProvinces() {
    String params = petParams();
    bool hasSpecificProvince = _currentProvince.id.isNotEmpty && _currentProvince.id != "-1";
    removePetSource();
    if (params.isNotEmpty && hasSpecificProvince) {
      bloc?.add(GetPetMapEvent(MapConstants.sourceIdPetMap, params));
    } else {
      if (_provincesLoaded && _provinces.isNotEmpty) {
        _shouldCancelProvinceLoading = false;
        _isLoadingProvinces = true;
        String currentFilterHash = _createFilterHash();
        bool needsReload = _lastFilterHash != currentFilterHash;
        if (needsReload) {
          _loadedProvinces.clear();
          _lastFilterHash = currentFilterHash;
        }
        String? fromDate = _fromDate != null ? MapUtils.datetimeToFormat2(_fromDate) : null;
        String? toDate = _toDate != null ? MapUtils.datetimeToFormat2(_toDate) : null;
        List<String>? diagnosticIds = _currentKinds.isNotEmpty ? _currentKinds : null;
        bloc?.add(GetPetMapByProvincesEvent(MapConstants.sourceIdPetMap, MapConstants.keyLayerPetMap, _provinces, fromDate: fromDate, toDate: toDate, diagnosticIds: diagnosticIds));
      } else {
        _loadProvinces();
      }
    }
  }

  String _createFilterHash() {
    String fromDate = _fromDate != null ? MapUtils.datetimeToFormat2(_fromDate) : '';
    String toDate = _toDate != null ? MapUtils.datetimeToFormat2(_toDate) : '';
    String diagnostics = _currentKinds.join(',');
    return '$fromDate|$toDate|$diagnostics';
  }

  void _getImageUser() {
    SharedPreferences.getInstance().then((prefs) {
      final Constants constants = Constants();
      if (prefs.containsKey(constants.image)) imageUser = prefs.getString(constants.image) ?? "";
    });
  }

  //================[BLOC STATE HANDLERS]================//

  //================[BLOC STATE HANDLING]================//

  void _handleBlocStates(BaseState state) async {
    switch (state.runtimeType) {
      case GetPetMapByProvinceState:
        _handlePetMapByProvinceState(state as GetPetMapByProvinceState);
        break;
      case LoadProvincesState:
        _handleLoadProvincesState(state as LoadProvincesState);
        break;
      case GetTreeParamsState:
        _handleTreeParamsState(state as GetTreeParamsState);
        break;
      case GetDemonstrationParadigmsState:
        _handleDemoState(state as GetDemonstrationParadigmsState);
        break;
      case GetListAgenciesState:
        _handleAgenciesState(state as GetListAgenciesState);
        break;
      case GetNutritionMapState:
        _handleLocationMapState(state as GetNutritionMapState);
        break;
      case GetWeatherMapState:
        _handleWeatherMapState(state as GetWeatherMapState);
        break;
      case GetDetailDemonstrationParadigmState:
        _handleDetailDemoState(state as GetDetailDemonstrationParadigmState);
        break;
      case GetDetailAgenciesState:
        _handleDetailAgenciesState(state as GetDetailAgenciesState);
        break;
      case GetCurrentLocationState:
        _handleCurrentLocationState(state as GetCurrentLocationState);
        break;
      case GetLocationState:
        _handleLocationState(state as GetLocationState);
        break;
      case LoadCurrentDetailWeatherState:
        _handleCurrentDetailWeatherState(state as LoadCurrentDetailWeatherState);
        break;
      case LoadDetailWeatherState:
        _handleDetailWeatherState(state as LoadDetailWeatherState);
        break;
      case LoadAudioLinkState:
        _handleLoadAudioLinkState(state as LoadAudioLinkState);
        break;
      case LoadingAudioState:
        _handleLoadingAudioState(state as LoadingAudioState);
        break;
      case PlayAudioState:
        _handleAudioState(state as PlayAudioState);
        break;
      case ShowErrorState:
        _handleErrorState(state as ShowErrorState);
        break;
      case GetPetMapDetailState:
        _handlePetDetailState(state as GetPetMapDetailState);
        break;
    }
  }

  void _handleTreeParamsState(GetTreeParamsState state) {
    bool needsTreeSelection = _needsTreeSelection(state.nutritionType);
    if (needsTreeSelection && (state.treeType.isEmpty || state.treeType == "")) {
      removeNutritionSource();
      return;
    }
    setLayerNutrition(state.nutritionType, state.treeType);
  }

  void _handleDemoState(GetDemonstrationParadigmsState state) => _handleModelGeoState(state.data);

  void _handleAgenciesState(GetListAgenciesState state) => _handleModelGeoState(state.data);

  void _handleDetailDemoState(GetDetailDemonstrationParadigmState state) => _showModelBottomSheet(state.data);

  void _handleDetailAgenciesState(GetDetailAgenciesState state) => _showModelBottomSheet(state.data);

  void _handleModelGeoState(MapGeoJsonModel data) {
    if (isCheckFeatureGeoData(data)) {
      addModelGeoMap(data, MapConstants.getImageNameModelMap(_selectTabMenuModel));
      if (isDeepLink) handleDeepLinkMapModel();
    }
  }

  void _handleCurrentLocationState(GetCurrentLocationState state) {
    _addressMap.value = state.response;
    if (_selectMenu == MapMenuEnum.weather) {
      _weatherController.updateCurrentLocationAddress(state.response);
      _weatherController.addCurrentLocationMarker(() => bloc?.add(GetCurrentLocationEvent(state.point)));
    }
  }

  void _handleLocationMapState(GetNutritionMapState state) => showNutritionBottomSheet(state.data, state.point);

  void _handleLocationState(GetLocationState state) => _addressMap.value = state.response;

  void _handleCurrentDetailWeatherState(LoadCurrentDetailWeatherState state) => _weatherController.setCurrentWeatherData(MapGeoJsonModel(state.data), state.point);

  void _handleDetailWeatherState(LoadDetailWeatherState state) {
    // Cancel any previous weather detail operations immediately
    if (bloc is MapTaskBloc) {
      (bloc as MapTaskBloc).cancelWeatherDetailLoading();
    }

    if (_isWeatherDetailBottomSheetShowing) {
      Navigator.of(context).pop();
      _isWeatherDetailBottomSheetShowing = false;
      _weatherController.cancelAudioLoading();
    }

    // Check if this weather data has audio link
    final weatherData = state.data;
    final hasAudioLink = weatherData['audio_link'] != null && weatherData['audio_link'].toString().isNotEmpty;

    // If no audio link, set loading state before showing popup
    if (!hasAudioLink) {
      _weatherController.handleLoadingStateChange(true);
    }

    _isLoadingWeatherDetail = true;
    // Reduce delay and make it more synchronous
    Future.delayed(const Duration(milliseconds: 50), () {
      if (_isLoadingWeatherDetail) {
        _isWeatherDetailBottomSheetShowing = true;
        _isLoadingWeatherDetail = false;
        _weatherController.showWeatherDetailBottomSheet(MapGeoJsonModel(state.data), state.point, () {
          isShowMapDemoBottomSheet = false;
          _isWeatherDetailBottomSheetShowing = false;
          // Ensure cleanup when popup is closed
          if (bloc is MapTaskBloc) {
            (bloc as MapTaskBloc).cancelWeatherDetailLoading();
          }
        });
      }
    });
    setPositionMapClick(state.point);
  }

  void _resetWeatherDetailState() {
    _isLoadingWeatherDetail = false;
    _lastWeatherDetailRequestId = null;
    _isWeatherDetailBottomSheetShowing = false;
    _weatherController.cancelAudioLoading();
    // Also cancel at bloc level
    if (bloc is MapTaskBloc) {
      (bloc as MapTaskBloc).cancelWeatherDetailLoading();
    }
  }

  void _handleLoadAudioLinkState(LoadAudioLinkState state) => _weatherController.updateExternalAudioLink(state.audioLink.isNotEmpty ? state.audioLink : null);

  void _handleLoadingAudioState(LoadingAudioState state) {
    debugPrint('MapTaskPage: _handleLoadingAudioState called with value = ${state.value}');
    _weatherController.handleLoadingStateChange(state.value);
  }

  void _handleAudioState(PlayAudioState state) => _isPlay.value = state.value;

  void _handleErrorState(ShowErrorState state) => UtilUI.showCustomDialog(context, state.resp);

  void _handleWeatherMapState(GetWeatherMapState state) {
    if (isCheckFeatureGeoData(state.data)) addWeatherGeoMap(state.data);
  }

  void _handlePetDetailState(GetPetMapDetailState state) {
    dataDialog = state.data;
    MapDialogUtils.showMapBottomSheet(
            context, topBodyPetWidget(dataDialog), tabMenuPetBodyWidget(context, dataDialog), tabBarPetWidget(context, _tabBodyController, dataDialog, imageUser, _expandedDescription),
            height: 0.60.sh)
        .then((value) {
      _expandedDescription.value = false;
    });
  }

  void _handlePetMapByProvinceState(GetPetMapByProvinceState state) {
    if (_shouldCancelProvinceLoading || _selectMenu != MapMenuEnum.pet) {
      return;
    }

    if (state.data.data.isNotEmpty) {
      addPetGeoMapByProvince(state.data, state.provinceId);
    }
    _loadedProvinces.add(state.provinceId);
    if (_loadedProvinces.length == state.totalProvinces) {
      _isLoadingProvinces = false;
      if (isDeepLink) handleDeepLinkMapModel();
    }
  }

  void _handleLoadProvincesState(LoadProvincesState state) {
    _provinces = state.data;
    _provincesLoaded = true;
    if (_selectMenu == MapMenuEnum.pet && !_shouldCancelProvinceLoading) {
      String currentFilterHash = _createFilterHash();
      if (_lastFilterHash != currentFilterHash) {
        _loadedProvinces.clear();
        _lastFilterHash = currentFilterHash;
      }
      String? fromDate = _fromDate != null ? MapUtils.datetimeToFormat2(_fromDate) : null;
      String? toDate = _toDate != null ? MapUtils.datetimeToFormat2(_toDate) : null;
      List<String>? diagnosticIds = _currentKinds.isNotEmpty ? _currentKinds : null;

      bloc?.add(GetPetMapByProvincesEvent(MapConstants.sourceIdPetMap, MapConstants.keyLayerPetMap, _provinces, fromDate: fromDate, toDate: toDate, diagnosticIds: diagnosticIds));
    }
  }

  //================[CLEANUP METHODS]================//

  void _clearLists() {
    _treeNutritionalList.clear();
    _treeTypeList.clear();
    _colorTreeValueList.clear();
    _currentKinds.clear();
  }

  void _disposeAudio() => MapAudioUtil.dispose();

  void _showModelBottomSheet(MapDataModel data) {
    if (!isShowMapDemoBottomSheet) {
      dataDialog = data;
      isShowMapDemoBottomSheet = true;
      _dataMap.value = MapDataModel(has_commented: dataDialog.has_commented, old_rate: dataDialog.old_rate, old_comment: dataDialog.old_comment);
      MapDialogUtils.showMapBottomSheet(context, topBodyModelWidget(dataDialog, _selectTabMenuModel), tabMenuBodyWidget(context, dataDialog), tabBarBodyWidget(context, dataDialog), height: 0.60.sh)
          .then((value) {
        isShowMapDemoBottomSheet = false;
        _expandedDescription.value = false;
        _expandedTime.value = false;
      });
    }
  }

  bool isCheckFeatureGeoData(MapGeoJsonModel data) {
    final features = data.data["data"]["features"];
    return features != null && features.isNotEmpty;
  }

  void addWeatherGeoMap(MapGeoJsonModel item) {
    clusterSource.addWeatherClusterMap(mapController: mapController, dataMap: item.data['data'], sourceId: MapConstants.sourceIdWeatherMap, keyChartName: MapConstants.keyLayerWeatherMap);
  }

  void showNutritionBottomSheet(MapGeoJsonModel data, LatLng point) {
    if (_treeNutritionType == null) return;
    bool needsTreeSelection = _needsTreeSelection(_treeNutritionType!.value);
    if (needsTreeSelection && (_treeType == null || _treeType?.value.isEmpty == true)) return;
    bloc?.add(GetLocationEvent(point));
    if (!isShowMapDemoBottomSheet) {
      final isRecommend = isShowLevelTree();
      _tabBodyController = TabController(length: isRecommend ? 2 : 1, vsync: this);
      if (_selectMenu == MapMenuEnum.nutrition) {
        MapDialogUtils.showWeatherInfoBottomSheet(context, addressNutritionWidget(_addressMap, _treeType),
                tabBarNutritionWidget(context, _addressMap, _tabBodyController, _treeType, _treeNutritionType, data, point, isRecommend: isRecommend))
            .then((value) {
          isShowMapDemoBottomSheet = false;
        });
      }
    }
    setPositionMapClick(point);
  }

  Future<void> _onMapCreated(TrackAsiaMapController initialMap) async {
    mapController = initialMap;
    _weatherController.setMapController(mapController);
    if (mapController?.cameraPosition != null) {
      final cameraPosition = mapController!.cameraPosition!;
      _weatherController.updateCameraFromWebView(
        cameraPosition.zoom,
        cameraPosition.target.longitude,
        cameraPosition.target.latitude,
      );
    }

    mapController?.requestMyLocationLatLng();
    _initFeatureTapped();
    _markerListerner();
    if (deepLink != null) _handleDeepLinkMapCreation();
  }

  void _handleDeepLinkMapCreation() {
    switch (deepLink!.menu) {
      case MapMenuEnum.model:
        _handleModelDeepLink();
        break;
      case MapMenuEnum.pet:
        _fetchPetMapWithProvinces();
        break;
      default:
    }
  }

  void _handleModelDeepLink() {
    switch (deepLink!.tab) {
      case MapModelEnum.demonstration:
        _selectTabMenuModel = MapModelEnum.demonstration;
        _tabMenuController.animateTo(0);
        bloc?.add(ChangeIndexTabEvent());
        bloc?.add(GetDemonstrationParadigmsEvent(MapConstants.sourceIdDemonMap, MapConstants.keyLayerDemonMap));
        break;
      case MapModelEnum.store:
        _selectTabMenuModel = MapModelEnum.store;
        _tabMenuController.animateTo(1);
        bloc?.add(ChangeIndexTabEvent());
        bloc?.add(GetListAgenciesEvent('shop', MapConstants.sourceIdStoreMap, MapConstants.keyLayerStoreMap));
        break;
      case MapModelEnum.storage:
        _selectTabMenuModel = MapModelEnum.storage;
        _tabMenuController.animateTo(2);
        bloc?.add(ChangeIndexTabEvent());
        bloc?.add(GetListAgenciesEvent('warehouse', MapConstants.sourceIdStorageMap, MapConstants.keyLayerStorageMap));
        break;
    }
  }

  void handleDeepLinkMapModel() {
    const double zoom = 10.0;
    final lat = double.parse(deepLink!.lat);
    final lng = double.parse(deepLink!.lng);
    final id = int.parse(deepLink!.id);

    mapController?.animateCamera(CameraUpdate.newLatLngZoom(LatLng(lat, lng), zoom));

    if (deepLink!.menu == MapMenuEnum.pet) {
      bloc?.add(GetPetMapDetailEvent(id, deepLink!.classable_type));
    } else {
      switch (_selectTabMenuModel) {
        case MapModelEnum.demonstration:
          bloc?.add(GetDetailDemonstrationParadigmEvent(id));
          break;
        case MapModelEnum.store:
        case MapModelEnum.storage:
          bloc?.add(GetDetailAgenciesEvent(id));
          break;
      }
    }
  }

  void _initFeatureTapped() {
    mapController?.onFeatureTapped.add((id, point, coordinates, _) async {
      switch (_selectMenu) {
        case MapMenuEnum.pet:
          await _handlePetFeatureTap(point, coordinates);
          break;
        case MapMenuEnum.nutrition:
          if (_treeNutritionType == null) return;
          bool needsTreeSelection = _needsTreeSelection(_treeNutritionType!.value);
          if (needsTreeSelection && (_treeType == null || _treeType?.value.isEmpty == true)) {
            MapDialogUtils.showTreeSelectionRequiredMessage(context, () => _showTreeSelectionPopup.value = true);
            return;
          }
          bloc?.add(GetNutritionMapEvent(_treeNutritionType!.value, _treeType?.value ?? "", coordinates));
          break;
        case MapMenuEnum.weather:
          await _handleWeatherFeatureTap(point);
          break;
        case MapMenuEnum.model:
          await _handleModelFeatureTap(point);
          break;
        default:
      }
    });
  }

  bool _needsTreeSelection(String nutritionType) {
    nutritionType = nutritionType.toLowerCase();
    return ["n", "p", "k", "npk", "ph", "ec", "cec", "om"].contains(nutritionType);
  }

  Future<void> _handlePetFeatureTap(Point<double> point, LatLng coordinates) async {
    var features = await getFeatureData(point, ["${MapConstants.keyLayerPetMap}_pet_circle_children"]);
    if (features?.isNotEmpty ?? false) {
      showMapPetBottomSheet(features!, point, coordinates);
    } else if (indexPetZoom <= 2) {
      mapController!.animateCamera(CameraUpdate.newLatLngZoom(coordinates, indexPetZoom * 6));
      indexPetZoom += 1;
    }
  }

  Future<void> _handleWeatherFeatureTap(Point<double> point) async {
    final coordinates = await mapController!.toLatLng(point);

    // Cancel any ongoing weather detail operations before starting new one
    if (bloc is MapTaskBloc) {
      (bloc as MapTaskBloc).cancelWeatherDetailLoading();
    }
    _resetWeatherDetailState();

    bloc?.add(GetLocationEvent(coordinates));
    await _weatherController.handleWeatherFeatureTap(point);
    await setPositionMapClick(coordinates);
  }

  Future<void> _handleModelFeatureTap(Point<double> point) async {
    final layerMap = {
      MapModelEnum.demonstration: "${MapConstants.keyLayerDemonMap}_image",
      MapModelEnum.store: "${MapConstants.keyLayerStoreMap}_image",
      MapModelEnum.storage: "${MapConstants.keyLayerStorageMap}_image",
    };

    final layerKey = layerMap[_selectTabMenuModel];
    if (layerKey == null) return;

    var features = await getFeatureData(point, [layerKey]);
    if (features?.isNotEmpty ?? false) {
      final data = MapUtils.handleFeaturesData(features!, isPoint: true);
      if (data != null) {
        if (_selectTabMenuModel == MapModelEnum.demonstration) {
          bloc?.add(GetDetailDemonstrationParadigmEvent(data.id));
        } else {
          bloc?.add(GetDetailAgenciesEvent(data.id));
        }
      }
    }
  }

  void _markerListerner() {
    mapController?.onSymbolTapped.add((argument) {
      // Handle marker tap if needed
    });
  }

  Widget tabMenuBodyWidget(BuildContext root, MapDataModel data) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 20.sp),
      height: 120.h,
      child: ListView.builder(
        shrinkWrap: true,
        scrollDirection: Axis.horizontal,
        itemCount: 3,
        itemBuilder: (context, index) => GestureDetector(
            onTap: () => _handleMenuAction(index, root, data),
            child: Padding(padding: const EdgeInsets.symmetric(horizontal: 10), child: Image.asset(MapConstants.menuListModel()[index].image, width: ((1.sw / 3) - 20.sp)))),
      ),
    );
  }

  void _handleMenuAction(int index, BuildContext root, MapDataModel data) {
    switch (index) {
      case 0:
        MapUtils.openMapApp(const LatLng(10.9514, 107.0855), LatLng(data.lat, data.lng));
        break;
      case 1:
        UtilUI.shareTo(root, data.deep_link, 'Option Share Dialog -> Choose "Share"', 'map task', hasDomain: true);
        break;
      case 2:
        if (!Constants().isLogin) {
          UtilUI.showCustomDialog(context, MultiLanguage.get('msg_login_create_account'));
        } else {
          UtilUI.goToNextPage(root, MapModelUpdatePage(data.classable_id, data.classable_type, title: data.name), funCallback: _reloadComment);
        }
        break;
    }
  }

  void _reloadComment(dynamic value) {
    if (value != null && value is Map) {
      _dataMap.value = MapDataModel(has_commented: true, old_rate: value['rate'], old_comment: value['comment']);
    }
  }

  @override
  Widget build(BuildContext context, {Color color = Colors.white}) {
    super.build(context, color: color);
    return Scaffold(
      appBar: _buildAppBarWidget(),
      body: Stack(children: [
        _buildTrackasiaMapWidget(),
        Loading(bloc),
        if (_isLoadingCurrentLocation) MapDialogUtils.buildCurrentLocationLoading(),
        if (_selectMenu == MapMenuEnum.nutrition && isShowMenuLayerOption()) _buildNutritionLayerWidget(),
        if (_selectMenu == MapMenuEnum.pet) _buildPetLayerWidget(),
        if (_selectMenu == MapMenuEnum.weather) _buildWeatherPanelWidget(),
        if (_selectMenu == MapMenuEnum.model) _buildModelLayerWidget(),
        popupMapMenuWidget(),
      ]),
    );
  }

  Widget _buildTrackasiaMapWidget() {
    return TrackAsiaMap(
      minMaxZoomPreference: const MinMaxZoomPreference(1, 24),
      styleString: constants.styleMap,
      zoomGesturesEnabled: true,
      compassEnabled: false,
      myLocationEnabled: Platform.isIOS ? true : false,
      initialCameraPosition: CameraPosition(target: _initialCameraPosition, zoom: _initialZoom),
      onMapCreated: _onMapCreated,
      onStyleLoadedCallback: () async {
        if (mapController != null) {
          await TrackAsiaMapSource.loadIcons(mapController!);
          await TrackAsiaMapSource.loadDonutChartIcons(mapController!);
        }
      },
      onMapClick: _onMapClick,
    );
  }

  Widget _buildNutritionLayerWidget() {
    return LayerTreeValueWidget(
      colorTreeValueList: _colorTreeValueList,
      treeNutritionType: _treeNutritionType,
      treeType: _treeType,
      onTreeTypeSelected: _setTreeType,
      onNutritionTypeSelected: _setTreeNutritionType,
      showTreeSelectionPopup: _showTreeSelectionPopup,
      isShowLevelTree: isShowLevelTree,
    );
  }

  Widget _buildPetLayerWidget() {
    return Align(alignment: Alignment.topLeft, child: DiseaseColorLegendWidget(key: _diseaseLegendKey));
  }

  Widget _buildWeatherPanelWidget() {
    return AnimatedBuilder(
      animation: _weatherController,
      builder: (context, child) {
        debugPrint(
            "MapTaskPage: Building WeatherPanelWidget - selectedLayer: ${_weatherController.selectedWeatherLayer}, showPanel: ${_weatherController.showWeatherPanel}, showWindWebView: ${_weatherController.showWindWebView}");
        return WeatherPanelWidget(
          selectedWeatherLayer: _weatherController.selectedWeatherLayer,
          showWeatherPanel: _weatherController.showWeatherPanel,
          isLoading: _weatherController.isWeatherLoading,
          onWeatherLayerChanged: _weatherController.updateWeatherLayer,
          onTogglePanel: _weatherController.toggleWeatherPanel,
          onRefreshWeather: () {},
          isTimelineActive: _weatherController.isTimelineActive,
          isPlaying: _weatherController.isPlaying,
          currentTimeIndex: _weatherController.currentTimeIndex,
          timelineHours: _weatherController.timelineHours,
          onTogglePlayPause: _weatherController.togglePlayPause,
          onTimelineSeek: _weatherController.onTimelineSeek,
          onResetTimeline: _weatherController.resetTimeline,
          onToggleTimeline: () {
            _weatherController.toggleTimelineVisibility();
            _weatherController.toggleVerticalScaleVisibility();
          },
          currentWeatherData: _weatherController.currentWeatherData,
          currentWeatherPoint: _weatherController.currentWeatherPoint,
          weatherController: _weatherController,
          slideAnimation: _weatherController.weatherSlideAnimation ?? const AlwaysStoppedAnimation(Offset.zero),
        );
      },
    );
  }

  Widget _buildModelLayerWidget() {
    return ModelDropdownWidget(
      selectedModelLayer: _selectTabMenuModel,
      onModelLayerChanged: (MapModelEnum newLayer) {
        setState(() {
          removeModelSource(isRemoveAll: false);
          _selectTabMenuModel = newLayer;
        });
        bloc?.add(ChangeIndexTabEvent());
        if (_selectTabMenuModel == MapModelEnum.demonstration) {
          bloc?.add(GetDemonstrationParadigmsEvent(MapConstants.sourceIdDemonMap, MapConstants.keyLayerDemonMap));
        } else if (_selectTabMenuModel == MapModelEnum.store) {
          bloc?.add(GetListAgenciesEvent('shop', MapConstants.sourceIdStoreMap, MapConstants.keyLayerStoreMap));
        } else if (_selectTabMenuModel == MapModelEnum.storage) {
          bloc?.add(GetListAgenciesEvent('warehouse', MapConstants.sourceIdStorageMap, MapConstants.keyLayerStorageMap));
        }
      },
    );
  }

  AppBar _buildAppBarWidget() {
    return AppBar(
      leading: IconButton(icon: const Icon(Icons.arrow_back_ios), onPressed: () => Navigator.of(context).pop()),
      titleSpacing: 0,
      title: UtilUI.createLabel(MapConstants.nameOptionMap(_selectMenu)),
      centerTitle: true,
    );
  }

  Widget popupMapMenuWidget() {
    return PopupMapMenuPanel(
      selectMenu: _selectMenu,
      isAtCurrentLocation: _isAtCurrentLocation,
      isShowMenuModelOption: isShowMenuModelOption,
      selectOptionMap: selectOptionMap,
      selectPositionMap: selectPositionMap,
      selectMenuMap: selectMenuMap,
      buildWeatherControlButtons: _selectMenu == MapMenuEnum.weather
          ? () => buildWeatherControlButtons(
                _weatherController,
                _toggleTimelineAndScale,
                _toggleWeatherIconsVisibility,
              )
          : null,
      weatherController: _weatherController,
    );
  }

  //================[MENU & NAVIGATION ACTIONS]================//

  void selectOptionMap() {
    switch (_selectMenu) {
      case MapMenuEnum.pet:
        _showPetMapFilter(context);
        break;
      case MapMenuEnum.nutrition:
        _showMapFarmingOption();
        break;
      case MapMenuEnum.weather:
        break;
      case MapMenuEnum.model:
        break;
      default:
        break;
    }
  }

  void selectPositionMap() {
    if (_isAtCurrentLocation) {
      _navigateToInitialPosition();
    } else {
      _navigateToCurrentLocation();
    }
  }

  void _navigateToInitialPosition() {
    mapController?.animateCamera(CameraUpdate.newCameraPosition(CameraPosition(target: _initialCameraPosition, zoom: _initialZoom)));
    setState(() => _isAtCurrentLocation = false);
  }

  void _navigateToCurrentLocation() async {
    setState(() => _isLoadingCurrentLocation = true);
    try {
      final position = await MapUtils.getCurrentPositionMap();
      final currentLatLng = LatLng(position.latitude, position.longitude);
      await mapController?.animateCamera(CameraUpdate.newCameraPosition(CameraPosition(target: currentLatLng, zoom: 15)));
      setPositionMapClick(currentLatLng);
      setState(() {
        _isAtCurrentLocation = true;
        _isLoadingCurrentLocation = false;
      });
    } catch (e) {
      debugPrint('Error getting current location: $e');
      setState(() => _isLoadingCurrentLocation = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Không thể lấy vị trí hiện tại: $e'), backgroundColor: Colors.red));
      }
    }
  }

  Future<void> selectMenuMap(int value) async {
    try {
      // Update menu state immediately for UI responsiveness
      switch (value) {
        case 1:
          _selectMenu = MapMenuEnum.pet;
          break;
        case 2:
          _selectMenu = MapMenuEnum.nutrition;
          break;
        case 3:
          _selectMenu = MapMenuEnum.weather;
          break;
        case 4:
          _selectMenu = MapMenuEnum.model;
          _selectTabMenuModel = MapModelEnum.demonstration;
          break;
      }

      // Trigger immediate UI update
      setState(() {});

      // Then perform async operations
      await _setDefault();
      await Future.delayed(const Duration(milliseconds: 150));

      // Load data for selected menu
      switch (value) {
        case 1:
          _fetchPetMapWithProvinces();
          break;
        case 2:
          addSourceNutrition();
          _showMapFarmingOption();
          break;
        case 3:
          // _weatherController.initializeWeatherMap(() {});
          _fetchWeatherMap();
          break;
        case 4:
          _fetchDemonstrationParadigmMap();
          break;
      }
    } catch (e) {
      logDebug("Error in selectMenuMap: $e");
    }
  }

  void selectMenuMapModel(value) {
    switch (value) {
      case MapModelEnum.demonstration:
        _fetchDemonstrationParadigmMap();
        break;
      case MapModelEnum.store:
        _fetchStoreMap();
        break;
      case MapModelEnum.storage:
        _fetchStogradeMap();
        break;
      default:
        break;
    }
  }

  void _showMapFarmingOption() {
    MapDialogUtils.showFarmingBottomSheet(
      context,
      title: 'Chọn loại cây',
      ValueListenableBuilder<List<MenuItemMap>>(
          valueListenable: _treeNutritionNotifier,
          builder: (context, treeNutritionList, child) {
            if (treeNutritionList.isEmpty) return Container();
            return LayerNutritionTreeMenuWidget(
              treeNutritionList: treeNutritionList,
              selectedNutritionType: _treeNutritionType,
              onNutritionTypeSelected: (item) {
                _setTreeNutritionType(item);
                if (_needsTreeSelection(item.value) && _treeType == null) {
                  removeNutritionSource();
                  Future.delayed(const Duration(milliseconds: 300), () {
                    _showTreeSelectionPopup.value = true;
                  });
                }
              },
              treeTypeList: _treeTypeList,
              selectedTreeType: _treeType,
              onTreeTypeSelected: (item) {
                Navigator.of(context).pop();
                _setTreeType(item);
              },
            );
          }),
    );
  }

  Widget tabBarBodyWidget(BuildContext root, MapDataModel data) {
    return Container(
      color: Colors.transparent,
      child: Column(
        children: [
          TabBar(
              padding: EdgeInsets.zero,
              isScrollable: true,
              controller: _tabBodyController,
              labelColor: Colors.blue,
              indicatorColor: Colors.blue,
              unselectedLabelColor: Colors.black,
              indicatorWeight: 1,
              labelPadding: EdgeInsets.symmetric(horizontal: 200.sp),
              indicatorSize: TabBarIndicatorSize.tab,
              indicatorPadding: EdgeInsets.zero,
              unselectedLabelStyle: const TextStyle(color: Colors.black87),
              indicator: UnderlineTabIndicator(borderSide: const BorderSide(width: 4.0, color: Colors.blue), insets: EdgeInsets.symmetric(horizontal: 40.sp)),
              tabs: _tabBodyController.length == 1 ? const [Tab(text: "Tổng quan")] : const [Tab(text: "Tổng quan"), Tab(text: "Hình ảnh")]),
          Expanded(
            child: TabBarView(
                controller: _tabBodyController,
                children: _tabBodyController.length == 1
                    ? [tabBodyOverviewWidget(root, _selectTabMenuModel, data, _expandedTime, _expandedDescription, imageUser, _reloadComment, _dataMap)]
                    : [tabBodyOverviewWidget(root, _selectTabMenuModel, data, _expandedTime, _expandedDescription, imageUser, _reloadComment, _dataMap), tabBodyImageWidget(data)]),
          ),
        ],
      ),
    );
  }

  void _onMapClick(Point<double> point, LatLng coordinates) async {
    if (_selectMenu == MapMenuEnum.pet) {
      var features = await getFeatureData(point, ["${MapConstants.keyLayerPetMap}_pet_circle_children"]);
      if (features?.isNotEmpty ?? false) {
        if (Platform.isIOS) showMapPetBottomSheet(features!, point, coordinates);
      }
      return;
    }

    if (_selectMenu == MapMenuEnum.weather) {
      // Cancel any ongoing weather operations before starting new one
      if (bloc is MapTaskBloc) {
        (bloc as MapTaskBloc).cancelWeatherDetailLoading();
      }
      _resetWeatherDetailState();

      bloc?.add(GetLocationEvent(coordinates));
      await _weatherController.handleWeatherFeatureTap(point);
      await setPositionMapClick(coordinates);
      return;
    }

    if (_selectMenu == MapMenuEnum.nutrition) {
      if (_treeNutritionType == null) return;
      bool needsTreeSelection = _needsTreeSelection(_treeNutritionType!.value);
      if (needsTreeSelection && (_treeType == null || _treeType?.value.isEmpty == true)) {
        MapDialogUtils.showTreeSelectionRequiredMessage(context, () => _showTreeSelectionPopup.value = true);
        return;
      }
      bloc!.add(GetNutritionMapEvent(_treeNutritionType!.value, _treeType?.value ?? "", coordinates));
      return;
    }
    _handleModelFeatureTap(point);
  }

  Future<List?> getFeatureData(Point<double> point, List<String> keys) async {
    final allLayers = await mapController?.getLayerIds() ?? [];
    List<String> matchingLayers = [];
    for (var key in keys) {
      for (var layerId in allLayers) {
        if (layerId is String && layerId.startsWith(key)) {
          matchingLayers.add(layerId);
        }
      }
    }
    if (matchingLayers.isNotEmpty) return await mapController?.queryRenderedFeatures(point, matchingLayers, null);
    return await mapController?.queryRenderedFeatures(point, keys, null);
  }

  void showMapPetBottomSheet(List<dynamic> features, Point<double> point, LatLng coordinates) async {
    try {
      final data = MapUtils.handleFeaturesData(features);
      if (data != null && data.classable_type.isNotEmpty && data.classable_id != 0) {
        bloc?.add(GetPetMapDetailEvent(data.classable_id, data.classable_type));
      }
    } catch (e) {
      logDebug(e.toString());
    }
  }

  Future<void> setPositionMapClick(LatLng coordinates) async {
    if (_selectMenu == MapMenuEnum.nutrition) {
      tappedNutritionPosition = coordinates;
      if (tappedNutritionSymbol != null) mapController!.removeSymbol(tappedNutritionSymbol!);
      final symbol = await mapController!.addSymbol(SymbolOptions(geometry: coordinates, iconImage: "assets/images/v9/map/ic_map_location.png", iconSize: 0.6));
      setState(() => tappedNutritionSymbol = symbol);
    } else if (_selectMenu == MapMenuEnum.weather) {
      if (tappedWeatherSymbol != null) mapController!.removeSymbol(tappedWeatherSymbol!);
      final symbol = await mapController!.addSymbol(SymbolOptions(geometry: coordinates, iconImage: "assets/images/v9/map/ic_map_location.png", iconSize: 0.6));
      setState(() => tappedWeatherSymbol = symbol);
    } else {
      if (tappedCurrentLocationSymbol != null) mapController!.removeSymbol(tappedCurrentLocationSymbol!);
      final symbol = await mapController!.addSymbol(SymbolOptions(geometry: coordinates, iconImage: "assets/images/v9/map/ic_map_location.png", iconSize: 0.6));
      setState(() => tappedCurrentLocationSymbol = symbol);
    }
  }

  void _setTreeNutritionType(value, {bool isUpdate = false}) {
    if (value != null && value.id != _treeNutritionType?.id) {
      _treeNutritionType = value;
      _treeTypeList = MapUtils.menuTreeTypeModel(value.value);
      _treeTypeNotifier.value = List.from(_treeTypeList);
      bool needsTree = _needsTreeSelection(value.value);
      if (needsTree && _treeType == null) {
        removeNutritionSource();
        _clearNutritionMarker();
        if (isShowMapDemoBottomSheet) {
          Navigator.of(context).pop();
          isShowMapDemoBottomSheet = false;
        }
      }
      if (!isShowLevelTree() || isUpdate) {
        loadTreeType();
      }
      _treeNutritionNotifier.notifyListeners();
      _treeTypeNotifier.notifyListeners();
    }
    setState(() {});
  }

  void _clearNutritionMarker() {
    if (tappedNutritionSymbol != null) {
      mapController?.removeSymbol(tappedNutritionSymbol!);
      tappedNutritionSymbol = null;
      tappedNutritionPosition = null;
    }
  }

  void _clearWeatherMarker() {
    if (tappedWeatherSymbol != null) {
      mapController?.removeSymbol(tappedWeatherSymbol!);
      tappedWeatherSymbol = null;
    }
  }

  void _setTreeType(value) {
    if (value != null) {
      _treeType = value;
      if (_treeNutritionType != null && isShowLevelTree()) {
        _colorTreeValueList = MapUtils.menuLayerColor(_treeNutritionType!.value, _treeType?.value);
        bloc!.add(GetTreeParamsEvent(_treeNutritionType!.value, value.value));
      }
      _treeTypeNotifier.notifyListeners();
    }
    setState(() {});
  }

  //================[DATA MANAGEMENT & CLEANUP]================//

  void loadTreeType() {
    _colorTreeValueList = MapUtils.menuLayerColor(_treeNutritionType!.value, _treeType?.value);
    bloc!.add(GetTreeParamsEvent(_treeNutritionType?.value ?? "", _treeType?.value ?? ""));
  }

  Future<void> _setDefault() async {
    try {
      _cancelAllOperations();
      _clearAllMarkers();
      _weatherController.resetWeatherState();
      await _removeAllSources();
    } catch (e) {
      logDebug("Error in _setDefault: $e");
    }
  }

  void _cancelAllOperations() {
    _shouldCancelProvinceLoading = true;
    _isLoadingProvinces = false;
    // Cancel weather detail operations
    _resetWeatherDetailState();
    if (bloc is MapTaskBloc) {
      (bloc as MapTaskBloc).cancelCurrentRequest();
      (bloc as MapTaskBloc).cancelProvinceLoading();
      (bloc as MapTaskBloc).cancelWeatherDetailLoading();
    }
    mapController?.invalidateAmbientCache();
  }

  void _clearAllMarkers() {
    _setSymbolsClear();
    _clearNutritionMarker();
    _clearWeatherMarker();
    _setTreeClear();
  }

  Future<void> _removeAllSources() async {
    try {
      await removeWeatherSource();
      await Future.delayed(const Duration(milliseconds: 100));
      await removeNutritionSource();
      await Future.delayed(const Duration(milliseconds: 100));
      await removeModelSource(isRemoveAll: true);
      await Future.delayed(const Duration(milliseconds: 100));
      await removePetSource();
      await Future.delayed(const Duration(milliseconds: 50));
      mapController?.clearSymbols();
    } catch (e) {
      logDebug("Error in _removeAllSources: $e");
    }
  }

  Future<void> removeWeatherSource() async {
    if (mapController != null) {
      TrackAsiaMapSource.removeSourceWeatherMap(mapController, MapConstants.sourceIdWeatherMap, MapConstants.keyLayerWeatherMap);
    }
  }

  /// Removes weather map layers and sources
  // Future<void> removeWeatherSource() async {
  //   try {
  //     _weatherController.resetWeatherState();
  //     if (mapController != null) {
  //       await TrackAsiaWeatherMapSource.removeAllLayerWeatherMap(mapController);
  //       await Future.delayed(const Duration(milliseconds: 50));
  //       await mapController?.clearSymbols();
  //     }
  //   } catch (e) {
  //     logDebug("Error in removeWeatherSource: $e");
  //   }
  // }

  Future<void> removePetSource() async {
    try {
      cancelProvinceLoading();
      _closeLegendIfVisible();
      if (mapController == null) return;
      await TrackAsiaMapSource.removePetLayerMap(mapController!, MapConstants.sourceIdPetMap, MapConstants.keyLayerPetMap);
    } catch (e) {
      logDebug("Error in removePetSource: $e");
    }
  }

  void cancelProvinceLoading() {
    _shouldCancelProvinceLoading = true;
    _isLoadingProvinces = false;
    if (bloc is MapTaskBloc) {
      final mapBloc = bloc as MapTaskBloc;
      mapBloc.cancelCurrentRequest();
      mapBloc.cancelProvinceLoading();
    }
  }

  void _setSymbolsClear() => mapController?.clearSymbols();

  void _setTreeClear() {
    _treeNutritionType = null;
    _treeType = null;
    _treeTypeList.clear();
  }

  void fitBoundsToCoordinates(List<dynamic> bbox) {
    if (bbox.isNotEmpty) {
      final LatLngBounds bounds = LatLngBounds(
        southwest: LatLng(bbox[1], bbox[0]),
        northeast: LatLng(bbox[3], bbox[2]),
      );
      mapController?.animateCamera(CameraUpdate.newLatLngBounds(bounds, left: 50.0, top: 50.0, right: 50.0, bottom: 50.0));
    }
  }

  void cleanTreeValue() => tagTreeValueController.clearTags();

  String petParams() {
    String params = '';

    if (_fromDate != null) params = 'from_date=${MapUtils.datetimeToFormat2(_fromDate)}&';
    if (_toDate != null) params += 'to_date=${MapUtils.datetimeToFormat2(_toDate)}&';
    if (_currentProvince.id != null && _currentProvince.id.isNotEmpty && _currentProvince.id != "-1") {
      params += 'province_id=${_currentProvince.id}&';
    }
    if (_currentKinds.isNotEmpty) {
      params += 'diagnostic_ids=[$_currentKinds]';
    } else if (params.isNotEmpty) {
      params = params.substring(0, params.length - 1);
    }

    return params;
  }

  void _prefetchPetMapData() {
    String params = petParams();
    bool hasSpecificProvince = _currentProvince.id.isNotEmpty && _currentProvince.id != "-1";
    removePetSource();
    if (params.isNotEmpty && hasSpecificProvince) {
      bloc?.add(GetPetMapEvent(MapConstants.sourceIdPetMap, params));
    } else {
      if (_provincesLoaded && _provinces.isNotEmpty) {
        _shouldCancelProvinceLoading = false;
        _isLoadingProvinces = true;
        _loadedProvinces.clear();
        String? fromDate = _fromDate != null ? MapUtils.datetimeToFormat2(_fromDate) : null;
        String? toDate = _toDate != null ? MapUtils.datetimeToFormat2(_toDate) : null;
        List<String>? diagnosticIds = _currentKinds.isNotEmpty ? _currentKinds : null;
        bloc?.add(GetPetMapByProvincesEvent(MapConstants.sourceIdPetMap, MapConstants.keyLayerPetMap, _provinces, fromDate: fromDate, toDate: toDate, diagnosticIds: diagnosticIds));
      } else {
        _loadProvinces();
      }
    }
  }

  void _fetchDemonstrationParadigmMap() => bloc?.add(GetDemonstrationParadigmsEvent(MapConstants.sourceIdDemonMap, MapConstants.keyLayerDemonMap));

  void _fetchStoreMap() => _fetchAgencyMap('shop', MapConstants.sourceIdStoreMap, MapConstants.keyLayerStoreMap);

  void _fetchStogradeMap() => _fetchAgencyMap('warehouse', MapConstants.sourceIdStorageMap, MapConstants.keyLayerStorageMap);

  void _fetchAgencyMap(String type, String sourceId, String layerId) => bloc?.add(GetListAgenciesEvent(type, sourceId, layerId));

  void _fetchWeatherMap() => bloc?.add(GetWeatherMapEvent(MapConstants.sourceIdWeatherMap));

  void addPetGeoMap(MapGeoJsonModel item) {
    if (item.data.isEmpty || mapController == null) return;
    clusterSource.addPetClusterMap(mapController: mapController, sourceId: MapConstants.sourceIdPetMap, dataMap: item.data, keyChartName: MapConstants.keyLayerPetMap);
  }

  void addPetGeoMapByProvince(MapGeoJsonModel item, String provinceId) {
    if (item.data.isEmpty || mapController == null || _shouldCancelProvinceLoading || _selectMenu != MapMenuEnum.pet) return;
    clusterSource.addPetClusterMap(
        context: context, mapController: mapController, dataMap: item.data, sourceId: MapConstants.sourceIdPetMap, keyChartName: MapConstants.keyLayerPetMap, provinceId: provinceId);
  }

  void addModelGeoMap(MapGeoJsonModel item, String image) =>
      clusterSource.addModelClusterMap(mapController: mapController, model: _selectTabMenuModel, dataMap: item.data['data'], sourceId: item.sourceId!, keyChartName: item.layerId!, image: image);

  void addSourceNutrition() {
    if (mapController != null) {
      TrackAsiaNutritionMapSource.addSourceNutritionMap(mapController, MapConstants.sourceIdNutritionMap);
    }
  }

  void setLayerNutrition(String nutritionType, String treeType) {
    bool needsTreeSelection = _needsTreeSelection(nutritionType);
    if (needsTreeSelection && (treeType.isEmpty || treeType == "")) {
      removeNutritionSource();
      return;
    }
    if (mapController != null) {
      TrackAsiaNutritionMapSource.setLayerNutritionMap(mapController, MapConstants.sourceIdNutritionMap, nutritionType, treeType);
    }
  }

  Future<void> removeNutritionSource() async {
    if (mapController == null) return;
    await TrackAsiaNutritionMapSource.removeAllLayerNutritionMap(mapController!);
  }

  Future<void> removeModelSource({bool isRemoveAll = true}) async {
    if (mapController == null) return;

    try {
      if (isRemoveAll) {
        await TrackAsiaMapSource.removeSourceModelMap(mapController!, MapConstants.sourceIdDemonMap, MapConstants.keyLayerDemonMap);
        await Future.delayed(const Duration(milliseconds: 50));
        await TrackAsiaMapSource.removeSourceModelMap(mapController!, MapConstants.sourceIdStoreMap, MapConstants.keyLayerStoreMap);
        await Future.delayed(const Duration(milliseconds: 50));
        await TrackAsiaMapSource.removeSourceModelMap(mapController!, MapConstants.sourceIdStorageMap, MapConstants.keyLayerStorageMap);
      } else {
        switch (_selectTabMenuModel) {
          case MapModelEnum.demonstration:
            await TrackAsiaMapSource.removeSourceModelMap(mapController!, MapConstants.sourceIdDemonMap, MapConstants.keyLayerDemonMap);
            break;
          case MapModelEnum.store:
            await TrackAsiaMapSource.removeSourceModelMap(mapController!, MapConstants.sourceIdStoreMap, MapConstants.keyLayerStoreMap);
            break;
          case MapModelEnum.storage:
            await TrackAsiaMapSource.removeSourceModelMap(mapController!, MapConstants.sourceIdStorageMap, MapConstants.keyLayerStorageMap);
            break;
        }
      }
    } catch (e) {
      logDebug("Error in removeModelSource: $e");
    }
  }

  void _showPetMapFilter(BuildContext context) async {
    if (!_provincesLoaded) {
      _loadProvinces();
      await Future.delayed(const Duration(milliseconds: 500));
    }

    final result = await Navigator.push(context, MaterialPageRoute(builder: (context) => MapPetFilterPage(_currentKinds, _currentProvince, _fromDate, _toDate)));

    if (result != null) {
      _currentKinds = result['petIds'] ?? [];
      _currentProvince = result['province'] ?? ItemModel();
      _fromDate = result['from_date'];
      _toDate = result['to_date'];
      _prefetchPetMapData();
    }
  }

  bool isShowMenuModelOption() => _selectMenu != MapMenuEnum.model && _selectMenu != MapMenuEnum.none && _selectMenu != MapMenuEnum.weather;

  bool isShowMenuLayerOption() => _treeNutritionType != null && _treeNutritionalList.isNotEmpty;

  bool isShowLevelTree() => _treeNutritionType != null;

  bool get isLoadingProvinces => _isLoadingProvinces;

  int get loadedProvincesCount => _loadedProvinces.length;
  int get totalProvincesCount => _provinces.length;

  void _closeLegendIfVisible() {
    final legendWidgetState = _diseaseLegendKey.currentState;
    if (legendWidgetState != null && legendWidgetState.isExpanded) {
      legendWidgetState.toggleExpanded();
    }
  }

  void _toggleTimelineAndScale() {
    _weatherController.toggleTimelineVisibility();
    _weatherController.toggleVerticalScaleVisibility();
    setState(() {});
  }

  void _toggleWeatherIconsVisibility() {
    _weatherController.toggleWeatherIconsVisibility();
    setState(() {});
  }
}

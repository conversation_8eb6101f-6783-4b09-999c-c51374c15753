import 'dart:collection';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:hainong/common/models/file_byte.dart';
import 'package:hainong/common/multi_language.dart';
import 'package:hainong/features/function/tool/map_task/map_constants.dart';
import 'package:hainong/features/function/tool/map_task/models/map_data_model.dart';
import 'package:hainong/features/function/tool/map_task/models/map_enum.dart';
import 'package:hainong/features/function/tool/map_task/models/map_item_menu_model.dart';
import 'package:hainong/features/function/tool/map_task/source/colors/map_nutrient_color.dart';
import 'package:hainong/features/shop/ui/import_ui_shop.dart';
import 'package:intl/intl.dart';
import 'package:trackasia_gl/trackasia_gl.dart';

//======================================DOCUMENT===========================================//

//======================================DOCUMENT===========================================//
class MapUtils {
  static IconData iconOptionMap(MapMenuEnum _selectMenu) {
    switch (_selectMenu) {
      case MapMenuEnum.pet:
        return Icons.filter_list_alt;
      case MapMenuEnum.nutrition:
        return Icons.layers;
      default:
        return Icons.filter_list_alt;
    }
  }

  static List<MenuItemMap> menuTreeTypeModel(String type) {
    switch (type) {
      case "npk":
      case "n":
      case "p":
      case "k":
      case "ph":
      case "ec":
      case "cec":
      case "om":
        return MapConstants.menuFruitItem();
      default:
        return [];
    }
  }

  static List<ColorItemMap> menuLayerColor(String type, typeTree) {
    switch (type) {
      case "npk":
        switch (typeTree) {
          case "lua":
          // return MapNutrientColor.luaNPKColor();
          case "sau_rieng":
          // return MapNutrientColor.sauriengNPKColor();
          case "buoi":
          // return MapNutrientColor.buoiNPKColor();
          case "xoai":
          // return MapNutrientColor.xoaiNPKColor();
          case "cam":
          // return MapNutrientColor.camNPKColor();
          case "nhan":
          // return MapNutrientColor.nhanNPKColor();
          case "thanh_long":
          // return MapNutrientColor.thanhLongNPKColor();
        }
        break;
      case "n":
        switch (typeTree) {
          case "lua":
            return MapNutrientColor.luaNColor();
          case "sau_rieng":
            return MapNutrientColor.sauriengNColor();
          case "buoi":
            return MapNutrientColor.buoiNColor();
          case "xoai":
            return MapNutrientColor.xoaiNColor();
          case "cam":
            return MapNutrientColor.camNColor();
          case "nhan":
            return MapNutrientColor.nhanNColor();
          case "thanh_long":
            return MapNutrientColor.thanhLongNColor();
        }
        break;
      case "p":
        switch (typeTree) {
          case "lua":
            return MapNutrientColor.luaPColor();
          case "sau_rieng":
            return MapNutrientColor.sauriengPColor();
          case "buoi":
            return MapNutrientColor.buoiPColor();
          case "xoai":
            return MapNutrientColor.xoaiPColor();
          case "cam":
            return MapNutrientColor.camPColor();
          case "nhan":
            return MapNutrientColor.nhanPColor();
          case "thanh_long":
            return MapNutrientColor.thanhLongPColor();
        }
        break;
      case "k":
        switch (typeTree) {
          case "lua":
            return MapNutrientColor.luaKColor();
          case "sau_rieng":
            return MapNutrientColor.sauriengKColor();
          case "buoi":
            return MapNutrientColor.buoiKColor();
          case "xoai":
            return MapNutrientColor.xoaiKColor();
          case "cam":
            return MapNutrientColor.camKColor();
          case "nhan":
            return MapNutrientColor.nhanKColor();
          case "thanh_long":
            return MapNutrientColor.thanhLongKColor();
        }
        break;
      case "ph":
        return MapNutrientColor.pHColor();
      case "ec":
        return MapNutrientColor.eCColor();
      case "cec":
        return MapNutrientColor.ceCColor();
      case "om":
        return MapNutrientColor.omCColor();
    }
    return [];
  }

  //======================================HANDLE===========================================//

  static MapDataModel? handleFeaturesData(List<dynamic> features, {bool isPoint = false}) {
    var feature = HashMap.from(features.first);
    try {
      if (isPoint) {
        final data = MapDataModel(
          id: convertFieldKeyToInt(feature, "id"),
          description: feature["properties"]["description"] ?? '',
          lat: feature["geometry"]["coordinates"][1] ?? 0.0,
          lng: feature["geometry"]["coordinates"][0] ?? 0.0,
        );
        return (data.id == -1 || data.id == 0) ? null : data;
      } else {
        final data = MapDataModel(
          id: convertFieldKeyToInt(feature, "id"),
          total_comment: convertFieldKeyToInt(feature, "total_comment"),
          total_user_comments: convertFieldKeyToInt(feature, "total_user_comments"),
          classable_id: convertFieldKeyToInt(feature, "classable_id"),
          description: feature["properties"]["description"] ?? '',
          address: feature["properties"]["address"] ?? "",
          suggest: feature["properties"]["suggest"] ?? "",
          name: feature["properties"]["category_name"] ?? "",
          updated_at: feature["properties"]["updated_at"] ?? "",
          percent: convertFieldKeyToDouble(feature, "percent"),
          rate: convertFieldKeyToDouble(feature, "rate"),
          images: feature["properties"]["image"] ?? [],
          category_image: feature["properties"]["category_image"] ?? '',
          lat: feature["geometry"]["coordinates"][1] ?? 0.0,
          lng: feature["geometry"]["coordinates"][0] ?? 0.0,
          classable_type: feature["properties"]["classable_type"] ?? "TrainingData",
          has_commented: feature["properties"]["has_commented"] ?? false,
          old_rate: convertFieldKeyToDouble(feature, "old_rate"),
          old_comment: feature["properties"]["old_comment"] ?? "",
        );
        return data;
      }
    } catch (e) {
      logDebug(e);
      return null;
    }
  }

  static int convertFieldKeyToInt(HashMap<dynamic, dynamic> feature, String key) {
    try {
      return feature["properties"][key] != null
          ? (feature["properties"][key] is String)
              ? int.parse((feature["properties"][key]))
              : (feature["properties"][key] is double)
                  ? (feature["properties"][key] as double).toInt()
                  : feature["properties"][key]
          : 0;
    } catch (e) {
      return 0;
    }
  }

  static double convertFieldKeyToDouble(HashMap<dynamic, dynamic> feature, String key) {
    try {
      return feature["properties"][key] != null
          ? (feature["properties"][key] is String)
              ? double.parse((feature["properties"][key]))
              : (feature["properties"][key] is int)
                  ? (feature["properties"][key] as int).toDouble()
                  : feature["properties"][key]
          : 0.0;
    } catch (e) {
      return 0.0;
    }
  }

  static double convertMapKeyToDouble(Map<String, dynamic> feature, String key) {
    try {
      return feature[key] != null
          ? (feature[key] is String)
              ? double.parse((feature[key]))
              : (feature[key] is int)
                  ? (feature[key] as int).toDouble()
                  : feature[key]
          : 0.0;
    } catch (e) {
      return 0.0;
    }
  }

  static Future<Position> getCurrentPositionMap() async {
    bool serviceEnabled;
    LocationPermission permission;
    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) return Future.error(MultiLanguage.get('msg_gps_disable'));
    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.deniedForever) return Future.error(MultiLanguage.get('msg_gps_deny_forever'));
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission != LocationPermission.whileInUse && permission != LocationPermission.always) return Future.error(MultiLanguage.get('msg_gps_denied'));
    }
    return await Geolocator.getCurrentPosition(desiredAccuracy: LocationAccuracy.high);
  }

  static Color hexToColor(String hexColor) {
    hexColor = hexColor.replaceAll("#", "");
    return Color(int.parse("0xFF$hexColor"));
  }

  static Future<void> openMapApp(LatLng _origin, LatLng _destination) async {
    final String googleUrl = 'https://www.google.com/maps/dir/?api=1&destination=${_destination.latitude},${_destination.longitude}&travelmode=driving';
    if (await canLaunchUrl(Uri.parse(googleUrl))) {
      await launchUrl(Uri.parse(googleUrl), mode: LaunchMode.externalApplication);
    } else {
      throw 'Unable open the map.';
    }
  }

  static int calculateTotalBytes(List<FileByte> images) {
    int total = 0;
    for (var fileByte in images) {
      total += fileByte.bytes.length;
    }
    return total;
  }

  static String datetimeToFormat(String dateString) {
    DateTime dateTime = DateTime.parse(dateString);
    String formattedDate = DateFormat('dd/MM/yyyy').format(dateTime);
    return formattedDate;
  }

  static String datetimeToFormat2(DateTime? date) {
    return date != null ? DateFormat('dd/MM/yyyy').format(date) : "";
  }

  static String getCurrentTimeFormatted() {
    final now = DateTime.now();
    final timeFormat = DateFormat('hh:mm a');
    final dateFormat = DateFormat('dd/MM/yyyy');
    return '${timeFormat.format(now)}; ${dateFormat.format(now)}';
  }

  static String getFormattedDateRange(DateTime? fromDate, DateTime? toDate) {
    if (fromDate == null || toDate == null) {
      return '';
    }
    final DateFormat formatter = DateFormat('dd.MM.yyyy');
    final String formattedFromDate = formatter.format(fromDate);
    final String formattedToDate = formatter.format(toDate);
    return '$formattedFromDate - $formattedToDate';
  }
  //======================================HANDLE===========================================//
}

void logDebug(dynamic message) {
  if (kDebugMode) {
    print("MAP=====$message");
  }
}

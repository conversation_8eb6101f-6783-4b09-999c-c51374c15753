import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hainong/common/util/util_ui.dart';

class MapDialogUtils {
  //================[BOTTOM SHEETS]================//

  static Future<void> showWeatherInfoBottomSheet(BuildContext context, Widget addressWidget, Widget contentWidget, {double? height, bool showCloseButton = true, String? title}) async {
    await showModalBottomSheet(
      isScrollControlled: true,
      context: context,
      backgroundColor: Colors.white,
      shape: _defaultSheetShape(),
      builder: (context) => StatefulBuilder(builder: (context, setState) => _buildSheetContent(height: height ?? 0.6.sh, children: [_buildDragLine(), addressWidget, Expanded(child: contentWidget)])),
    );
  }

  static Future<void> showFarmingBottomSheet(BuildContext context, Widget contentWidget, {Widget? secondaryWidget, double? height, String? title}) async {
    await showModalBottomSheet(
      isScrollControlled: true,
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildSheet(
        height: height ?? 0.84.sh,
        children: [
          if (title != null) _buildSheetHeader(title, context),
          Expanded(child: contentWidget),
          if (secondaryWidget != null) ...[Divider(color: Colors.grey.shade300, height: 33.h), secondaryWidget],
        ],
      ),
    );
  }

  static Future<void> showMapBottomSheet(BuildContext context, Widget topWidget, Widget menuWidget, Widget contentWidget, {double? height}) async {
    await showModalBottomSheet(
      isScrollControlled: true,
      context: context,
      backgroundColor: Colors.white,
      shape: _defaultSheetShape(),
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => _buildSheetContent(height: height ?? 0.84.sh, children: [_buildDragLine(), topWidget, menuWidget, Expanded(child: contentWidget)]),
      ),
    );
  }

  static Future<T?> showBottomSheet<T>(BuildContext context, Widget contentWidget, {double? height, bool showCloseButton = false}) async {
    return await showModalBottomSheet<T>(
      isScrollControlled: true,
      context: context,
      backgroundColor: Colors.white,
      shape: _defaultSheetShape(),
      builder: (context) => _buildSheetContent(height: height ?? 0.5.sh, children: [if (showCloseButton) _buildCloseButton(context), contentWidget]),
    );
  }

  //================[DIALOGS]================//

  static Future<void> showExitDialog(BuildContext context) async {
    final result = await UtilUI.showCustomDialog(context, 'Nhấn "Đồng Ý" để thoát khỏi bản đồ nông nghiệp',
        title: "Thông báo", alignMessageText: TextAlign.center, isActionCancel: true, isClose: true, lblOK: "Đồng ý", lblCancel: "Không");
    if (result == true && context.mounted) Navigator.pop(context);
  }

  static Future<T?> showCustomDialog<T>(BuildContext context, Widget contentWidget, {bool dismissible = false}) {
    return showGeneralDialog<T>(
      context: context,
      barrierDismissible: dismissible,
      barrierLabel: MaterialLocalizations.of(context).modalBarrierDismissLabel,
      barrierColor: Colors.black54,
      transitionDuration: const Duration(milliseconds: 200),
      pageBuilder: (context, animation1, animation2) => contentWidget,
    );
  }

  //================[PRIVATE HELPERS]================//

  static RoundedRectangleBorder _defaultSheetShape() {
    return RoundedRectangleBorder(borderRadius: BorderRadius.only(topLeft: Radius.circular(20.sp), topRight: Radius.circular(20.sp)));
  }

  static Widget _buildSheetContent({required double height, required List<Widget> children, EdgeInsets? padding}) {
    return SingleChildScrollView(
      physics: const ClampingScrollPhysics(),
      child: Container(
        height: height,
        padding: padding ?? EdgeInsets.symmetric(horizontal: 20.sp, vertical: 20.sp),
        decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.only(topLeft: Radius.circular(16.sp), topRight: Radius.circular(16.sp))),
        child: Column(mainAxisAlignment: MainAxisAlignment.start, crossAxisAlignment: CrossAxisAlignment.center, children: children),
      ),
    );
  }

  static Widget _buildSheet({required double height, required List<Widget> children, EdgeInsets? padding}) {
    return Container(
      height: height,
      margin: EdgeInsets.only(top: 0.12.sh),
      decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.only(topLeft: Radius.circular(24.r), topRight: Radius.circular(24.r))),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: children),
    );
  }

  static Widget _buildSheetHeader(String title, BuildContext context) {
    return Stack(
      children: [
        Container(
          width: double.infinity,
          alignment: Alignment.center,
          decoration: BoxDecoration(color: Colors.blue.shade100.withOpacity(0.2), borderRadius: BorderRadius.only(topLeft: Radius.circular(24.r), topRight: Radius.circular(24.r))),
          child: Column(
            children: [
              _buildDragLine(),
              Container(padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 16.h), child: Text(title, style: TextStyle(fontSize: 42.sp, fontWeight: FontWeight.w600, color: Colors.black87))),
            ],
          ),
        ),
        _buildCloseButton(context),
      ],
    );
  }

  static Widget _buildDragLine() {
    return Container(
      margin: EdgeInsets.only(top: 16.sp),
      child: Container(
        width: 120.sp,
        height: 12.sp,
        decoration: BoxDecoration(color: Colors.grey, borderRadius: BorderRadius.circular(24.sp)),
      ),
    );
  }

  /// Build close button for sheets
  static Widget _buildCloseButton(BuildContext context, {double? top, double? right}) {
    return Positioned(
      top: top ?? 16.h,
      right: right ?? 24.w,
      child: GestureDetector(
        onTap: () => Navigator.pop(context),
        child: Container(
          padding: EdgeInsets.all(8.w),
          decoration: BoxDecoration(
            color: Colors.white,
            shape: BoxShape.circle,
            boxShadow: [BoxShadow(color: Colors.black.withOpacity(0.1), blurRadius: 8, offset: const Offset(0, 2))],
          ),
          child: Icon(Icons.close, size: 64.sp, color: Colors.black87),
        ),
      ),
    );
  }

  //================[ADDITIONAL METHODS FROM OTHER FILES]================//

  /// Legacy dialog popup method (from dialog_utils.dart)
  static Future showDialogPopup(BuildContext context, Widget bodyWidget, {bool isDismiss = false}) {
    return showDialog<void>(
      barrierDismissible: isDismiss,
      context: context,
      builder: (context) => Dialog(shape: RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(30.sp))), child: bodyWidget),
    );
  }

  /// Legacy bottom sheet popup method (from dialog_utils.dart)
  static Future showBottomSheetPopup(BuildContext context, Widget bodyWidget, {double? height}) async {
    await showModalBottomSheet(
      isScrollControlled: true,
      isDismissible: true,
      context: context,
      backgroundColor: Colors.white,
      builder: (context) => SizedBox(
        height: height ?? 0.48.sh,
        child: bodyWidget,
      ),
    );
  }

  /// Get popup background decoration (from map_dialog_util.dart)
  static BoxDecoration getPopupBackground({double borderRadius = 12}) {
    return BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(24.sp),
      border: Border.all(color: Colors.blue[500]!, width: 1),
    );
  }

  static void showTreeSelectionRequiredMessage(BuildContext context, Function() action) {
    final snackBar = SnackBar(
      content: Row(
        children: [
          Icon(Icons.eco_outlined, color: Colors.white, size: 24.sp),
          SizedBox(width: 12.sp),
          Expanded(child: Text('Vui lòng chọn loại cây trước khi xem thông tin dinh dưỡng', style: TextStyle(fontSize: 36.sp, fontWeight: FontWeight.w500))),
        ],
      ),
      backgroundColor: Colors.orange[700],
      behavior: SnackBarBehavior.floating,
      margin: EdgeInsets.only(left: 20.sp, right: 20.sp, bottom: 20.sp),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.sp)),
      duration: const Duration(seconds: 3),
      action: SnackBarAction(label: 'CHỌN CÂY', textColor: Colors.white, onPressed: action),
      elevation: 6,
    );

    ScaffoldMessenger.of(context)
      ..hideCurrentSnackBar()
      ..showSnackBar(snackBar);
  }

  static Widget buildCurrentLocationLoading() {
    return Container(
      alignment: Alignment.center,
      color: Colors.black.withOpacity(0.4),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 32.sp, vertical: 24.sp),
        margin: EdgeInsets.symmetric(horizontal: 40.sp),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20.sp),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.15),
              blurRadius: 20.sp,
              offset: Offset(0, 8.sp),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              width: 46.sp,
              height: 46.sp,
              child: CircularProgressIndicator(
                valueColor: const AlwaysStoppedAnimation<Color>(Color(0xFF2196F3)),
                strokeWidth: 3.sp,
              ),
            ),
            SizedBox(height: 16.sp),
            Text(
              'Đang lấy vị trí hiện tại...',
              style: TextStyle(
                fontSize: 28.sp,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF333333),
                letterSpacing: 0.2,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

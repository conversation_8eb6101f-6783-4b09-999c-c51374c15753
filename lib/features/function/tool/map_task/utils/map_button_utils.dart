import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hainong/features/function/tool/map_task/map_constants.dart';

/// Optimized Map Button Utilities
class MapButtonUtils {
  //================[CONFIGURATIONS]================//

  // Consolidated decorations
  static BoxDecoration getStandardButtonDecoration({
    Color backgroundColor = Colors.white,
    double borderRadius = 12.0,
    double shadowOpacity = 0.5,
    double shadowSpreadRadius = 1.0,
    double shadowBlurRadius = 5.0,
    Offset shadowOffset = const Offset(0, 1),
  }) =>
      BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(shadowOpacity),
            spreadRadius: shadowSpreadRadius,
            blurRadius: shadowBlurRadius,
            offset: shadowOffset,
          )
        ],
      );

  static BoxDecoration getWeatherControlButtonDecoration({
    Color backgroundColor = Colors.white,
    double borderRadius = 30.0,
    double shadowOpacity = 0.1,
    double shadowBlurRadius = 30.0,
    Offset shadowOffset = const Offset(0, 10),
  }) =>
      BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(shadowOpacity),
            offset: shadowOffset,
            blurRadius: shadowBlurRadius,
          )
        ],
      );

  static BoxDecoration getTimelineButtonDecoration({
    required bool isActive,
    required Color activeColor,
    Color inactiveColor = Colors.grey,
    double size = 68.0,
    double borderWidth = 2.5,
  }) =>
      BoxDecoration(
        color: isActive ? activeColor : inactiveColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(size / 2),
        border: Border.all(
          color: isActive ? activeColor : inactiveColor.withOpacity(0.2),
          width: borderWidth,
        ),
      );

  static BoxDecoration getDropdownDecoration({
    double borderRadius = 20.0,
    double shadowOpacity = 0.5,
    double shadowSpreadRadius = 1.0,
    double shadowBlurRadius = 5.0,
    Offset shadowOffset = const Offset(0, 1),
  }) =>
      getStandardButtonDecoration(
        borderRadius: borderRadius,
        shadowOpacity: shadowOpacity,
        shadowSpreadRadius: shadowSpreadRadius,
        shadowBlurRadius: shadowBlurRadius,
        shadowOffset: shadowOffset,
      );

  // Common configurations
  static EdgeInsets get standardButtonPadding => const EdgeInsets.all(12);
  static EdgeInsets get weatherControlPadding => EdgeInsets.all(30.sp);
  static EdgeInsets get positionButtonPadding => EdgeInsets.only(bottom: 100.sp, right: 30.sp);
  static EdgeInsets get menuButtonContainerPadding => EdgeInsets.all(24.sp);
  static EdgeInsets get dropdownItemPadding => EdgeInsets.symmetric(vertical: 30.sp);
  static double get weatherControlIconSize => 60.sp;
  static double get dropdownImageSize => 72.0;
  static double getTimelineIconSize(double buttonSize) => buttonSize * 0.6;
  static Offset get dropdownOffset => const Offset(-54, 50);

  //================[CORE BUTTON BUILDERS]================//

  /// Universal button builder
  static Widget _buildButton({
    required IconData icon,
    required VoidCallback onPressed,
    Color iconColor = Colors.black,
    Color backgroundColor = Colors.white,
    double iconSize = MapConstants.defaultIconSize,
    double borderRadius = 12.0,
    EdgeInsets? padding,
    BoxShadow? customShadow,
    bool useWeatherStyle = false,
  }) {
    final decoration = customShadow != null
        ? BoxDecoration(
            color: backgroundColor,
            borderRadius: BorderRadius.circular(borderRadius),
            boxShadow: [customShadow],
          )
        : (useWeatherStyle
            ? getWeatherControlButtonDecoration(
                backgroundColor: backgroundColor,
                borderRadius: borderRadius,
              )
            : getStandardButtonDecoration(
                backgroundColor: backgroundColor,
                borderRadius: borderRadius,
              ));

    final content = Container(
      decoration: decoration,
      padding: padding ?? (useWeatherStyle ? weatherControlPadding : standardButtonPadding),
      child: Icon(icon, color: iconColor, size: iconSize),
    );

    return useWeatherStyle
        ? Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: onPressed,
              borderRadius: BorderRadius.circular(borderRadius),
              child: content,
            ),
          )
        : GestureDetector(onTap: onPressed, child: content);
  }

  //================[PUBLIC BUTTON BUILDERS]================//

  static Widget buildStandardMapButton({
    required IconData icon,
    required VoidCallback onPressed,
    Color iconColor = Colors.black,
    Color backgroundColor = Colors.white,
    double iconSize = MapConstants.defaultIconSize,
    double borderRadius = 12.0,
    EdgeInsets padding = const EdgeInsets.all(12),
    BoxShadow? customShadow,
  }) =>
      _buildButton(
        icon: icon,
        onPressed: onPressed,
        iconColor: iconColor,
        backgroundColor: backgroundColor,
        iconSize: iconSize,
        borderRadius: borderRadius,
        padding: padding,
        customShadow: customShadow,
      );

  static Widget buildWeatherControlButton({
    required IconData icon,
    required VoidCallback onPressed,
    required Color color,
    Color backgroundColor = Colors.white,
    double borderRadius = 30.0,
  }) =>
      _buildButton(
        icon: icon,
        onPressed: onPressed,
        iconColor: color,
        backgroundColor: backgroundColor,
        iconSize: weatherControlIconSize,
        borderRadius: borderRadius,
        useWeatherStyle: true,
      );

  static Widget buildTimelineButton({
    required IconData icon,
    required VoidCallback? onPressed,
    required bool isActive,
    required Color activeColor,
    required double size,
    Color inactiveColor = Colors.grey,
    Color disabledColor = Colors.grey,
  }) {
    final iconColor = isActive ? Colors.white : (onPressed != null ? activeColor : disabledColor);
    return Container(
      width: size,
      height: size,
      decoration: getTimelineButtonDecoration(
        isActive: isActive,
        activeColor: activeColor,
        inactiveColor: inactiveColor,
        size: size,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(size / 2),
          child: Icon(icon, color: iconColor, size: getTimelineIconSize(size)),
        ),
      ),
    );
  }

  static Widget buildPositionButton({
    required VoidCallback onPressed,
    IconData icon = Icons.near_me_outlined,
    Color iconColor = Colors.black,
    Color backgroundColor = Colors.white,
    double borderRadius = 12.0,
  }) =>
      Align(
        alignment: Alignment.bottomRight,
        child: SafeArea(
          child: buildStandardMapButton(
            icon: icon,
            onPressed: onPressed,
            iconColor: iconColor,
            backgroundColor: backgroundColor,
            borderRadius: borderRadius,
          ),
        ),
      );

  static Widget buildMenuToggleButton({
    IconData icon = Icons.menu,
    Color iconColor = Colors.black,
    Color backgroundColor = Colors.white,
    double borderRadius = 12.0,
    double iconSize = MapConstants.defaultIconSize,
  }) =>
      Container(
        decoration: getStandardButtonDecoration(
          backgroundColor: backgroundColor,
          borderRadius: borderRadius,
        ),
        padding: standardButtonPadding,
        child: Icon(icon, color: iconColor, size: iconSize),
      );

  static Widget buildOptionButton({
    required IconData icon,
    required VoidCallback onPressed,
    Color iconColor = Colors.black,
    Color backgroundColor = Colors.white,
    double borderRadius = 12.0,
  }) =>
      buildStandardMapButton(
        icon: icon,
        onPressed: onPressed,
        iconColor: iconColor,
        backgroundColor: backgroundColor,
        borderRadius: borderRadius,
      );

  //================[GROUP BUILDERS]================//

  static Widget buildWeatherControlGroup({
    required bool showWeatherPanel,
    required VoidCallback onTogglePanel,
    required Color primaryColor,
  }) =>
      Positioned(
        top: 60,
        right: 0,
        child: SafeArea(
          child: Container(
            padding: menuButtonContainerPadding,
            child: buildWeatherControlButton(
              icon: showWeatherPanel ? Icons.close : Icons.layers_rounded,
              onPressed: onTogglePanel,
              color: primaryColor,
            ),
          ),
        ),
      );

  static Widget buildWeatherControlGroupWithIcons({
    required bool showWeatherPanel,
    required VoidCallback onTogglePanel,
    required Color primaryColor,
    required bool weatherIconsVisible,
    required VoidCallback onToggleWeatherIcons,
  }) =>
      Positioned(
        top: 60,
        right: 0,
        child: SafeArea(
          child: Container(
            padding: menuButtonContainerPadding,
            child: Column(
              children: [
                buildWeatherControlButton(
                  icon: showWeatherPanel ? Icons.close : Icons.layers_rounded,
                  onPressed: onTogglePanel,
                  color: primaryColor,
                ),
                SizedBox(height: 20.sp),
                buildWeatherControlButton(
                  icon: weatherIconsVisible ? Icons.wb_sunny : Icons.wb_sunny_outlined,
                  onPressed: onToggleWeatherIcons,
                  color: weatherIconsVisible ? primaryColor : Colors.grey,
                ),
              ],
            ),
          ),
        ),
      );

  static Widget buildMenuButtonGroup({
    required Widget menuButton,
    Widget? optionButton,
    Widget? positionButton, // Thay đổi thành nullable để có thể ẩn
    double spacing = 20.0,
    Widget? weatherIconsToggleButton,
  }) =>
      Align(
        alignment: Alignment.topRight,
        child: Container(
          margin: const EdgeInsets.all(6),
          padding: menuButtonContainerPadding,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              menuButton,
              if (optionButton != null) ...[SizedBox(height: spacing), optionButton],
              // Chỉ hiển thị position button nếu không null
              if (positionButton != null) ...[SizedBox(height: spacing), positionButton],
              if (weatherIconsToggleButton != null) ...[SizedBox(height: spacing), weatherIconsToggleButton],
            ],
          ),
        ),
      );

  //================[DROPDOWN BUILDERS]================//

  static Widget buildMapMenuDropdown({
    required List<dynamic> menuItems,
    required dynamic selectedMenuId,
    required Function(int) onMenuSelected,
    required dynamic Function(int) getEnumFromId,
    IconData buttonIcon = Icons.menu,
    Color buttonIconColor = Colors.black,
    Color backgroundColor = Colors.white,
    double buttonBorderRadius = 12.0,
    double buttonIconSize = MapConstants.defaultIconSize,
  }) =>
      buildDropdownMenu<dynamic>(
        items: menuItems,
        selectedItem: menuItems.where((item) => getEnumFromId(item.id) == selectedMenuId).isNotEmpty ? menuItems.firstWhere((item) => getEnumFromId(item.id) == selectedMenuId) : null,
        onChanged: (selectedItem) {
          if (selectedItem?.id != null) onMenuSelected(selectedItem.id as int);
        },
        getItemName: (item) => item.name ?? '',
        getItemImage: (item) => item.image ?? '',
        getItemId: (item) => item.id ?? 0,
        getEnumFromId: (id) => getEnumFromId(id as int),
        buttonIcon: buttonIcon,
        buttonIconColor: buttonIconColor,
        backgroundColor: backgroundColor,
        buttonBorderRadius: buttonBorderRadius,
        buttonIconSize: buttonIconSize,
      );

  static Widget buildDropdownMenu<T>({
    required List<T> items,
    required T? selectedItem,
    required void Function(T?) onChanged,
    required String Function(T) getItemName,
    required String Function(T) getItemImage,
    required dynamic Function(T) getItemId,
    required dynamic Function(dynamic) getEnumFromId,
    IconData buttonIcon = Icons.menu,
    Color buttonIconColor = Colors.black,
    Color backgroundColor = Colors.white,
    double buttonBorderRadius = 12.0,
    double buttonIconSize = MapConstants.defaultIconSize,
    double dropdownWidth = 0.55,
    bool showCheckIcon = true,
    Color checkIconColor = Colors.black87,
  }) =>
      DropdownButtonHideUnderline(
        child: DropdownButton2<String>(
          alignment: Alignment.topRight,
          customButton: buildMenuToggleButton(
            icon: buttonIcon,
            iconColor: buttonIconColor,
            backgroundColor: backgroundColor,
            borderRadius: buttonBorderRadius,
            iconSize: buttonIconSize,
          ),
          dropdownStyleData: DropdownStyleData(
            offset: dropdownOffset,
            direction: DropdownDirection.left,
            width: dropdownWidth.sw,
            decoration: getDropdownDecoration(borderRadius: 20.sp),
            maxHeight: 0.35.sh,
          ),
          items: items.map((item) {
            final itemEnum = getEnumFromId(getItemId(item));
            final isSelected = selectedItem != null && getEnumFromId(getItemId(selectedItem)) == itemEnum;

            return DropdownMenuItem<String>(
              value: getItemName(item),
              child: _buildDropdownMenuItem(
                item: item,
                isSelected: isSelected,
                showCheckIcon: showCheckIcon,
                checkIconColor: checkIconColor,
                getItemName: getItemName,
                getItemImage: getItemImage,
                getItemId: getItemId,
                isLastItem: getItemId(item) == 4,
              ),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              final selectedItem = items.firstWhere((item) => getItemName(item) == value);
              onChanged(selectedItem);
            }
          },
          iconStyleData: IconStyleData(
            icon: Icon(buttonIcon),
            iconEnabledColor: Colors.green,
            iconDisabledColor: buttonIconColor,
            iconSize: buttonIconSize,
          ),
        ),
      );

  static Widget _buildDropdownMenuItem<T>({
    required T item,
    required bool isSelected,
    required bool showCheckIcon,
    required Color checkIconColor,
    required String Function(T) getItemName,
    required String Function(T) getItemImage,
    required dynamic Function(T) getItemId,
    required bool isLastItem,
  }) =>
      Column(
        children: [
          SizedBox(height: dropdownItemPadding.vertical / 2),
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                if (showCheckIcon && isSelected) Icon(Icons.check, color: checkIconColor),
                SizedBox(width: 20.sp),
                Expanded(child: Text(getItemName(item))),
                SizedBox(
                  width: dropdownImageSize.w,
                  height: dropdownImageSize.w,
                  child: Image.asset(getItemImage(item)),
                ),
              ],
            ),
          ),
          SizedBox(height: dropdownItemPadding.vertical / 2),
          if (!isLastItem) const Divider(color: Colors.grey, height: 2, thickness: 0),
        ],
      );
}

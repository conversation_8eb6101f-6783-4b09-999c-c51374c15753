import 'package:just_audio/just_audio.dart';

class MapAudioUtil {
  static AudioPlayer? _player;
  static String _currentAudioUrl = '';
  static bool _isPlaying = false;

  /// Initialize audio player with URL
  static Future<void> initializePlayer(String audioUrl) async {
    if (audioUrl.isEmpty) return;

    _currentAudioUrl = audioUrl;
    _player?.dispose();
    _player = AudioPlayer();

    try {
      await _player!.setUrl(audioUrl);
      _setupPlayerStateListener();
      await _player!.play();
      _isPlaying = true;
    } catch (e) {
      print('Error initializing audio player: $e');
    }
  }

  /// Setup player state listener for auto-handling completion
  static void _setupPlayerStateListener() {
    _player?.playerStateStream.listen((playerState) {
      if (playerState.processingState == ProcessingState.completed) {
        _isPlaying = false;
        _player?.seek(const Duration(seconds: 0));
        _player?.pause();
      }
    });
  }

  /// Play or pause audio based on current state
  static Future<void> togglePlayPause() async {
    if (_currentAudioUrl.isEmpty || _player == null) return;

    try {
      if (_isPlaying) {
        await _player!.pause();
      } else {
        await _player!.play();
      }
      _isPlaying = !_isPlaying;
    } catch (e) {
      print('Error toggling audio playback: $e');
    }
  }

  /// Play audio
  static Future<void> play() async {
    if (_player != null && !_isPlaying) {
      try {
        await _player!.play();
        _isPlaying = true;
      } catch (e) {
        print('Error playing audio: $e');
      }
    }
  }

  /// Pause audio
  static Future<void> pause() async {
    if (_player != null && _isPlaying) {
      try {
        await _player!.pause();
        _isPlaying = false;
      } catch (e) {
        print('Error pausing audio: $e');
      }
    }
  }

  /// Stop audio and reset position
  static Future<void> stop() async {
    if (_player != null) {
      try {
        await _player!.stop();
        await _player!.seek(const Duration(seconds: 0));
        _isPlaying = false;
      } catch (e) {
        print('Error stopping audio: $e');
      }
    }
  }

  /// Dispose audio player and cleanup resources
  static Future<void> dispose() async {
    try {
      if (_player != null) {
        if (_isPlaying) {
          await _player!.stop();
        }
        await _player!.dispose();
        _player = null;
        _isPlaying = false;
        _currentAudioUrl = '';
      }
    } catch (e) {
      print('Error disposing audio player: $e');
    }
  }

  /// Get current playing state
  static bool get isPlaying => _isPlaying;

  /// Get current audio URL
  static String get currentAudioUrl => _currentAudioUrl;

  /// Check if player is initialized
  static bool get isInitialized => _player != null;

  /// Auto-play audio with URL
  static Future<void> autoPlayAudio(String audioUrl) async {
    if (audioUrl.isNotEmpty) {
      await initializePlayer(audioUrl);
    }
  }

  /// Handle weather audio playback
  static Future<void> handleWeatherAudio(bool shouldPlay, String audioUrl) async {
    if (shouldPlay && audioUrl.isNotEmpty) {
      // Play audio with URL
      await autoPlayAudio(audioUrl);
    } else {
      // Stop audio completely (not just pause)
      await stop();
    }
  }
}

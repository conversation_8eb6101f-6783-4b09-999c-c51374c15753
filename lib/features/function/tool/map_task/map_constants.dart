import 'package:flutter/material.dart';
import 'package:hainong/features/function/tool/map_task/models/map_enum.dart';
import 'package:hainong/features/function/tool/map_task/models/map_item_menu_model.dart';
import 'package:hainong/features/function/tool/map_task/models/map_pet_item.dart';
import 'package:hainong/features/function/tool/map_task/source/colors/map_nutrient_color.dart';
import 'package:hainong/features/post/ui/import_lib_ui_post.dart';
import 'package:trackasia_gl/trackasia_gl.dart';

class MapConstants {
  static const String weatherTileUrl = "https://dev.gis-api.hainong.vn/weather_tiles/{z}/{x}/{y}.pbf";
  // Map Source IDs
  static const String sourceIdPetMap = "source_id_pet_map";
  static const String sourceIdWeatherMap = "source_id_weather_map";
  static const String sourceIdNutritionMap = "source_id_nutrition_map";
  static const String sourceIdDemonMap = "source_id_demo_map";
  static const String sourceIdStoreMap = "source_id_store_map";
  static const String sourceIdStorageMap = "source_id_storage_map";

  // Map Layer Keys
  static const String keyLayerPetMap = "layer_pet_map";
  static const String keyLayerWeatherMap = "layer_weather_map";
  static const String keyLayerDemonMap = "layer_demo_map";
  static const String keyLayerStoreMap = "layer_store_map";
  static const String keyLayerStorageMap = "layer_storage_map";

  // Map Configuration
  static const double defaultZoom = 4.8;
  static const double deepLinkZoom = 10.0;
  static const double maxZoomLevel = 24.0;
  static const double minZoomLevel = 1.0;

  // Pet Map Zoom Configuration
  static const double petZoomMultiplier = 6.0;
  static const int petZoomMaxIterations = 2;
  static const double defaultLatitude = 15.7146441;
  static const double defaultLongitude = 106.401633;

  // Map Preferences
  static const MinMaxZoomPreference mapTaskZoomPreference = MinMaxZoomPreference(5, 12);
  static const LatLng defaultMapCenter = LatLng(15.7146441, 106.401633);
  static const CameraPosition defaultCameraPosition = CameraPosition(target: defaultMapCenter, zoom: 5.0);

  // UI Constants
  static const double bottomSheetHeight = 0.60;

  // Font Size Constants
  static const double defaultTitleFontSize = 38.0;
  static const double defaultContentFontSize = 32.0;
  static const double defaultContentSmallFontSize = 28.0;
  static const double defaultIconSize = 30.0;

  static const double panelMenuWidthRatio = 0.38;
  static const double panelActionWidthRatio = 0.38;
  static const double panelPetInfoWidthRatio = 0.38;
  static const double panelModelMenuWidthRatio = 0.35;
  static const double panelPetFilterWidthRatio = 0.88;
  static const double panelWeatherMenuWidthRatio = 0.4;
  static const double panelWeatherInfoWidthRatio = 0.24;
  static const double marginKeep = 42.0;

  static List<MenuItemMap> menuListModel() => [
        MenuItemMap(id: 1, image: "assets/images/v9/map/ic_map_model_direction.png", name: "Chỉ đường"),
        MenuItemMap(id: 2, image: "assets/images/v9/map/ic_map_model_share.png", name: "Chia sẻ"),
        MenuItemMap(id: 3, image: "assets/images/v9/map/ic_map_model_update.png", name: "Cập nhật"),
      ];

  static List<MenuItemMap> menuListPet() => [
        MenuItemMap(id: 1, image: "assets/images/v9/map/ic_map_model_direction.png", name: "Chỉ đường"),
        MenuItemMap(id: 2, image: "assets/images/v9/map/ic_map_model_share.png", name: "Chia sẻ"),
        MenuItemMap(id: 3, image: "assets/images/v9/map/ic_map_contribute.png", name: "Đóng góp"),
      ];

  static List<MenuItemMap> regionTreeType() => [
        MenuItemMap(id: 1, image: "", name: "Miền Bắc", value: "Miền Bắc"),
        MenuItemMap(id: 2, image: "", name: "Miền Trung", value: "Miền Trung"),
        MenuItemMap(id: 3, image: "", name: "Tây Nguyên", value: "Tây Nguyên"),
        MenuItemMap(id: 4, image: "", name: "Đông Nam Bộ", value: "Đông Nam Bộ"),
        MenuItemMap(id: 5, image: "", name: "Vùng ven biển", value: "Vùng ven biển"),
        MenuItemMap(id: 6, image: "", name: "Vùng tứ giác Long Xuyên", value: "Vùng tứ giác Long Xuyên"),
        MenuItemMap(id: 7, image: "", name: "Vùng Đồng Tháp mười", value: "Vùng Đồng Tháp mười"),
        MenuItemMap(id: 8, image: "", name: "Vùng Phù Xa (Sông Tiền - Sông hậu)", value: "Vùng Phù Xa (Sông Tiền - Sông hậu)"),
        MenuItemMap(id: 9, image: "", name: "Vùng bán đảo Cà Mau", value: "Vùng bán đảo Cà Mau"),
        MenuItemMap(id: 10, image: "", name: "Vùng đồng bằng sông cửu long", value: "Vùng đồng bằng sông cửu long"),
      ];

  static List<MenuItemMap> farmingTreeType() => [
        MenuItemMap(id: 0, image: "", name: "Canh tác vô cơ", value: "0"),
        MenuItemMap(id: 1, image: "", name: "Canh tác hữu cơ", value: "1"),
      ];

  static List<MenuItemMap> menuNutritionalTypeModel() => [
        MenuItemMap(id: 1, image: "assets/images/v9/map/ic_map_npk.png", name: "N/P/K", value: "npk"),
        MenuItemMap(id: 2, image: "assets/images/v9/map/ic_map_dam.png", name: "Đạm", value: "n"),
        MenuItemMap(id: 3, image: "assets/images/v9/map/ic_map_lan.png", name: "Lân", value: "p"),
        MenuItemMap(id: 4, image: "assets/images/v9/map/ic_map_kali.png", name: "Kali", value: "k"),
        MenuItemMap(id: 5, image: "assets/images/v9/map/ic_map_ph.png", name: "pH", value: "ph"),
        MenuItemMap(id: 6, image: "assets/images/v9/map/ic_map_ec.png", name: "EC", value: "ec"),
        MenuItemMap(id: 7, image: "assets/images/v9/map/ic_map_cec.png", name: "CEC", value: "cec"),
        MenuItemMap(id: 8, image: "assets/images/v9/map/ic_map_om.png", name: "OM", value: "om"),
      ];

  static List<MenuItemMap> menuNutritionalTypeBottomModel() => [
        MenuItemMap(id: 1, image: "assets/images/v9/map/ic_map_npk.png", name: "Tất cả", value: "npk"),
        MenuItemMap(id: 2, image: "assets/images/v9/map/ic_map_dam.png", name: "Đạm", value: "n"),
        MenuItemMap(id: 3, image: "assets/images/v9/map/ic_map_lan.png", name: "Lân", value: "p"),
        MenuItemMap(id: 4, image: "assets/images/v9/map/ic_map_kali.png", name: "Kali", value: "k"),
        MenuItemMap(id: 5, image: "assets/images/v9/map/ic_map_ph.png", name: "pH", value: "ph"),
        MenuItemMap(id: 6, image: "assets/images/v9/map/ic_map_ec.png", name: "EC", value: "ec"),
        MenuItemMap(id: 7, image: "assets/images/v9/map/ic_map_cec.png", name: "CEC", value: "cec"),
        MenuItemMap(id: 8, image: "assets/images/v9/map/ic_map_om.png", name: "OM", value: "om")
      ];

  static MenuItemMap menuTreeRiceItem() => MenuItemMap(id: 1, image: "assets/images/v9/map/ic_map_lua.png", name: "Lúa", value: "lua");

  static List<MenuItemMap> menuFruitItem() => [
        MenuItemMap(id: 2, image: "assets/images/v9/map/ic_map_saurieng.png", name: "Sầu riêng", value: "sau_rieng"),
        MenuItemMap(id: 3, image: "assets/images/v9/map/ic_map_buoi.png", name: "Bưởi", value: "buoi"),
        MenuItemMap(id: 4, image: "assets/images/v9/map/ic_map_thanhlong.png", name: "Thanh long", value: "thanh_long"),
        MenuItemMap(id: 5, image: "assets/images/v9/map/ic_map_xoai.png", name: "Xoài", value: "xoai"),
        MenuItemMap(id: 6, image: "assets/images/v9/map/ic_map_cam.png", name: "Cam", value: "cam"),
        MenuItemMap(id: 7, image: "assets/images/v9/map/ic_map_nhan.png", name: "Nhãn", value: "nhan")
      ];

  static List<MenuItemMap> menuTreeValueModel() => [
        MenuItemMap(id: 1, image: "assets/images/v9/map/ic_map_npk.png", name: "N/P/K", value: "npk"),
        MenuItemMap(id: 2, image: "assets/images/v9/map/ic_map_dam.png", name: "Đạm", value: "n"),
        MenuItemMap(id: 3, image: "assets/images/v9/map/ic_map_lan.png", name: "Lân", value: "p"),
        MenuItemMap(id: 4, image: "assets/images/v9/map/ic_map_kali.png", name: "Kali", value: "k"),
        MenuItemMap(id: 5, image: "assets/images/v9/map/ic_map_ph.png", name: "pH", value: "ph"),
        MenuItemMap(id: 6, image: "assets/images/v9/map/ic_map_ec.png", name: "EC", value: "ec"),
        MenuItemMap(id: 7, image: "assets/images/v9/map/ic_map_cec.png", name: "CEC", value: "cec"),
        MenuItemMap(id: 8, image: "assets/images/v9/map/ic_map_om.png", name: "OM", value: "om"),
      ];

  static String nameOptionMap(MapMenuEnum _selectMenu) {
    switch (_selectMenu) {
      case MapMenuEnum.pet:
        return "Bản đồ sâu bệnh";
      case MapMenuEnum.nutrition:
        return "Bản đồ canh tác";
      case MapMenuEnum.weather:
        return "Bản đồ thời tiết";
      case MapMenuEnum.model:
        return "Bản đồ mô hình";
      default:
        return "Bản đồ nông nghiệp";
    }
  }

  static String getImageNameModelMap(MapModelEnum _selectMenu) {
    switch (_selectMenu) {
      case MapModelEnum.demonstration:
        return "ic_map_model_marker_demo";
      case MapModelEnum.store:
        return "ic_map_model_marker_store";
      case MapModelEnum.storage:
        return "ic_map_model_marker_storage";
      default:
        return "ic_map_model_marker_demo";
    }
  }

  static MapModelEnum indexEnumModelMap(int index) {
    switch (index) {
      case 1:
        return MapModelEnum.demonstration;
      case 2:
        return MapModelEnum.store;
      case 3:
        return MapModelEnum.storage;
      default:
        return MapModelEnum.demonstration;
    }
  }

  static MapMenuEnum indexEnumMenuMap(int index) {
    switch (index) {
      case 1:
        return MapMenuEnum.pet;
      case 2:
        return MapMenuEnum.nutrition;
      case 3:
        return MapMenuEnum.weather;
      default:
        return MapMenuEnum.model;
    }
  }

  static List<MenuItemMap> menuMapOption() => [
        MenuItemMap(id: 1, image: "assets/images/v9/map/ic_map_pet.png", name: "Sâu bệnh"),
        MenuItemMap(id: 2, image: "assets/images/v9/map/ic_map_farming.png", name: "Canh tác"),
        MenuItemMap(id: 3, image: "assets/images/v9/map/ic_map_weather.png", name: "Thời tiết"),
        MenuItemMap(id: 4, image: "assets/images/v9/map/ic_map_model.png", name: "Mô hình")
      ];

  static IconData iconOptionMap(MapMenuEnum selectMenu) {
    switch (selectMenu) {
      case MapMenuEnum.pet:
        return Icons.filter_list_alt;
      case MapMenuEnum.nutrition:
        return Icons.layers;
      case MapMenuEnum.weather:
        return Icons.layers_rounded;
      default:
        return Icons.filter_list_alt;
    }
  }

  static List<MenuItemMap> menuTreeTypeModel(String type) {
    switch (type) {
      case "npk":
      case "n":
      case "p":
      case "k":
      case "ph":
      case "ec":
      case "cec":
      case "om":
        return menuFruitItem();
      default:
        return [];
    }
  }

  static List<ColorItemMap> menuLayerColor(String type, typeTree) {
    switch (type) {
      case "npk":
        switch (typeTree) {
          case "lua":
          case "sau_rieng":
          case "buoi":
          case "xoai":
          case "cam":
          case "nhan":
          case "thanh_long":
        }
        break;
      case "n":
        switch (typeTree) {
          case "lua":
            return MapNutrientColor.luaNColor();
          case "sau_rieng":
            return MapNutrientColor.sauriengNColor();
          case "buoi":
            return MapNutrientColor.buoiNColor();
          case "xoai":
            return MapNutrientColor.xoaiNColor();
          case "cam":
            return MapNutrientColor.camNColor();
          case "nhan":
            return MapNutrientColor.nhanNColor();
          case "thanh_long":
            return MapNutrientColor.thanhLongNColor();
        }
        break;
      case "p":
        switch (typeTree) {
          case "lua":
            return MapNutrientColor.luaPColor();
          case "sau_rieng":
            return MapNutrientColor.sauriengPColor();
          case "buoi":
            return MapNutrientColor.buoiPColor();
          case "xoai":
            return MapNutrientColor.xoaiPColor();
          case "cam":
            return MapNutrientColor.camPColor();
          case "nhan":
            return MapNutrientColor.nhanPColor();
          case "thanh_long":
            return MapNutrientColor.thanhLongPColor();
        }
        break;
      case "k":
        switch (typeTree) {
          case "lua":
            return MapNutrientColor.luaKColor();
          case "sau_rieng":
            return MapNutrientColor.sauriengKColor();
          case "buoi":
            return MapNutrientColor.buoiKColor();
          case "xoai":
            return MapNutrientColor.xoaiKColor();
          case "cam":
            return MapNutrientColor.camKColor();
          case "nhan":
            return MapNutrientColor.nhanKColor();
          case "thanh_long":
            return MapNutrientColor.thanhLongKColor();
        }
        break;
      case "ph":
        return MapNutrientColor.pHColor();
      case "ec":
        return MapNutrientColor.eCColor();
      case "cec":
        return MapNutrientColor.ceCColor();
      case "om":
        return MapNutrientColor.omCColor();
    }
    return [];
  }

  static String getDisplayText(String value) {
    switch (value) {
      case "npk":
        return "Hiển thị tất cả";
      case "n":
        return "Hàm lượng Đạm (N)";
      case "p":
        return "Hàm lượng Lân (P)";
      case "k":
        return "Hàm lượng Kali (K)";
      case "ph":
        return "Độ pH";
      case "ec":
        return "Độ dẫn điện (EC)";
      case "cec":
        return "Trao đổi chất (CEC)";
      case "om":
        return "Chất hữu cơ (OM)";
      default:
        return "Hàm lượng ${value.toUpperCase()}";
    }
  }

  static String getOptionLabel(String menuType) {
    switch (menuType.toLowerCase()) {
      case 'pet':
        return 'Bộ lọc';
      case 'nutrition':
        return 'Dinh dưỡng';
      case 'weather':
        return 'Cài đặt';
      case 'model':
        return 'Mô hình';
      default:
        return 'Tùy chọn';
    }
  }

  static Color getMenuColor(int id) {
    switch (id) {
      case 1:
        return Colors.green;
      case 2:
        return Colors.orange;
      case 3:
        return Colors.blue;
      case 4:
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  static final List<String> imageIconMapPaths = [
    'assets/images/v9/map/pet/1.png',
    'assets/images/v9/map/pet/2.png',
    'assets/images/v9/map/pet/3.png',
    'assets/images/v9/map/pet/4.png',
    'assets/images/v9/map/ic_map_storage_medium.png',
    'assets/images/v9/map/ic_map_storage_large.png',
    'assets/images/v9/map/ic_map_store_medium.png',
    'assets/images/v9/map/ic_map_store_large.png',
    'assets/images/v9/map/ic_map_model_marker_demo.png',
    'assets/images/v9/map/ic_map_model_marker_store.png',
    'assets/images/v9/map/ic_map_model_marker_storage.png',
    'assets/images/v9/map/weather/01d.png',
    'assets/images/v9/map/weather/01dd-1.png',
    'assets/images/v9/map/weather/01dd.png',
    'assets/images/v9/map/weather/01ddd.png',
    'assets/images/v9/map/weather/01n.png',
    'assets/images/v9/map/weather/02d.png',
    'assets/images/v9/map/weather/02n.png',
    'assets/images/v9/map/weather/03d.png',
    'assets/images/v9/map/weather/3n.png',
    'assets/images/v9/map/weather/03n.png',
    'assets/images/v9/map/weather/04d.png',
    'assets/images/v9/map/weather/04n.png',
    'assets/images/v9/map/weather/6-02.png',
    'assets/images/v9/map/weather/9d.png',
    'assets/images/v9/map/weather/09d.png',
    'assets/images/v9/map/weather/09n.png',
    'assets/images/v9/map/weather/10d.png',
    'assets/images/v9/map/weather/10dd.png',
    'assets/images/v9/map/weather/10n.png',
    'assets/images/v9/map/weather/11d.png',
    'assets/images/v9/map/weather/11dd.png',
    'assets/images/v9/map/weather/11n.png',
    'assets/images/v9/map/weather/13d.png',
    'assets/images/v9/map/weather/13n.png',
    'assets/images/v9/map/weather/50d.png',
    'assets/images/v9/map/weather/50n.png',
    'assets/images/v9/map/weather/50d-1.png',
  ];

  // Pet Map Crop Categories
  static List<MapPetItem> petMapCropCategories() => [
        MapPetItem(id: 1, name: "Lúa", color: const Color(0xFF0304af), icon: Icons.grass),
        MapPetItem(id: 2, name: "Cà phê", color: const Color(0xFF006600), icon: Icons.local_cafe),
        MapPetItem(id: 3, name: "Sầu riêng", color: const Color(0xFF00ff99), icon: Icons.eco),
        MapPetItem(id: 4, name: "Hồ tiêu", color: const Color(0xFF00b4d8), icon: Icons.grain),
      ];

  // Model Tab Constants
  static const String modelTabDemonstration = "Mô hình trình diễn";
  static const String modelTabStore = "Cửa hàng";
  static const String modelTabWarehouse = "Kho hàng";

  static const String modelTabDemonstrationImage = "ic_map_model_demo.png";
  static const String modelTabStoreImage = "ic_map_model_store.png";
  static const String modelTabWarehouseImage = "ic_map_model_warehouse.png";

  static String getImageForValue(String value) {
    final nutritionList = MapConstants.menuNutritionalTypeModel();
    final nutritionItem = nutritionList.firstWhere((item) => item.value == value, orElse: () => MenuItemMap(image: "assets/images/v9/map/ic_map_npk.png"));
    return nutritionItem.image;
  }
}

// Class để lưu trữ thông tin về loại cây và màu tương ứng


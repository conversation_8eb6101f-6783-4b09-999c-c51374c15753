import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:hainong/common/api_client.dart';
import 'package:hainong/common/base_bloc.dart';
import 'package:hainong/common/base_response.dart';
import 'package:hainong/common/constants.dart';
import 'package:hainong/common/models/file_byte.dart';
import 'package:hainong/common/models/item_list_model.dart';
import 'package:hainong/features/function/tool/diagnose_pests/diagnose_pests_repository.dart';
import 'package:hainong/features/function/tool/map_task/map_constants.dart';
import 'package:hainong/features/function/tool/map_task/models/map_address_model.dart';
import 'package:hainong/features/function/tool/map_task/models/map_data_model.dart';
import 'package:hainong/features/function/tool/map_task/models/map_item_menu_model.dart';
import 'package:hainong/features/function/tool/map_task/models/map_item_model.dart';
import 'package:hainong/features/function/tool/map_task/models/map_response_model.dart';
import 'package:hainong/features/function/tool/map_task/utils/map_utils.dart';
import 'package:hainong/features/function/tool/suggestion_map/mappage_repository.dart';
import 'package:http/http.dart' as http;
import 'package:trackasia_gl/trackasia_gl.dart';

class LoadListNameEvent extends BaseEvent {}

class LoadNutritionalTypeEvent extends BaseEvent {}

class LoadTreeTypeEvent extends BaseEvent {
  final String type;
  LoadTreeTypeEvent(this.type);
}

class GetTreeParamsEvent extends BaseEvent {
  String nutritionType;
  String treeTyle;
  GetTreeParamsEvent(this.nutritionType, this.treeTyle);
}

class PostRatingEvent extends BaseEvent {
  final List<FileByte> list;
  final String content;
  final int rate;
  final int id;
  final String type;
  PostRatingEvent(this.id, this.list, this.content, this.rate, this.type);
}

class PostImageMapEvent extends BaseEvent {
  final List<FileByte> list;
  final String content;
  final int id;
  final String type;
  PostImageMapEvent(this.id, this.content, this.type, this.list);
}

class GetTreeValuesEvent extends BaseEvent {
  GetTreeValuesEvent();
}

class GetPetMapByProvinceEvent extends BaseEvent {
  final String sourceId;
  final String keyChartName;
  final String provinceId;
  final List<ItemModel> provinces;
  final int currentIndex;
  final String? fromDate;
  final String? toDate;
  final List<String>? diagnosticIds;
  GetPetMapByProvinceEvent(this.sourceId, this.keyChartName, this.provinceId, this.provinces, this.currentIndex, {this.fromDate, this.toDate, this.diagnosticIds});
}

class GetPetMapByProvincesEvent extends BaseEvent {
  final String sourceId;
  final String keyChartName;
  final List<ItemModel> provinces;
  final String? fromDate;
  final String? toDate;
  final List<String>? diagnosticIds;
  GetPetMapByProvincesEvent(this.sourceId, this.keyChartName, this.provinces, {this.fromDate, this.toDate, this.diagnosticIds});
}

class GetPetMapByProvinceState extends BaseState {
  final String provinceId;
  final int currentIndex;
  final int totalProvinces;
  final MapGeoJsonModel data;
  GetPetMapByProvinceState(this.provinceId, this.currentIndex, this.totalProvinces, this.data);
}

class GetDemonstrationParadigmsEvent extends BaseEvent {
  String sourceId;
  String layerId;
  GetDemonstrationParadigmsEvent(this.sourceId, this.layerId);
}

class GetDetailDemonstrationParadigmEvent extends BaseEvent {
  final int id;
  GetDetailDemonstrationParadigmEvent(this.id);
}

class GetListAgenciesEvent extends BaseEvent {
  final String type;
  final String sourceId;
  final String layerId;
  GetListAgenciesEvent(this.type, this.sourceId, this.layerId);
}

class GetNutritionMapEvent extends BaseEvent {
  LatLng point;
  String nutritionType;
  String treeTyle;
  GetNutritionMapEvent(this.nutritionType, this.treeTyle, this.point);
}

class GetDetailAgenciesEvent extends BaseEvent {
  final int id;
  GetDetailAgenciesEvent(this.id);
}

class GetReverseGeocodingEvent extends BaseEvent {
  LatLng point;
  GetReverseGeocodingEvent(this.point);
}

class LoadImageMapEvent extends BaseEvent {}

class ChangeRatingStarEvent extends BaseEvent {}

class ShowKeyboardEvent extends BaseEvent {
  final bool value;
  ShowKeyboardEvent(this.value);
}

class GetCurrentLocationEvent extends BaseEvent {
  final LatLng point;
  GetCurrentLocationEvent(this.point);
}

class GetCurrentLocationMarketEvent extends BaseEvent {
  final LatLng point;
  GetCurrentLocationMarketEvent(this.point);
}

class GetLocationEvent extends BaseEvent {
  final LatLng point;
  final MapAddressModel? address;
  final MapGeoJsonModel? data;
  GetLocationEvent(this.point, {this.data, this.address});
}

class GetPetMapDetailEvent extends BaseEvent {
  final String type;
  final int id;
  GetPetMapDetailEvent(this.id, this.type);
}

class CreateDiagnostisPestEvent extends BaseEvent {
  final LatLng point;
  final List<FileByte> files;
  final String pest_name, description, tree_name, province_id, district_id, address;
  CreateDiagnostisPestEvent(this.point, this.province_id, this.district_id, this.address, this.tree_name, this.pest_name, this.description, this.files);
}

class LoadCategorysEvent extends BaseEvent {
  String? name;
  LoadCategorysEvent({this.name});
}

class LoadProvincesEvent extends BaseEvent {}

class ChangeProvinceEvent extends BaseEvent {}

class ChangeIndexTabEvent extends BaseEvent {}

class ChangeIndexTabState extends BaseState {}

class SendContributeEvent extends BaseEvent {
  final MapAddressModel address;
  final LatLng point;
  final name, zone_crops, pH, EC, CEC, farming_method;
  final List<FileByte> files;
  final List<String> crops;
  SendContributeEvent(this.address, this.point, this.name, this.crops, this.zone_crops, this.pH, this.EC, this.CEC, this.farming_method, this.files);
}

class SendDiseasesPositionEvent extends BaseEvent {
  final String id;
  final List<LatLng> points;
  SendDiseasesPositionEvent(this.id, this.points);
}

class DrawMapEvent extends BaseEvent {}

class LoadCurrentDetailWeatherEvent extends BaseEvent {
  final LatLng point;
  LoadCurrentDetailWeatherEvent(this.point);
}

class LoadDetailWeatherEvent extends BaseEvent {
  final LatLng point;
  final String? id;
  final bool isShowDetail;
  LoadDetailWeatherEvent(this.point, {this.id, this.isShowDetail = false});
}

class LoadingAudioEvent extends BaseEvent {
  final bool value;
  LoadingAudioEvent(this.value);
}

class PlayAudioEvent extends BaseEvent {
  final bool value;
  PlayAudioEvent(this.value);
}

class ShowErrorEvent extends BaseEvent {
  String error;
  ShowErrorEvent(this.error);
}

class PlayAudioState extends BaseState {
  final bool value;
  PlayAudioState(this.value);
}

class LoadingAudioState extends BaseState {
  final bool value;
  LoadingAudioState(this.value);
}

class DrawMapState extends BaseState {}

class LoadNutritionalTypeState extends BaseState {
  final List<MenuItemMap> list;
  LoadNutritionalTypeState(this.list);
}

class LoadTreeTypeState extends BaseState {
  final List<MenuItemMap> list;
  LoadTreeTypeState(this.list);
}

class GetPetMapEvent extends BaseEvent {
  String sourceId;
  String params;
  GetPetMapEvent(this.sourceId, this.params);
}

class GetWeatherMapEvent extends BaseEvent {
  String sourceId;
  String? params;
  GetWeatherMapEvent(this.sourceId, {this.params});
}

class GetTreeParamsState extends BaseState {
  String nutritionType;
  String treeType;
  GetTreeParamsState(this.nutritionType, this.treeType);
}

class GetWeatherMapState extends BaseState {
  MapGeoJsonModel data;
  GetWeatherMapState(this.data);
}

class GetNutritionMapState extends BaseState {
  MapGeoJsonModel data;
  LatLng point;
  GetNutritionMapState(this.data, this.point);
}

class LoadImageMapState extends BaseState {}

class ChangeRatingStarState extends BaseState {}

class GetDemonstrationParadigmsState extends BaseState {
  MapGeoJsonModel data;
  GetDemonstrationParadigmsState(this.data);
}

class GetDetailDemonstrationParadigmState extends BaseState {
  final MapDataModel data;
  GetDetailDemonstrationParadigmState(this.data);
}

class GetPetMapDetailState extends BaseState {
  final MapDataModel data;
  GetPetMapDetailState(this.data);
}

class GetTreeValuesState extends BaseState {
  GetTreeValuesState();
}

class GetListAgenciesState extends BaseState {
  final MapGeoJsonModel data;
  GetListAgenciesState(this.data);
}

class GetDetailAgenciesState extends BaseState {
  final MapDataModel data;
  GetDetailAgenciesState(this.data);
}

class GetReverseGeocodingState extends BaseState {
  String address;
  GetReverseGeocodingState(this.address);
}

class ShowKeyboardState extends BaseState {
  final bool value;

  ShowKeyboardState(this.value);
}

class PostRatingState extends BaseState {
  final BaseResponse response;
  PostRatingState(this.response);
}

class GetCurrentLocationState extends BaseState {
  final MapAddressModel response;
  final LatLng point;
  GetCurrentLocationState(this.response, this.point);
}

class GetCurrentLocationMarketState extends BaseState {
  final MapAddressModel response;
  final LatLng point;
  GetCurrentLocationMarketState(this.response, this.point);
}

class GetLocationState extends BaseState {
  final MapAddressModel response;
  final LatLng point;
  final MapAddressModel? address;
  final MapGeoJsonModel? data;
  GetLocationState(this.response, this.point, this.address, this.data);
}

class PostImageMapState extends BaseState {
  final BaseResponse response;
  PostImageMapState(this.response);
}

class CreateDiagnostisPestSuccessState extends BaseState {
  final BaseResponse resp;
  CreateDiagnostisPestSuccessState(this.resp);
}

class LoadCategorysState extends BaseState {
  final List<PetModel> list;
  const LoadCategorysState(this.list);
}

class LoadCategorysFilterState extends BaseState {
  final List<TreeModel> list;
  LoadCategorysFilterState(this.list);
}

class LoadProvincesState extends BaseState {
  final List<ItemModel> data;
  LoadProvincesState(this.data);
}

class ChangeProvinceState extends BaseState {}

class CreateQuestionState extends BaseState {
  final ItemModel data;
  const CreateQuestionState(this.data);
}

class SendDiseasesPositionState extends BaseState {
  final BaseResponse response;
  const SendDiseasesPositionState(this.response);
}

class LoadCurrentDetailWeatherState extends BaseState {
  final Map<String, dynamic> data;
  LatLng point;
  LoadCurrentDetailWeatherState(this.data, this.point);
}

class LoadDetailWeatherState extends BaseState {
  final Map<String, dynamic> data;
  LatLng point;
  LoadDetailWeatherState(this.data, this.point);
}

class LoadAudioLinkState extends BaseState {
  final String audioLink;
  final LatLng point;
  LoadAudioLinkState(this.audioLink, this.point);
}

class LoadAudioLinkEvent extends BaseEvent {
  final LatLng point;
  LoadAudioLinkEvent(this.point);
}

class AudioLinkFailedEvent extends BaseEvent {
  final LatLng point;
  AudioLinkFailedEvent(this.point);
}

class GetPetMapState extends BaseState {
  MapGeoJsonModel data;
  GetPetMapState(this.data);
}

class MapTaskBloc extends BaseBloc {
  final repository = DiagnosePestsRepository();
  late http.Client client;
  late http.Response response;
  bool _shouldCancelProvinceLoading = false;
  bool _shouldCancelAudioLoading = false;

  // Add unique request tracking
  String? _currentWeatherDetailRequestId;
  String? _currentAudioRequestId;

  MapTaskBloc() {
    client = http.Client();
    _setupEventHandlers();
  }

  void _setupEventHandlers() {
    on<ShowKeyboardEvent>((event, emit) => emit(ShowKeyboardState(event.value)));
    on<LoadTreeTypeEvent>((event, emit) => emit(LoadTreeTypeState(MapConstants.menuTreeTypeModel(event.type))));
    on<LoadImageMapEvent>((event, emit) => emit(LoadImageMapState()));
    on<ChangeRatingStarEvent>((event, emit) => emit(ChangeRatingStarState()));
    on<GetTreeParamsEvent>((event, emit) => emit(GetTreeParamsState(event.nutritionType, event.treeTyle)));
    on<LoadNutritionalTypeEvent>((event, emit) => emit(LoadNutritionalTypeState(MapConstants.menuNutritionalTypeModel())));
    on<ChangeIndexTabEvent>((event, emit) => emit(ChangeIndexTabState()));
    on<ChangeProvinceEvent>((event, emit) => emit(ChangeProvinceState()));
    on<PlayAudioEvent>((event, emit) => emit(PlayAudioState(event.value)));
    on<ShowErrorEvent>((event, emit) => emit(ShowErrorState(event.error)));
    on<GetPetMapEvent>((event, emit) async {
      try {
        emit(const BaseState(isShowLoading: true));
        cancelCurrentRequest();
        response = await client.get(Uri.parse('${Constants().baseUrl}${Constants().apiVersion}diagnostics/pets_map?${event.params}'));
        if (response.statusCode == 200) {
          final data = jsonDecode(response.body);
          emit(GetPetMapState(MapGeoJsonModel(data, sourceId: event.sourceId)));
        }
      } catch (e) {
        logDebug("Error fetching pet map: $e");
      } finally {
        emit(const BaseState(isShowLoading: false));
      }
    });
    on<GetWeatherMapEvent>((event, emit) async {
      emit(const BaseState(isShowLoading: true));
      cancelCurrentRequest();
      response = await client.get(Uri.parse('${Constants().baseUrl}${Constants().apiVersion}weather/weather_in_a_week'));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        emit(GetWeatherMapState(MapGeoJsonModel(data, sourceId: event.sourceId)));
      }
    });
    on<GetNutritionMapEvent>((event, emit) async {
      emit(const BaseState(isShowLoading: true));
      final params = event.nutritionType == 'ec' || event.nutritionType == 'ph' || event.nutritionType == 'om' || event.nutritionType == 'cec'
          ? 'layer_name=do phi nhieu dat&prop_option=${event.nutritionType}&lat=${event.point.latitude}&lng=${event.point.longitude}&layer_name=${event.treeTyle}&prop_option=${event.nutritionType}&lat=${event.point.latitude}&lng=${event.point.longitude}'
          : 'layer_name=${event.treeTyle}&prop_option=${event.nutritionType}&lat=${event.point.latitude}&lng=${event.point.longitude}';
      final api = await http.get(Uri.parse('${Constants().mapUrl}/api/soil_geos/location_info?$params'));
      final response = BaseResponse().fromJson(jsonDecode(api.body), MapResponseModel());
      debugPrint(api.request!.url.toString());
      if (response.data != null) {
        debugPrint(response.data.data.toString());
        final data = MapGeoJsonModel(response.data.data);
        emit(GetNutritionMapState(data, event.point));
      } else {
        emit(const BaseState(isShowLoading: false));
      }
    });
    on<GetDemonstrationParadigmsEvent>((event, emit) async {
      emit(const BaseState(isShowLoading: true));
      cancelCurrentRequest();
      response = await client.get(Uri.parse('${Constants().baseUrl}${Constants().apiVersion}maps/demonstration_paradigms'));
      debugPrint(response.request!.url.toString());
      debugPrint("======>${response.body}");
      emit(const BaseState(isShowLoading: false));
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        emit(GetDemonstrationParadigmsState(MapGeoJsonModel(data, sourceId: event.sourceId, layerId: event.layerId)));
      }
    });
    on<GetDetailDemonstrationParadigmEvent>((event, emit) async {
      emit(const BaseState(isShowLoading: true));
      final response = await ApiClient().getAPI('${Constants().apiVersion}maps/demonstration_paradigms/${event.id}', MapDataModel(), hasHeader: true);
      debugPrint(response.data.toString());
      response.checkOK() ? emit(GetDetailDemonstrationParadigmState(response.data)) : emit(const BaseState());
    });
    on<GetPetMapDetailEvent>((event, emit) async {
      emit(const BaseState(isShowLoading: true));
      final response = await ApiClient().getAPI('${Constants().apiVersion}training_data/detail?classable_type=${event.type}&classable_id=${event.id}', MapDataModel(), hasHeader: true);
      response.checkOK() ? emit(GetPetMapDetailState(response.data)) : emit(const BaseState());
    });
    on<GetListAgenciesEvent>((event, emit) async {
      emit(const BaseState(isShowLoading: true));
      cancelCurrentRequest();
      response = await client.get(Uri.parse('${Constants().baseUrl}${Constants().apiVersion}maps/agencies?agency_type=${event.type}'));
      debugPrint(response.body.toString());
      emit(const BaseState(isShowLoading: false));
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        emit(GetListAgenciesState(MapGeoJsonModel(data, sourceId: event.sourceId, layerId: event.layerId)));
      }
    });
    on<GetDetailAgenciesEvent>((event, emit) async {
      emit(const BaseState(isShowLoading: true));
      final response = await ApiClient().getAPI('${Constants().apiVersion}maps/agencies/${event.id}', MapDataModel(), hasHeader: true);
      response.checkOK() ? emit(GetDetailAgenciesState(response.data)) : emit(const BaseState());
    });
    on<GetReverseGeocodingEvent>((event, emit) async {
      final api = await http.get(Uri.parse('https://map.hainong.vn/api/v1/reverse?lang=vi&point.lon=${event.point.longitude}&point.lat=${event.point.latitude}'));
      final response = jsonDecode(api.body);
      if (response != null) {
        emit(GetReverseGeocodingState(response["features"][0]["properties"]["label"]));
      }
    });
    on<PostRatingEvent>((event, emit) async {
      emit(const BaseState(isShowLoading: true));
      final response = await ApiClient().postAPI("${Constants().apiVersion}maps/comment_maps", 'POST', BaseResponse(),
          body: {"content": event.content, "rate": event.rate.toString(), "commentable_type": event.type, "commentable_id": event.id.toString()},
          realFiles: event.list,
          paramFile: "attachment[file][]");
      emit(PostRatingState(response));
    });
    on<GetCurrentLocationMarketEvent>((event, emit) async {
      try {
        final apiUrl = '${Constants().baseUrl}${Constants().apiVersion}locations/address_full?lat=${event.point.latitude}&lng=${event.point.longitude}';
        final marketClient = http.Client();
        try {
          final response = await marketClient.get(Uri.parse(apiUrl));
          if (response.statusCode == 200) {
            final data = jsonDecode(response.body);
            final address = MapAddressModel.fromJson(data['data']);
            final addressWithLocation = address.copyWith(latitude: event.point.latitude, longitude: event.point.longitude);
            emit(GetCurrentLocationMarketState(addressWithLocation, event.point));
          }
        } finally {
          marketClient.close();
        }
      } catch (e) {
        debugPrint("GetCurrentLocationMarketEvent error: $e");
      }
    });
    on<GetCurrentLocationEvent>((event, emit) async {
      try {
        final apiUrl = '${Constants().baseUrl}${Constants().apiVersion}locations/address_full?lat=${event.point.latitude}&lng=${event.point.longitude}';
        final locationClient = http.Client();
        try {
          final response = await locationClient.get(Uri.parse(apiUrl)).timeout(
                const Duration(seconds: 20),
                onTimeout: () => throw TimeoutException('Location API timeout after 20 seconds', const Duration(seconds: 20)),
              );

          if (response.statusCode == 200) {
            final data = jsonDecode(response.body);
            final address = MapAddressModel.fromJson(data['data']);
            final addressWithLocation = address.copyWith(latitude: event.point.latitude, longitude: event.point.longitude);
            emit(GetCurrentLocationState(addressWithLocation, event.point));
          } else {
            debugPrint("GetCurrentLocationEvent API error - Status: ${response.statusCode}, Body: ${response.body}");
            emit(ShowErrorState(BaseResponse(msg: "Không thể tải thông tin vị trí. Vui lòng thử lại.")));
          }
        } finally {
          locationClient.close();
        }
      } catch (e) {
        debugPrint("GetCurrentLocationEvent error: $e");
        if (e is SocketException) {
          emit(ShowErrorState(BaseResponse(msg: "Lỗi kết nối mạng khi tải vị trí. Vui lòng kiểm tra kết nối internet.")));
        } else if (e is TimeoutException) {
          emit(ShowErrorState(BaseResponse(msg: "Kết nối vị trí bị timeout. Vui lòng thử lại sau.")));
        } else if (e.toString().contains('Connection') || e.toString().contains('cancelled')) {
          emit(ShowErrorState(BaseResponse(msg: "Lỗi kết nối server vị trí. Vui lòng thử lại sau.")));
        } else {
          emit(ShowErrorState(BaseResponse(msg: "Không thể tải thông tin vị trí. Vui lòng thử lại.")));
        }
      } finally {
        emit(const BaseState(isShowLoading: false));
      }
    });
    on<GetLocationEvent>((event, emit) async {
      final response = await ApiClient().getAPI('${Constants().apiVersion}locations/address_full?lat=${event.point.latitude}&lng=${event.point.longitude}', MapAddressModel(), hasHeader: true);
      response.checkOK() ? emit(GetLocationState(response.data, event.point, event.address, event.data)) : emit(const BaseState());
    });
    on<PostImageMapEvent>((event, emit) async {
      emit(const BaseState(isShowLoading: true));
      final response = await ApiClient().postAPI("${Constants().apiVersion}maps/image_maps", 'POST', BaseResponse(),
          body: {"note": event.content, "classable_type": event.type, "classable_id": event.id.toString()}, realFiles: event.list, paramFile: "attachment[file][]");
      emit(PostImageMapState(response));
    });
    on<CreateDiagnostisPestEvent>((event, emit) async {
      emit(const BaseState(isShowLoading: true));
      final response = await repository.createDiagnosticContribute(
          event.files, event.province_id, event.district_id, event.address, event.tree_name, event.pest_name, event.description, event.point.latitude.toString(), event.point.longitude.toString());
      emit(CreateDiagnostisPestSuccessState(response));
    });
    on<LoadCategorysEvent>((event, emit) async {
      final resp = await ApiClient().getAPI('${Constants().apiVersion}diagnostics/categories', TreesModel(passUnknown: true), hasHeader: false);
      if (resp.checkOK()) {
        final List<TreeModel> plants = resp.data.list;
        if (event.name?.isNotEmpty == true) {
          final List<PetModel> list = [];
          final TreeModel plant = plants.firstWhere((element) => element.name == event.name, orElse: () => TreeModel());
          for (var item in plant.diagnostics) {
            list.add(item);
          }
          emit(LoadCategorysState(list));
        } else {
          emit(LoadCategorysFilterState(plants));
        }
      }
    });
    on<LoadProvincesEvent>((event, emit) async {
      final resp = await MapPageRepository().loadProvinces();
      if (resp.checkOK() && resp.data.list.isNotEmpty) {
        emit(LoadProvincesState(resp.data.list));
      }
    });
    on<SendContributeEvent>((event, emit) async {
      emit(const BaseState(isShowLoading: true));
      final response = await ApiClient().postAPI('${Constants().apiVersion}info_diseases', 'POST', ItemModel(),
          body: {
            'address': event.name ?? "",
            'province_id': event.address.provinceId.toString(),
            'district_id': event.address.districtId.toString(),
            'ward_id': event.address.wardId.toString(),
            'lat': event.point.latitude.toString(),
            'lng': event.point.longitude.toString(),
            'crops': jsonEncode(event.crops),
            'zone_crops': event.zone_crops,
            'pH': event.pH,
            'EC': event.EC,
            'CEC': event.CEC,
            'farming_method': event.farming_method
          },
          realFiles: event.files,
          paramFile: "attachment[file][]");
      if (response.checkOK(passString: true)) {
        emit(CreateQuestionState(response.data));
      } else {
        emit(ShowErrorState(response.data));
      }
    });
    on<SendDiseasesPositionEvent>((event, emit) async {
      emit(const BaseState(isShowLoading: true));
      final body = {'info_disease_id': event.id.toString()};
      int i = 0;
      for (var point in event.points) {
        body.putIfAbsent('position[$i][lat]', () => point.latitude.toString());
        body.putIfAbsent('position[$i][long]', () => point.longitude.toString());
        i++;
      }
      dynamic resp = await ApiClient().postAPI('${Constants().apiVersion}info_diseases/user_info_diseases', 'POST', BaseResponse(), body: body);
      if (resp.checkOK(passString: true)) {
        emit(SendDiseasesPositionState(resp));
      }
    });

    on<LoadCurrentDetailWeatherEvent>((event, emit) async {
      emit(const BaseState(isShowLoading: true));
      try {
        Map<String, dynamic> weatherData = {};
        final lat = event.point.latitude.toStringAsFixed(5);
        final lng = event.point.longitude.toStringAsFixed(5);
        cancelCurrentRequest();
        final response = await client.get(Uri.parse('${Constants().baseUrl}${Constants().apiVersion}weather/details?lat=$lat&lng=$lng'));
        if (response.statusCode == 200) {
          final data = jsonDecode(response.body);
          debugPrint(data.toString());
          weatherData = data["data"]["current_date"];
        }
        emit(LoadCurrentDetailWeatherState(weatherData, event.point));
      } catch (e) {
        debugPrint("Lỗi khi tải thông tin thời tiết: $e");
      }
    });

    on<LoadDetailWeatherEvent>((event, emit) async {
      final requestId = DateTime.now().millisecondsSinceEpoch.toString();
      _currentWeatherDetailRequestId = requestId;
      cancelAudioLoading();
      emit(const BaseState(isShowLoading: true));
      try {
        Map<String, dynamic> weatherData = {};
        final lat = event.point.latitude.toStringAsFixed(5);
        final lng = event.point.longitude.toStringAsFixed(5);
        cancelCurrentRequest();
        if (_currentWeatherDetailRequestId != requestId) return;
        final response = await client.get(Uri.parse('${Constants().baseUrl}${Constants().apiVersion}weather/details?lat=$lat&lng=$lng'));
        if (_currentWeatherDetailRequestId != requestId) return;
        if (response.statusCode == 200) {
          final data = jsonDecode(response.body);
          debugPrint(data.toString());
          weatherData = data["data"]["current_date"];
        } else {
          emit(ShowErrorState("Không thể tải thông tin thời tiết"));
          return;
        }
        final hasAudioLink = weatherData['audio_link'] != null && weatherData['audio_link'].toString().isNotEmpty;
        if (_currentWeatherDetailRequestId != requestId) return;
        emit(LoadDetailWeatherState(weatherData, event.point));
        if (!hasAudioLink) {
          emit(LoadingAudioState(true));
          add(LoadAudioLinkEvent(event.point));
        }
      } catch (e) {
        if (_currentWeatherDetailRequestId == requestId) {
          debugPrint("LoadDetailWeatherEvent error: $e");
        }
      }
    });

    on<LoadAudioLinkEvent>((event, emit) async {
      final audioRequestId = DateTime.now().millisecondsSinceEpoch.toString();
      _currentAudioRequestId = audioRequestId;
      cancelCurrentRequest();
      debugPrint('LoadAudioLinkEvent: Started new audio fetch with ID $audioRequestId for ${event.point}');
      _shouldCancelAudioLoading = false;
      final audioData = await _fetchAudioLinkWithRetry(event.point, audioRequestId);
      if (_currentAudioRequestId != audioRequestId) return;
      if (audioData != null && audioData.isNotEmpty) {
        emit(LoadAudioLinkState(audioData, event.point));
        emit(LoadingAudioState(false));
      } else {
        debugPrint('LoadAudioLinkEvent: No audio found after all retries');
      }
    });
    on<AudioLinkFailedEvent>((event, emit) async {
      emit(LoadingAudioState(false));
    });

    on<LoadingAudioEvent>((event, emit) async {
      if (event.value) {
        emit(LoadingAudioState(true));
        await Future.delayed(const Duration(milliseconds: 10000));
      }
      emit(LoadingAudioState(false));
    });
    on<GetPetMapByProvincesEvent>((event, emit) async {
      emit(const BaseState(isShowLoading: true));
      _shouldCancelProvinceLoading = false;
      for (int i = 0; i < event.provinces.length; i++) {
        if (_shouldCancelProvinceLoading) break;
        final province = event.provinces[i];
        add(GetPetMapByProvinceEvent(event.sourceId, event.keyChartName, province.id, event.provinces, i, fromDate: event.fromDate, toDate: event.toDate, diagnosticIds: event.diagnosticIds));
        await Future.delayed(const Duration(milliseconds: 200));
      }
    });

    on<GetPetMapByProvinceEvent>((event, emit) async {
      try {
        if (_shouldCancelProvinceLoading) return;
        String params = 'province_id=${event.provinceId}';
        if (event.fromDate != null && event.fromDate!.isNotEmpty) {
          params += '&from_date=${event.fromDate}';
        }
        if (event.toDate != null && event.toDate!.isNotEmpty) {
          params += '&to_date=${event.toDate}';
        }
        if (event.diagnosticIds != null && event.diagnosticIds!.isNotEmpty) {
          params += '&diagnostic_ids=[${event.diagnosticIds!.join(',')}]';
        }
        response = await client.get(Uri.parse('${Constants().baseUrl}${Constants().apiVersion}diagnostics/pets_map?$params'));
        if (_shouldCancelProvinceLoading) return;
        if (response.statusCode == 200) {
          final data = jsonDecode(response.body);
          final provinceData = MapGeoJsonModel(data, sourceId: "${event.sourceId}_province_${event.provinceId}");
          emit(GetPetMapByProvinceState(event.provinceId, event.currentIndex, event.provinces.length, provinceData));
        } else {
          emit(GetPetMapByProvinceState(event.provinceId, event.currentIndex, event.provinces.length, MapGeoJsonModel({}, sourceId: "${event.sourceId}_province_${event.provinceId}")));
        }
      } catch (e) {
        debugPrint("GetPetMapByProvinceEvent error: $e");
      }
    });
  }

  Future<String?> _fetchAudioLinkWithRetry(LatLng point, String requestId) async {
    const maxRetries = 10;
    const baseDelay = Duration(seconds: 1);
    cancelCurrentRequest();
    for (int attempt = 0; attempt < maxRetries; attempt++) {
      if (_shouldCancelAudioLoading || _currentAudioRequestId != requestId) {
        debugPrint('Audio loading cancelled at attempt ${attempt + 1} for request $requestId');
        return null;
      }
      try {
        final lat = point.latitude.toStringAsFixed(5);
        final lng = point.longitude.toStringAsFixed(5);
        final urlAudio = '${Constants().baseUrl}${Constants().apiVersion}weather/details_audio_link?lat=$lat&lng=$lng';
        final audioResponse = await client.get(Uri.parse(urlAudio));
        if (_shouldCancelAudioLoading || _currentAudioRequestId != requestId) return null;
        if (audioResponse.statusCode == 200) {
          final audioData = jsonDecode(audioResponse.body);
          if (audioData['success'] == true && audioData['data'] != null && audioData['data'].toString().isNotEmpty) {
            debugPrint('audioData: ${audioData.toString()}');
            return audioData['data'];
          } else {
            debugPrint('Audio link fetch attempt ${attempt + 1} not found at lat=$lat, lng=$lng');
            if (attempt < maxRetries - 1) {
              await Future.delayed(baseDelay * (attempt + 1));
            } else {
              debugPrint('All $maxRetries retries exhausted - audio link not found');
              if (_currentAudioRequestId == requestId) {
                add(AudioLinkFailedEvent(point));
              }
            }
          }
        } else {
          if (attempt < maxRetries - 1) {
            await Future.delayed(baseDelay * (attempt + 1));
          } else {
            if (_currentAudioRequestId == requestId) {
              add(AudioLinkFailedEvent(point));
            }
          }
        }
      } catch (e) {
        if (attempt < maxRetries - 1) {
          await Future.delayed(baseDelay * (attempt + 1));
        } else {
          if (_currentAudioRequestId == requestId) {
            add(AudioLinkFailedEvent(point));
          }
        }
      }
    }
    return null;
  }

  void cancelCurrentRequest() {
    client.close();
    client = http.Client();
  }

  void cancelProvinceLoading() => _shouldCancelProvinceLoading = true;

  void cancelAudioLoading() {
    _shouldCancelAudioLoading = true;
    _currentAudioRequestId = null;
    debugPrint('MapTaskBloc: Audio loading cancelled - cleared currentAudioRequestId');
  }

  void cancelWeatherDetailLoading() {
    _currentWeatherDetailRequestId = null;
    cancelAudioLoading();
    debugPrint('MapTaskBloc: Weather detail loading cancelled');
  }
}

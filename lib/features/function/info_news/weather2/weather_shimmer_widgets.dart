import 'package:flutter/material.dart';
import 'package:hainong/common/ui/animation/shimmer_animation.dart';
import 'package:hainong/common/ui/import_lib_ui.dart';

class WeatherShimmerWidgets {
  // Main weather icon shimmer
  static Widget weatherIcon() {
    return ShimmerCircle(size: 380.sp);
  }

  // Weather status text shimmer
  static Widget weatherStatus() {
    return ShimmerText(width: 280.sp, height: 52.sp);
  }

  // Temperature shimmer
  static Widget temperature() {
    return ShimmerText(width: 240.sp, height: 240.sp);
  }

  // Temperature min/max shimmer
  static Widget tempMinMax() {
    return ShimmerText(width: 60.sp, height: 40.sp);
  }

  // Weather detail item shimmer (wind, humidity, etc.)
  static Widget weatherDetailItem() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        ShimmerBox(width: 60.sp, height: 60.sp, borderRadius: BorderRadius.circular(8.sp)),
        <PERSON><PERSON><PERSON><PERSON>(height: 20.sp),
        Container(
          height: 160.sp,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              ShimmerText(width: 80.sp, height: 32.sp),
              SizedBox(height: 10.sp),
              ShimmerText(width: 60.sp, height: 52.sp),
            ],
          ),
        ),
      ],
    );
  }

  // Hourly weather item shimmer
  static Widget hourlyWeatherItem() {
    return Container(
      width: 140.sp,
      margin: EdgeInsets.only(right: 20.sp),
      padding: EdgeInsets.all(20.sp),
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(20.sp),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          ShimmerText(width: 60.sp, height: 32.sp),
          ShimmerBox(width: 80.sp, height: 80.sp, borderRadius: BorderRadius.circular(8.sp)),
          ShimmerText(width: 50.sp, height: 36.sp),
        ],
      ),
    );
  }

  // 14-day forecast item shimmer
  static Widget fourteenDayItem() {
    return Container(
      margin: EdgeInsets.only(bottom: 20.sp),
      padding: EdgeInsets.all(26.sp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(26.sp),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          ShimmerText(width: 80.sp, height: 42.sp),
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              ShimmerBox(width: 100.sp, height: 100.sp, borderRadius: BorderRadius.circular(8.sp)),
              SizedBox(width: 20.sp),
              ShimmerText(width: 120.sp, height: 36.sp),
            ],
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              ShimmerText(width: 60.sp, height: 48.sp),
              ShimmerText(width: 50.sp, height: 36.sp),
            ],
          ),
        ],
      ),
    );
  }

  // Location name shimmer
  static Widget locationName() {
    return ShimmerText(width: 620.sp, height: 62.sp);
  }

    static Widget dateName() {
    return ShimmerText(width: 420.sp, height: 52.sp);
  }

  // Complete weather details row shimmer
  static Widget weatherDetailsRow() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        weatherDetailItem(),
        weatherDetailItem(),
        weatherDetailItem(),
        weatherDetailItem(),
      ],
    );
  }

  // Hourly forecast list shimmer
  static Widget hourlyForecastList() {
    return SizedBox(
      height: 240.sp,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: 5,
        itemBuilder: (context, index) => hourlyWeatherItem(),
      ),
    );
  }

  // 14-day forecast list shimmer
  static Widget fourteenDayForecastList() {
    return SizedBox(
      height: 700.sp,
      child: ListView.builder(
        shrinkWrap: true,
        padding: EdgeInsets.zero,
        physics: const BouncingScrollPhysics(),
        itemCount: 7,
        itemBuilder: (context, index) => fourteenDayItem(),
      ),
    );
  }
}
import 'package:hainong/common/api_client.dart';
import 'package:hainong/common/base_bloc.dart';
import 'package:hainong/common/base_response.dart';
import 'package:hainong/common/constants.dart';
import 'package:hainong/common/util/util.dart';
import 'package:hainong/features/profile/profile_bloc.dart';
import 'package:hainong/features/signup/sign_up_repository.dart';

import '../weather/weather_list_province_model.dart';
import '../weather/weather_model.dart';
import 'models/user_location_model.dart';

class LoadWeatherAgriculturalEvent extends BaseEvent {
  final String lat;
  final String lon;
  final bool openModule, isSendLocation;
  LoadWeatherAgriculturalEvent(this.lat, this.lon, {this.openModule = false, this.isSendLocation = false});
}

class LoadWeatherAgriculturalState extends BaseState {
  final BaseResponse response;
  LoadWeatherAgriculturalState(this.response);
}

class LoadAudioWeatherAgriculturalEvent extends BaseEvent {
  final String lat;
  final String lon;
  final bool isRequest;
  LoadAudioWeatherAgriculturalEvent(this.lat, this.lon, {this.isRequest = false});
}

class LoadAudioWeatherAgriculturalState extends BaseState {
  final String data;
  final bool isRequest;
  LoadAudioWeatherAgriculturalState(this.data, this.isRequest);
}

class GetLatLonAddressAgriculturalEvent extends BaseEvent {
  final String address;
  GetLatLonAddressAgriculturalEvent(this.address);
}

class GetLatLonAddressAgriculturalState extends BaseState {
  final BaseResponse response;
  final String lat;
  final String lon;
  GetLatLonAddressAgriculturalState(this.response, this.lat, this.lon);
}

class LoadListWeatherAgriculturalEvent extends BaseEvent {
  final int? page;
  LoadListWeatherAgriculturalEvent({this.page});
}

class LoadListWeatherAgriculturalState extends BaseState {
  final BaseResponse response;
  const LoadListWeatherAgriculturalState(this.response);
}

class PlayAudioAgriculturalEvent extends BaseEvent {
  final bool value;
  PlayAudioAgriculturalEvent(this.value);
}

class PlayAudioAgriculturalState extends BaseState {
  final bool value;
  PlayAudioAgriculturalState(this.value);
}

class LoadingAudioAgriculturalEvent extends BaseEvent {
  final bool value;
  LoadingAudioAgriculturalEvent(this.value);
}

class LoadingAudioAgriculturalState extends BaseState {
  final bool value;
  LoadingAudioAgriculturalState(this.value);
}

class LoadNegativeWeatherStatusAgriculturalEvent extends BaseEvent {}

class ChangeNegativeWeatherStatusAgriculturalEvent extends BaseEvent {
  final int id;
  final String status;
  ChangeNegativeWeatherStatusAgriculturalEvent(this.id, this.status);
}

class LoadNegativeWeatherStatusAgriculturalState extends BaseState {
  dynamic response;
  LoadNegativeWeatherStatusAgriculturalState(this.response);
}

class ChangeNegativeWeatherStatusAgriculturalState extends BaseState {
  final BaseResponse response;
  final String status;
  ChangeNegativeWeatherStatusAgriculturalState(this.response, this.status);
}

// User Locations Events and States
class LoadUserLocationsEvent extends BaseEvent {}

class LoadUserLocationsState extends BaseState {
  final BaseResponse response;
  LoadUserLocationsState(this.response);
}

class AddUserLocationEvent extends BaseEvent {
  final double lat;
  final double lng;
  final String address;
  AddUserLocationEvent(this.lat, this.lng, this.address);
}

class AddUserLocationState extends BaseState {
  final BaseResponse response;
  AddUserLocationState(this.response);
}

class UpdateUserLocationEvent extends BaseEvent {
  final int id;
  final double lat;
  final double lng;
  final String address;
  UpdateUserLocationEvent(this.id, this.lat, this.lng, this.address);
}

class UpdateUserLocationState extends BaseState {
  final BaseResponse response;
  UpdateUserLocationState(this.response);
}

class DeleteUserLocationEvent extends BaseEvent {
  final int id;
  DeleteUserLocationEvent(this.id);
}

class DeleteUserLocationState extends BaseState {
  final BaseResponse response;
  DeleteUserLocationState(this.response);
}

class SetCurrentLocationEvent extends BaseEvent {
  final int userLocationId;
  SetCurrentLocationEvent(this.userLocationId);
}

class SetCurrentLocationState extends BaseState {
  final BaseResponse response;
  SetCurrentLocationState(this.response);
}

class WeatherAgriculturalBloc extends BaseBloc {
  WeatherAgriculturalBloc() : super(hasGetAddressMap: true) {
    on<LoadingAudioAgriculturalEvent>((event, emit) async {
      if (event.value) {
        emit(LoadingAudioAgriculturalState(true));
        await Future.delayed(const Duration(milliseconds: 10000));
      }
      emit(LoadingAudioAgriculturalState(false));
    });

    on<LoadWeatherAgriculturalEvent>((event, emit) async {
      emit(const BaseState(isShowLoading: true));
      String temp = '';
      if (event.lat.isNotEmpty && event.lon.isNotEmpty) {
        temp = '?lat=${event.lat}&lng=${event.lon}${event.openModule ? "&open_module=true" : ""}';
      }
      final response = await ApiClient().getAPI(Constants().apiVersion + 'weather/details$temp', WeatherModel(), hasHeader: true);
      emit(LoadWeatherAgriculturalState(response));

      if (event.isSendLocation) {
        ApiClient().postAPI(Constants().apiVersion + 'weather/user_location', 'POST', BaseResponse(), body: {'lat': event.lat, 'lng': event.lon});
      }
    });

    on<LoadListWeatherAgriculturalEvent>((event, emit) async {
      emit(const BaseState(isShowLoading: true));
      final response = await ApiClient().getAPI(Constants().apiVersion + 'weather/weather_places', WeatherListModels(), hasHeader: false);
      emit(LoadListWeatherAgriculturalState(response));
    });

    on<GetLatLonAddressAgriculturalEvent>((event, emit) async {
      emit(const BaseState(isShowLoading: true));
      final response = await ApiClient().getAPI(Constants().apiVersion + 'locations/latlong?address=${event.address}', WeatherListModel(), hasHeader: false);
      if (response.checkOK()) {
        emit(GetLatLonAddressAgriculturalState(response, response.data.lat, response.data.lng));
      } else {
        emit(GetLatLonAddressAgriculturalState(response, '', ''));
      }
    });

    on<LoadAudioWeatherAgriculturalEvent>((event, emit) async {
      emit(const BaseState(isShowLoading: true));
      String temp = '';
      if (event.lat.isNotEmpty && event.lon.isNotEmpty) {
        temp = '?lat=${event.lat}&lng=${event.lon}';
      }
      final response = await ApiClient().getAPI(Constants().apiVersion + 'weather/details_audio_link$temp', BaseResponse(), hasHeader: true);
      emit(LoadAudioWeatherAgriculturalState(response.data != "null" && response.data != null ? response.data : '', event.isRequest));
    });

    on<LoadProvinceProfileEvent>((event, emit) async {
      emit(const BaseState(isShowLoading: true));
      final response = await SignUpRepository().loadProvince();
      emit(LoadProvinceProfileState(response));
    });

    on<PlayAudioAgriculturalEvent>((event, emit) => emit(PlayAudioAgriculturalState(event.value)));

    on<LoadNegativeWeatherStatusAgriculturalEvent>((event, emit) async {
      final resp = await ApiClient().getData('negative_weather_notices');
      if (resp != null && Util.checkKeyFromJson(resp, 'status') && Util.checkKeyFromJson(resp, 'id')) {
        emit(LoadNegativeWeatherStatusAgriculturalState(resp));
      }
    });

    on<ChangeNegativeWeatherStatusAgriculturalEvent>((event, emit) async {
      emit(const BaseState(isShowLoading: true));
      final resp = await ApiClient().postJsonAPI(Constants().apiVersion + 'negative_weather_notices/${event.id}', BaseResponse(), {'status': event.status}, method: 'PUT');
      if (resp.checkOK(passString: true)) {
        emit(ChangeNegativeWeatherStatusAgriculturalState(resp, event.status));
      }
    });

    // User Locations Handlers
    on<LoadUserLocationsEvent>((event, emit) async {
      emit(const BaseState(isShowLoading: true));
      final response = await ApiClient().getAPI('${Constants().apiVersion}user_locations', UserLocationListModel(), hasHeader: true);
      if (response.checkOK()) {
        emit(LoadUserLocationsState(response));
      }
    });

    on<AddUserLocationEvent>((event, emit) async {
      emit(const BaseState(isShowLoading: true));
      final response = await ApiClient().postAPI('${Constants().apiVersion}user_locations', 'POST', BaseResponse(),
          body: {
            'lat': event.lat.toString(),
            'lng': event.lng.toString(),
            'address': event.address,
          },
          hasHeader: true);
      emit(AddUserLocationState(response));
    });

    on<UpdateUserLocationEvent>((event, emit) async {
      emit(const BaseState(isShowLoading: true));
      final response = await ApiClient().postAPI('${Constants().apiVersion}user_locations/${event.id}', 'PUT', BaseResponse(),
          body: {
            'lat': event.lat.toString(),
            'lng': event.lng.toString(),
            'address': event.address,
          },
          hasHeader: true);

      emit(UpdateUserLocationState(response));
    });

    on<DeleteUserLocationEvent>((event, emit) async {
      emit(const BaseState(isShowLoading: true));
      final response = await ApiClient().postAPI('${Constants().apiVersion}user_locations/${event.id}', 'DELETE', BaseResponse(), hasHeader: true);
      emit(DeleteUserLocationState(response));
    });

    on<SetCurrentLocationEvent>((event, emit) async {
      emit(const BaseState(isShowLoading: true));
      final response =
          await ApiClient().postAPI('${Constants().apiVersion}user_locations/current_location', 'PUT', BaseResponse(), body: {'user_location_id': event.userLocationId.toString()}, hasHeader: true);
      emit(SetCurrentLocationState(response));
    });
  }
}

import 'dart:math';

import 'package:hainong/common/ui/button_image_widget.dart';
import 'package:hainong/common/ui/import_lib_ui.dart';
import 'package:hainong/common/ui/loading.dart';
import 'package:trackasia_gl/trackasia_gl.dart';

import '../../tool/map_task/components/map_weather/weather_constants.dart';
import '../../tool/map_task/components/map_weather/weather_map_controller.dart';
import '../../tool/map_task/map_task_bloc.dart';
import '../../tool/map_task/models/map_address_model.dart';
import '../../tool/map_task/models/map_response_model.dart';
import '../weather/weather_model.dart';
import 'components/weather_map_panel_widget.dart';

class WeatherMapPage extends BasePage {
  final WeatherModel? currentWeatherData;
  final String? currentLatitude;
  final String? currentLongitude;

  WeatherMapPage({
    Key? key,
    this.currentWeatherData,
    this.currentLatitude,
    this.currentLongitude,
  }) : super(key: key, pageState: _WeatherMapPageState());
}

class _WeatherMapPageState extends BasePageState with TickerProviderStateMixin {
  // Map Controllers
  TrackAsiaMapController? mapController;
  late WeatherMapController _weatherController;

  // State Management
  bool _isLoadingWeatherDetail = false;
  bool _isWeatherDetailBottomSheetShowing = false;

  // Weather Data
  WeatherModel? currentWeatherData;
  String? currentLatitude;
  String? currentLongitude;

  // UI State Notifiers
  final ValueNotifier<MapAddressModel> _addressMap = ValueNotifier<MapAddressModel>(MapAddressModel());
  final ValueNotifier<bool> _isPlay = ValueNotifier(false);

  // Camera Position State
  final LatLng _initialCameraPosition = const LatLng(15.7146441, 106.401633);
  final double _initialZoom = 4.8;

  @override
  void initState() {
    super.initState();

    // Lưu thông tin thời tiết hiện tại từ widget
    final widget = this.widget as WeatherMapPage;
    currentWeatherData = widget.currentWeatherData;
    currentLatitude = widget.currentLatitude;
    currentLongitude = widget.currentLongitude;

    _initializeWeatherController();
    _initializeBloc();
  }

  @override
  void dispose() {
    _weatherController.removeListener(_weatherControllerListener);
    _resetWeatherDetailState();
    if (bloc is MapTaskBloc) {
      (bloc as MapTaskBloc).cancelWeatherDetailLoading();
      (bloc as MapTaskBloc).cancelCurrentRequest();
    }
    _weatherController.dispose();
    super.dispose();
  }

  void _initializeWeatherController() {
    _weatherController = WeatherMapController();
    // Khởi tạo timelineHours ngay lập tức để tránh LateInitializationError
    _weatherController.initialize(
      tickerProvider: this,
      context: context,
      bloc: null,
      addressMap: null,
      isPlay: null,
      clusterSource: null,
    );
  }

  void _weatherControllerListener() {
    if (mounted) {
      setState(() {});
    }
  }

  void _initializeBloc() {
    bloc = MapTaskBloc();
    bloc!.stream.listen(_handleBlocStates);
  }

  void _handleBlocStates(BaseState state) {
    if (state is LoadDetailWeatherState) {
      _isLoadingWeatherDetail = false;

      final weatherData = MapGeoJsonModel(state.data);
      _weatherController.showWeatherDetailBottomSheet(
        weatherData,
        state.point,
        () {
          _isWeatherDetailBottomSheetShowing = false;
        },
      );
      _isWeatherDetailBottomSheetShowing = true;
    } else if (state is LoadCurrentDetailWeatherState) {
      final weatherData = MapGeoJsonModel(state.data);
      _weatherController.setCurrentWeatherData(weatherData, state.point);
    } else if (state is LoadAudioLinkState) {
      _weatherController.updateExternalAudioLink(state.audioLink);
      _weatherController.handleAudioLoadingSuccess();
    } else if (state is AudioLinkFailedEvent) {
      _weatherController.handleAudioLoadingFailed();
    } else if (state is LoadingAudioState) {
      _weatherController.handleLoadingStateChange(state.value);
    }
  }

  void _resetWeatherDetailState() {
    _isLoadingWeatherDetail = false;
    _isWeatherDetailBottomSheetShowing = false;
  }

  Future<void> _onMapCreated(TrackAsiaMapController initialMap) async {
    mapController = initialMap;
    _weatherController.setMapController(mapController);

    // Sử dụng thông tin thời tiết hiện tại nếu có
    if (currentLatitude != null && currentLongitude != null) {
      final currentPosition = LatLng(double.parse(currentLatitude!), double.parse(currentLongitude!));
      await mapController!.animateCamera(CameraUpdate.newLatLngZoom(currentPosition, 10.0));

      _weatherController.updateCameraFromWebView(
        10.0,
        currentPosition.longitude,
        currentPosition.latitude,
      );
    } else if (mapController?.cameraPosition != null) {
      final cameraPosition = mapController!.cameraPosition!;
      _weatherController.updateCameraFromWebView(
        cameraPosition.zoom,
        cameraPosition.target.longitude,
        cameraPosition.target.latitude,
      );
    }

    mapController?.requestMyLocationLatLng();

    // Initialize weather map after map is created
    await _initializeWeatherMap();
  }

  Future<void> _initializeWeatherMap() async {
    _weatherController.addListener(_weatherControllerListener);

    // Truyền dữ liệu thời tiết hiện tại nếu có
    LatLng? currentPosition;
    if (currentLatitude != null && currentLongitude != null) {
      currentPosition = LatLng(double.parse(currentLatitude!), double.parse(currentLongitude!));
    }

    _weatherController.initializeWeatherMap(() {
      setState(() {});
    }, currentPosition: currentPosition, currentWeatherData: currentWeatherData);
  }

  Future<void> _handleMapFeatureTap(Point<double> point, LatLng coordinates) async {
    if (_isLoadingWeatherDetail || _isWeatherDetailBottomSheetShowing) {
      return;
    }
    if (bloc is MapTaskBloc) {
      (bloc as MapTaskBloc).cancelWeatherDetailLoading();
    }
    _resetWeatherDetailState();

    await _weatherController.handleWeatherFeatureTap(point);
  }

  @override
  Widget build(BuildContext context, {Color color = Colors.white}) {
    super.build(context, color: color);
    return Scaffold(
      body: Stack(
        children: [
          _buildTrackasiaMapWidget(),
          Loading(bloc),
          _buildWeatherPanelWidget(),
          _buildWeatherControlButtons(),
        ],
      ),
    );
  }

  Widget _buildTrackasiaMapWidget() {
    return TrackAsiaMap(
      minMaxZoomPreference: const MinMaxZoomPreference(1, 24),
      styleString: Constants().styleMap,
      zoomGesturesEnabled: true,
      compassEnabled: false,
      myLocationEnabled: false,
      initialCameraPosition: CameraPosition(target: _initialCameraPosition, zoom: _initialZoom),
      onMapCreated: _onMapCreated,
      onMapClick: _handleMapFeatureTap,
      onCameraIdle: () {
        if (mapController != null) {
          _weatherController.syncCameraToWebView();
        }
      },
    );
  }

  Widget _buildWeatherPanelWidget() {
    return AnimatedBuilder(
      animation: _weatherController,
      builder: (context, child) {
        return WeatherPanelWidget(
          selectedWeatherLayer: _weatherController.selectedWeatherLayer,
          showWeatherPanel: _weatherController.showWeatherPanel,
          isLoading: _weatherController.isWeatherLoading,
          onWeatherLayerChanged: _weatherController.updateWeatherLayer,
          onTogglePanel: _weatherController.toggleWeatherPanel,
          onRefreshWeather: () {},
          isTimelineActive: _weatherController.isTimelineActive,
          isPlaying: _weatherController.isPlaying,
          currentTimeIndex: _weatherController.currentTimeIndex,
          timelineHours: _weatherController.timelineHours,
          onTogglePlayPause: _weatherController.togglePlayPause,
          onTimelineSeek: _weatherController.onTimelineSeek,
          onResetTimeline: _weatherController.resetTimeline,
          onToggleTimeline: () {
            _weatherController.toggleTimelineVisibility();
            _weatherController.toggleVerticalScaleVisibility();
          },
          currentWeatherData: _weatherController.currentWeatherData,
          currentWeatherPoint: _weatherController.currentWeatherPoint,
          weatherController: _weatherController,
          slideAnimation: _weatherController.weatherSlideAnimation ?? const AlwaysStoppedAnimation(Offset.zero),
        );
      },
    );
  }

  Widget _buildWeatherControlButtons() {
    bool shouldShowCloudButton = _weatherController.selectedWeatherLayer != WeatherConstants.wind;

    return Positioned(
      top: WidgetsBinding.instance.window.padding.top.sp - 12.sp,
      right: 16.sp,
      child: Column(
        children: [
          if (shouldShowCloudButton) ...[
            _buildWeatherControlButton(
              icon: Icons.wb_sunny,
              isActive: _weatherController.weatherIconsVisible,
              onTap: _toggleWeatherIconsVisibility,
            ),
            SizedBox(height: 20.sp),
          ],
          _buildWeatherControlButton(
            icon: Icons.timeline,
            isActive: (_weatherController.timelineVisible || _weatherController.verticalScaleVisible),
            onTap: _toggleTimelineAndScale,
          ),
        ],
      ),
    );
  }

  Widget _buildWeatherControlButton({
    required IconData icon,
    required bool isActive,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 100.sp,
        height: 100.sp,
        decoration: BoxDecoration(
          color: isActive ? const Color(0xFF2BCE85) : Colors.white.withOpacity(0.9),
          borderRadius: BorderRadius.circular(30.sp),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 4.sp,
              offset: Offset(0, 2.sp),
            ),
          ],
        ),
        child: Icon(
          icon,
          color: isActive ? Colors.white : Colors.black87,
          size: 60.sp,
        ),
      ),
    );
  }

  void _toggleTimelineAndScale() {
    _weatherController.toggleTimelineVisibility();
    _weatherController.toggleVerticalScaleVisibility();
  }

  void _toggleWeatherIconsVisibility() {
    _weatherController.toggleWeatherIconsVisibility();
  }
}

import 'package:hainong/common/util/util.dart';

class UserLocationListModel {
  final List<UserLocationModel> list = [];

  UserLocationListModel fromJson(json) {
    if (json != null && json.isNotEmpty) json.forEach((v) => list.add(UserLocationModel().fromJson(v)));
    return this;
  }
}

class UserLocationModel {
  int id = -1;
  int userId = -1;
  double lat = 0.0;
  double lng = 0.0;
  String? deletedAt;
  String createdAt = '';
  String updatedAt = '';
  String fullAddress = '';
  String? street;
  String wardName = '';
  String districtName = '';
  String provinceName = '';
  int manualInfo = 0;
  int provinceId = -1;
  int districtId = -1;
  int wardId = -1;
  String weatherStatus = '';
  bool isFavorite = false;
  String weatherStatusIcon = '';
  double temp = 0.0;

  UserLocationModel({
    this.id = -1,
    this.userId = -1,
    this.lat = 0.0,
    this.lng = 0.0,
    this.deletedAt,
    this.createdAt = '',
    this.updatedAt = '',
    this.fullAddress = '',
    this.street,
    this.wardName = '',
    this.districtName = '',
    this.provinceName = '',
    this.manualInfo = 0,
    this.provinceId = -1,
    this.districtId = -1,
    this.wardId = -1,
    this.weatherStatus = '',
    this.isFavorite = false,
    this.weatherStatusIcon = '',
    this.temp = 0.0,
  });

  UserLocationModel fromJson(json) {
    id = Util.getValueFromJson(json, 'id', -1);
    userId = Util.getValueFromJson(json, 'user_id', -1);
    lat = Util.getValueFromJson(json, 'lat', 0.0);
    lng = Util.getValueFromJson(json, 'lng', 0.0);
    deletedAt = Util.getValueFromJson(json, 'deleted_at', null);
    createdAt = Util.getValueFromJson(json, 'created_at', '');
    updatedAt = Util.getValueFromJson(json, 'updated_at', '');
    fullAddress = Util.getValueFromJson(json, 'full_address', '');
    street = Util.getValueFromJson(json, 'street', null);
    wardName = Util.getValueFromJson(json, 'ward_name', '');
    districtName = Util.getValueFromJson(json, 'district_name', '');
    provinceName = Util.getValueFromJson(json, 'province_name', '');
    manualInfo = Util.getValueFromJson(json, 'manual_info', 0);
    provinceId = Util.getValueFromJson(json, 'province_id', -1);
    districtId = Util.getValueFromJson(json, 'district_id', -1);
    wardId = Util.getValueFromJson(json, 'ward_id', -1);
    weatherStatus = Util.getValueFromJson(json, 'weather_status', '');
    isFavorite = Util.getValueFromJson(json, 'is_favorite', false);
    weatherStatusIcon = Util.getValueFromJson(json, 'weather_status_icon', '');
    temp = Util.getValueFromJson(json, 'temp', 0.0);
    return this;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'lat': lat,
      'lng': lng,
      'deleted_at': deletedAt,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'full_address': fullAddress,
      'street': street,
      'ward_name': wardName,
      'district_name': districtName,
      'province_name': provinceName,
      'manual_info': manualInfo,
      'province_id': provinceId,
      'district_id': districtId,
      'ward_id': wardId,
      'weather_status': weatherStatus,
      'is_favorite': isFavorite,
      'weather_status_icon': weatherStatusIcon,
      'temp': temp,
    };
  }

  String get formattedTemperature => '${temp.toInt()}°';

  String get shortAddress {
    List<String> parts = [];
    if (wardName.isNotEmpty) parts.add(wardName);
    if (districtName.isNotEmpty) parts.add(districtName);
    if (provinceName.isNotEmpty) parts.add(provinceName);
    return parts.join(', ');
  }
}

import 'dart:async';
import 'dart:typed_data';

import 'package:geolocator/geolocator.dart';
import 'package:hainong/common/count_down_bloc.dart';
import 'package:hainong/common/database_helper.dart';
import 'package:hainong/common/ui/banner_2nong.dart';
import 'package:hainong/common/ui/button_image_widget.dart';
import 'package:hainong/common/ui/ignore_button.dart';
import 'package:hainong/common/ui/image_network_asset.dart';
import 'package:hainong/common/ui/import_lib_ui.dart';
import 'package:hainong/common/ui/label_custom.dart';
import 'package:hainong/common/ui/loading.dart';
import 'package:hainong/common/ui/popup_address.dart';
import 'package:hainong/features/login/login_onetime_page.dart';
import 'package:just_audio/just_audio.dart';

import '../weather/ui/dialog_list_address.dart';
import '../weather/ui/weather_setting_notification.dart';
import '../weather/weather_list_province_model.dart';
import '../weather/weather_model.dart';
import 'weather_agricultural_bloc.dart';
import 'weather_location_management_page.dart';
import 'weather_map_page.dart';
import 'weather_shimmer_widgets.dart';

class WeatherAgriculturalPage extends BasePage {
  WeatherAgriculturalPage({super.key}) : super(pageState: _WeatherAgriculturalPageState());
}

extension StringCasingExtension on String {
  String toCapitalized() => length > 0 ? '${this[0].toUpperCase()}${substring(1).toLowerCase()}' : '';
  String toTitleCase() => replaceAll(RegExp(' +'), ' ').split(' ').map((str) => str.toCapitalized()).join(' ');
}

class _WeatherAgriculturalPageState extends BasePageState {
  final GlobalKey _globalKey = GlobalKey();
  WeatherModel _weatherData = WeatherModel();
  String _audioWeather = '', _provinceSelectId = '0', _negativeNotices = 'disagree';
  AudioPlayer _player = AudioPlayer();
  late String latitude, longitude, curLat, curLng;
  bool _isPlay = false, _showFourteenDays = false;
  List<WeatherListModel> _listWeather = [];
  int _count = 0, _negativeNoticeId = -1, _selectedHourIndex = 0;
  Uint8List? _screenFile;
  bool? _isPause;
  CurrentWeatherGroup? _selectedHourData;

  @override
  void dispose() {
    if (constants.isLogin) constants.indexPage = null;
    try {
      _listWeather.clear();
      _weatherData.nextDay.clear();
      _weatherData.currentDate.currentWeatherGroup.clear();
    } catch (_) {}
    try {
      if (_player.playing) _player.stop().whenComplete(() => _player.dispose());
    } catch (_) {}
    super.dispose();
  }

  @override
  void initState() {
    bloc = WeatherAgriculturalBloc();
    bloc!.stream.listen((state) async {
      if (state is LoadWeatherAgriculturalState) {
        isResponseNotError(state.response);
        if (_screenFile != null) _screenFile = null;
        _count++;
        if (_count < 2) bloc!.add(LoadingEvent(true));
        _audioWeather = '';
        if (state.response.data is WeatherModel) {
          _weatherData = state.response.data;
          _selectedHourIndex = 0;
          _selectedHourData = _weatherData.currentDate.currentWeatherGroup.isNotEmpty ? _weatherData.currentDate.currentWeatherGroup[0] : null;
        }
        if (_weatherData.currentDate.audio_link.isEmpty) {
          bloc!.add(LoadAudioWeatherAgriculturalEvent(latitude, longitude));
        } else {
          _autoPlayAudio(_weatherData.currentDate.audio_link);
        }
      } else if (state is LoadListWeatherAgriculturalState) {
        _count++;
        if (state.response.data is WeatherListModels) _listWeather = state.response.data.list;
        if (_count < 2)
          bloc!.add(LoadingEvent(true));
        else
          _playPauseAudio();
      } else if (state is LoadAudioWeatherAgriculturalState && state.data.isNotEmpty) {
        _autoPlayAudio(state.data);
      } else if (state is GetLatLonAddressAgriculturalState && isResponseNotError(state.response)) {
        _isPlay = false;
        _audioWeather = '';
        await _player.stop();
        latitude = state.lat;
        longitude = state.lon;
        bloc!.add(LoadWeatherAgriculturalEvent(latitude, longitude));
      } else if (state is LoadNegativeWeatherStatusAgriculturalState) {
        _negativeNotices = state.response['status'] ?? 'disagree';
        _negativeNoticeId = state.response['id'] ?? -1;
      } else if (state is ChangeNegativeWeatherStatusAgriculturalState) {
        _negativeNotices = state.status;
      }
    });
    super.initState();
    _checkAddress();
    Geolocator.getCurrentPosition().then((value) {
      _audioWeather = '';
      latitude = curLat = value.latitude.toString();
      longitude = curLng = value.longitude.toString();
      bloc!.add(LoadWeatherAgriculturalEvent(latitude, longitude, openModule: true, isSendLocation: constants.isLogin));
    });
    bloc!.add(LoadListWeatherAgriculturalEvent());
    bloc!.add(LoadNegativeWeatherStatusAgriculturalEvent());
  }

  String _getWeatherBackground() {
    final iconToUse = _selectedHourData != null ? _selectedHourData!.weatherStatusIcon : _weatherData.currentDate.weatherStatusIcon;
    if (iconToUse.isEmpty) return 'assets/images/v12/bg_weather_sunny.png';
    final icon = iconToUse.toLowerCase();
    if (icon.contains('rain') || icon.contains('drizzle')) {
      return 'assets/images/v12/bg_weather_rain.png';
    } else if (icon.contains('storm') || icon.contains('thunder')) {
      return 'assets/images/v12/bg_weather_stormy.png';
    } else if (icon.contains('cloud')) {
      return 'assets/images/v12/bg_weather_no_sunny.png';
    } else if (icon.contains('night') || icon.contains('moon')) {
      return 'assets/images/v12/bg_weather_dark.png';
    } else {
      return 'assets/images/v12/bg_weather_sunny.png';
    }
  }

  void _selectOption() async {
    if (bloc!.state.isShowLoading) return;
    if (constants.isLogin) {
      UtilUI.goToNextPage(
          context,
          WeatherLocationManagementPage(
            currentWeatherData: _weatherData,
            currentLatitude: latitude,
            currentLongitude: longitude,
          ));
    } else {
      UtilUI.showCustomDialog(context, MultiLanguage.get(LanguageKey().msgLoginOrCreate)).whenComplete(() {
        UtilUI.logout();
        UtilUI.clearAllPages(context);
        UtilUI.goToPage(context, LoginOneTimePage(), null);
      });
    }
  }

  void _navigateToWeatherMap() {
    if (bloc!.state.isShowLoading) return;
    UtilUI.goToNextPage(
        context,
        WeatherMapPage(
          currentWeatherData: _weatherData,
          currentLatitude: latitude,
          currentLongitude: longitude,
        ));
  }

  void _goToNoticeList() {
    UtilUI.goBack(context, false);
    UtilUI.goToNextPage(context, WeatherSettingPage());
    Util.trackActivities('weathers', path: 'List Weather Screen -> Open Notice List Weather}');
    if (_player.playing) _playPauseAudio();
  }

  void _initPlayController() async {
    if (_audioWeather.isEmpty) return;
    _player = AudioPlayer();
    _player.setUrl(_audioWeather).whenComplete(() {
      _player.playerStateStream.listen((playerState) {
        if (playerState.processingState == ProcessingState.completed) {
          _isPlay = false;
          bloc!.add(PlayAudioAgriculturalEvent(false));
          _player.seek(const Duration(seconds: 0)).whenComplete(() => _player.pause());
        }
      });
      _playPauseAudio();
    });
  }

  void _changeStatusNegative(bool value) {
    if (_negativeNoticeId == -1) return;
    bloc!.add(ChangeNegativeWeatherStatusAgriculturalEvent(_negativeNoticeId, value ? "agree" : "disagree"));
  }

  void _changedProvince(WeatherListModel item) async {
    await _player.stop();
    await _player.dispose();
    _provinceSelectId = item.id.toString();
    _audioWeather = '';
    latitude = item.lat;
    longitude = item.lng;
    _isPlay = false;
    bloc!.add(PlayAudioAgriculturalEvent(false));
    bloc!.add(LoadWeatherAgriculturalEvent(item.lat, item.lng));
  }

  void _playPauseAudio({bool changeAudio = true}) {
    if (_audioWeather.isEmpty) {
      bloc!.add(LoadingAudioAgriculturalEvent(true));
      try {
        bloc!.add(LoadAudioWeatherAgriculturalEvent(latitude, longitude, isRequest: true));
      } catch (_) {}
    } else if (!bloc!.state.isShowLoading) {
      if (!_isPlay && _isPause == true) return;

      _isPlay = !_isPlay;
      bloc!.add(PlayAudioAgriculturalEvent(_isPlay));
      if (changeAudio) _isPlay ? _player.play() : _player.pause();
      bloc!.add(LoadingAudioAgriculturalEvent(false));
    }
  }

  void _pause() {
    if (!_isPlay) return;
    _isPlay = false;
    bloc!.add(PlayAudioAgriculturalEvent(false));
    _player.pause();
  }

  int _getHour() => int.parse(Util.dateToString(DateTime.now().add(const Duration(hours: 1)), pattern: 'HH'));

  String _formatDateFromISO(String isoDate) {
    if (isoDate.isEmpty) return '';
    try {
      final dateTime = DateTime.parse(isoDate);
      return Util.dateToString(dateTime, pattern: 'dd/MM');
    } catch (e) {
      return '';
    }
  }

  String _formatTimeFromISO(String isoDate) {
    if (isoDate.isEmpty) return '';
    try {
      final dateTime = DateTime.parse(isoDate);
      return Util.dateToString(dateTime, pattern: 'HH:mm');
    } catch (e) {
      return '';
    }
  }

  void _selectHourItem(int index) {
    if (index < _weatherData.currentDate.currentWeatherGroup.length) {
      setState(() {
        _selectedHourIndex = index;
        _selectedHourData = _weatherData.currentDate.currentWeatherGroup[index];
      });
    }
  }

  // Get current display data (either selected hour or current weather)
  CurrentDate get _currentDisplayData {
    if (_selectedHourData != null) {
      // Create a copy of current date with selected hour data
      // Only update fields that exist in CurrentWeatherGroup
      final displayData = CurrentDate(
        temp: _selectedHourData!.temp,
        weatherStatus: _selectedHourData!.weatherStatus,
        weatherStatusIcon: _selectedHourData!.weatherStatusIcon,
        // Keep original data for fields not available in hourly data
        tempMin: _weatherData.currentDate.tempMin,
        tempMax: _weatherData.currentDate.tempMax,
        windSpeed: _weatherData.currentDate.windSpeed,
        humidity: _weatherData.currentDate.humidity,
        percent_rain: _weatherData.currentDate.percent_rain,
        uv: _weatherData.currentDate.uv,
        location_fullname: _weatherData.currentDate.location_fullname,
      );
      return displayData;
    }
    return _weatherData.currentDate;
  }

  // Check if weather data is loaded
  bool get _isDataLoaded => _weatherData.currentDate.weatherStatusIcon.isNotEmpty;

  @override
  Widget build(BuildContext context, {Color color = Colors.white}) => Stack(alignment: Alignment.bottomRight, children: [
        RepaintBoundary(
          key: _globalKey,
          child: Scaffold(
              backgroundColor: Colors.white,
              body: BlocBuilder(
                  bloc: bloc,
                  buildWhen: (_, newState) => newState is LoadWeatherAgriculturalState,
                  builder: (_, __) => Container(
                      decoration: BoxDecoration(image: DecorationImage(image: AssetImage(_getWeatherBackground()), fit: BoxFit.cover)),
                      child: Column(mainAxisAlignment: MainAxisAlignment.start, crossAxisAlignment: CrossAxisAlignment.start, children: [
                        // Header with location and settings
                        Container(
                            padding: EdgeInsets.fromLTRB(40.sp, WidgetsBinding.instance.window.padding.top.sp + 20.sp, 40.sp, 40.sp),
                            child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                              ButtonImageWidget(5, () {
                                UtilUI.goBack(context, false);
                              }, Icon(Icons.arrow_back, color: Colors.white, size: 80.sp)),
                              Expanded(
                                  child: Column(children: [
                                ButtonImageWidget(5, () {
                                  if (bloc!.state.isShowLoading) return;
                                  showOptionProvince(context, MultiLanguage.get(languageKey.lblProvince), _listWeather, _provinceSelectId).then((value) {
                                    if (value != null) _changedProvince(value);
                                  });
                                },
                                    _isDataLoaded
                                        ? LabelCustom(_weatherData.currentDate.location_fullname.isNotEmpty ? _weatherData.currentDate.location_fullname : 'Tân Phú, Quận 7, Hồ Chí Minh',
                                            color: Colors.white, size: 48.sp, align: TextAlign.center, weight: FontWeight.w600)
                                        : WeatherShimmerWidgets.locationName()),
                                SizedBox(height: 10.sp),
                                _isDataLoaded
                                    ? LabelCustom(Util.dateToString(DateTime.now(), pattern: 'dd tháng MM, yyyy'), color: Colors.white, size: 36.sp, align: TextAlign.center, weight: FontWeight.w500)
                                    : WeatherShimmerWidgets.dateName(),
                              ])),
                              ButtonImageWidget(0, _selectOption, Icon(Icons.settings_outlined, color: Colors.white, size: 80.sp))
                            ])),
                        Expanded(
                            child: Container(
                                padding: EdgeInsets.symmetric(horizontal: 40.sp, vertical: 20.sp),
                                child: Column(children: [
                                  Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
                                    Expanded(
                                      child: Column(
                                        children: [
                                          Row(
                                            crossAxisAlignment: CrossAxisAlignment.center,
                                            children: [
                                              Column(
                                                crossAxisAlignment: CrossAxisAlignment.center,
                                                mainAxisAlignment: MainAxisAlignment.center,
                                                children: [
                                                  Container(
                                                      width: 380.sp,
                                                      height: 380.sp,
                                                      alignment: Alignment.center,
                                                      child: _isDataLoaded
                                                          ? ImageNetworkAsset(
                                                              path: '${constants.baseUrlImage}/images/weather_icons/${_currentDisplayData.weatherStatusIcon}.png',
                                                              width: 380.sp,
                                                              height: 380.sp,
                                                              fit: BoxFit.cover,
                                                              uiError: const SizedBox())
                                                          : WeatherShimmerWidgets.weatherIcon()),
                                                  SizedBox(height: 20.sp),
                                                  _isDataLoaded
                                                      ? LabelCustom(
                                                          _currentDisplayData.weatherStatus.toCapitalized(),
                                                          color: Colors.white,
                                                          size: 48.sp,
                                                          weight: FontWeight.w500,
                                                          shadows: [Shadow(color: Colors.black.withOpacity(0.5), offset: const Offset(2, 2), blurRadius: 4)],
                                                        )
                                                      : WeatherShimmerWidgets.weatherStatus()
                                                ],
                                              ),
                                              SizedBox(width: 40.sp),
                                              Row(
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: [
                                                  _isDataLoaded
                                                      ? LabelCustom(
                                                          '${_currentDisplayData.temp.round()}',
                                                          color: Colors.white,
                                                          size: 240.sp,
                                                          weight: FontWeight.w600,
                                                          shadows: [Shadow(color: Colors.black.withOpacity(0.5), offset: const Offset(2, 2), blurRadius: 4)],
                                                        )
                                                      : WeatherShimmerWidgets.temperature(),
                                                  LabelCustom(
                                                    '°C',
                                                    color: Colors.white,
                                                    size: 80.sp,
                                                    weight: FontWeight.w600,
                                                    shadows: [Shadow(color: Colors.black.withOpacity(0.5), offset: const Offset(2, 2), blurRadius: 4)],
                                                  ),
                                                ],
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                    SizedBox(width: 10.sp),
                                    // Temperature and details
                                    Column(crossAxisAlignment: CrossAxisAlignment.center, children: [
                                      Row(crossAxisAlignment: CrossAxisAlignment.center, children: [
                                        Column(children: [
                                          _isDataLoaded
                                              ? LabelCustom(
                                                  '${_currentDisplayData.tempMax.round()}°',
                                                  color: Colors.white,
                                                  size: 40.sp,
                                                  weight: FontWeight.w400,
                                                  shadows: [Shadow(color: Colors.black.withOpacity(0.2), offset: const Offset(2, 2), blurRadius: 4)],
                                                )
                                              : WeatherShimmerWidgets.tempMinMax(),
                                          SizedBox(height: 10.sp),
                                          Container(
                                              width: 24.sp,
                                              height: 360.sp,
                                              decoration: BoxDecoration(
                                                  gradient: const LinearGradient(begin: Alignment.topCenter, end: Alignment.bottomCenter, colors: [Color(0xFFFF4A4A), Color(0xFF2D81FF)]),
                                                  borderRadius: BorderRadius.circular(10.sp)),
                                              child: Stack(children: [
                                                Container(
                                                    width: 36.sp,
                                                    height: 36.sp,
                                                    decoration: const BoxDecoration(
                                                        color: Colors.white, shape: BoxShape.circle, boxShadow: [BoxShadow(color: Color(0x3F000000), blurRadius: 4, offset: Offset(0, 4))]))
                                              ])),
                                          SizedBox(height: 10.sp),
                                          _isDataLoaded
                                              ? LabelCustom(
                                                  '${_currentDisplayData.tempMin.round()}°',
                                                  color: Colors.white,
                                                  size: 40.sp,
                                                  weight: FontWeight.w400,
                                                  shadows: [Shadow(color: Colors.black.withOpacity(0.2), offset: const Offset(2, 2), blurRadius: 4)],
                                                )
                                              : WeatherShimmerWidgets.tempMinMax(),
                                        ])
                                      ]),
                                    ])
                                  ]),
                                  // Weather details row - Keep original data as hourly data doesn't have these details
                                  _isDataLoaded
                                      ? Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, crossAxisAlignment: CrossAxisAlignment.start, children: [
                                          _WeatherDetailItem('Sức gió', '${_weatherData.currentDate.windSpeed}km/h', 'wind'),
                                          _WeatherDetailItem('Độ ẩm', '${_weatherData.currentDate.humidity}%', 'humidity'),
                                          _WeatherDetailItem('Khả năng\nmưa', '${_weatherData.currentDate.percent_rain.round()}%', 'rain'),
                                          _WeatherDetailItem('Chỉ số\nbức xạ', '${_weatherData.currentDate.uv}', 'uv')
                                        ])
                                      : WeatherShimmerWidgets.weatherDetailsRow(),
                                  SizedBox(height: 60.sp),
                                  // Hourly forecast card
                                  Container(
                                      width: double.infinity,
                                      padding: EdgeInsets.all(40.sp),
                                      decoration: BoxDecoration(
                                          color: Colors.white, borderRadius: BorderRadius.circular(30.sp), boxShadow: const [BoxShadow(color: Color(0x3F000000), blurRadius: 4, offset: Offset(0, 4))]),
                                      child: Column(children: [
                                        // Tab selector
                                        Row(children: [
                                          Expanded(
                                            child: GestureDetector(
                                                onTap: () => setState(() => _showFourteenDays = false),
                                                child: Container(
                                                    alignment: Alignment.center,
                                                    padding: EdgeInsets.symmetric(vertical: 20.sp, horizontal: 40.sp),
                                                    decoration: BoxDecoration(border: !_showFourteenDays ? Border(bottom: BorderSide(color: const Color(0xFF2BCE85), width: 6.sp)) : null),
                                                    child: LabelCustom('Hôm nay', color: !_showFourteenDays ? Colors.black : Colors.grey, size: 42.sp))),
                                          ),
                                          Expanded(
                                            child: GestureDetector(
                                                onTap: () => setState(() => _showFourteenDays = true),
                                                child: Container(
                                                    alignment: Alignment.center,
                                                    padding: EdgeInsets.symmetric(vertical: 20.sp, horizontal: 40.sp),
                                                    decoration: BoxDecoration(border: _showFourteenDays ? Border(bottom: BorderSide(color: const Color(0xFF2BCE85), width: 6.sp)) : null),
                                                    child: LabelCustom('14 ngày tới', color: _showFourteenDays ? Colors.black : Colors.grey, size: 42.sp))),
                                          )
                                        ]),
                                        SizedBox(height: 40.sp),
                                        if (!_showFourteenDays) ...[
                                          _isDataLoaded && _weatherData.currentDate.currentWeatherGroup.isNotEmpty
                                              ? SizedBox(
                                                  height: 240.sp,
                                                  child: ListView.builder(
                                                      scrollDirection: Axis.horizontal,
                                                      itemCount: _weatherData.currentDate.currentWeatherGroup.length,
                                                      itemBuilder: (context, index) {
                                                        final item = _weatherData.currentDate.currentWeatherGroup[index];
                                                        final isSelected = index == _selectedHourIndex;
                                                        return GestureDetector(
                                                            // onTap: () => _selectHourItem(index),
                                                            child: Container(
                                                                width: 140.sp,
                                                                margin: EdgeInsets.only(right: 20.sp),
                                                                padding: EdgeInsets.all(20.sp),
                                                                decoration: BoxDecoration(color: isSelected ? const Color(0xFF2BCE85) : Colors.transparent, borderRadius: BorderRadius.circular(20.sp)),
                                                                child: Column(mainAxisAlignment: MainAxisAlignment.spaceEvenly, children: [
                                                                  LabelCustom(item.date != "" ? item.date.split(' ')[1] : '', color: isSelected ? Colors.white : Colors.black, size: 32.sp),
                                                                  ImageNetworkAsset(
                                                                      path: '${constants.baseUrlImage}/images/weather_icons/${item.weatherStatusIcon}.png',
                                                                      width: 80.sp,
                                                                      height: 80.sp,
                                                                      fit: BoxFit.fitHeight,
                                                                      uiError: const SizedBox()),
                                                                  LabelCustom('${item.temp.round()}°', color: isSelected ? Colors.white : Colors.black, size: 36.sp, weight: FontWeight.w500)
                                                                ])));
                                                      }))
                                              : WeatherShimmerWidgets.hourlyForecastList()
                                        ] else ...[
                                          // 14-day forecast - Extended height
                                          _isDataLoaded && _weatherData.nextDay.isNotEmpty
                                              ? SizedBox(
                                                  height: 700.sp,
                                                  child: ListView.builder(
                                                      shrinkWrap: true,
                                                      padding: EdgeInsets.zero,
                                                      physics: const BouncingScrollPhysics(),
                                                      itemCount: _weatherData.nextDay.length,
                                                      itemBuilder: (context, index) {
                                                        final day = _weatherData.nextDay[index];
                                                        return Container(
                                                            margin: EdgeInsets.only(bottom: 20.sp),
                                                            padding: EdgeInsets.all(26.sp),
                                                            decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(26.sp), border: Border.all(color: Colors.grey.shade200)),
                                                            child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                                                              SizedBox(child: LabelCustom(_formatDateFromISO(day.date), color: Colors.black, size: 42.sp, weight: FontWeight.w500)),
                                                              Row(mainAxisAlignment: MainAxisAlignment.start, children: [
                                                                ImageNetworkAsset(
                                                                    path: '${constants.baseUrlImage}/images/weather_icons/${day.weatherStatusIcon}.png',
                                                                    width: 100.sp,
                                                                    height: 100.sp,
                                                                    fit: BoxFit.fitHeight,
                                                                    uiError: const SizedBox()),
                                                                SizedBox(width: 20.sp),
                                                                LabelCustom(day.weatherStatus.toCapitalized(), color: Colors.black, size: 36.sp, weight: FontWeight.normal),
                                                              ]),
                                                              Column(crossAxisAlignment: CrossAxisAlignment.end, children: [
                                                                LabelCustom('${day.tempMax.round()}°', color: Colors.black, size: 48.sp, weight: FontWeight.w500),
                                                                LabelCustom('${day.tempMin.round()}°', color: const Color(0XFF868686), size: 36.sp, weight: FontWeight.normal),
                                                              ])
                                                            ]));
                                                      }))
                                              : WeatherShimmerWidgets.fourteenDayForecastList(),
                                          SizedBox(height: 20.sp),
                                          Container(
                                              width: double.infinity,
                                              margin: EdgeInsets.symmetric(horizontal: 120.sp),
                                              padding: EdgeInsets.symmetric(vertical: 28.sp),
                                              decoration: BoxDecoration(color: const Color(0xFF2BCE85), borderRadius: BorderRadius.circular(25.sp)),
                                              child: LabelCustom('Dự báo nhiệt độ các tháng tới', color: Colors.white, size: 42.sp, align: TextAlign.center, weight: FontWeight.w500))
                                        ],
                                        if (!_showFourteenDays) ...[
                                          SizedBox(height: 20.sp),
                                          GestureDetector(
                                            onTap: _navigateToWeatherMap,
                                            child: Container(
                                                width: double.infinity,
                                                margin: EdgeInsets.symmetric(horizontal: 140.sp),
                                                padding: EdgeInsets.symmetric(vertical: 26.sp),
                                                decoration: BoxDecoration(color: const Color(0xFF2BCE85), borderRadius: BorderRadius.circular(25.sp)),
                                                child: LabelCustom('Bản đồ lượng mưa', color: Colors.white, size: 48.sp, align: TextAlign.center, weight: FontWeight.w500)),
                                          )
                                        ]
                                      ])),
                                  if (!_showFourteenDays) ...[
                                    SizedBox(height: 40.sp),
                                    Stack(children: [
                                      Container(
                                          width: double.infinity,
                                          padding: EdgeInsets.all(40.sp),
                                          margin: EdgeInsets.only(top: 40.sp),
                                          decoration: BoxDecoration(
                                              color: Colors.white,
                                              borderRadius: BorderRadius.circular(30.sp),
                                              boxShadow: const [BoxShadow(color: Color(0x3F000000), blurRadius: 4, offset: Offset(0, 4))]),
                                          child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                                            Row(children: [
                                              BlocBuilder(
                                                  bloc: bloc,
                                                  buildWhen: (oldState, newState) => newState is PlayAudioAgriculturalState,
                                                  builder: (context, state) {
                                                    return ButtonImageWidget(
                                                        50, _playPauseAudio, Icon(_isPlay ? Icons.pause_circle_filled : Icons.volume_up, color: _isPlay ? Colors.red : Colors.green, size: 80.sp));
                                                  }),
                                              SizedBox(width: 20.sp),
                                              LabelCustom('Nghe tin dự báo', color: const Color(0xFF05381F), size: 42.sp, weight: FontWeight.w500),
                                              const Spacer(),
                                            ]),
                                            SizedBox(height: 20.sp),
                                            LabelCustom('#text cảnh báo', color: const Color(0xFF8C8C8C), size: 36.sp),
                                            SizedBox(height: 20.sp),
                                            Row(children: [const Spacer(), LabelCustom('Xem thêm', color: const Color(0xFF1DA644), size: 36.sp)])
                                          ])),
                                      Positioned(
                                          top: 0.sp,
                                          right: 0,
                                          child: Container(
                                              padding: EdgeInsets.symmetric(horizontal: 48.sp, vertical: 26.sp),
                                              decoration: const BoxDecoration(
                                                  color: Color(0xFFFF4A4A),
                                                  borderRadius: BorderRadius.only(topLeft: Radius.circular(28), bottomRight: Radius.circular(28)),
                                                  boxShadow: [BoxShadow(color: Color(0x3F000000), blurRadius: 4, offset: Offset(0, 4))]),
                                              child: Row(mainAxisSize: MainAxisSize.min, children: [
                                                Icon(Icons.flash_on, color: Colors.yellow, size: 48.sp),
                                                SizedBox(width: 10.sp),
                                                LabelCustom('Cảnh báo nông vụ', color: Colors.white, size: 42.sp, weight: FontWeight.w600)
                                              ])))
                                    ]),
                                  ]
                                ]))),
                        Container(
                            width: double.infinity,
                            height: 110.sp,
                            margin: EdgeInsets.symmetric(horizontal: 80.sp, vertical: 32.sp),
                            padding: EdgeInsets.symmetric(horizontal: 40.sp, vertical: 20.sp),
                            alignment: Alignment.center,
                            decoration: BoxDecoration(color: const Color(0x59D9D9D9), borderRadius: BorderRadius.circular(26.sp)),
                            child: Row(crossAxisAlignment: CrossAxisAlignment.center, mainAxisAlignment: MainAxisAlignment.center, children: [
                              LabelCustom(
                                'Cảnh báo mưa giông bất thường',
                                color: Colors.white,
                                size: 42.sp,
                                weight: FontWeight.w700,
                                line: 1,
                                align: TextAlign.center,
                              ),
                              BlocBuilder(
                                  bloc: bloc,
                                  buildWhen: (oldStt, newStt) => newStt is LoadNegativeWeatherStatusAgriculturalState || newStt is ChangeNegativeWeatherStatusAgriculturalState,
                                  builder: (context, state) =>
                                      Switch(activeColor: Colors.green, activeTrackColor: Colors.white, value: _negativeNotices == "agree", onChanged: (value) => _changeStatusNegative(value)))
                            ]))
                      ])))),
        ),
        const Banner2Nong('weather'),
        Loading(bloc)
      ]);

  Widget _WeatherDetailItem(String label, String value, String icon) {
    return Column(mainAxisAlignment: MainAxisAlignment.spaceBetween, crossAxisAlignment: CrossAxisAlignment.center, children: [
      Image.asset("assets/images/v12/ic_weather_$icon.png", width: 60.sp, height: 60.sp, fit: BoxFit.fill),
      SizedBox(height: 20.sp),
      Container(
        height: 160.sp,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            LabelCustom(label,
                color: Colors.white, size: 32.sp, align: TextAlign.center, weight: FontWeight.w500, shadows: [Shadow(color: Colors.black.withOpacity(0.2), offset: const Offset(2, 2), blurRadius: 4)]),
            SizedBox(height: 10.sp),
            LabelCustom(value, color: Colors.white, size: 52.sp, weight: FontWeight.w600, shadows: [Shadow(color: Colors.black.withOpacity(0.2), offset: const Offset(2, 2), blurRadius: 4)])
          ],
        ),
      ),
    ]);
  }

  Future showOptionAddress(BuildContext context, String title, {bool hasTitle = true, bool hasAdd = true}) {
    return showDialog(context: context, barrierDismissible: true, builder: (context) => DialogListAddressCustom(hintText: title));
  }

  Future showOptionProvince(BuildContext context, String title, List<WeatherListModel> values, String id, {bool hasTitle = true}) {
    return showDialog(
        context: context,
        barrierDismissible: true,
        builder: (context) => Align(
            alignment: Alignment.center,
            child: Container(
                width: 0.8.sw,
                height: 150.sp * values.length + (hasTitle ? 120.sp : 0),
                margin: EdgeInsets.only(top: 300.sp, bottom: 80.sp),
                decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(30.sp)),
                child: SingleChildScrollView(child: Column(mainAxisSize: MainAxisSize.min, children: createItems(context, title, values, id, hasTitle: hasTitle))))));
  }

  List<Widget> createItems(BuildContext context, String title, List<WeatherListModel> values, String id, {bool hasTitle = true}) {
    final line = Container(color: Colors.grey.shade300, height: 2.sp);
    List<Widget> list = [];
    if (hasTitle) {
      list.add(SizedBox(height: 120.sp, child: Center(child: LabelCustom(title, color: Colors.black87))));
    }
    for (var i = 0; i < values.length; i++) {
      list.add(line);
      list.add(OutlinedButton(
          style: OutlinedButton.styleFrom(side: const BorderSide(color: Colors.transparent), padding: EdgeInsets.zero),
          onPressed: () => Navigator.of(context).pop(values[i]),
          child: Container(
              color: id != values[i].id.toString() ? Colors.transparent : StyleCustom.buttonColor,
              width: 1.sw,
              height: 148.sp,
              alignment: Alignment.center,
              child: LabelCustom(values[i].name, color: id != values[i].id.toString() ? StyleCustom.primaryColor : Colors.white))));
    }
    return list;
  }

  void _autoPlayAudio(String link) {
    _audioWeather = link;
    setState(() => _initPlayController());
    bloc!.add(LoadingAudioAgriculturalEvent(false));
  }

  Future<void> _checkAddress() async {
    if (!Constants().isLogin) return;
    final prefs = await SharedPreferences.getInstance();
    if ((prefs.getString('address') ?? '').isNotEmpty) return;
    final ignore = await DBHelperUtil().getSetting('ignore_address_popup');
    if (ignore == 'true') return;

    final addressBloc = CountDownBloc();
    final value = await UtilUI.showCustomDialog(
        context,
        'Hoàn thiện hồ sơ người dùng (Cập nhật địa chỉ)'
        '                                                              ',
        isActionCancel: true,
        lblOK: 'Bổ sung ngay',
        lblCancel: 'Bỏ qua',
        alignMessageText: TextAlign.left,
        isClose: true,
        extend: IgnoreButton(addressBloc, () => _tapIgnore(addressBloc)));

    if (value != null) {
      if (value == true) {
        _isPause = true;
        _pause();
        showDialog(
            context: context,
            useSafeArea: false,
            barrierDismissible: false,
            barrierColor: Colors.transparent,
            builder: (_) => Scaffold(backgroundColor: Colors.transparent, body: PopupAddress())).whenComplete(() => _isPause = null);
      } else
        _saveIgnore(addressBloc);
    } else if (!addressBloc.isClosed) {
      addressBloc.close();
    }
  }

  void _tapIgnore(addressBloc) => addressBloc.add(CountDownEvent(value: addressBloc.state.value == 0 ? 1 : 0));

  void _saveIgnore(addressBloc) {
    DBHelperUtil().setSetting('ignore_address_popup', addressBloc.state.value > 0 ? 'true' : '');
    addressBloc.close();
  }
}

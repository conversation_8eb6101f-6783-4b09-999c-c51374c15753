import 'package:hainong/common/ui/import_lib_ui.dart';
import 'package:hainong/common/ui/label_custom.dart';
import 'package:trackasia_gl/trackasia_gl.dart';

import '../../../tool/map_task/components/map_weather/weather_constants.dart';
import '../../../tool/map_task/components/map_weather/weather_dropdown_widget.dart';
import '../../../tool/map_task/components/map_weather/weather_timeline_controls.dart';
import '../../../tool/map_task/components/map_weather/weather_wind_webview_widget.dart';
import '../../../tool/map_task/map_constants.dart';
import '../../../tool/map_task/models/map_response_model.dart';
import '../../../tool/map_task/source/colors/map_weather_color.dart';

class WeatherPanelWidget extends StatefulWidget {
  final String selectedWeatherLayer;
  final bool showWeatherPanel, isLoading, isTimelineActive, isPlaying;
  final bool? showWeatherPopup;
  final int currentTimeIndex;
  final List<DateTime> timelineHours;
  final Function(String) onWeatherLayerChanged;
  final VoidCallback onTogglePanel, onRefreshWeather, onResetTimeline, onToggleTimeline;
  final Function(bool) onTogglePlayPause;
  final Function(double) onTimelineSeek;
  final Animation<Offset> slideAnimation;
  final ValueNotifier<MapGeoJsonModel?>? currentWeatherData;
  final ValueNotifier<LatLng?>? currentWeatherPoint;
  final dynamic weatherController;

  const WeatherPanelWidget({
    Key? key,
    required this.selectedWeatherLayer,
    required this.showWeatherPanel,
    required this.isLoading,
    required this.onWeatherLayerChanged,
    required this.onTogglePanel,
    required this.onRefreshWeather,
    required this.isTimelineActive,
    required this.isPlaying,
    required this.currentTimeIndex,
    required this.timelineHours,
    required this.onTogglePlayPause,
    required this.onTimelineSeek,
    required this.onResetTimeline,
    required this.onToggleTimeline,
    required this.slideAnimation,
    this.currentWeatherData,
    this.currentWeatherPoint,
    this.weatherController,
    this.showWeatherPopup,
  }) : super(key: key);

  @override
  State<WeatherPanelWidget> createState() => _WeatherPanelWidgetState();
}

class _WeatherPanelWidgetState extends State<WeatherPanelWidget> with TickerProviderStateMixin {
  double _timelineOffset = 0.0, _panelOffset = 0.0, _weatherInfoOffset = 0.0;
  bool _isDragging = false, _isPanelDragging = false, _isWeatherInfoDragging = false;
  bool _showCloudLayer = true;
  bool _showTimelinePopup = true;
  bool _showVerticalScale = true;
  late AnimationController _timelineAnimationController;
  late Animation<double> _timelineSlideAnimation;
  final GlobalKey<WeatherWindWebViewWidgetState> _webViewKey = GlobalKey<WeatherWindWebViewWidgetState>();

  @override
  void initState() {
    super.initState();
    _initializeAnimations();

    // Thiết lập callback cho weather controller
    if (widget.weatherController != null) {
      widget.weatherController!.setWeatherIconsVisibilityCallback((visible) {
        _webViewKey.currentState?.setWeatherIconsVisibility(visible);
      });
    }
  }

  @override
  void dispose() {
    _timelineAnimationController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(WeatherPanelWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    _handleWidgetUpdates(oldWidget);

    // Handle forced WebView camera sync
    if (widget.weatherController?.needsWebViewSync == true) {
      debugPrint(
          "WeatherPanel: Force sync detected - zoom: ${widget.weatherController?.lastKnownZoom}, lng: ${widget.weatherController?.lastKnownLng}, lat: ${widget.weatherController?.lastKnownLat}");
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          setState(() {});
          widget.weatherController?.clearWebViewSyncFlag();
        }
      });
    }
  }

  void _initializeAnimations() {
    _timelineAnimationController = AnimationController(duration: const Duration(milliseconds: 400), vsync: this);
    _timelineSlideAnimation = Tween<double>(begin: 1.0, end: 0.0).animate(CurvedAnimation(parent: _timelineAnimationController, curve: Curves.easeOutCubic));

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.isTimelineActive && mounted) {
        Future.delayed(const Duration(milliseconds: 100), () {
          if (mounted) _timelineAnimationController.value = 1.0;
        });
      }
    });
  }

  void _handleWidgetUpdates(WeatherPanelWidget oldWidget) {
    if (widget.isTimelineActive != oldWidget.isTimelineActive) {
      widget.isTimelineActive ? _timelineAnimationController.forward() : _timelineAnimationController.reverse();
      if (widget.isTimelineActive) setState(() => _timelineOffset = 0.0);
    }

    if (widget.showWeatherPanel != oldWidget.showWeatherPanel) {
      setState(() {
        _panelOffset = 0.0;
        _weatherInfoOffset = 0.0;
      });
    }

    if (widget.showWeatherPanel && _hasValidWeatherData() && !_hasValidWeatherDataPrevious(oldWidget)) {
      setState(() => _weatherInfoOffset = 0.0);
    }

    if (widget.selectedWeatherLayer != oldWidget.selectedWeatherLayer) setState(() {});
  }

  bool _hasValidWeatherData() {
    return widget.currentWeatherData?.value != null;
  }

  bool _hasValidWeatherDataPrevious(WeatherPanelWidget oldWidget) {
    return oldWidget.currentWeatherData?.value != null;
  }

  @override
  Widget build(BuildContext context) {
    // Kiểm tra wind mode và state riêng của wind panel
    final isWindMode = widget.selectedWeatherLayer == WeatherConstants.wind;
    final shouldShowPanel = isWindMode ? (widget.showWeatherPanel && widget.weatherController?.showWindPanel == true) : widget.showWeatherPanel;
    final shouldShowTimeline = isWindMode ? (widget.isTimelineActive && widget.weatherController?.showWindPanel == true) : widget.isTimelineActive;
    final shouldShowTimelinePopup = shouldShowTimeline && (widget.showWeatherPopup ?? true);

    return SizedBox(
      width: double.infinity,
      height: double.infinity,
      child: Stack(
        children: [
          if (isWindMode && widget.weatherController?.showWindWebView == true) ...[
            Positioned.fill(
              child: Builder(
                builder: (context) {
                  return WeatherWindWebViewWidget(
                    key: _webViewKey,
                    currentTimestamp: _getCurrentTimestamp(),
                    currentTimeIndex: _getCurrentTimeIndex(),
                    isVisible: true,
                    isPlaying: widget.isPlaying,
                    weatherIconsVisible: widget.weatherController?.weatherIconsVisible,
                    onWebViewReady: () {
                      debugPrint("Wind WebView is ready and loaded");
                      if (widget.weatherController?.needsWebViewSync == true) {
                        Future.delayed(const Duration(milliseconds: 500), () {
                          if (mounted) setState(() {});
                        });
                      }
                    },
                    onCameraChanged: (zoom, lng, lat) {
                      if (widget.weatherController != null) {
                        widget.weatherController.updateCameraFromWebView(zoom, lng, lat);
                      }
                    },
                    syncZoom: widget.weatherController?.lastKnownZoom,
                    syncLng: widget.weatherController?.lastKnownLng,
                    syncLat: widget.weatherController?.lastKnownLat,
                    needsForceSync: widget.weatherController?.needsWebViewSync ?? false,
                  );
                },
              ),
            ),
          ],
          if (shouldShowPanel) WeatherDropdownWidget(selectedWeatherLayer: widget.selectedWeatherLayer, onLayerChanged: widget.onWeatherLayerChanged),
          if (shouldShowTimelinePopup && (widget.weatherController?.timelineVisible ?? true)) _buildTimelinePopup(),
          if (shouldShowTimeline && (widget.weatherController?.verticalScaleVisible ?? true)) _buildVerticalScale(),
          if (shouldShowTimeline && (widget.weatherController?.verticalScaleVisible ?? true)) _buildWeatherInfoContent(),
        ],
      ),
    );
  }

  // Helper methods để lấy timestamp và index an toàn
  String _getCurrentTimestamp() {
    try {
      if (widget.weatherController != null) {
        return widget.weatherController.getCurrentTimeStepForWebView();
      }
    } catch (e) {
      debugPrint("Error getting timestamp: $e");
    }
    final now = DateTime.now();
    final day = now.day.toString().padLeft(2, '0');
    final month = now.month.toString().padLeft(2, '0');
    final year = now.year.toString().substring(2);
    final hour = now.hour.toString().padLeft(2, '0');
    return "${day}-${month}-${year}_$hour";
  }

  int _getCurrentTimeIndex() {
    try {
      if (widget.weatherController != null) {
        return widget.weatherController.getCurrentTimeIndexForWebView();
      }
    } catch (e) {
      debugPrint("Error getting time index: $e");
    }
    return widget.currentTimeIndex;
  }

  Widget _buildTimelinePopup() {
    final timelineHeight = 360.sp;
    return AnimatedBuilder(
      animation: _timelineSlideAnimation,
      builder: (context, child) {
        final animationOffset = _timelineSlideAnimation.value * timelineHeight;
        final totalOffset = animationOffset + _timelineOffset.clamp(0.0, timelineHeight);
        return Positioned(
          bottom: -totalOffset,
          left: 0,
          right: 0,
          child: SingleChildScrollView(
            physics: const BouncingScrollPhysics(),
            child: WeatherTimelineControls.buildTimelinePopup(
              timelineContent: _buildTimelineContent(),
              onDragStart: () => _isDragging = true,
              onDragUpdate: _handleTimelineDrag,
              onDragEnd: _handleTimelineDragEnd,
              height: timelineHeight,
            ),
          ),
        );
      },
    );
  }

  Widget _buildTimelineContent() {
    if (widget.timelineHours.isEmpty) {
      return Column(
        children: [
          _buildWeatherAddress(),
          Container(
            height: 200.sp,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(color: WeatherLayersConfig.getLayerById(widget.selectedWeatherLayer).color),
                  SizedBox(height: 20.sp),
                  Text(
                    'Đang tải timeline...',
                    style: TextStyle(color: MapWeatherColor.darkGrey, fontSize: 32.sp),
                  ),
                ],
              ),
            ),
          ),
        ],
      );
    }

    return Column(
      children: [
        _buildWeatherAddress(),
        _buildTimelineHeader(),
        SizedBox(height: 20.sp),
        Expanded(child: _buildFiveDayTimeline()),
      ],
    );
  }

  Widget _buildWeatherAddress() {
    return Container(
      height: 72.sp,
      padding: EdgeInsets.symmetric(horizontal: 12.sp),
      margin: EdgeInsets.only(bottom: 16.sp),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16.sp),
        border: Border.all(color: Colors.white.withOpacity(0.2), width: 1),
      ),
      child: widget.currentWeatherData != null
          ? ValueListenableBuilder<MapGeoJsonModel?>(
              valueListenable: widget.currentWeatherData!,
              builder: (context, weatherData, child) {
                if (weatherData != null) {
                  return Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Row(
                              children: [
                                Icon(Icons.location_on, color: Colors.blue, size: 52.sp),
                                SizedBox(width: 8.sp),
                                Expanded(
                                  child: Text(
                                    weatherData.data['location_full_name'] ?? '',
                                    style: TextStyle(fontSize: 38.sp, color: Colors.black87, fontWeight: FontWeight.w500),
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  );
                } else {
                  return _buildAddressLoadingState();
                }
              },
            )
          : _buildAddressLoadingState(),
    );
  }

  Widget _buildAddressLoadingState() {
    return Row(
      children: [
        SizedBox(
          width: 52.sp,
          height: 52.sp,
          child: const CircularProgressIndicator(
            color: Colors.blue,
            strokeWidth: 3.0,
          ),
        ),
        SizedBox(width: 8.sp),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                height: 16.sp,
                width: 200.sp,
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(8.sp),
                ),
              ),
              SizedBox(height: 8.sp),
              Container(
                height: 12.sp,
                width: 120.sp,
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(6.sp),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTimelineHeader() {
    return WeatherTimelineControls.buildTimelineHeader(
      isPlaying: widget.isPlaying,
      onTogglePlayPause: () => widget.onTogglePlayPause(!widget.isPlaying),
      onResetTimeline: widget.onResetTimeline,
      timeDisplay: _buildTimeDisplay(),
      activeColor: WeatherLayersConfig.getLayerById(widget.selectedWeatherLayer).color,
      buttonSize: 68.sp,
      disabledColor: MapWeatherColor.lightGrey,
    );
  }

  Widget _buildTimeDisplay() {
    if (widget.timelineHours.isEmpty || widget.currentTimeIndex >= widget.timelineHours.length) {
      return WeatherTimelineControls.buildTimeDisplay(
        formattedTime: 'Đang tải...',
        textColor: MapWeatherColor.darkGrey,
        fontSize: 36.0,
        fontWeight: FontWeight.bold,
      );
    }
    return WeatherTimelineControls.buildTimeDisplay(
      formattedTime: WeatherTimelineControls.formatDateTime(widget.timelineHours[widget.currentTimeIndex]),
      textColor: MapWeatherColor.darkGrey,
      fontSize: 36.0,
      fontWeight: FontWeight.bold,
    );
  }

  Widget _buildFiveDayTimeline() => Column(children: [_buildDayLabels(), Expanded(child: _buildTimelineSlider())]);

  Widget _buildDayLabels() {
    final weekdays = WeatherTimelineControls.getWeekdayNamesStartingFromToday(widget.timelineHours.isNotEmpty ? widget.timelineHours.first : DateTime.now(), WeatherConstants.totalTimelineDays);
    return WeatherTimelineControls.buildDayLabels(
      weekdayNames: weekdays,
      currentDayIndex: _getCurrentDay() - 1,
      activeColor: WeatherLayersConfig.getLayerById(widget.selectedWeatherLayer).color,
      totalDays: WeatherConstants.totalTimelineDays,
    );
  }

  Widget _buildTimelineSlider() {
    if (widget.timelineHours.isEmpty) {
      return Container(
        height: 100.sp,
        child: Center(
          child: Text(
            'Đang tải timeline...',
            style: TextStyle(color: MapWeatherColor.darkGrey, fontSize: 32.sp),
          ),
        ),
      );
    }

    final maxIndex = widget.timelineHours.length - 1;
    final safeValue = maxIndex > 0 ? widget.currentTimeIndex / maxIndex : 0.0;

    return WeatherTimelineControls.buildTimelineSlider(
      context: context,
      value: safeValue.clamp(0.0, 1.0),
      onChanged: _handleSliderChanged,
      onChangeStart: (_) => widget.isPlaying ? widget.onTogglePlayPause(false) : null,
      onChangeEnd: _handleSliderChangeEnd,
      activeColor: WeatherLayersConfig.getLayerById(widget.selectedWeatherLayer).color,
      totalDays: WeatherConstants.totalTimelineDays,
    );
  }

  void _handleTimelineDrag(double deltaY) {
    if (!_isDragging) return;

    setState(() {
      if (deltaY > 0) {
        _timelineOffset += deltaY;
      } else if (_timelineOffset > 0) {
        _timelineOffset = (_timelineOffset + deltaY).clamp(0.0, 220.sp);
      }
    });
  }

  void _handleTimelineDragEnd() {
    _isDragging = false;
    if (_timelineOffset > 120.sp) {
      debugPrint("Timeline dragged down - hiding timeline and vertical scale");
      widget.onToggleTimeline();
    }
    setState(() => _timelineOffset = 0.0);
  }

  void _handleSliderChanged(double value) {
    if (widget.timelineHours.isEmpty) return;

    final maxIndex = widget.timelineHours.length - 1;
    final newIndex = (value * maxIndex).round();
    if (newIndex != widget.currentTimeIndex && newIndex >= 0 && newIndex < widget.timelineHours.length) {
      widget.onTimelineSeek(value);
    }
  }

  void _handleSliderChangeEnd(double value) {
    widget.onTimelineSeek(value);
  }

  int _getCurrentDay() {
    if (widget.timelineHours.isEmpty || widget.currentTimeIndex >= widget.timelineHours.length) return 1;
    final currentTime = widget.timelineHours[widget.currentTimeIndex];
    final startTime = widget.timelineHours.first;
    return currentTime.difference(startTime).inDays + 1;
  }

  Widget _buildVerticalScale() {
    return Positioned(
      bottom: 160,
      right: 0,
      child: SizedBox(
        width: WeatherConstants.verticalScaleWidth,
        child: Column(
          children: [
            _buildScaleHeader(),
            _buildScaleValue(WeatherLayersConfig.getMaxValue(widget.selectedWeatherLayer)),
            SizedBox(height: 12.sp),
            _buildScaleBar(),
            SizedBox(height: 12.sp),
            _buildScaleValue(WeatherLayersConfig.getMinValue(widget.selectedWeatherLayer)),
          ],
        ),
      ),
    );
  }

  Widget _buildScaleHeader() {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 20.sp),
      child: Column(
        children: [
          Icon(WeatherLayersConfig.getLayerById(widget.selectedWeatherLayer).icon, color: WeatherLayersConfig.getLayerById(widget.selectedWeatherLayer).color, size: 40.sp),
          SizedBox(height: 5.sp),
          Text(
            WeatherLayersConfig.getLayerById(widget.selectedWeatherLayer).title,
            style: TextStyle(fontSize: MapConstants.defaultContentFontSize.sp, fontWeight: FontWeight.normal, color: WeatherLayersConfig.getLayerById(widget.selectedWeatherLayer).color),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildScaleValue(String value) {
    return Text(value, style: TextStyle(fontSize: MapConstants.defaultContentFontSize.sp, fontWeight: FontWeight.w600, color: MapWeatherColor.darkGrey));
  }

  Widget _buildScaleBar() {
    final gradientConfig = _getGradientConfig();
    return Container(
      height: 460.sp,
      width: gradientConfig['width'],
      margin: EdgeInsets.symmetric(horizontal: 12.sp),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(32.sp),
        gradient: gradientConfig['gradient'],
        border: Border.all(color: WeatherLayersConfig.getLayerById(widget.selectedWeatherLayer).color.withOpacity(0.4), width: 1),
        boxShadow: gradientConfig['showShadow']
            ? [BoxShadow(color: WeatherLayersConfig.getLayerById(widget.selectedWeatherLayer).color.withOpacity(0.2), offset: const Offset(0, 2), blurRadius: 6, spreadRadius: 0)]
            : null,
      ),
    );
  }

  Map<String, dynamic> _getGradientConfig() {
    switch (widget.selectedWeatherLayer) {
      case WeatherConstants.temperature:
        return {'gradient': MapWeatherColor.getTemperatureGradient(), 'width': 26.sp, 'showShadow': true};
      case WeatherConstants.precipitation:
        return {'gradient': MapWeatherColor.getRainfallGradient(), 'width': 26.sp, 'showShadow': true};
      case WeatherConstants.wind:
        return {'gradient': MapWeatherColor.getWindGradient(), 'width': 28.sp, 'showShadow': true};
      default:
        return {
          'gradient': LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: WeatherLayersConfig.getLayerGradient(widget.selectedWeatherLayer).colors.reversed.toList(),
          ),
          'width': 32.sp,
          'showShadow': false
        };
    }
  }

  Widget _buildWeatherInfoContent() {
    // Kiểm tra null safety trước khi sử dụng
    if (widget.currentWeatherData == null) {
      return const SizedBox.shrink(); // Trả về widget rỗng nếu null
    }

    return Positioned(
      bottom: 400.sp,
      left: 16.sp,
      child: ValueListenableBuilder<MapGeoJsonModel?>(
        valueListenable: widget.currentWeatherData!,
        builder: (context, weatherData, child) {
          if (weatherData == null) return _buildWeatherInfoPlaceholder();

          try {
            final data = weatherData.data;
            final imageUrl = data['icon'] ?? data['weather_status_icon'];
            final temp = data['temp_day'] ?? data['temp']?.toString() ?? "N/A";
            final description = data['description'] ?? data['weather_status'] ?? "N/A";

            return Container(
              width: 200.sp,
              height: 360.sp,
              padding: EdgeInsets.all(16.sp),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.9),
                borderRadius: BorderRadius.circular(16.sp),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 8.sp,
                    offset: Offset(0, 2.sp),
                  ),
                ],
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _buildWeatherIcon(imageUrl),
                  SizedBox(height: 12.sp),
                  _buildTemperatureDisplay(temp),
                  SizedBox(height: 8.sp),
                  _buildDescriptionDisplay(description),
                ],
              ),
            );
          } catch (e) {
            debugPrint('Error building weather info content: $e');
            return _buildWeatherInfoPlaceholder();
          }
        },
      ),
    );
  }

  Widget _buildWeatherInfoPlaceholder() {
    return Container(
      width: 200.sp,
      height: 360.sp,
      padding: EdgeInsets.all(16.sp),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.9),
        borderRadius: BorderRadius.circular(16.sp),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8.sp,
            offset: Offset(0, 2.sp),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.cloud, size: 80.sp, color: Colors.grey.shade400),
          SizedBox(height: 12.sp),
          LabelCustom(
            'Nhấn vào bản đồ để xem thông tin thời tiết',
            color: Colors.grey.shade600,
            size: 28.sp,
            align: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildWeatherIcon(String? imageUrl) {
    return Container(
      width: 160.sp,
      height: 160.sp,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Colors.black87.withOpacity(0.5), Colors.black87.withOpacity(0.2)],
        ),
        borderRadius: BorderRadius.circular(24.sp),
        border: Border.all(color: Colors.white.withOpacity(0.3), width: 1),
      ),
      child: imageUrl != null && imageUrl.isNotEmpty
          ? ClipRRect(
              borderRadius: BorderRadius.circular(24.sp),
              child: Image.network(
                imageUrl,
                width: 160.sp,
                height: 160.sp,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => Icon(
                  Icons.cloud,
                  size: 80.sp,
                  color: Colors.white,
                ),
              ),
            )
          : Icon(
              Icons.cloud,
              size: 80.sp,
              color: Colors.white,
            ),
    );
  }

  Widget _buildTemperatureDisplay(String temp) {
    return LabelCustom(
      '$temp°C',
      color: Colors.black87,
      size: 48.sp,
      weight: FontWeight.w600,
    );
  }

  Widget _buildDescriptionDisplay(String description) {
    return LabelCustom(
      description,
      color: Colors.grey.shade700,
      size: 32.sp,
      align: TextAlign.center,
    );
  }
}

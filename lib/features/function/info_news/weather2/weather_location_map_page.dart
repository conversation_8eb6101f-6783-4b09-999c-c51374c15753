import 'dart:math';

import 'package:flutter/services.dart';
import 'package:geolocator/geolocator.dart';
import 'package:hainong/common/ui/button_image_widget.dart';
import 'package:hainong/common/ui/import_lib_ui.dart';
import 'package:hainong/common/ui/label_custom.dart';
import 'package:trackasia_gl/trackasia_gl.dart';

import '../weather/weather_model.dart';
import 'models/user_location_model.dart';
import 'weather_agricultural_bloc.dart';

class WeatherLocationMapPage extends BasePage {
  final UserLocationModel? editLocation;
  final bool isEdit;
  final WeatherModel? currentWeatherData;
  final String? currentLatitude;
  final String? currentLongitude;

  WeatherLocationMapPage({
    Key? key,
    this.editLocation,
    this.isEdit = false,
    this.currentWeatherData,
    this.currentLatitude,
    this.currentLongitude,
  }) : super(key: key, pageState: _WeatherLocationMapPageState());
}

class _WeatherLocationMapPageState extends BasePageState {
  TrackAsiaMapController? mapController;
  LatLng? selectedPosition;
  String selectedAddress = '';
  bool isLoadingAddress = false;
  UserLocationModel? editLocation;
  bool isEdit = false;
  WeatherModel? currentWeatherData;
  String? currentLatitude;
  String? currentLongitude;

  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  List<dynamic> _searchResults = [];
  bool _showSearchResults = false;
  bool _isSearching = false;
  bool _isSelectingAddress = false; // Flag để ngăn API call khi đang chọn địa chỉ
  Symbol? tappedLocationSymbol;

  @override
  void initState() {
    super.initState();
    bloc = WeatherAgriculturalBloc();

    final widget = this.widget as WeatherLocationMapPage;
    editLocation = widget.editLocation;
    isEdit = widget.isEdit;
    currentWeatherData = widget.currentWeatherData;
    currentLatitude = widget.currentLatitude;
    currentLongitude = widget.currentLongitude;

    bloc!.stream.listen((state) async {
      if (state is AddUserLocationState) {
        if (state.response.success) {
          UtilUI.goBack(context, true);
        } else {
          UtilUI.showCustomDialog(context, state.response.data);
        }
      } else if (state is UpdateUserLocationState) {
        if (state.response.success) {
          UtilUI.goBack(context, true);
        } else {
          UtilUI.showCustomDialog(context, state.response.data);
        }
      } else if (state is GetLatLonAddressAgriculturalState) {
        if (state.response.success) {
          selectedAddress = 'Vị trí đã chọn';
        }
      } else if (state is GetAddressFromMapState) {
        setState(() {
          _searchResults = state.resp ?? [];
          _showSearchResults = _searchResults.isNotEmpty;
          _isSearching = false;
        });
      }
    });

    _initializePosition();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    // Ngăn API call khi đang chọn địa chỉ
    if (_isSelectingAddress) {
      return;
    }

    final query = _searchController.text.trim();
    if (query.isEmpty) {
      setState(() {
        _showSearchResults = false;
        _searchResults = [];
      });
      return;
    }

    if (query.length >= 2) {
      setState(() {
        _isSearching = true;
      });
      bloc!.add(GetAddressFromMapEvent(query));
    }
  }

  void _selectAddress(Map<String, dynamic> address) {
    try {
      // Set flag để ngăn API call
      _isSelectingAddress = true;

      final coordinates = address['geometry']['coordinates'];
      final lat = coordinates[1] as double;
      final lng = coordinates[0] as double;
      final label = address['properties']['label'] as String;
      selectedPosition = LatLng(lat, lng);
      selectedAddress = label;
      _searchController.text = label;
      _searchFocusNode.unfocus();
      setState(() {
        _showSearchResults = false;
        _searchResults = [];
        _isSearching = false;
      });
      setPositionMapClick(selectedPosition!);

      // Reset flag sau khi hoàn thành
      _isSelectingAddress = false;
    } catch (e) {
      print('Error selecting address: $e');
      // Reset flag trong trường hợp lỗi
      _isSelectingAddress = false;
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.error, color: Colors.white, size: 40.sp),
                SizedBox(width: 20.sp),
                Expanded(
                  child: LabelCustom(
                    'Lỗi khi chọn địa chỉ. Vui lòng thử lại.',
                    color: Colors.white,
                    size: 32.sp,
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20.sp),
            ),
            margin: EdgeInsets.all(40.sp),
          ),
        );
      }
    }
  }

  void _initializePosition() async {
    if (isEdit && editLocation != null) {
      selectedPosition = LatLng(editLocation!.lat, editLocation!.lng);
      selectedAddress = editLocation!.fullAddress;
    } else if (currentLatitude != null && currentLongitude != null && currentWeatherData != null) {
      // Sử dụng thông tin thời tiết hiện tại được truyền từ weather_agricultural_page
      selectedPosition = LatLng(double.parse(currentLatitude!), double.parse(currentLongitude!));
      selectedAddress = currentWeatherData!.currentDate.location_fullname.isNotEmpty ? currentWeatherData!.currentDate.location_fullname : 'Vị trí hiện tại';
    } else {
      try {
        Position position = await Geolocator.getCurrentPosition();
        selectedPosition = LatLng(position.latitude, position.longitude);
        _reverseGeocode(selectedPosition!);
      } catch (e) {
        selectedPosition = const LatLng(10.762622, 106.660172);
        selectedAddress = 'Vị trí mặc định';
      }
    }
    setState(() {});
  }

  void _onMapCreated(TrackAsiaMapController controller) async {
    mapController = controller;
    try {
      final ByteData bytes = await rootBundle.load('assets/images/v9/map/ic_map_location.png');
      final Uint8List imageData = bytes.buffer.asUint8List();
      await mapController!.addImage("custom-marker", imageData);
    } catch (e) {
      print('Error loading marker image: $e');
    }

    if (selectedPosition != null) {
      setPositionMapClick(selectedPosition!);
    }
  }

  void _onMapClick(Point<double> point, LatLng coordinates) {
    selectedPosition = coordinates;
    setPositionMapClick(coordinates);
    _reverseGeocode(coordinates);
    setState(() {});
  }

  Future<void> setPositionMapClick(LatLng coordinates) async {
    if (mapController == null) return;
    try {
      if (tappedLocationSymbol != null) {
        await mapController!.removeSymbol(tappedLocationSymbol!);
      }
      final symbol = await mapController!.addSymbol(SymbolOptions(geometry: coordinates, iconImage: "assets/images/v9/map/ic_map_location.png", iconSize: 0.8));
      await mapController!.animateCamera(CameraUpdate.newLatLngZoom(coordinates, 15));
      setState(() => tappedLocationSymbol = symbol);
    } catch (e) {
      print('Error setting position: $e');
    }
  }

  void _reverseGeocode(LatLng position) {
    setState(() {
      isLoadingAddress = true;
      selectedAddress = 'Đang tải địa chỉ...';
    });
    setState(() {
      isLoadingAddress = false;
      selectedAddress = 'Lat: ${position.latitude.toStringAsFixed(6)}, Lng: ${position.longitude.toStringAsFixed(6)}';
    });
  }

  void _saveLocation() {
    if (selectedPosition == null) {
      UtilUI.showCustomDialog(context, 'Vui lòng chọn vị trí trên bản đồ');
      return;
    }

    if (isEdit && editLocation != null) {
      bloc!.add(UpdateUserLocationEvent(
        editLocation!.id,
        selectedPosition!.latitude,
        selectedPosition!.longitude,
        selectedAddress,
      ));
    } else {
      bloc!.add(AddUserLocationEvent(
        selectedPosition!.latitude,
        selectedPosition!.longitude,
        selectedAddress,
      ));
    }
  }

  @override
  Widget build(BuildContext context, {Color color = Colors.white}) {
    super.build(context);
    return GestureDetector(
      onTap: () {
        // Hide search results and unfocus when tapping outside
        _searchFocusNode.unfocus();
        setState(() {
          _showSearchResults = false;
        });
      },
      child: Scaffold(
        backgroundColor: const Color(0xFF2BCE85),
        appBar: AppBar(
          backgroundColor: const Color(0xFF2BCE85),
          elevation: 0,
          toolbarHeight: 160.sp,
          leadingWidth: 80.sp,
          leading: Padding(
            padding: EdgeInsets.only(left: 20.sp),
            child: ButtonImageWidget(5, () {
              UtilUI.goBack(context, false);
            }, Icon(Icons.arrow_back, color: Colors.white, size: 60.sp)),
          ),
          title: Container(
            height: 92.sp,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(60.sp),
              boxShadow: [BoxShadow(color: Colors.black.withOpacity(0.1), spreadRadius: 1, blurRadius: 8, offset: const Offset(0, 3))],
            ),
            child: TextField(
              controller: _searchController,
              focusNode: _searchFocusNode,
              decoration: InputDecoration(
                hintText: 'Tìm kiếm địa chỉ...',
                hintStyle: TextStyle(color: Colors.grey.shade500, fontSize: 38.sp),
                prefixIcon: Padding(padding: EdgeInsets.all(20.sp), child: Icon(Icons.search, color: const Color(0xFF2BCE85), size: 50.sp)),
                suffixIcon: _isSearching
                    ? Container(
                        width: 20.sp,
                        height: 20.sp,
                        margin: EdgeInsets.all(20.sp),
                        child: const CircularProgressIndicator(
                          strokeWidth: 2,
                          color: Color(0xFF2BCE85),
                        ),
                      )
                    : _searchController.text.isNotEmpty
                        ? IconButton(
                            onPressed: () {
                              _searchController.clear();
                              setState(() {
                                _showSearchResults = false;
                                _searchResults = [];
                              });
                            },
                            icon: Icon(Icons.clear, color: Colors.grey.shade500, size: 45.sp),
                          )
                        : null,
                border: InputBorder.none,
                contentPadding: EdgeInsets.only(
                  left: 50.sp,
                  right: 50.sp,
                  top: 10.sp,
                  bottom: 0,
                ),
              ),
              style: TextStyle(
                fontSize: 38.sp,
                color: Colors.black87,
              ),
            ),
          ),
          actions: [
            Padding(
              padding: EdgeInsets.only(right: 20.sp),
              child: ButtonImageWidget(0, () {
                UtilUI.goBack(context, false);
              }, Icon(Icons.close, color: Colors.white, size: 60.sp)),
            ),
          ],
        ),
        body: Stack(
          children: [
            // Main content
            Container(
              width: double.infinity,
              color: Colors.white,
              child: Column(
                children: [
                  // Map
                  Expanded(
                    child: Stack(
                      children: [
                        Container(
                          child: selectedPosition != null
                              ? TrackAsiaMap(
                                  minMaxZoomPreference: const MinMaxZoomPreference(1, 24),
                                  styleString: Constants().styleMap,
                                  zoomGesturesEnabled: false,
                                  compassEnabled: false,
                                  myLocationEnabled: false,
                                  initialCameraPosition: CameraPosition(target: selectedPosition!, zoom: 15),
                                  onMapCreated: _onMapCreated,
                                  onMapClick: _onMapClick,
                                )
                              : const Center(child: CircularProgressIndicator()),
                        ),
                        // Positioned(top: 0, left: 0, right: 0, bottom: 0, child: Container(color: Colors.transparent)),
                      ],
                    ),
                  ),
                  Container(
                    width: double.infinity,
                    margin: EdgeInsets.symmetric(horizontal: 40.sp, vertical: 20.sp),
                    child: ButtonImageWidget(
                      0,
                      _saveLocation,
                      Container(
                        padding: EdgeInsets.symmetric(vertical: 26.sp),
                        decoration: BoxDecoration(
                          color: const Color(0xFF2BCE85),
                          borderRadius: BorderRadius.circular(25.sp),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              isEdit ? Icons.update : Icons.add_location,
                              color: Colors.white,
                              size: 60.sp,
                            ),
                            SizedBox(width: 20.sp),
                            LabelCustom(
                              isEdit ? 'Cập nhật địa điểm' : 'Thêm địa điểm',
                              color: Colors.white,
                              size: 42.sp,
                              weight: FontWeight.w500,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            if (_showSearchResults && _searchResults.isNotEmpty)
              Positioned(
                top: 10.sp,
                left: 40.sp,
                right: 40.sp,
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(25.sp),
                    border: Border.all(color: Colors.grey.shade300),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.2),
                        spreadRadius: 2,
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: ConstrainedBox(
                    constraints: BoxConstraints(
                      maxHeight: 560.sp,
                    ),
                    child: ListView.separated(
                      shrinkWrap: true,
                      physics: const BouncingScrollPhysics(),
                      padding: EdgeInsets.zero,
                      itemCount: _searchResults.length,
                      separatorBuilder: (context, index) => Divider(
                        height: 1,
                        color: Colors.grey.shade200,
                      ),
                      itemBuilder: (context, index) {
                        final address = _searchResults[index];
                        final label = address['properties']['label'] ?? '';

                        return ListTile(
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: 40.sp,
                            vertical: 20.sp,
                          ),
                          leading: Icon(
                            Icons.location_on,
                            color: const Color(0xFF2BCE85),
                            size: 50.sp,
                          ),
                          title: LabelCustom(
                            label,
                            color: Colors.black87,
                            size: 38.sp,
                          ),
                          onTap: () => _selectAddress(address),
                        );
                      },
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

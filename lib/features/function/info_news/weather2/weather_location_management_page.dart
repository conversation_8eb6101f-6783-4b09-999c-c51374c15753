import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:hainong/common/ui/button_image_widget.dart';
import 'package:hainong/common/ui/image_network_asset.dart';
import 'package:hainong/common/ui/import_lib_ui.dart';
import 'package:hainong/common/ui/label_custom.dart';
import 'package:hainong/common/ui/loading.dart';

import 'models/user_location_model.dart';
import 'weather_agricultural_bloc.dart';
import 'weather_location_map_page.dart';

class WeatherLocationManagementPage extends BasePage {
  WeatherLocationManagementPage({Key? key}) : super(key: key, pageState: _LocationManagementPageState());
}

class _LocationManagementPageState extends BasePageState {
  List<UserLocationModel>? _userLocations;
  UserLocationModel? _currentLocation;
  List<UserLocationModel>? _savedLocations;

  @override
  void initState() {
    super.initState();
    bloc = WeatherAgriculturalBloc();
    bloc!.stream.listen((state) async {
      if (state is LoadUserLocationsState) {
        _userLocations = (state.response.data as UserLocationListModel).list;
        _processLocations();
      } else if (state is DeleteUserLocationState) {
        if (state.response.success) {
          _showMessageSuccess('Xóa địa chỉ thành công');
          _loadLocations();
        } else {
          UtilUI.showCustomDialog(context, state.response.data);
        }
      } else if (state is SetCurrentLocationState) {
        if (state.response.success) {
          _loadLocations();
        } else {
          UtilUI.showCustomDialog(context, state.response.data);
        }
      }
    });
    _loadLocations();
  }

  void _loadLocations() {
    bloc!.add(LoadUserLocationsEvent());
  }

  Future<void> _onRefresh() async {
    _loadLocations();
  }

  void _processLocations() {
    _currentLocation = null;
    _savedLocations = [];
    if (_userLocations != null) {
      for (var location in _userLocations!) {
        if (!location.isFavorite) {
          _currentLocation = location;
        } else {
          _savedLocations?.add(location);
        }
      }
    }
    setState(() {});
  }

  void _navigateToAddLocation() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => WeatherLocationMapPage(isEdit: false),
      ),
    );
    if (result == true) {
      _loadLocations();
    }
  }

  void _navigateToEditLocation(UserLocationModel location, BuildContext? context) async {
    if (context != null) {
      Slidable.of(context)?.close();
    }

    final result = await Navigator.push(
      this.context,
      MaterialPageRoute(
        builder: (context) => WeatherLocationMapPage(
          editLocation: location,
          isEdit: true,
        ),
      ),
    );
    if (result == true) {
      _loadLocations();
    }
  }

  void _deleteLocation(UserLocationModel location, BuildContext? context) {
    UtilUI.showCustomDialog(
      this.context,
      'Bạn có chắc muốn xóa địa điểm "${location.shortAddress}"?',
      title: 'Xóa địa điểm',
      isActionCancel: true,
    ).then((result) {
      if (result == true) {
        bloc!.add(DeleteUserLocationEvent(location.id));
        if (context != null) {
          Slidable.of(context)?.close();
        }
      }
    });
  }

  void _setCurrentLocation(UserLocationModel location, BuildContext? context) {
    UtilUI.showCustomDialog(
      this.context,
      'Bạn có muốn đặt "${location.fullAddress}" làm địa điểm hiện tại?',
      title: 'Đặt làm địa điểm hiện tại',
      isActionCancel: true,
    ).then((result) {
      if (result == true) {
        bloc!.add(SetCurrentLocationEvent(location.id));
        if (context != null) {
          Slidable.of(context)?.close();
        }
      }
    });
  }

  @override
  Widget build(BuildContext context, {Color color = Colors.white}) {
    super.build(context);
    return Scaffold(
      backgroundColor: const Color(0xFF2BCE85),
      body: Stack(
        children: [
          Column(
            children: [
              // Header
              Container(
                padding: EdgeInsets.fromLTRB(40.sp, WidgetsBinding.instance.window.padding.top.sp + 40.sp, 40.sp, 40.sp),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    ButtonImageWidget(5, () {
                      UtilUI.goBack(context, false);
                    }, Icon(Icons.arrow_back, color: Colors.white, size: 60.sp)),
                    LabelCustom('Địa điểm của tôi', color: Colors.white, size: 54.sp, weight: FontWeight.w600),
                    ButtonImageWidget(0, () {
                      UtilUI.goBack(context, false);
                    }, Icon(Icons.close, color: Colors.white, size: 60.sp))
                  ],
                ),
              ),

              // Content
              Expanded(
                child: Container(
                  width: double.infinity,
                  decoration: const BoxDecoration(color: Colors.white),
                  child: RefreshIndicator(
                    onRefresh: _onRefresh,
                    color: const Color(0xFF2BCE85),
                    backgroundColor: Colors.white,
                    child: SingleChildScrollView(
                      physics: const AlwaysScrollableScrollPhysics(),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          SizedBox(height: 40.sp),
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 40.sp),
                            child: Row(
                              children: [
                                Icon(Icons.location_on, color: const Color(0xFF2BCE85), size: 56.sp),
                                SizedBox(width: 20.sp),
                                LabelCustom('Địa điểm hiện tại', color: const Color(0xFF2BCE85), size: 48.sp, weight: FontWeight.w600),
                              ],
                            ),
                          ),
                          SizedBox(height: 30.sp),
                          _currentLocation == null ? const SizedBox() : _buildLocationItem(_currentLocation!, isCurrentLocation: true),
                          SizedBox(height: 40.sp),
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 40.sp),
                            child: Row(
                              children: [
                                Icon(Icons.bookmark, color: const Color(0xFFFF4A4A), size: 56.sp),
                                SizedBox(width: 20.sp),
                                LabelCustom('Địa điểm đã lưu', color: const Color(0xFFFF4A4A), size: 48.sp, weight: FontWeight.w600),
                              ],
                            ),
                          ),
                          SizedBox(height: 30.sp),
                          _savedLocations == null
                              ? const SizedBox()
                              : _savedLocations!.isEmpty
                                  ? _buildEmptyLocation()
                                  : Column(
                                      children: [
                                        ListView.builder(
                                          shrinkWrap: true,
                                          physics: const NeverScrollableScrollPhysics(),
                                          padding: EdgeInsets.symmetric(horizontal: 40.sp, vertical: 10.sp),
                                          itemCount: _savedLocations?.length ?? 0,
                                          itemBuilder: (context, index) {
                                            return _buildSlidableLocationItem(_savedLocations![index]);
                                          },
                                        ),
                                      ],
                                    ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(horizontal: 60.sp, vertical: 20.sp),
                color: Colors.white,
                child: ButtonImageWidget(
                  0,
                  _navigateToAddLocation,
                  Container(
                    margin: EdgeInsets.all(40.sp),
                    padding: EdgeInsets.symmetric(vertical: 30.sp),
                    decoration: BoxDecoration(
                      color: const Color(0xFF2BCE85),
                      borderRadius: BorderRadius.circular(25.sp),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.add, color: Colors.white, size: 60.sp),
                        SizedBox(width: 20.sp),
                        LabelCustom('Thêm địa điểm', color: Colors.white, size: 48.sp, weight: FontWeight.w500),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
          Loading(bloc),
        ],
      ),
    );
  }

  Widget _buildLocationItem(UserLocationModel location, {bool isCurrentLocation = false}) {
    return Container(
      margin: EdgeInsets.only(bottom: 20.sp, left: 40.sp, right: 40.sp),
      padding: EdgeInsets.symmetric(horizontal: 30.sp, vertical: 30.sp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(25.sp),
        border: Border.all(color: Colors.grey.shade200, width: 2),
        boxShadow: [BoxShadow(color: Colors.grey.withOpacity(0.1), spreadRadius: 1, blurRadius: 5, offset: const Offset(0, 2))],
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                LabelCustom(
                  location.fullAddress.isNotEmpty ? location.fullAddress : location.shortAddress,
                  color: Colors.black,
                  size: 42.sp,
                  weight: FontWeight.w500,
                ),
                SizedBox(height: 10.sp),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Row(
                children: [
                  Container(
                    width: 100.sp,
                    height: 100.sp,
                    decoration: BoxDecoration(
                      color: const Color(0xFFF5F5F5),
                      borderRadius: BorderRadius.circular(15.sp),
                    ),
                    child: ImageNetworkAsset(
                      path: '${Constants().baseUrlImage}/images/weather_icons/${location.weatherStatusIcon}.png',
                      width: 100.sp,
                      height: 100.sp,
                      fit: BoxFit.cover,
                      uiError: const SizedBox(),
                    ),
                  ),
                  SizedBox(width: 20.sp),
                  LabelCustom(
                    location.formattedTemperature,
                    color: Colors.black,
                    size: 42.sp,
                    weight: FontWeight.w600,
                  ),
                ],
              ),
              SizedBox(height: 10.sp),
              LabelCustom(
                location.weatherStatus.isNotEmpty ? location.weatherStatus : 'Chưa có dữ liệu',
                color: Colors.grey.shade600,
                size: 36.sp,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSlidableLocationItem(UserLocationModel location) {
    return Slidable(
      key: ValueKey(location.id),
      endActionPane: ActionPane(
        motion: const ScrollMotion(),
        extentRatio: 0.5,
        dragDismissible: false,
        children: [
          SlidableAction(
            onPressed: (context) => _setCurrentLocation(location, context),
            backgroundColor: const Color(0xFF2BCE85),
            foregroundColor: Colors.white,
            icon: Icons.star,
            label: 'Hiện tại',
            padding: EdgeInsets.symmetric(horizontal: 10.sp, vertical: 0.sp),
            borderRadius: BorderRadius.horizontal(left: Radius.circular(25.sp)),
            autoClose: false,
          ),
          SlidableAction(
            onPressed: (context) => _navigateToEditLocation(location, context),
            backgroundColor: Colors.blue,
            foregroundColor: Colors.white,
            icon: Icons.edit,
            label: 'Sửa',
            padding: EdgeInsets.symmetric(horizontal: 10.sp, vertical: 0.sp),
            autoClose: false,
          ),
          SlidableAction(
            onPressed: (context) => _deleteLocation(location, context),
            backgroundColor: Colors.red,
            foregroundColor: Colors.white,
            icon: Icons.delete,
            label: 'Xóa',
            padding: EdgeInsets.symmetric(horizontal: 10.sp, vertical: 0.sp),
            borderRadius: BorderRadius.horizontal(right: Radius.circular(25.sp)),
            autoClose: false,
          ),
        ],
      ),
      child: Container(
        margin: EdgeInsets.only(bottom: 20.sp),
        padding: EdgeInsets.symmetric(horizontal: 30.sp, vertical: 30.sp),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(25.sp),
          border: Border.all(color: Colors.grey.shade200, width: 2),
          boxShadow: [BoxShadow(color: Colors.grey.withOpacity(0.1), spreadRadius: 1, blurRadius: 5, offset: const Offset(0, 2))],
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  LabelCustom(
                    location.fullAddress.isNotEmpty ? location.fullAddress : location.shortAddress,
                    color: Colors.black,
                    size: 42.sp,
                    weight: FontWeight.w500,
                  ),
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Row(
                  children: [
                    Container(
                      width: 100.sp,
                      height: 100.sp,
                      decoration: BoxDecoration(
                        color: const Color(0xFFF5F5F5),
                        borderRadius: BorderRadius.circular(15.sp),
                      ),
                      child: ImageNetworkAsset(
                        path: '${Constants().baseUrlImage}/images/weather_icons/${location.weatherStatusIcon}.png',
                        width: 100.sp,
                        height: 100.sp,
                        fit: BoxFit.cover,
                        uiError: const SizedBox(),
                      ),
                    ),
                    SizedBox(width: 20.sp),
                    LabelCustom(
                      location.formattedTemperature,
                      color: Colors.black,
                      size: 42.sp,
                      weight: FontWeight.w600,
                    ),
                  ],
                ),
                SizedBox(height: 10.sp),
                LabelCustom(
                  location.weatherStatus.isNotEmpty ? location.weatherStatus : 'Chưa có dữ liệu',
                  color: Colors.grey.shade600,
                  size: 36.sp,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyLocation() {
    return Padding(
      padding: const EdgeInsets.only(top: 20.0),
      child: SizedBox(
          height: 200.sp,
          child: Center(
              child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.location_off,
                color: Colors.grey.shade400,
                size: 80.sp,
              ),
              SizedBox(height: 20.sp),
              LabelCustom(
                'Chưa có địa điểm đã lưu',
                color: Colors.grey.shade600,
                size: 36.sp,
              ),
            ],
          ))),
    );
  }

  void _showMessageSuccess(String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.transparent,
      builder: (BuildContext context) {
        return Center(
          child: Container(
            margin: EdgeInsets.symmetric(horizontal: 40.sp, vertical: 20.sp),
            padding: EdgeInsets.symmetric(horizontal: 30.sp, vertical: 20.sp),
            decoration: BoxDecoration(
              color: const Color(0xFF2BCE85),
              borderRadius: BorderRadius.circular(20.sp),
              boxShadow: [BoxShadow(color: Colors.black.withOpacity(0.2), spreadRadius: 2, blurRadius: 10, offset: const Offset(0, 4))],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.check_circle, color: Colors.white, size: 40.sp),
                SizedBox(width: 20.sp),
                Flexible(child: LabelCustom(message, color: Colors.white, size: 32.sp, align: TextAlign.center)),
              ],
            ),
          ),
        );
      },
    );

    // Tự động đóng dialog sau 2 giây
    Future.delayed(const Duration(seconds: 2), () {
      if (Navigator.canPop(context)) {
        Navigator.pop(context);
      }
    });
  }
}

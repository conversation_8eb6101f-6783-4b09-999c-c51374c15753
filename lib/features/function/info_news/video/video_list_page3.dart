import 'dart:async';
import 'dart:io';
import 'package:hainong/common/models/item_list_model.dart';
import 'package:hainong/common/ui/import_lib_ui.dart';
import 'package:hainong/common/ui/label_custom.dart';
import 'package:hainong/features/comment/ui/comment_page.dart';
import 'package:hainong/features/post/model/post.dart';
import 'video_bloc3.dart';
import 'video_ui3.dart';

class VideoListPage extends BasePage {
  VideoListPage({Key? key, String tag = '', int idDetail = -1, bool isFav = false}):super(key: key, pageState: _VideoListPageState(tag, idDetail, isFav));
}

class _VideoListPageState extends BasePageState {
  late VideoBloc _bloc;
  _VideoListPageState(String tag, int idDetail, bool isFav) {
    _bloc = VideoBloc(tag, idDetail, isFav);
  }

  @override
  void dispose() {
    _bloc.close();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _bloc.stream.listen((state) {
      if (state is AddFavoriteState) {
        isResponseNotError(state.response);
      } else if (state is RemoveFavoriteState) {
        isResponseNotError(state.response, passString: true);
      } else if (state is CreatePostState && isResponseNotError(state.response)) {
        UtilUI.showCustomDialog(context, state.response.data, title: 'Thông báo');
      }
    });
  }

  @override
  Widget build(BuildContext context, {Color color = Colors.white}) =>
    Scaffold(backgroundColor: Colors.black, body: Stack(children: [
      BlocBuilder(bloc: _bloc, buildWhen: (_,newS) => newS is LoadListState || newS is AutoSwitchState,
        builder: (_,__) => _bloc.list.isEmpty ? const SizedBox() : PageView.custom(scrollDirection: Axis.vertical,
          onPageChanged: _bloc.onPageChanged, controller: _bloc.scaleController, padEnds: false, allowImplicitScrolling: true,
          childrenDelegate: SliverChildBuilderDelegate((_,index) => Item(_bloc.list[index], index, _bloc), childCount: _bloc.list.length)
        )),

      if (Platform.isAndroid)
      BlocBuilder(bloc: _bloc, buildWhen: (_,newS) => newS is HideAddressState,
          builder: (_,__) => _bloc.isInit != _bloc.index ? Container(width: 1.sw,
              height: 1.sh, color: Colors.transparent) : const SizedBox()),

      Container(child: BlocBuilder(bloc: _bloc,
        buildWhen: (_,newDtl) => newDtl is LoadTechProDtlState || newDtl is ChangeStatusManageState,
        builder: (_,__) => _bloc.list.isEmpty || _bloc.isComment != null ? const SizedBox() : Column(children: [
          Padding(padding: EdgeInsets.symmetric(vertical: 80.sp), child:
            Button('assets/images/ic_love_${_bloc.list[_bloc.index]['is_favourite']??false ? 'fill' : 'outline'}.png',
                _bloc.like, _bloc.list[_bloc.index]['total_favourites']??0, 'Yêu thích',
                color: (_bloc.list[_bloc.index]['is_favourite']??false) ? Colors.red : Colors.white)),
          Button('assets/images/ic_comment.png', _comment2, _bloc.list[_bloc.index]['total_comment']??0, 'Bình luận'),
          Padding(padding: EdgeInsets.symmetric(vertical: 80.sp), child:
            Button('', (){}, _bloc.list[_bloc.index]['viewed']??0, 'Lượt xem', icon: Icons.remove_red_eye)),
          Button('assets/images/ic_share.png', _share, 0, 'Chia sẻ'),
        ], mainAxisSize: MainAxisSize.min)), alignment: Alignment.bottomRight,
        margin: EdgeInsets.fromLTRB(0, 100, 40.sp, 0.2.sh)),

      BlocBuilder(bloc: _bloc, buildWhen: (_,newS) => newS is ChangeStatusManageState,
        builder: (_,__) => _bloc.isComment != null ? const SizedBox() :
          SizedBox(height: 0.2.sh, child: Scaffold(backgroundColor: Colors.transparent,
            appBar: AppBar(elevation: 0, centerTitle: true,
                backgroundColor: Colors.transparent, title: _title()))))
    ]));

  Widget _title() {
    if (_bloc.idDetail > 0) return const SizedBox();
    if (_bloc.isFav) return LabelCustom('Video quan tâm', weight: FontWeight.w400, size: 40.sp);
    if (_bloc.tag.isNotEmpty) return LabelCustom('#' + _bloc.tag, weight: FontWeight.w400, size: 40.sp);
    return BlocBuilder(bloc: _bloc, buildWhen: (_,newS) => newS is AutoSwitchState,
        builder: (_,__) => Row(children: [
          _tab('Mới nhất', 'normal'),
          const SizedBox(),
          _tab('Nổi bật', 'outstanding'),
          const SizedBox(),
        ], mainAxisAlignment: MainAxisAlignment.spaceAround));
  }

  Widget _tab(String title, String type) => GestureDetector(onTap: () => _bloc.changeTab(type),
    child: Container(decoration: BoxDecoration(
      border: Border(bottom: BorderSide(color: _bloc.tab == type ? Colors.white : Colors.transparent))),
      child: LabelCustom(title, size: 40.sp, weight: FontWeight.w400)
  ));

  void _share() {
    if (_bloc.lock) return;
    _bloc.lock = true;

    if(!constants.isLogin) {
      _shareToApp();
      return;
    }

    final List<ItemModel> options = [
      ItemModel(id: 'share_app', name: 'Chia sẻ qua ứng dụng khác'),
      ItemModel(id: 'share_post', name: 'Chia sẻ lên tường của tôi')
    ];
    UtilUI.showOptionDialog(context, MultiLanguage.get('ttl_option'), options, '').then((value) {
      if (value != null) value.id == 'share_app' ? _shareToApp() : _bloc.shareToPost();
    }).whenComplete(() => Timer(const Duration(milliseconds: 2000), () => _bloc.lock = false));
  }

  void _shareToApp() {
    _bloc.lock = true;
    UtilUI.shareTo(context, '/short_videos/' + _bloc.list[_bloc.index]['id'].toString(), 'Short video -> Option Share Dialog -> Choose "Share"', 'short_videos');
    Timer(const Duration(milliseconds: 2000), () => _bloc.lock = false);
  }

  void _comment() {
    _bloc.play(id: 0);
    showDialog(useSafeArea: true, context: context, builder: (_) => Column(children: [
      SizedBox(height: 0.6.sh, child:
      CommentPage(Post(classable_id: _bloc.list[_bloc.index]['id'].toString(),
          classable_type: _bloc.list[_bloc.index]['classable_type']), hasHeader: true, height: 0.6.sh))
    ], mainAxisSize: MainAxisSize.min, mainAxisAlignment: MainAxisAlignment.end)).whenComplete(() {
      _bloc.play();
      _bloc.loadDetail();
    });
  }

  void _comment2() {
    _bloc.startComment();
    showDialog(useSafeArea: true, context: context, barrierColor: Colors.transparent,
      builder: (_) => Column(children: [
        SizedBox(height: 0.6.sh, child: CommentPage(Post(classable_id: _bloc.list[_bloc.index]['id'].toString(),
            classable_type: _bloc.list[_bloc.index]['classable_type']), openKeyboard: true, height: 0.6.sh, notStatusBar: true))
      ], mainAxisSize: MainAxisSize.min, mainAxisAlignment: MainAxisAlignment.end)).whenComplete(_bloc.endComment);
  }
}
import 'dart:async';
import 'dart:io';
import 'package:video_thumbnail/video_thumbnail.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';
import 'package:advn_video/advn_video.dart';
import 'package:hainong/common/base_bloc.dart';
import 'package:hainong/common/ui/image_network_asset.dart';
import 'package:hainong/common/ui/import_lib_base_ui.dart';
import 'package:hainong/common/ui/label_custom.dart';
import 'package:hainong/common/util/util.dart';
import 'package:hainong/common/util/util_ui.dart';
import 'video_bloc3.dart';
import 'video_list_page3.dart';

class Button extends StatelessWidget {
  final String asset, title;
  final Function funAction;
  final int num;
  final IconData? icon;
  final Color color;
  const Button(this.asset, this.funAction, this.num, this.title, {this.icon, this.color = Colors.white, Key? key}) : super (key: key);

  @override
  Widget build(BuildContext context) {
    String title = Util().formatNum2(num.toDouble(), digit: 1);
    if (title.isEmpty) title = this.title;
    return GestureDetector(onTap: () => funAction(), child: Column(children: [
      icon == null ? Image.asset(asset, height: 80.sp, width: 80.sp, color: color) :
      Icon(icon, color: color, size: 80.sp),
      SizedBox(height: 10.sp),
      LabelCustom(title, size: 30.sp, weight: FontWeight.w400)
    ]));
  }
}

class Item extends StatefulWidget {
  late _ItemState pageState;
  Item(dynamic item, int index, VideoBloc bloc, {Key? key}) : super(key: key) {
    pageState = _ItemState(item, index, bloc);
  }

  @override
  _ItemState createState() => pageState;
}
class _ItemState extends State<Item> {
  final dynamic item;
  final int index;
  final VideoBloc bloc;
  final BaseBloc _bloc = BaseBloc(init: const BaseState(isShowLoading: true));
  late String type;
  _ItemState(this.item, this.index, this.bloc) {
    type = item['media_type'] == 'youtube' ? (YoutubePlayer.convertUrlToId(item['video_url']??'')??'') : 'video';
  }

  @override
  void dispose() {
    _bloc.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(height: 1.sh, width: 1.sw, child: Stack(children: [
      type == 'video' ? _Video(item['id'], index, item['video_url']??'', item['image']??'', type, bloc, _bloc) :
        _Youtube(item['id'], index, item['video_url']??'', item['image']??'', type, bloc, _bloc),
      Padding(padding: EdgeInsets.fromLTRB(40.sp, 100, 240.sp, View.of(context).padding.bottom.sp),
          child: Column(children: [
            Row(children: [
              ClipRRect(borderRadius: BorderRadius.circular(200),
                  child: Image.asset('assets/images/bg_splash.png', width: 100.sp, height: 100.sp, fit: BoxFit.cover)),
              LabelCustom(' 2Nông', size: 40.sp)
            ]),
            SizedBox(height: 20.sp),
            if ((item['title']??'').isNotEmpty) LabelCustom(item['title'], size: 40.sp, weight: FontWeight.w500),
            SizedBox(height: 20.sp),
            if ((item['description']??'').isNotEmpty) LabelCustom(item['description'], size: 34.sp, weight: FontWeight.w400),
            SizedBox(height: 20.sp),
            _Hashtag(item['tags'], bloc)
          ], crossAxisAlignment: CrossAxisAlignment.start, mainAxisAlignment: MainAxisAlignment.end))
    ], alignment: Alignment.center));
  }
}

class _Hashtag extends StatelessWidget {
  final VideoBloc bloc;
  final dynamic tags;
  const _Hashtag(this.tags, this.bloc, {Key? key}):super(key: key);
  @override
  Widget build(BuildContext context) {
    if (tags == null || tags.isEmpty) return const SizedBox();
    final List<Widget> list = [];
    for (var item in tags) {
      list.add(GestureDetector(onTap: () {
        if (bloc.tag.isEmpty) {
          bloc.play(id: 0);
          UtilUI.goToNextPage(context, VideoListPage(tag: item), funCallback: (value) => bloc.play());
        } else UtilUI.goToPage(context, VideoListPage(tag: item), null);
      }, child: LabelCustom('#' + item, size: 36.sp, weight: FontWeight.bold)));
    }
    return Wrap(children: list, spacing: 20.sp, runSpacing: 20.sp);
  }
}

class _Video extends StatefulWidget {
  final int id, index;
  final String url, type, image;
  final VideoBloc bloc;
  final BaseBloc subBloc;
  const _Video(this.id, this.index, this.url, this.image, this.type, this.bloc, this.subBloc, {Key? key}):super(key: key);
  @override
  _VideoState createState() => _VideoState();
}
class _VideoState extends State<_Video> {
  final ctrVideo = BetterVideoPlayerController();
  bool _isPlay = true;
  int _count = 0;
  Widget? _thumbnail;
  Timer? timePlay, timeForce;
  late StreamSubscription _stream;
  late StreamSubscription<BetterVideoPlayerEvent> _streamVideo;

  @override
  void initState() {
    _thumbnail = Image.network(widget.image, width: 1.sw, height: 1.sh, fit: BoxFit.fitHeight);
    _getThumbnail();
    _streamVideo = ctrVideo.playerEventStream.listen(_listenerPlayer);
    _stream = widget.bloc.stream.listen((state) {
      if (!mounted) return;
      if (state is SetHeightState) {
        final value = state.height.toInt() == widget.id;
        _setPlayVideo(value, value, playStart: state.ext['reset'], index: state.ext['index']);
      }
    });
    super.initState();
  }

  @override
  void dispose() {
    if (ctrVideo.videoPlayerValue != null && ctrVideo.videoPlayerValue!.isPlaying) {
      ctrVideo.pause().whenComplete(() {
        ctrVideo.dispose();
      });
    } else ctrVideo.dispose();
    _streamVideo.cancel();
    _stream.cancel();
    if (timePlay != null) {
      timePlay!.cancel();
      timePlay = null;
    }
    if (timeForce != null) {
      timeForce!.cancel();
      timeForce = null;
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) => GestureDetector(onTap: _onTap, child:
    Stack(alignment: Alignment.center, children: [
      BlocBuilder(bloc: widget.bloc, buildWhen: (_,newS) => newS is BaseState && newS.isShowLoading ||
          newS is HideAddressState || newS is ChangeStatusManageState,
        builder: (_,__) {
          //print('\nadvn-video: render opacity 0.6 bg');
          return widget.bloc.isComment == null ? Opacity(opacity: 0.6, child: _thumbnail) : const SizedBox();
        }),

      BlocBuilder(bloc: widget.bloc, buildWhen: (_,newS) => newS is ChangeStatusManageState,
        builder: (_,__) {
          //print('\nadvn-video: render video');
          return widget.bloc.isComment == null ? BetterVideoPlayer(
              controller: ctrVideo, bgColor: Colors.transparent, isScale: true, isCalculateScale: false,
              configuration: const BetterVideoPlayerConfiguration(autoPlay: false,
                  controls: BetterVideoPlayerControls(hideTime: true, isFullScreen: false, showControl: false)),
              dataSource: BetterVideoPlayerDataSource(BetterVideoPlayerDataSourceType.network, widget.url)) :
          Align(alignment: Alignment.topCenter, child: SizedBox(width: 1.sw, height: 0.4.sh, child: BetterVideoPlayer(
              controller: ctrVideo, bgColor: Colors.transparent, isScale: true, isCalculateScale: false,
              configuration: const BetterVideoPlayerConfiguration(autoPlay: false,
                  controls: BetterVideoPlayerControls(hideTime: true, isFullScreen: false, showControl: false)),
              dataSource: BetterVideoPlayerDataSource(BetterVideoPlayerDataSourceType.network, widget.url))));
        }
      ),

      if (Platform.isAndroid)
      BlocBuilder(bloc: widget.bloc, buildWhen: (_,newS) => newS is BaseState && newS.isShowLoading || newS is HideAddressState,
        builder: (_,__) {
          //print('\nadvn-video: render opacity 1 bg');
          return widget.bloc.isComment == null ? AnimatedOpacity(opacity: widget.bloc.isInit == widget.index ? 0 : 1,
              duration: const Duration(milliseconds: 2000), child: _thumbnail) : const SizedBox();
        }),

      BlocBuilder(bloc: widget.subBloc, builder: (_,__) {
        //print('\nadvn-video: render play/pause');
        if (_isPlay) return const SizedBox();
        return BlocBuilder(bloc: widget.bloc, buildWhen: (_,newS) => newS is ChangeStatusManageState,
          builder: (_,__) => widget.bloc.isComment == null ? Container(width: 150.sp, height: 150.sp,
            child: Icon(Icons.play_arrow, color: Colors.white, size: 100.sp),
            decoration: BoxDecoration(color: Colors.black45, borderRadius: BorderRadius.circular(200))) : const SizedBox());
      })
    ])
  );

  void _onTap() => _setPlayVideo(!_isPlay, true);

  Future<void> _getThumbnail() async {
    try {
      final data = await VideoThumbnail.thumbnailData(video: widget.url, quality: 25, imageFormat: ImageFormat.JPEG);
      if (data != null) {
        _thumbnail = Image.memory(data, width: 1.sw, height: 1.sh, fit: BoxFit.fitHeight);
        widget.bloc.add(LoadingEvent(true));
      }
    } catch (_) {}
  }

  void _listenerPlayer(event) async {
    if (!mounted) return;
    switch (event.type) {
      case BetterVideoPlayerEventType.onPlayEnd:
        widget.bloc.isComment == null ? widget.bloc.playNext() : ctrVideo.play();
        break;
      case BetterVideoPlayerEventType.onPlay:
        if (widget.bloc.isInit == null) {
          widget.bloc.isInit = widget.index;
          if (Platform.isAndroid) {
            timePlay = Timer(const Duration(milliseconds: 200), () {
              timePlay!.cancel();
              timePlay = null;
              widget.bloc.add(HideAddressEvent());
            });
          } else widget.bloc.add(HideAddressEvent());
        }
    }
  }

  void _forcePlayVideo() {
    if (_count > 4 || (widget.bloc.list.isNotEmpty && widget.bloc.index != widget.index)) {
      if (_count > 4) {
        _onTap();
        timeForce = Timer(const Duration(milliseconds: 2000), () {
          timeForce!.cancel();
          timeForce = null;
          _onTap();
          widget.bloc.isInit = widget.index;
          widget.bloc.add(HideAddressEvent());
        });
      }
      return;
    }
    _count++;
    if (ctrVideo.videoPlayerValue == null || (Platform.isAndroid && !ctrVideo.videoPlayerValue!.isInitialized)) {
      timeForce = Timer(const Duration(milliseconds: 500), () {
        timeForce!.cancel();
        timeForce = null;
        _forcePlayVideo();
      });
      return;
    }
    timeForce = Timer(const Duration(milliseconds: 100), () => ctrVideo.play().whenComplete(() {
      timeForce!.cancel();
      timeForce = null;
      if (ctrVideo.videoPlayerValue == null || !ctrVideo.videoPlayerValue!.isPlaying) {
        timeForce = Timer(const Duration(milliseconds: 500), () {
          timeForce!.cancel();
          timeForce = null;
          _forcePlayVideo();
        });
      }
    }));
  }

  void _setPlayVideo(bool value, bool setValue, {bool playStart = false, int? index}) async {
    _isPlay = value;
    if (value) {
      if (ctrVideo.videoPlayerValue != null && ctrVideo.videoPlayerValue!.isPlaying) return;
      _forcePlayVideo();
    } else {
      await ctrVideo.pause();
      if (playStart && index == widget.index) {
        await ctrVideo.seekTo(const Duration(milliseconds: 0));
        widget.bloc.add(ChangeStatusManageEvent());
        _isPlay = true;
        widget.subBloc.add(LoadingEvent(false));
      }
      _count = 0;
    }
    if (setValue || index == widget.index) widget.subBloc.add(LoadingEvent(value));
  }
}

class _Youtube extends StatefulWidget {
  final int id, index;
  final String url, type, image;
  final VideoBloc bloc;
  final BaseBloc subBloc;
  const _Youtube(this.id, this.index, this.url, this.image, this.type, this.bloc, this.subBloc, {Key? key}):super(key: key);
  @override
  _YoutubeState createState() => _YoutubeState();
}
class _YoutubeState extends State<_Youtube> {
  dynamic ctrYTB, ctrYTBCmt, firstImage;
  Widget? _ytb, _ytbCmt, _thumbnail;
  bool _isShort = false, _isPlay = false;
  int _count = 1;
  late StreamSubscription _stream;
  Timer? timeForce;

  @override
  void dispose() {
    if (ctrYTB != null) {
      ctrYTB.pause();
      ctrYTB.removeListener(_listenerYTB);
      ctrYTB.dispose();
      ctrYTB = null;
      ctrYTBCmt.removeListener(_listenerYTB);
      ctrYTBCmt.dispose();
      ctrYTBCmt = null;
    }
    if (timeForce != null) {
      timeForce!.cancel();
      timeForce = null;
    }
    _stream.cancel();
    super.dispose();
  }

  @override
  void initState() {
    _initYTB();
    _thumbnail = Image.network(widget.image, width: 1.sw, height: 1.sh, fit: BoxFit.fitHeight);
    _getThumbnail();
    _stream = widget.bloc.stream.listen((state) {
      if (state is SetHeightState) {
        final value = state.height.toInt() == widget.id;
        play(value, setValue: value, playStart: state.ext['reset'], index: state.ext['index']);
      } else if (state is ChangeStatusManageState && ctrYTB != null && _isPlay) {
        if (widget.bloc.isComment != null) {
          ctrYTB.pause();
          ctrYTBCmt.seekTo(ctrYTB.value.position);
          ctrYTBCmt.play();
        } else {
          ctrYTBCmt.pause();
          ctrYTB.seekTo(ctrYTBCmt.value.position);
          ctrYTB.play();
        }
      } else if (state is ChangeStatusManageState && ctrYTB != null && !_isPlay && widget.bloc.isComment != null) {
        Timer(const Duration(seconds: 1), () => setState(() {}));
      }
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) => GestureDetector(onTap: _onTap, child:
    BlocBuilder(bloc: widget.subBloc, builder: (_,__) => Stack(children: [
    if (ctrYTB != null) Scaffold(backgroundColor: Colors.transparent,
        appBar: AppBar(elevation: 0, backgroundColor: Colors.transparent, automaticallyImplyLeading: false),
        body: GestureDetector(onTap: _onTap, child: _isShort ? _ytb : Container(height: 1.sh, width: 1.sw,
            alignment: Alignment.center, color: Colors.black, child: _ytb,
            padding: EdgeInsets.only(bottom: View.of(context).padding.bottom.sp + kToolbarHeight)))),

    BlocBuilder(bloc: widget.bloc, buildWhen: (_,newS) => newS is ChangeStatusManageState, builder: (_,__) {
      if (ctrYTB == null) return const SizedBox();
      return Align(alignment: Alignment.topCenter, child: SizedBox(width: 1.sw, height: 0.4.sh,
          child: AnimatedOpacity(opacity: widget.bloc.isComment == null ? 0 : 1,
              duration: const Duration(milliseconds: 300), child: _ytbCmt)));
    }),

    if (!_isPlay && widget.bloc.isComment == null) BlocBuilder(bloc: widget.bloc,
      buildWhen: (_,newS) => newS is BaseState && newS.isShowLoading,
      builder: (_,__) => _isShort ? Scaffold(backgroundColor: Colors.transparent,
        appBar: AppBar(elevation: 0, backgroundColor: Colors.black, automaticallyImplyLeading: false),
        body: Transform.scale(scale: 2.68, child: SizedBox(width: 1.sw, height: 1.sh, child: _thumbnail))) : _thumbnail!
    ),

    if (!_isPlay && ctrYTB != null)
      Container(width: 150.sp, height: 150.sp, child: Icon(Icons.play_arrow, color: Colors.white, size: 100.sp),
          decoration: BoxDecoration(color: Colors.black45, borderRadius: BorderRadius.circular(200)))
  ], alignment: Alignment.center)));

  void _onTap() => play(!_isPlay, setValue: true);

  Future<void> _getThumbnail() async {
    _thumbnail = ImageNetworkAsset(path: 'https://i3.ytimg.com/vi/${widget.type}/sddefault.jpg', cache: true, rateCache: 2,
        asset: 'assets/images/v8/ic_transparent.png', error: 'assets/images/v8/ic_transparent.png', width: 1.sw, fit: BoxFit.fitWidth);
    widget.bloc.add(LoadingEvent(true));
  }

  void _forcePlayYTB() {
    if (_count > 4) {
      _onTap();
      timeForce = Timer(const Duration(seconds: 1), () {
        _clearTime();
        _onTap();
      });
      return;
    }
    _count++;
    timeForce = Timer(const Duration(milliseconds: 1000), () {
      _clearTime();
      if (_isPlay && (!ctrYTB.value.isReady || !ctrYTB.value.isPlaying)) _setPlayYTB(_isPlay, false);
    });
  }

  void _clearTime() {
    timeForce!.cancel();
    timeForce = null;
  }

  Future<void> _initYTB() async {
    if (ctrYTB != null) return;
    ctrYTB = YoutubePlayerController(initialVideoId: widget.type, flags: const YoutubePlayerFlags(loop: true,
        autoPlay: false, enableCaption: false, hideThumbnail: true, hideControls: true));
    ctrYTB.addListener(_listenerYTB);

    _isShort = widget.url.contains('shorts');
    _ytb = YoutubePlayer(controller: ctrYTB, width: 1.sw, onEnded: (data) => widget.bloc.playNext(), onReady: () {
      if (_isPlay && !ctrYTB.value.isPlaying) ctrYTB.pause();
    }, aspectRatio: _isShort ? 0.5 : 16/9);

    ctrYTBCmt = YoutubePlayerController(initialVideoId: widget.type, flags: const YoutubePlayerFlags(loop: true,
        autoPlay: false, enableCaption: false, hideThumbnail: false, hideControls: true));
    ctrYTBCmt.addListener(_listenerYTBCmt);

    _ytbCmt = YoutubePlayer(controller: ctrYTBCmt, width: 1.sw, onEnded: (data) {
      ctrYTBCmt.seekTo(const Duration(milliseconds: 0));
      ctrYTBCmt.play();
    });
  }

  void _listenerYTB() {
    if (_isPlay && ctrYTB.value.isPlaying) {
      widget.subBloc.add(LoadingEvent(true));
      return;
    }
    if (!_isPlay && ctrYTB.value.isPlaying) ctrYTB.pause();
  }

  void _listenerYTBCmt() {
    if (widget.bloc.isComment == null && ctrYTBCmt.value.isPlaying) ctrYTBCmt.pause();
  }

  void _setPlayYTB(bool value, bool setValue, {bool playStart = false}) {
    _isPlay = value;
    if (value) {
      _initYTB();
      if (!ctrYTB.value.isPlaying) {
        if (ctrYTB.value.isReady) ctrYTB.play();
        _forcePlayYTB();
      }
    } else {
      ctrYTB?.pause();
      if (playStart) {
        ctrYTB?.removeListener(_listenerYTB);
        ctrYTB?.dispose();
        ctrYTB = null;
        _ytb = null;

        ctrYTBCmt?.removeListener(_listenerYTBCmt);
        ctrYTBCmt?.dispose();
        ctrYTBCmt = null;
        _ytbCmt = null;

        widget.subBloc.add(LoadingEvent(false));
      }
      _count = 0;
    }
    if (setValue) widget.subBloc.add(LoadingEvent(value));
  }

  void play(bool play, {bool setValue = true, bool playStart = false, int? index}) {
    _setPlayYTB(play, setValue, playStart: playStart);
  }
}
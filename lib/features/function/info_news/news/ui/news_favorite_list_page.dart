import 'dart:async';
import 'package:hainong/common/database_helper.dart';
import 'package:hainong/common/ui/import_lib_ui.dart';
import 'package:hainong/common/ui/loading.dart';
import 'package:hainong/common/ui/tab_item.dart';
import 'package:hainong/features/post/bloc/post_list_bloc.dart';
import '../news_bloc.dart';
import '../news_model.dart';
import '../../video/video_list_page3.dart';
import 'news_detail2_page.dart';
import 'news_item.dart';

class NewsFavoriteListPage extends BasePage {
  NewsFavoriteListPage({Key? key}):super(key: key, pageState: _NewsFavoriteListPageState());
}

class _NewsFavoriteListPageState extends BasePageState {
  int _page = 1, _lock = 0;
  final List<NewsModel> _list = [NewsModel()];
  final ScrollController _scroller = ScrollController();

  @override
  void dispose() {
    _list.clear();
    _scroller.dispose();
    DBHelper().clearTable('news');
    super.dispose();
  }

  @override
  initState() {
    bloc = NewsBloc();
    super.initState();
    bloc!.stream.listen((state) {
      if (state is LoadListLikeNewsState) {
        if (isResponseNotError(state.response)) {
          final list = state.response.data.list;
          if (list.isNotEmpty) _list.addAll(list);
          list.length == 20 ? _page++ : _page = 0;
        }
        _lock = 0;
      }
    });
    _loadMore();
    _scroller.addListener(() {
      if (_page > 0 && _scroller.position.maxScrollExtent == _scroller.position.pixels) _loadMore();
    });
  }

  @override
  Widget build(BuildContext context, {Color color = Colors.white}) => Stack(
      children: [
        Scaffold(appBar: AppBar(title: UtilUI.createLabel('Danh sách tin quan tâm'), centerTitle: true),
            body: Column(children: [
              Container(color: Colors.white, child: Row(children: [
                TabItem('Tin nông nghiệp', 0, true, _changeTab, parseTitle: false),
                TabItem('Tin video', 1, false, _changeTab, parseTitle: false),
              ])),
              Expanded(child: RefreshIndicator(onRefresh: _reload, child: BlocBuilder(bloc: bloc,
                  buildWhen: (state1, state2) => state2 is ChangeTabState || state2 is LoadListLikeNewsState,
                  builder: (context, state) => ListView.separated(
                      padding: EdgeInsets.zero, controller: _scroller, itemCount: _list.length,
                      itemBuilder: (context, index) {
                        if (index > 0) return NewsItem(_list[index], index, _playNext, isEdit: false, reload: _reload);
                        return const SizedBox();
                      },
                      separatorBuilder: (context, index) => const Divider(height: 1),
                      physics: const AlwaysScrollableScrollPhysics()))))
            ]), backgroundColor: Colors.white
        ),
        Loading(bloc)
      ]
  );

  void _loadMore() => bloc!.add(LoadListLikeNewsEvent(_page, 0));

  void _changeTab(int index) {
    if (index == 1) UtilUI.goToNextPage(context, VideoListPage(isFav: true));
    String type = index == 0 ? 'Articles' : 'Video';
    Util.trackActivities(type.toLowerCase(), path: 'Favorite $type List Screen -> Choose Tab "$type"');
  }

  Future<void> _reload() async {
    if (_lock == 1) return;
    _list.removeRange(1, _list.length);
    _page = 1;
    _lock = 1;
    Timer(const Duration(seconds: 1), () => setState(() => _loadMore()));
  }

  void _playNext(int index, {bool isPre = true}) {
    isPre ? index++ : index--;
    if (index < 1) return;
    if (index == _list.length) return;
    Navigator.of(context).pushReplacement(PageRouteBuilder(pageBuilder: (context, animation, secondaryAnimation) =>
        NewsDetail2Page(_list[index], index, funPlayNext: _playNext, isNext: index < _list.length - 1, funReload: _reload),
        transitionDuration: const Duration(milliseconds: 500),
        reverseTransitionDuration: const Duration(milliseconds: 500),
        transitionsBuilder: (context, animation, secondaryAnimation, child) =>
            SlideTransition(child: child, position: animation.drive(Tween(begin: Offset(isPre ? 1 : -1, 0), end: Offset.zero)
                .chain(CurveTween(curve: Curves.easeInOut))))
    ));
    if (index == _list.length - 5) _loadMore();
  }
}

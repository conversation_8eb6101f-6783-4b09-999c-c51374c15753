import 'dart:async';
import 'package:flutter_html/flutter_html.dart';
import 'package:just_audio/just_audio.dart';
import 'package:url_launcher/url_launcher.dart';

import 'package:hainong/common/base_response.dart';
import 'package:hainong/common/database_helper.dart';
import 'package:hainong/common/models/item_list_model.dart';
import 'package:hainong/common/ui/button_image_widget.dart';
import 'package:hainong/common/ui/label_custom.dart';
import 'package:hainong/common/ui/import_lib_ui.dart';
import 'package:hainong/features/comment/ui/comment_page.dart';
import 'package:hainong/features/post/model/post.dart';
import 'package:hainong/features/profile/ui/show_avatar_page.dart';
import 'news_list_page.dart';
import '../news_bloc.dart';
import '../news_model.dart';

class NewsDetail2Page extends BasePage {
  final NewsModel item;
  final bool isNext;
  final Function? funPlayNext, funReload;
  final int index;
  NewsDetail2Page(this.item, this.index, {this.funPlayNext, this.funReload, Key? key,
    this.isNext = true}) : super(key: key, pageState: _NewsDetailPageState());
}

class _NewsDetailPageState extends BasePageState with WidgetsBindingObserver {
  AudioPlayer? _player;
  bool _isPlay = false, _checkPostAuto = false;
  bool? _hasResume;
  String _content = '';

  @override
  void dispose() {
    try {
      if (_player != null) _player!.stop().whenComplete(() => _player!.dispose());
    } catch (_) {}
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    final item = (widget as NewsDetail2Page).item;
    bloc = NewsBloc(isNews: true, id: item.id);
    if (item.content.contains('2@25!NEWS\$2@25')) {
      DBHelper().getJsonById('id', item.id, 'news').then((value) {
        if (value != null) {
          _content = value['content']??'';
          bloc!.add(SetHeightEvent(1));
        }
      });
    } else {
      _content = item.content;
      bloc!.add(SetHeightEvent(1));
    }
    bloc!.add(CheckProcessPostAutoInNewsEvent());
    super.initState();
    _initPlayController();
    bloc!.stream.listen((state) {
      if (state is AddFavoriteState) {
        _handleResponseAddFavorite(state);
      } else if (state is RemoveFavoriteState) {
        _handleResponseRemoveFavorite(state);
      } else if (state is CreatePostState && isResponseNotError(state.response))  {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content:
          Text(MultiLanguage.get('msg_'+(_checkPostAuto?'':'in')+'active_process_post_auto'))));
      } else if(state is CheckProcessPostAutoInNewsState) {
        _checkPostAuto = state.isActive ?? false;
      }
    });
    DBHelperUtil().isSwipeLeft().then((value) {
      if (value) Timer(const Duration(seconds: 1), () => _showGuidSwipeLeft());
    });
  }

  @override
  Widget build(BuildContext context, {Color color = Colors.white}) {
    final page = widget as NewsDetail2Page;
    return Stack(children: [
      Scaffold(appBar: AppBar(elevation: 5, titleSpacing: 0,
          title: UtilUI.createLabel('Chi tiết bài viết', textAlign: TextAlign.center), actions: [
            IconButton(onPressed: _showMenu, icon: Image.asset('assets/images/ic_share.png',
                width: 48.sp, height: 48.sp, color: Colors.white))
          ], centerTitle: true),
        backgroundColor: Colors.white,
        body: GestureDetector(child: ListView(//controller: _mainScroller,
              padding: EdgeInsets.symmetric(vertical: page.index > -1 ? 1 : 0), children: [
            Padding(padding: EdgeInsets.all(20.sp), child: Text(page.item.title, style: TextStyle(fontSize: 76.sp,
                color: Colors.black, fontWeight: FontWeight.w500), textAlign: TextAlign.justify)),
            Padding(padding: EdgeInsets.only(left:20.sp), child: Row(children: [
              Icon(Icons.calendar_today, color: StyleCustom.textColor6C, size: 32.sp),
              SizedBox(width: 10.sp),
              Text(Util.dateToString(Util.stringToDateTime(page.item.created_at),
                  locale: constants.localeVI, pattern: 'dd/MM/yyyy'),
                  style: TextStyle(color: StyleCustom.textColor6C, fontSize: 42.sp)),
              SizedBox(width: 10.sp),
              page.index > 1 && page.funPlayNext != null ? ButtonImageWidget(32.sp,
                      () => _playPreNext(pre: false), Icon(Icons.skip_previous, size: 92.sp, color: Colors.red)) :
              Icon(Icons.skip_previous, size: 92.sp, color: Colors.grey),
              Padding(padding: EdgeInsets.symmetric(horizontal: 20.sp), child:
              _hasAudio() ? BlocBuilder(bloc: bloc,
                  buildWhen: (oldState, newState) => newState is PlayAudioState,
                  builder: (context, state) {
                    bool play = false;
                    if (state is PlayAudioState) play = state.value;
                    return ButtonImageWidget(32.sp, _playPauseAudio,
                        Icon(play ? Icons.pause_circle_filled : Icons.play_circle_fill,
                            color: Colors.red, size: 92.sp));
                  }) : Icon(Icons.play_circle_fill, color: Colors.grey, size: 92.sp)),
              page.funPlayNext != null && page.isNext ? ButtonImageWidget(32.sp,
                  _playPreNext, Icon(Icons.skip_next, size: 92.sp, color: Colors.red)) :
              Icon(Icons.skip_next, size: 92.sp, color: Colors.grey),
              BlocBuilder(
                  bloc: bloc,
                  buildWhen: (state1, state2) =>
                  state2 is AddFavoriteState || state2 is RemoveFavoriteState  || state2 is LoadListWithCatState,
                  builder: (context, state) => constants.isLogin ? GestureDetector(
                    onTap: () async {
                      if (await UtilUI().alertVerifyPhone(context)) return;
                      if(page.item.is_favourite){
                        bloc!.add(RemoveFavoriteEvent(page.item.favourite_id));
                      } else {
                        bloc!.add(AddFavoriteEvent(page.item.id,page.item.classable_type));
                      }
                    },
                    child: Padding(
                      padding: EdgeInsets.only(left: 50.sp,right: 15.sp),
                      child: Image.asset(
                          page.item.is_favourite
                              ? 'assets/images/ic_love_fill.png'
                              : 'assets/images/ic_love_outline.png',
                          height: 60.sp,
                          width: 60.sp),
                    ),
                  ) : const SizedBox()),

              if (_hasAudio() && NewsListPage.autoPLayNews != null) Expanded(child: Column(children: [
                BlocBuilder(bloc: bloc, buildWhen: (state1, state2) => state2 is AutoSwitchState,
                    builder: (context, state) => Switch(value: NewsListPage.autoPLayNews??false, onChanged: (value) {
                      if (NewsListPage.autoPLayNews == value) return;
                      NewsListPage.autoPLayNews = value;
                      bloc!.add(AutoSwitchEvent(value));
                    }, activeColor: Colors.green)),
                Padding(padding: EdgeInsets.only(right: 20.sp), child: Text('Tự chuyển bài', style: TextStyle(color: StyleCustom.textColor6C, fontSize: 30.sp))),
              ], crossAxisAlignment: CrossAxisAlignment.end))
            ])),
            if (page.item.content.isNotEmpty) BlocBuilder(bloc: bloc,
              buildWhen: (_,newS) => newS is SetHeightState, builder: (_,state) {
                if (state is! SetHeightState) return const SizedBox();
                return Html(data: _content.replaceAll('<br>', '\n'),
                  shrinkWrap: true,
                  style: {
                    "p,span": Style(fontSize: FontSize(22), fontFamily: 'Tahoma, Geneva, sans-serif')
                  },
                  extensions: [
                    TagExtension(
                      tagsToExtend: {"img"},
                      builder: (ext) {
                        final src = ext.attributes['src'] ?? '';
                        return GestureDetector(
                          onTap: () {
                            if (!src.contains('http')) return;
                            if (_isPlay) {
                              _playPauseAudio();
                              _hasResume = false;
                            }
                            UtilUI.goToNextPage(context, ShowAvatarPage(src, fnBack: () {
                              UtilUI.goBack(context, false);
                              if (_hasResume != null) {
                                _playPauseAudio();
                                _hasResume = null;
                              }
                            }));
                          },
                          child: Image.network(src, width: 1.sw, fit: BoxFit.fitWidth,
                              errorBuilder: (_,__,___) => Image.asset('assets/images/ic_default.png',
                                  width: 1.sw, fit: BoxFit.fitWidth)
                          )
                        );
                      },
                    ),
                  ],
                  onAnchorTap: (url,_,__) {
                    if (url == null || !url.contains('http') || Util.isImage(url)) return;
                    if (_isPlay) {
                      _playPauseAudio();
                      _hasResume = true;
                    }
                    launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
                  },
                );
              }),
            Padding(padding: EdgeInsets.all(20.sp),
                child: Text(MultiLanguage.get('comment'), style: TextStyle(fontSize: 48.sp, color: Colors.black, fontWeight: FontWeight.w500))),
            CommentPage(Post(classable_id: page.item.id.toString(), classable_type: page.item.classable_type2), hasHeader: false)
          ]), onHorizontalDragEnd: (details) {
            final dx = details.velocity.pixelsPerSecond.dx;
            final page = widget as NewsDetail2Page;
            if (dx.abs() > 10 && ((page.index > 1 && dx > 0) || (page.isNext && dx < 0))
                && page.funPlayNext != null) _playPreNext(pre: dx < 0, showConfirm: true);
          }))
    ]);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.inactive && _isPlay && _hasResume == null) {
      _playPauseAudio();
      _hasResume = true;
    }
    if (state == AppLifecycleState.resumed && _hasResume == true) {
      _playPauseAudio();
      _hasResume = null;
    }
  }

  bool _hasAudio() => (widget as NewsDetail2Page).item.audio_link.isNotEmpty;

  void _initPlayController() {
    if (!_hasAudio()) return;
    _player = AudioPlayer();
    _player!.setUrl((widget as NewsDetail2Page).item.audio_link).whenComplete(() {
      if (NewsListPage.autoPLayNews??false) _playPauseAudio();
      _player!.playerStateStream.listen((playerState) {
        if (playerState.processingState == ProcessingState.completed) {
          if (_isPlay && (NewsListPage.autoPLayNews??false)) _playNext();
          _player!.seek(const Duration(seconds: 0)).whenComplete(() => _player!.pause().whenComplete(() => _isPlay = false));
          bloc!.add(PlayAudioEvent(false));
        }
      });
    });
  }

  void _playPreNext({bool pre = true, bool showConfirm = false}) {
    if (_player == null) {
      _playNext(pre: pre);
      return;
    }
    _player!.pause().whenComplete(() {
      _isPlay = false;
      bloc!.add(PlayAudioEvent(false));
      _playNext(pre: pre);
    });
  }

  void _playNext({bool pre = true}) {
    final page = widget as NewsDetail2Page;
    final next = page.funPlayNext;
    if (next != null) {
      _player?.pause();
      next(page.index, isPre: pre);
    }
  }

  void _playPauseAudio({bool changeAudio = true}) {
    _isPlay = !_isPlay;
    bloc!.add(PlayAudioEvent(_isPlay));
    if (changeAudio) _isPlay ? _player?.play() : _player?.pause();
  }

  void _shareToApp() {
    final page = widget as NewsDetail2Page;
    UtilUI.shareTo(context, '/tin-tuc/${page.item.id}', 'News Detail -> Option Share Dialog -> Choose "Share"', 'articles');
  }

  void _showMenu() async {
    if (await UtilUI().alertVerifyPhone(context)) return;
    final List<ItemModel> options = [
      ItemModel(id: 'share_app', name: 'Chia sẻ qua ứng dụng khác'),
     if(constants.isLogin) ItemModel(id: 'share_post', name: 'Chia sẻ lên tường của tôi')
    ];
    UtilUI.showOptionDialog(context, MultiLanguage.get('ttl_option'), options, '').then((value) async {
      if (value != null) {
        value.id == 'share_app' ? _shareToApp() : _shareToPost();
      }
    });
    Util.trackActivities('articles', path: 'Articles Detail -> Share Menu -> Open Option Dialog');
  }

  void _shareToPost() {
    final page = widget as NewsDetail2Page;
    bloc!.add(CreatePostEvent('${Constants().domain}/tin-tuc/${page.item.id}'));
    Util.trackActivities('articles', path: 'Articles Detail -> Tap Button Share To Social Screen');
  }

  bool _handleResponse(response, {bool passString = false}) {
    final BaseResponse tmp = response as BaseResponse;
    if (tmp.checkTimeout()) UtilUI.showDialogTimeout(context);
    else if (tmp.checkOK(passString: passString)) return true;
    else UtilUI.showCustomDialog(context, tmp.data);
    return false;
  }

  void _handleResponseAddFavorite(AddFavoriteState state) {
    if (_handleResponse(state.response)) {
      final page = widget as NewsDetail2Page;
      page.item.is_favourite = true;
      page.item.favourite_id = state.response.data.id;
      if (page.funReload != null) page.funReload!();
    }
  }

  void _handleResponseRemoveFavorite(RemoveFavoriteState state) {
    if (_handleResponse(state.response, passString: true)) {
      final page = widget as NewsDetail2Page;
      page.item.is_favourite = false;
      if (page.funReload != null) page.funReload!();
    }
  }

  void _showGuidSwipeLeft() => showDialog(useSafeArea: false, context: context, builder: (context) =>
    Dialog(child: GestureDetector(child: Container(color: Colors.black54, alignment: Alignment.centerRight, child: Column(
        mainAxisSize: MainAxisSize.min, crossAxisAlignment: CrossAxisAlignment.center, children: [
      Image.asset('assets/images/v7/ic_swipe_left.png', width: 200.sp),
      const SizedBox(height: 10),
      LabelCustom('Vuốt trái\nđể xem thêm', weight: FontWeight.w400, size: 48.sp, align: TextAlign.center)
    ])), onTap: () => UtilUI.goBack(context, null)), insetPadding: EdgeInsets.zero, backgroundColor: Colors.transparent, elevation: 0), barrierColor: Colors.transparent);
}
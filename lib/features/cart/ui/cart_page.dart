import 'dart:convert';
import 'dart:io';
import 'package:hainong/common/models/item_list_model.dart';
import 'package:hainong/common/ui/auto_fill_address_state.dart';
import 'package:hainong/common/ui/button_image_widget.dart';
import 'package:hainong/common/ui/import_lib_ui.dart';
import 'package:hainong/common/ui/label_custom.dart';
import 'package:hainong/common/ui/loading.dart';
import 'package:hainong/common/ui/measured_size.dart';
import 'package:hainong/common/ui/select_address.dart';
import 'package:hainong/common/ui/textfield_custom.dart';
import 'package:hainong/features/discount_code/dis_code_list_page.dart';
import 'package:hainong/features/login/login_onetime_page.dart';
import 'package:hainong/features/main/bloc/main_bloc.dart' as mainBloc;
import 'package:hainong/features/order_history/order_history2_page.dart';
import '../cart_model.dart';
import '../cart_bloc.dart';
import 'cart_item.dart';

class CartPage extends BasePage {
  CartPage({Key? key}) : super(pageState: _CartPageState(), key: key);
}

class _CartPageState extends AutoFillAddressState {
  final TextEditingController _ctrName = TextEditingController(), _ctrPhone = TextEditingController(), _ctrEmail = TextEditingController();
  final FocusNode _fcName = FocusNode(), _fcPhone = FocusNode(), _fcEmail = FocusNode();
  final Map<String, CartModel> _cart = {};
  final colorItem = const Color(0XFFF5F6F8);
  final require = LabelCustom(' (*)', color: Colors.red, size: 36.sp, weight: FontWeight.normal);
  bool _isEmpty = true;
  dynamic _coupon, _countCart, _totals, _lock;//, _countCoupon;

  @override
  loadFiles(List<File> files) {}

  @override
  void dispose() {
    _saveInfo();
    _fcName.removeListener(_listener);
    _fcPhone.removeListener(_listener);
    _fcEmail.removeListener(_listener);
    fcAddress.removeListener(_listener);
    _ctrName.dispose();
    _fcName.dispose();
    _ctrPhone.dispose();
    _fcPhone.dispose();
    _ctrEmail.dispose();
    _fcEmail.dispose();
    _cart.clear();
    super.dispose();
  }

  @override
  void initState() {
    initBloc(CartBloc());
    SharedPreferences.getInstance().then((value) {
      _cart.putIfAbsent('-1#-1', () => CartModel());
      _cart.addAll(Util.getCarts(value));
      _getAll();
      setState(() {
        if (_cart.length > 1) _isEmpty = false;
      });
      _setInfo(value);
    });
    super.initState();
    _fcName.addListener(_listener);
    _fcPhone.addListener(_listener);
    _fcEmail.addListener(_listener);
    fcAddress.addListener(_listener);
  }

  @override
  void listenBloc(state) {
    if (state is ChangeQtyState) {
      if (state.clearCoupon != null && _coupon != null) _clearCoupon();
      if (_getQuantity() == 0) {
        if (_coupon != null) _clearCoupon();
        setState(() => _isEmpty = true);
      }
      _getAll();
    } else if (state is GetAddressBaseState) _saveInfo();
    else if (state is HideAddressState) {
      _saveInfo();
      fcAddress.unfocus();
    } else if (state is CreateOrderState) _handleCreateOrder(state);
    else if (state is LoadOrderDtlState && isResponseNotError(state.resp)) _checkOrder(state.resp.data);
  }

  @override
  Widget build(BuildContext context, {Color color = Colors.white}) => Scaffold(appBar:
      AppBar(titleSpacing: 0, centerTitle: true, elevation: 5, title: UtilUI.createLabel('Giỏ hàng')),
    backgroundColor: color,
    body: GestureDetector(onTap: clearFocus, child: Stack(children: [createUI(), Loading(bloc)])));

  @override
  Widget createUI() => Container(width: 1.sw, padding: EdgeInsets.all(40.sp), child: Column(children: [
      if (!_isEmpty) Expanded(child: ListView.builder(padding: EdgeInsets.zero,
        itemCount: _cart.length,
        itemBuilder: (context, index) {
          if (index > 0) return CartItem((bloc as CartBloc), _cart.values.elementAt(index));
          return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Row(children: [_title('Họ tên'), require]),
            Padding(padding: EdgeInsets.only(top: 16.sp, bottom: 40.sp),
                child: TextFieldCustom(_ctrName, _fcName, _fcPhone, 'Nhập họ tên',
                    size: 42.sp, color: colorItem, borderColor: colorItem,
                    padding: EdgeInsets.all(30.sp))),

            Row(children: [
              Expanded(child: Column(children: [
                Row(children: [_title('Số ĐT'), require]),
                Padding(padding: EdgeInsets.only(top: 16.sp, bottom: 40.sp),
                    child: TextFieldCustom(_ctrPhone, _fcPhone, _fcEmail, 'Nhập số điện thoại',
                        size: 42.sp, color: colorItem, borderColor: colorItem,
                        type: TextInputType.phone, padding: EdgeInsets.all(30.sp))),
              ])),
              SizedBox(width: 20.sp),
              Expanded(child: Column(children: [
                _title('Email'),
                Padding(padding: EdgeInsets.only(top: 16.sp, bottom: 40.sp),
                    child: TextFieldCustom(_ctrEmail, _fcEmail, fcAddress, 'Nhập email',
                        size: 42.sp, color: colorItem, borderColor: colorItem,
                        type: TextInputType.emailAddress, padding: EdgeInsets.all(30.sp))),
              ], crossAxisAlignment: CrossAxisAlignment.start))
            ]),

            Row(children: [_title('Địa chỉ'), require]),
            Padding(padding: EdgeInsets.only(top: 16.sp, bottom: 40.sp), child: Row(children: [
              Expanded(child: TextFieldCustom(ctrAddress, fcAddress, null, 'Nhập địa chỉ',
                  size: 42.sp, color: colorItem, onChanged: changedAddress,
                  borderColor: colorItem, maxLine: 0, padding: EdgeInsets.all(30.sp),
                  suffix: GestureDetector(onTap: searchAddress,
                      child: const Icon(Icons.search, color: Colors.black38)),
                  inputAction: TextInputAction.newline, type: TextInputType.multiline)),
              SizedBox(width: 32.sp),
              ButtonImageWidget(5, openMap, Image.asset('assets/images/v5/ic_map_main2.png',
                  width: 128.sp, height: 128.sp, fit: BoxFit.scaleDown))
            ])),
            Padding(padding: EdgeInsets.symmetric(horizontal: 10.sp), child: SelectAddress(selectAddress, bloc!)),

            Row(children: [
              Expanded(child: Column(children: [
                Row(children: [_title('Tỉnh/Thành phố'), require]),
                Padding(child: TextFieldCustom(ctrProvince, fcProvince, null, '', size: 42.sp,
                    color: colorItem, borderColor: colorItem, readOnly: true, onPressIcon: selectProvince,
                    type: TextInputType.text, inputAction: TextInputAction.next, maxLine: 0,
                    suffix: Icon(Icons.arrow_drop_down, color: Colors.grey, size: 64.sp),
                    padding: EdgeInsets.fromLTRB(30.sp, 30.sp, 0, 30.sp)), padding: EdgeInsets.only(top: 16.sp, bottom: 20.sp))
              ], crossAxisAlignment: CrossAxisAlignment.start)),
              const SizedBox(width: 10),
              Expanded(child: Column(children: [
                Row(children: [_title('Quận/Huyện'), require]),
                Padding(child: TextFieldCustom(ctrDistrict, fcDistrict, null, '', size: 42.sp,
                    color: colorItem, borderColor: colorItem, readOnly: true, onPressIcon: selectDistrict,
                    type: TextInputType.multiline, inputAction: TextInputAction.newline, maxLine: 0,
                    suffix: Icon(Icons.arrow_drop_down, color: Colors.grey, size: 64.sp),
                    padding: EdgeInsets.fromLTRB(30.sp, 30.sp, 0, 30.sp)), padding: EdgeInsets.only(top: 16.sp, bottom: 20.sp))
              ], crossAxisAlignment: CrossAxisAlignment.start)),
            ], crossAxisAlignment: CrossAxisAlignment.start),

            _title('Phường/Xã'),
            Padding(padding: EdgeInsets.only(top: 16.sp, bottom: 20.sp),
              child: TextFieldCustom(ctrWard, fcWard, null, '', size: 42.sp,
                color: colorItem, borderColor: colorItem, readOnly: true, onPressIcon: selectWard,
                type: TextInputType.multiline, inputAction: TextInputAction.newline, maxLine: 0,
                suffix: Icon(Icons.arrow_drop_down, color: Colors.grey, size: 64.sp),
                padding: EdgeInsets.all(30.sp))),

            SizedBox(height: 20.sp),
            Row(children: [
              LabelCustom('Mã giảm giá: ', color: Colors.black, size: 42.sp, weight: FontWeight.w400),
              ButtonImageWidget(0, _openCouponList, Image.asset('assets/images/v8/ic_dis_code.png', color: Colors.black, height: 56.sp))
            ]),
            SizedBox(height: 40.sp)
          ]);
        }
      )),
      _totalPaymentWidget(),
      BlocBuilder(bloc: bloc, buildWhen: (oldS, newS) => newS is ChangeQtyState,
        builder: (context, state) {
          if (_isEmpty) {
                return Column(children: [
                  Icon(Icons.shopping_cart_outlined, size: 200.sp, color: const Color(0xFFCDCDCD)),
                  LabelCustom('Giỏ hàng đang trống', size: 64.sp, color: const Color(0xFFCDCDCD), weight: FontWeight.normal)
                ]);
          }
          return ButtonImageWidget(16.sp, _createOrder, Container(padding: EdgeInsets.all(40.sp),
              width: 1.sw - 80.sp, child: LabelCustom('Tạo đơn hàng',
                  color: Colors.white, size: 48.sp, weight: FontWeight.normal,
                  align: TextAlign.center)), color: StyleCustom.primaryColor);
        }),
    ], mainAxisAlignment: MainAxisAlignment.center));

  Widget _title(String title) => LabelCustom(title, color: const Color(0xFF787878), size: 36.sp, weight: FontWeight.normal);

  Widget _totalPaymentWidget() => _isEmpty ? const SizedBox() :
    Padding(child: BlocBuilder(bloc: bloc, buildWhen: (oldS, newS) => newS is ChangeQtyState,
      builder: (context, state) {
        return _totals != null ? Column(children: [
          Row(mainAxisAlignment: MainAxisAlignment.end, children: [
            LabelCustom('Tổng tiền: ', color: Colors.black, size: 42.sp, weight: FontWeight.normal),
            MeasuredSize(child: LabelCustom(Util.doubleToString(_totals[0]) + ' đ', color: Colors.black87, size: 46.sp),
                onChange: (size) => bloc!.add(ChangeSizeEvent(type: 'total', width: size.width)))
          ]),
          if (_coupon != null) Padding(child: Row(mainAxisAlignment: MainAxisAlignment.end,
              children: [
                LabelCustom('Giảm giá: ', color: Colors.black, size: 42.sp, weight: FontWeight.normal),
                SpaceCartOrder(bloc, 'dis'),
                MeasuredSize(child: LabelCustom('-' + Util.doubleToString(_totals[1]) + ' đ', color: Colors.red, size: 46.sp),
                    onChange: (size) => bloc!.add(ChangeSizeEvent(type: 'dis', width: size.width)))
              ]), padding: EdgeInsets.only(top: 10.sp)),
          SizedBox(height: 10.sp),
          Row(mainAxisAlignment: MainAxisAlignment.end, children: [
            LabelCustom('Thanh toán: ', color: Colors.black, size: 42.sp, weight: FontWeight.normal),
            SpaceCartOrder(bloc, 'pay'),
            MeasuredSize(child: LabelCustom(Util.doubleToString(_totals[2]) + ' đ', color: Colors.red, size: 46.sp),
                onChange: (size) => bloc!.add(ChangeSizeEvent(type: 'pay', width: size.width)))
          ])
        ]) : const SizedBox();
      }), padding: EdgeInsets.symmetric(vertical: 40.sp));

  void _listener() {
    //if (!fcAddress.hasFocus && ctrAddress.text.trim().isEmpty) setProvince(ItemModel());
    if (!_fcName.hasFocus || !_fcPhone.hasFocus || !_fcEmail.hasFocus || !fcAddress.hasFocus) {
      _saveInfo();
      if (!fcAddress.hasFocus) {
        showPopupAddress = null;
        if (bloc!.startAutoSearch != null) bloc!.startAutoSearch = false;
        return;
      }
    }

    if (fcAddress.hasFocus && bloc!.startAutoSearch != true) {
      bloc!.startAutoSearch = true;
      showPopupAddress = true;
      autoSearch();
    }
  }

  void _saveInfo() => SharedPreferences.getInstance().then((prefs) {
    prefs.setString('cart_info', jsonEncode({
      "name": _ctrName.text,
      "phone": _ctrPhone.text,
      "email": _ctrEmail.text,
      "address": ctrAddress.text,
      "province_id": province.id,
      "province_name": province.name,
      "district_id": district.id,
      "district_name": district.name,
      "ward_id": ward.id,
      "ward_name": ward.name,
      if (address != null) "lat": address!.latitude,
      if (address != null) "lng": address!.longitude
    }));
  });

  void _setInfo(SharedPreferences prefs) {
    final province = ItemModel(), district = ItemModel(), ward = ItemModel();
    province.id = prefs.getString('province_id')??'';
    province.name = prefs.getString('province_name')??'';
    district.id = prefs.getString('district_id')??'';
    district.name = prefs.getString('district_name')??'';
    ward.id = prefs.getString('ward_id')??'';
    ward.name = prefs.getString('ward_name')??'';

    bloc!.count = 3;
    if (ward.id.isEmpty) bloc!.count = 2;
    if (district.id.isEmpty) bloc!.count = 1;
    bloc!.add(LoadProvinceBaseEvent());
    //final cartInfo = prefs.getString('cart_info')??'';
    //if (cartInfo.isEmpty) {
      _ctrName.text = prefs.getString('name')??'';
      _ctrPhone.text = prefs.getString('phone')??'';
      _ctrEmail.text = prefs.getString('email')??'';
      ctrAddress.text = prefs.getString('address')??'';

      String temp = '';
      if (ward.name.isNotEmpty) temp = ', ' + ward.name;
      if (district.name.isNotEmpty) temp += ', ' + district.name;
      if (province.name.isNotEmpty) temp += ', ' + province.name;
      ctrAddress.text += temp;
      if (ctrAddress.text.isNotEmpty) bloc!.add(GetLocationBaseEvent(ctrAddress.text));
    //}
    /*else {
      final json = jsonDecode(cartInfo);
      _ctrName.text = json['name'];
      _ctrPhone.text = json['phone'];
      _ctrEmail.text = json['email'];
      ctrAddress.text = json['address'];

      province.id = json['province_id'];
      province.name = json['province_name'];
      district.id = json['district_id'];
      district.name = json['district_name'];
      ward.id = json['ward_id'];
      ward.name = json['ward_name'];
      if (json.containsKey('lat') && json.containsKey('lng')) address = LatLng(json['lat'], json['lng']);

      if (_ctrName.text.isEmpty) _ctrName.text = prefs.getString('name')??'';
      if (_ctrPhone.text.isEmpty) _ctrPhone.text = prefs.getString('phone')??'';
      if (_ctrEmail.text.isEmpty) _ctrEmail.text = prefs.getString('email')??'';
      if (ctrAddress.text.isEmpty) ctrAddress.text = prefs.getString('address')??'';

      if (province.id.isEmpty) {
        province.id = prefs.getString('province_id') ?? '';
        province.name = prefs.getString('province_name') ?? '';
      }
      if (district.id.isEmpty) {
        district.id = prefs.getString('district_id') ?? '';
        district.name = prefs.getString('district_name') ?? '';
      }
      if (ward.id.isEmpty) {
        ward.id = prefs.getString('ward_id') ?? '';
        ward.name = prefs.getString('ward_name') ?? '';
      }
      if (ctrAddress.text.isEmpty) {
        String temp = '';
        if (ward.name.isNotEmpty) temp = ', ' + ward.name;
        if (district.name.isNotEmpty) temp += ', ' + district.name;
        if (province.name.isNotEmpty) temp += ', ' + province.name;
        ctrAddress.text += temp;
        if (ctrAddress.text.isNotEmpty) bloc!.add(GetLocationEvent(ctrAddress.text));
      }
    }*/
    setProvince(province, loadDistrict: true, clearDistrict: false);
    setDistrict(district, loadWard: true, clearWard: false);
    setWard(ward);

    /*if (_cart.length > 1) {
      DBHelper().getAllJsonWithCond('coupon').then((values) {
        if (values != null && values.isNotEmpty) _setCoupon(values.first);
      });
    }*/
  }

  void _setCoupon(value) {
    _coupon ??= {};
    _coupon.addAll(value);
    bloc!.add(ChangeQtyEvent(CartModel(), CartDtlModel()));
  }

  void _clearCoupon({bool hasSetState = false}) {
    _cart.forEach((key, value) => value.coupon = null);
    _coupon?.clear();
    _coupon = null;
    if (hasSetState) {
      _getAll();
      setState(() {});
    }
    //DBHelper().clearTable('coupon');
  }

  void _getAll() {
    if (_totals != null) {
      _totals.clear();
      _totals = null;
    }

    double total = 0, discount = 0, temp, tempDis, max;
    int count = 0;
    if (_coupon != null) count = (_coupon['quantity']??0x7FFFFFFFFFFFFFFF) - (_coupon['invoice_users_count']??0);

    for(int i = _cart.length - 1; i > 0; i--) {
      temp = _cart.values.elementAt(i).getPaymentTotal();
      total += temp;
      if (temp > 0) {
        if (count > 0 && _existsCoupon(_cart.keys.elementAt(i))) {
          tempDis = (_coupon['value'] ?? .0).toDouble();
          if (_coupon['coupon_type'] != 'money') {
            max = _coupon['max_value'] ?? .0;
            tempDis = temp * tempDis / 100;
            if (tempDis > max && max > 0) tempDis = max;
          }
          discount += tempDis;
          count --;
        }
      }
    };

    if (discount > total) discount = total;
    _totals = [total, discount, total - discount];
  }

  bool _existsCoupon(String key) {
    final CartModel cart = _cart[key]!;
    for(var ele in _coupon['shop_ids']) {
      if (cart.shop_id == ele) {
        cart.coupon = _coupon['coupon_code'];
        return true;
      }
    }
    return false;
  }

  double _getQuantity() {
    double total = 0;
    _cart.forEach((key, value) => total += value.getQuantity());
    return total;
  }

  void _checkOrder(data) {
    int count = data['invoice_users_count']??0, coupon = _coupon['invoice_users_count']??0;
    if (coupon == count) _createOrder(checkCoupon: false);
    else if (count == (_coupon['quantity']??0x7FFFFFFFFFFFFFFF)) {
      UtilUI.showCustomDialog(context, 'Số lượng mã giảm giá hiện tại đã hết.'
          '\nHãy chọn mã giảm giá khác để áp dụng vào đơn hàng.').whenComplete(() => _clearCoupon(hasSetState: true));
    } else if (coupon < count) {
      UtilUI.showCustomDialog(context, 'Số lượng mã giảm giá đang áp dụng có thay đổi (ít hơn so với hiện tại).'
          '\nHãy chọn lại mã giảm giá để áp dụng vào đơn hàng.').whenComplete(() => _clearCoupon(hasSetState: true));
    } else _createOrder(checkCoupon: false);
  }

  void _createOrder({bool checkCoupon = true}) {
    if (_lock == true) return;
    if (!constants.isLogin) {
      UtilUI.showCustomDialog(context, MultiLanguage.get(LanguageKey().msgLoginOrCreate)).whenComplete(() {
        UtilUI.logout();
        UtilUI.clearAllPages(context);
        UtilUI.goToPage(context, LoginOneTimePage(), null);
      });
      return;
    }

    if (_ctrName.text.trim().isEmpty) {
      UtilUI.showCustomDialog(context, 'Nhập họ tên').whenComplete(() => _fcName.requestFocus());
      return;
    }
    if (_ctrPhone.text.trim().length < 10) {
      UtilUI.showCustomDialog(context, 'Nhập số điện thoại đầy đủ').whenComplete(() => _fcPhone.requestFocus());
      return;
    }
    if (ctrAddress.text.trim().length < 10) {
      UtilUI.showCustomDialog(context, 'Nhập địa chỉ đầy đủ').whenComplete(() => fcAddress.requestFocus());
      return;
    }
    if (province.id.isEmpty) {
      UtilUI.showCustomDialog(context, 'Chọn tỉnh/Thành phố').whenComplete(() => fcProvince.requestFocus());
      return;
    }
    if (district.id.isEmpty) {
      UtilUI.showCustomDialog(context, 'Chọn quận/Huyện').whenComplete(() => fcDistrict.requestFocus());
      return;
    }
    /*if (ward.id.isEmpty) {
      UtilUI.showCustomDialog(context, 'Chọn phường/Xã').whenComplete(() => fcWard.requestFocus());
      return;
    }*/

    if (checkCoupon && _coupon != null && _coupon['quantity'] != null) {
      bloc!.add(LoadOrderDtlEvent(_coupon!['id'], false));
      return;
    }

    final List<CartModel> list = [];
    for (var shop in _cart.values) {
      if (shop.shop_id > 0 && shop.items.isNotEmpty && shop.getQuantity() > 0) list.add(shop);
    }
    _countCart = list.length;

    if (list.isNotEmpty) {
      _lock = true;
      bloc!.add(CreateOrderEvent(list, _ctrName.text, _ctrPhone.text, _ctrEmail.text, ctrAddress.text, _coupon,
        province.id, district.id, ward.id));
    }
  }

  void _handleCreateOrder(CreateOrderState state) {
    if (state.cart.isEmpty) {
      _clearCoupon();
      _isEmpty = true;
      setState(() {});
      BlocProvider.of<mainBloc.MainBloc>(context).add(mainBloc.CountCartMainEvent());
      UtilUI.showCustomDialog(context, 'Đặt hàng thành công',
          title: 'Thông báo').whenComplete(() {
        UtilUI.goBack(context, false);
        UtilUI.goToNextPage(context, OrderHistory2Page());
      });
    } else {
      //String msg = 'Bạn chưa tạo xong đơn hàng cho shop:';
      _cart.clear();
      _cart.putIfAbsent('-1#-1', () => CartModel());
      for (int i = 0; i < state.cart.length; i++) {
        //msg += '\n- ' + state.cart[i].seller_name;
        _cart.putIfAbsent('${state.cart[i].shop_id}#${state.cart[i].business_association_id}', () => state.cart[i]);
      }
      if (_countCart != null && state.cart.length - 1 != _countCart) _clearCoupon(hasSetState: true);
      //msg += '\nVui lòng thực hiện tạo đơn hàng lại cho shop trên';
      //UtilUI.showCustomDialog(context, msg, alignMessageText: TextAlign.left);
      setState(() {});
      if (state.error != null) isResponseNotError(state.error!);
    }
    _lock = null;
    _countCart = null;
  }

  void _openCouponList() {
    final List temp = [];
    final list = _cart.values;
    dynamic ele;
    for (int i = list.length - 1; i > 0; i--) {
      ele = list.elementAt(i);
      ele.coupon = null;
      temp.add([ele.shop_id, ele.getPaymentTotal()]);
    }
    UtilUI.goToNextPage(context, DisCodeListPage(hasSelect: true, shops: temp), funCallback: (value) => _setCoupon(value));
  }
}
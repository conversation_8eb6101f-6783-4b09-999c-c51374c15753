import 'dart:async';
import 'dart:io';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
//import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:firebase_dynamic_links/firebase_dynamic_links.dart';
import 'package:hainong/common/api_client.dart';
import 'package:hainong/common/base_response.dart';
import 'package:hainong/common/count_down_bloc.dart';
import 'package:hainong/common/database_helper.dart';
import 'package:hainong/common/import_lib_system.dart';
import 'package:hainong/common/models/item_list_model.dart';
import 'package:hainong/common/ui/scan_qr_bc.dart';
import 'package:hainong/features/function/support/mission/mission_bloc.dart';
import 'package:hainong/features/home/<USER>/home_state.dart';
import 'package:hainong/features/home/<USER>/home_event.dart';
import 'package:hainong/features/main/bloc/scroll_bloc.dart';
import 'package:hainong/features/main2/ui/main2_page.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:zalo_flutter/zalo_flutter.dart';
import 'login_model.dart';

export 'package:hainong/common/count_down_bloc.dart';
export 'package:hainong/features/main2/ui/main2_page.dart';
export 'package:hainong/features/main/bloc/scroll_bloc.dart';
export 'package:hainong/features/home/<USER>/home_event.dart';
export 'package:hainong/features/home/<USER>/home_state.dart';
export 'package:hainong/features/function/support/mission/mission_bloc.dart';

class LoginOneTimeBloc extends BaseBloc {
  final TextEditingController loginKey = TextEditingController();
  TextEditingController? otp1, otp2, otp3, otp4, otp5, otp6;
  FocusNode? fcOtp1, fcOtp2, fcOtp3, fcOtp4, fcOtp5, fcOtp6;
  List<String>? backgrounds;
  int? imagePage, _countEnv, countOTP, _userId;
  String? refCode, refLink, imei, errorOTP;
  bool? unQuestion, _lock, allowClosePopup, isPolicies;

  @override
  Future<void> close() async {
    loginKey.dispose();
    backgrounds?.clear();
    otp1?.dispose();
    fcOtp1?.dispose();
    super.close();
  }

  LoginOneTimeBloc(this.refCode, this.refLink) {
    on<LoadDistrictEvent>(_sendRefQR); //send referral QR
    on<LoadSubPostHomeEvent>(_loginViaSocial); //login via social
    on<ChangeIndexHomeEvent>(_countDownOTP); //count down OTP
    on<AddHashTagHomeEvent>(_verifyOTP); //verify OTP
    on<JoinMissionEvent>(_requestOTP); //request OTP
    on<HideClearScrollEvent>(_hideShowOTP); //hide vs show OTP UI
    on<ShowClearSearchEvent>((event, emit) => emit(ShowClearSearchState(event.value))); //check phone changing
    on<MissionEmptyEvent>((event, emit) => emit(MissionEmptyState(event.value, isShowLoading: _lock == true))); //check OTP changing
    on<WarningPostHomeEvent>((event, emit) => emit(WarningPostHomeState(BaseResponse(data: errorOTP), event.index))); //error verifying OTP

    //slide backgrounds
    on<CountDownEvent>((event, emit) {
      imagePage = event.value;
      emit(CountDownState(value: event.value));
    });

    //unquestioning
    on<LoadMembersEvent>((event, emit) {
      unQuestion = !(unQuestion == true);
      emit(LoadMembersState(unQuestion));
    });

    //select policies checkbox
    on<CollapseHeaderScrollEvent>((event, emit) {
      isPolicies = isPolicies == null ? true : !isPolicies!;
      emit(CollapseHeaderScrollState(true));
      emit(ShowClearSearchState(loginKey.text.length == 10 && isPolicies == true));
    });

    //get background images
    on<AddImageHomeEvent>(_getBackgrounds);
    add(AddImageHomeEvent());

    SharedPreferences.getInstance().then((prefs) {
      loginKey.text = prefs.getString(Constants().loginKey)??'';
      changeTextPhone(loginKey.text);
    });
  }

  void changeEnv() async {
    data.clearFocus();
    _countEnv ??= 0;
    _countEnv = _countEnv! + 1;
    if (_countEnv != null && _countEnv! > 50) {
      _countEnv = null;
      final List<ItemModel> list = [
        ItemModel(id: 'dev', name: 'Develop'),
        ItemModel(id: 'staging', name: 'Staging'),
        ItemModel(id: 'uat', name: 'UAT'),
        ItemModel(name: 'Live')
      ];
      UtilUI.showOptionDialog(data.context, 'Chọn môi trường', list,
          (await SharedPreferences.getInstance()).getString('env') ?? '', allowOff: false).then((value) async {
        if (value != null) {
          final prefs = await SharedPreferences.getInstance();
          final env = prefs.getString('env') ?? '';
          if (env != value.id) {
            await DBHelperUtil().clearAdsModule();
            prefs.remove('bg_login');
            Util.chooseEnv(value.id);

            add(AddImageHomeEvent());
            prefs.setString('env', value.id);
          }
        }
      });
    }
  }

  FutureOr<void> _getBackgrounds(event, emit) async {
    backgrounds?.clear();
    backgrounds = null;
    imagePage = null;
    emit(AddImageHomeState());

    final prefs = await SharedPreferences.getInstance();
    backgrounds = prefs.getStringList('bg_login');
    if (backgrounds != null && backgrounds!.isNotEmpty) {
      if (backgrounds!.length > 1) imagePage = 0;
      emit(AddImageHomeState());
      return;
    }

    final resp = await ApiClient().getList('wallpapers?type=login&', isOnePage: true);
    if (resp.isNotEmpty) {
      final prefs = await SharedPreferences.getInstance();
      backgrounds = resp.cast();
      if (resp.length > 1) imagePage = 0;
      emit(AddImageHomeState());
      prefs.setStringList('bg_login', backgrounds!);
    }
  }

  FutureOr<bool> _saveDeviceInfo() async {
    var info = await UtilUI.getDeviceInfo();
    imei = info['imei'];
    if (imei!.isEmpty || info['token'] == '') {
      UtilUI.showCustomDialog(data.context, MultiLanguage.get('msg_error_get_device_id'));
      return false;
    }

    final resp = await ApiClient().postJsonAPI(Constants().apiVersion + 'devices', null, {
    'device_id': imei,
    'device_name': info['name'],
    'device_type': info['type'],
    'os_version': info['os'],
    'app_version': info['version'],
    'device_token': info['token'],
    'apns_topic': info['apns_topic'],
    'apple_notice_token': info['apple_notice_token']
    }, dataIsJson: true, hasHeader: false);

    return data.isResponseNotError(resp);
  }

  void changeTextPhone(String value) => add(ShowClearSearchEvent(value.length == 10 && isPolicies == true));

  void _resetOTP() {
    data.clearFocus();
    errorOTP = null;
    countOTP = null;
    if (otp2 == null) return;
    otp1!.text = '';
    otp2!.text = '';
    otp3!.text = '';
    otp4!.text = '';
    otp5!.text = '';
    otp6!.text = '';
  }
  void requestOTP() => add(JoinMissionEvent(0, 0));
  FutureOr<void> _requestOTP(event, emit) async {
    if (_lock == true) return;
    if (loginKey.text.length < 10 || isPolicies != true) return;
    _lock = true;

    _resetOTP();
    emit(WarningPostHomeState(BaseResponse(data: errorOTP), 0));

    emit(const BaseState(isShowLoading: true));
    if (!(await _saveDeviceInfo())) {
      emit(const BaseState());
      _lock = null;
      return;
    }

    emit(const BaseState(isShowLoading: true));
    final resp = await ApiClient().postJsonAPI(Constants().apiVersion + 'login_otp/request_otp', null, {
      'loginkey': loginKey.text,
      if (refCode != null) 'referral_code': refCode!
    }, dataIsJson: true);

    emit(const BaseState(isShowLoading: true));
    data.isResponseNotError(resp, passString: true) ? showOTP(value: true) : emit(const BaseState());
    _lock = null;
  }

  void _disposeOTP() {
    otp2!.removeListener(_listenOTP);
    otp3!.removeListener(_listenOTP);
    otp4!.removeListener(_listenOTP);
    otp5!.removeListener(_listenOTP);
    otp6!.removeListener(_listenOTP);
    fcOtp2!.removeListener(_listenOTP);
    fcOtp3!.removeListener(_listenOTP);
    fcOtp4!.removeListener(_listenOTP);
    fcOtp5!.removeListener(_listenOTP);
    fcOtp6!.removeListener(_listenOTP);
    //fcOtp1!.dispose();
    fcOtp2!.dispose();
    fcOtp3!.dispose();
    fcOtp4!.dispose();
    fcOtp5!.dispose();
    fcOtp6!.dispose();
    //otp1!.dispose();
    otp2!.dispose();
    otp3!.dispose();
    otp4!.dispose();
    otp5!.dispose();
    otp6!.dispose();
    otp2 = otp3 = otp4 = otp5 = otp6 = null;
    fcOtp2 = fcOtp3 = fcOtp4 = fcOtp5 = fcOtp6 = null;
    countOTP = null;
  }

  void changeOTP(TextEditingController ctr, FocusNode? nextCtr, String value, {FocusNode? preCtr}) {
    final full = !isEmptyOTP();
    if (value.isEmpty) {
      preCtr?.requestFocus();
    } else {
      if (ctr.text.length == 2) ctr.text = value.substring(1, 2);
      if (ctr.text.length > 5) {
        otp1!.text = value.substring(0,1);
        otp2!.text = value.substring(1,2);
        otp3!.text = value.substring(2,3);
        otp4!.text = value.substring(3,4);
        otp5!.text = value.substring(4,5);
        otp6!.text = value.substring(5,6);
        verifyOTP();
      }

      if (nextCtr == null) {
        if (full) verifyOTP();
      } else nextCtr.requestFocus();
    }
    add(MissionEmptyEvent(full));
  }

  void showOTP({bool value = false}) => add(HideClearScrollEvent(value));
  FutureOr<void> _hideShowOTP(event, emit) async {
    emit(const BaseState(isShowLoading: true));
    if (event.value) {
      if (otp1 == null && fcOtp1 == null) {
        otp1 = TextEditingController();
        fcOtp1 = FocusNode();
        otp1!.addListener(_listenOTP);
        fcOtp1!.addListener(_listenOTP);
      }
      otp2 ??= TextEditingController();
      otp3 ??= TextEditingController();
      otp4 ??= TextEditingController();
      otp5 ??= TextEditingController();
      otp6 ??= TextEditingController();
      fcOtp2 ??= FocusNode();
      fcOtp3 ??= FocusNode();
      fcOtp4 ??= FocusNode();
      fcOtp5 ??= FocusNode();
      fcOtp6 ??= FocusNode();
      countOTP = 120 + 1;
      add(ChangeIndexHomeEvent(countOTP!));
      fcOtp1!.requestFocus();
      otp2!.addListener(_listenOTP);
      otp3!.addListener(_listenOTP);
      otp4!.addListener(_listenOTP);
      otp5!.addListener(_listenOTP);
      otp6!.addListener(_listenOTP);
      fcOtp2!.addListener(_listenOTP);
      fcOtp3!.addListener(_listenOTP);
      fcOtp4!.addListener(_listenOTP);
      fcOtp5!.addListener(_listenOTP);
      fcOtp6!.addListener(_listenOTP);
    } else _disposeOTP();
    otp1!.text = '';
    emit(HideClearScrollState(event.value));
    _lock = null;
  }

  FutureOr<void> _countDownOTP(ChangeIndexHomeEvent event, emit) async {
    emit(ChangeIndexHomeState(event.index, isShowLoading: _lock == true));
    if (countOTP != null && countOTP! > 0) {
      countOTP = countOTP! - 1;
      Timer(const Duration(seconds: 1), () => add(ChangeIndexHomeEvent(event.index)));
    }
  }

  bool isEmptyOTP() => otp1!.text.isEmpty || otp2!.text.isEmpty || otp3!.text.isEmpty ||
      otp4!.text.isEmpty || otp5!.text.isEmpty || otp6!.text.isEmpty;

  void _listenOTP() {
    if (fcOtp1!.hasFocus) {
      _setLastPosition(otp1!, fcOtp1!);
      return;
    }
    if (fcOtp2 !=null && fcOtp2!.hasFocus) {
      _setLastPosition(otp2!, fcOtp2!);
      return;
    }
    if (fcOtp3 !=null && fcOtp3!.hasFocus) {
      _setLastPosition(otp3!, fcOtp3!);
      return;
    }
    if (fcOtp4 !=null && fcOtp4!.hasFocus) {
      _setLastPosition(otp4!, fcOtp4!);
      return;
    }
    if (fcOtp5 !=null && fcOtp5!.hasFocus) {
      _setLastPosition(otp5!, fcOtp5!);
      return;
    }
    if (fcOtp6 !=null && fcOtp6!.hasFocus) _setLastPosition(otp6!, fcOtp6!);
  }

  void _setLastPosition(TextEditingController ctr, FocusNode fc) {
    if (ctr.text.isNotEmpty) ctr.selection = TextSelection.fromPosition(TextPosition(offset: ctr.text.length));
  }

  void setErrorVerify(String error, int status) => add(WarningPostHomeEvent('', error, status));

  void verifyOTP() => add(AddHashTagHomeEvent());
  FutureOr<void> _verifyOTP(event, emit) async {
    if (_lock == true) return;
    if (isEmptyOTP()) return;
    _lock = true;

    errorOTP = null;
    emit(WarningPostHomeState(BaseResponse(data: errorOTP), 0, isShowLoading: true));
    data.clearFocus();

    emit(const BaseState(isShowLoading: true));
    final resp = await ApiClient().postAPI(Constants().apiVersion + 'login_otp/login', 'POST', LoginModel(), body: {
      'loginkey': loginKey.text,
      'verify_code': otp1!.text + otp2!.text + otp3!.text + otp4!.text + otp5!.text + otp6!.text,
      'device_id': imei??''
    }, hasHeader: false);

    if (data.isResponseNotError(resp, showError: false)) {
      emit(const BaseState(isShowLoading: true));
      if (await _saveInfo(resp.data)) emit(AddHashTagHomeState());
      else emit(const BaseState());
      showOTP();
    } else {
      errorOTP = resp.data;
      setErrorVerify(errorOTP??'', 1);
    }
    _lock = null;
  }

  FutureOr<void> _loginViaSocial(event, emit) async {
    if (_lock == true) return;
    _lock = true;
    emit(const BaseState(isShowLoading: true));
    if (!(await _saveDeviceInfo())) {
      emit(const BaseState());
      _lock = null;
      return;
    }

    String? token;
    switch(event.id) {
      case 'facebook': token = await _loginViaFaceBook(); break;
      case 'google': token = await _loginViaGoogle(); break;
      case 'zalo': token = await _loginViaZalo(); break;
      case 'apple': token = await _loginViaAppleID(); break;
    }
    if (token != null && token.isNotEmpty) {
      emit(const BaseState(isShowLoading: true));
      final resp = await ApiClient().postAPI(Constants().baseUrlIPortal + '/ssos/v1/auths/login_social', 'POST',
        LoginModel(), body: {
          'partner_type': event.id,
          'partner_token': token,
          'device_id': imei!,
          if (refCode != null) 'referral_code': refCode!
        }, fullPath: true, hasHeader: false);

      _lock = null;
      if (data.isResponseNotError(resp)) {
        emit(const BaseState(isShowLoading: true));
        if (await _saveInfo(resp.data)) emit(AddHashTagHomeState());
        else emit(const BaseState());
        return;
      }
    } else _lock = null;
    emit(const BaseState());
  }

  void loginViaAppleID() => add(LoadSubPostHomeEvent('apple'));
  Future<String> _loginViaAppleID() => SignInWithApple.getAppleIDCredential(
      scopes: [
        AppleIDAuthorizationScopes.email,
        AppleIDAuthorizationScopes.fullName
      ]
  ).then((appleIdCredential) async {
    final oAuthProvider = OAuthProvider('apple.com');
    final credential = oAuthProvider.credential(
        idToken: appleIdCredential.identityToken,
        accessToken: appleIdCredential.authorizationCode
    );

    final result = await FirebaseAuth.instance.signInWithCredential(credential);
    if (result.user != null) {
      return appleIdCredential.identityToken??'';
    }
    return '';
  }).onError((error, stackTrace) => '').catchError((error) => '');

  void loginViaFaceBook() => add(LoadSubPostHomeEvent('facebook'));
  Future<String> _loginViaFaceBook() async => '';
    /*FacebookAuth.i.login(loginBehavior: LoginBehavior.webOnly).then((LoginResult account) async {
      if (account.status == LoginStatus.success) {
        final token = await FacebookAuth.i.accessToken;
        if (token != null) return token.tokenString;
      }
      return '';
    }).onError((_,__) => '').catchError((_) => '');*/

  void loginViaGoogle() => add(LoadSubPostHomeEvent('google'));
  Future<String> _loginViaGoogle() async {
    final GoogleSignIn _googleSignIn = GoogleSignIn(scopes: <String>[
      'email',
    ]);
    final account = await _googleSignIn.signIn();
    if (account != null) return (await account.authentication).idToken??'';
    return '';
  }

  //void loginViaZalo() => bloc!.add(LoadSubPostHomeEvent('zalo'));
  void loginViaZalo() async {
    final env = (await SharedPreferences.getInstance()).getString('env')??'';
    if (env != 'dev' || Platform.isIOS) add(LoadSubPostHomeEvent('zalo'));
    else {
      final hashKey = await ZaloFlutter.getHashKeyAndroid();
      UtilUI.showCustomDialog(data.context, hashKey).whenComplete(() {
        Clipboard.setData(ClipboardData(text: hashKey??''));
        add(LoadSubPostHomeEvent('zalo'));
      });
    }
  }
  Future<String> _loginViaZalo({int isFirst = 0}) async {
    try {
      ZaloFlutter.setTimeout(Duration(seconds: isFirst == 0 ? 30 : 90));
      final Map<dynamic, dynamic>? data = await ZaloFlutter.login();
      if (data != null && data['isSuccess'] == true) {
        if(Platform.isAndroid) {
          return data['data']['access_token']??'';
        } else {
          return data['data']['accessToken']??'';
        }
      } else if (data != null) {
        //DBHelperUtil().hasLogFile().then((value) => value ? Util().logFile('\n\nZalo error: ' + data.toString()) : '');
      }
    } catch (e) {
      //DBHelperUtil().hasLogFile().then((value) => value ? Util().logFile('\n\nZalo error: ' + e.toString()) : '');
    }
    if (isFirst == 0) return _loginViaZalo(isFirst: isFirst + 1);
    return '';
  }

  FutureOr<bool> _saveInfo(LoginModel user) async {
    UtilUI.saveInfo(data.context, user, loginKey.text, '');
    final db = DBHelperUtil();
    db.clearChatBot();
    dynamic temp = await db.getSetting('ignore_referral');
    if (temp != null && !user.id.toString().contains(temp)) {
      db.clearSetting('ignore_referral');
      temp = null;
    }
    if (temp == null && (user.referral_code == null || user.referral_code!.isEmpty) && user.phone.isNotEmpty) {
      _userId = user.id;
      otp1 ??= TextEditingController();
      fcOtp1 ??= FocusNode();
      otp1!.text = '';
      return true;
    }
    UtilUI.goToPage(data.context, Main2Page(), null);
    return false;
  }

  void turnOnOffUnQuestioning() => add(LoadMembersEvent(0));

  void ignoreQuestion({bool closePopup = true}) {
    _allowClosePopup(closePopup);
    UtilUI.goToPage(data.context, Main2Page(), null);
    if (unQuestion == true) {
      final db = DBHelperUtil();
      db.setSetting('ignore_referral', _userId.toString());
    }
  }

  void openScanQR() {
    UtilUI.goToNextPage(data.context, ScanQRBCPage(), funCallback: _scanQRCallback);
  }

  void _scanError() => UtilUI.showCustomDialog(data.context, 'Mã không hợp lệ').whenComplete(() => sendRefQR(error: 'error'));
  void _scanQRCallback(value) {
    try {
      if (value != null) {
        if (!value.contains('http')) {
          _scanError();
          return;
        }

        FirebaseDynamicLinks.instance.getDynamicLink(Uri.parse(value)).then((link) {
          if (link != null) {
            otp1!.text = link.link.queryParameters['referral_code']??'';
            if (otp1!.text.isNotEmpty) sendRefQR();
          }
        }).catchError((_) {
          _scanError();
        }).onError((_,__) {
          _scanError();
        });
      }
    } catch (_) {
      _scanError();
    }
  }

  void sendRefQR({String error = ''}) => add(LoadDistrictEvent(error));
  FutureOr<void>  _sendRefQR(LoadDistrictEvent event, emit) async {
    if (_lock == true) return;
    if (event.idProvince.contains('error')) {
      emit(AddHashTagHomeState());
      return;
    }
    if (otp1!.text.trim().isEmpty) {
      UtilUI.showCustomDialog(data.context, 'Nhập hoặc quét QR mã giới thiệu').whenComplete(() => fcOtp1!.requestFocus());
      return;
    }

    _lock = true;
    _allowClosePopup(true);
    emit(const BaseState(isShowLoading: true));
    final resp = await ApiClient().postAPI(Constants().apiVersion + 'login_otp/update_referral_code', 'POST',
        LoginModel(), body: {'referral_code': otp1!.text});
    if (data.isResponseNotError(resp, showError: false)) {
      UtilUI.saveInfo(data.context, resp.data, loginKey.text, '');
      ignoreQuestion(closePopup: false);
    } else {
      await UtilUI.showCustomDialog(data.context, resp.data);
      emit(AddHashTagHomeState());
    }
    _lock = null;
  }

  void _allowClosePopup(bool value) {
    allowClosePopup = true;
    if (value) Navigator.pop(data.context);
  }
}
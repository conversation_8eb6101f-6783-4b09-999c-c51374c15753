import 'dart:io';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/services.dart';
import 'package:hainong/common/ui/button_image_widget.dart';
import 'package:hainong/common/ui/import_lib_ui.dart';
import 'package:hainong/common/ui/label_custom.dart';
import 'package:hainong/common/ui/loading.dart';
import 'package:hainong/common/ui/textfield_custom.dart';
import '../function/support/user_guide/user_guide_page.dart';
import '../referral_management/referral_detail_page.dart';
import 'login_onetime_bloc.dart';

class LoginOneTimePage extends BasePage {
  LoginOneTimePage({String? refCode, String? refType, Key? key}) :
        super(pageState: _LoginOneTimePageState(refCode, refType), key: key);
}

class _LoginOneTimePageState extends BasePageState implements IBaseBloc {
  final _height = 0.4.sh;

  _LoginOneTimePageState(String? refCode, String? refType) {
    constants.token = null;
    bloc = LoginOneTimeBloc(refCode, refType);
    bloc!.stream.listen((state) {
      if (state is AddHashTagHomeState) _showRefPopup();
    });
  }

  @override
  void initState() {
    super.initState();
    bloc!.data = this;
  }

  @override
  Widget build(BuildContext context, {Color color = Colors.white}) {
    final ctr = bloc as LoginOneTimeBloc;
    return GestureDetector(onTap: clearFocus, child: Scaffold(body: Stack(children: [
      ListView(padding: EdgeInsets.zero, children: [
        GestureDetector(onTap: ctr.changeEnv, child: BlocBuilder(
            bloc: bloc, buildWhen: (_,newS) => newS is AddImageHomeState,
            builder: (_,__) {
              if (ctr.backgrounds == null) {
                return Image.asset('assets/images/v11/ic_logo_login_v11.png', height: _height - 70, width: 1.sw,
                    fit: BoxFit.scaleDown, filterQuality: FilterQuality.high);
              }
              if (ctr.backgrounds!.length == 1) {
                return FadeInImage.assetNetwork(placeholder: 'assets/images/ic_logo_login.png',
                  image: ctr.backgrounds![0], width: 1.sw, height: _height - 70,
                  fit: BoxFit.scaleDown, imageErrorBuilder: (_,__,___) => const SizedBox());
              }
              return CarouselSlider.builder(itemCount: ctr.backgrounds!.length,
                  options: CarouselOptions(viewportFraction: 1, initialPage: 0,
                      autoPlay: true, height: _height - 70,
                      onPageChanged: (index, reason) => bloc!.add(CountDownEvent(value: index))),
                  itemBuilder: (context, index, realIndex) =>
                      FadeInImage.assetNetwork(placeholder: 'assets/images/bg_white.png',
                          image: ctr.backgrounds![index], width: 1.sw,
                          fit: BoxFit.scaleDown, height: _height - 70,
                          imageErrorBuilder: (_,__,___) => const SizedBox())
              );
            }
        )),

        BlocBuilder(bloc: bloc, buildWhen: (_,countS) => countS is CountDownState || countS is AddImageHomeState,
          builder: (_,__) => ctr.imagePage == null ? const SizedBox(height: 70) :
            Container(height: 70, alignment: Alignment.center,
            child: ListView.builder(
               padding: const EdgeInsets.only(top: 10, bottom: 50),
               physics: const NeverScrollableScrollPhysics(),
               scrollDirection: Axis.horizontal, shrinkWrap: true,
               itemCount: ctr.backgrounds!.length,
               itemBuilder: (_,index) => Container(width: 10, height: 10,
                  margin: const EdgeInsets.symmetric(horizontal: 8),
                  decoration: BoxDecoration(borderRadius: BorderRadius.circular(5),
                    color: index == ctr.imagePage ? Colors.orange : const Color(0xFFCDCDCD))
               )
            ))),

        Container(width: 1.sw, padding: EdgeInsets.all(60.sp),
          decoration: BoxDecoration(color: color, borderRadius: const BorderRadius.vertical(top: Radius.circular(30)),
          boxShadow: [BoxShadow(color: const Color(0xFF14F985).withOpacity(0.2), blurRadius: 50, offset: const Offset(0, -50))]),
          child: BlocBuilder(bloc: bloc, buildWhen: (_,newSHide) => newSHide is HideClearScrollState, builder: (_,stateHide) {
            if (stateHide is HideClearScrollState && stateHide.value) {
              return SizedBox(height: _height - 120.sp, child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                RichText(text: TextSpan(
                  text: 'Vui lòng nhập mã xác thực OTP đã gửi đến số ' + ctr.loginKey.text,
                  style: TextStyle(fontSize: 48.sp, color: const Color(0xFF2C2E35), fontWeight: FontWeight.w400),
                  children: [
                    TextSpan(text: ' Thay đổi số',
                      style: TextStyle(fontSize: 52.sp, color: const Color(0xFF12723D), fontWeight: FontWeight.w700),
                      recognizer: TapGestureRecognizer()..onTap = ctr.showOTP)
                  ]
                )),

                Padding(padding: EdgeInsets.only(top: 76.sp, bottom: 27.sp), child: BlocBuilder(bloc: bloc,
                  buildWhen: (_,newS) => newS is WarningPostHomeState, builder: (_,__) {
                    int border = ctr.errorOTP == null ? 0xFFC5C5C5 : 0xFFDE4841;
                    return Row(mainAxisAlignment: MainAxisAlignment.spaceAround, children: [
                      Expanded(child: TextFieldCustom(ctr.otp1!, ctr.fcOtp1, ctr.fcOtp2, '', align: TextAlign.center,
                          borderColor: Color(border), sizeBorder: 8, size: 48.sp, maxLength: 6,
                          padding: EdgeInsets.symmetric(vertical: 50.sp, horizontal: 20.sp),
                          textColor: const Color(0xFF878787),
                          type: const TextInputType.numberWithOptions(),
                          inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'[0-9]'))],
                          onChanged: (edit, value) => ctr.changeOTP(ctr.otp1!, ctr.fcOtp2, value))),
                      const SizedBox(width: 10),
                      Expanded(child: TextFieldCustom(ctr.otp2!, ctr.fcOtp2, ctr.fcOtp3, '', align: TextAlign.center,
                          borderColor: Color(border), sizeBorder: 8, size: 48.sp, maxLength: 2,
                          padding: EdgeInsets.symmetric(vertical: 50.sp, horizontal: 20.sp),
                          textColor: const Color(0xFF878787),
                          type: const TextInputType.numberWithOptions(),
                          inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'[0-9]'))],
                          onChanged: (edit, value) => ctr.changeOTP(ctr.otp2!, ctr.fcOtp3, value, preCtr: ctr.fcOtp1))),
                      const SizedBox(width: 10),
                      Expanded(child: TextFieldCustom(ctr.otp3!, ctr.fcOtp3, ctr.fcOtp4, '', align: TextAlign.center,
                          borderColor: Color(border), sizeBorder: 8, size: 48.sp, maxLength: 2,
                          padding: EdgeInsets.symmetric(vertical: 50.sp, horizontal: 20.sp),
                          textColor: const Color(0xFF878787),
                          type: const TextInputType.numberWithOptions(),
                          inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'[0-9]'))],
                          onChanged: (edit, value) => ctr.changeOTP(ctr.otp3!, ctr.fcOtp4, value, preCtr: ctr.fcOtp2))),
                      const SizedBox(width: 10),
                      Expanded(child: TextFieldCustom(ctr.otp4!, ctr.fcOtp4, ctr.fcOtp5, '', align: TextAlign.center,
                          borderColor: Color(border), sizeBorder: 8, size: 48.sp, maxLength: 2,
                          padding: EdgeInsets.symmetric(vertical: 50.sp, horizontal: 20.sp),
                          textColor: const Color(0xFF878787),
                          type: const TextInputType.numberWithOptions(),
                          inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'[0-9]'))],
                          onChanged: (edit, value) => ctr.changeOTP(ctr.otp4!, ctr.fcOtp5, value, preCtr: ctr.fcOtp3))),
                      const SizedBox(width: 10),
                      Expanded(child: TextFieldCustom(ctr.otp5!, ctr.fcOtp5, ctr.fcOtp6, '', align: TextAlign.center,
                          borderColor: Color(border), sizeBorder: 8, size: 48.sp, maxLength: 2,
                          padding: EdgeInsets.symmetric(vertical: 50.sp, horizontal: 20.sp),
                          textColor: const Color(0xFF878787),
                          type: const TextInputType.numberWithOptions(),
                          inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'[0-9]'))],
                          onChanged: (edit, value) => ctr.changeOTP(ctr.otp5!, ctr.fcOtp6, value, preCtr: ctr.fcOtp4))),
                      const SizedBox(width: 10),
                      Expanded(child: TextFieldCustom(ctr.otp6!, ctr.fcOtp6, null, '', align: TextAlign.center,
                          borderColor: Color(border), sizeBorder: 8, size: 48.sp, maxLength: 2,
                          padding: EdgeInsets.symmetric(vertical: 50.sp, horizontal: 20.sp),
                          textColor: const Color(0xFF878787),
                          type: const TextInputType.numberWithOptions(), inputAction: TextInputAction.done,
                          inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'[0-9]'))],
                          onChanged: (edit, value) => ctr.changeOTP(ctr.otp6!, null, value, preCtr: ctr.fcOtp5),
                          onSubmit: () => bloc!.add(AddHashTagHomeEvent()))),
                    ]);
                  })),

                BlocBuilder(bloc: bloc, buildWhen: (_,newS) => newS is ChangeIndexHomeState || newS is WarningPostHomeState,
                  builder: (_,state) {
                    if (ctr.countOTP != null && ctr.countOTP! > 0 && ctr.errorOTP == null) {
                      return Row(children: [
                        Container(alignment: Alignment.center, child: const CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF2B9846)), strokeWidth: 1),
                          color: Colors.transparent, width: 42.sp, height: 42.sp),
                        Expanded(child: LabelCustom('  Mã hiệu lực trong ...', size: 42.sp, color: const Color(0xFF878787), weight: FontWeight.w400)),
                        LabelCustom(ctr.countOTP.toString(), size: 48.sp, color: const Color(0xFF2C2E35), weight: FontWeight.w500)
                      ]);
                    } else {
                      final temp = ButtonImageWidget(0, ctr.requestOTP,
                          LabelCustom('Gửi lại mã', size: 48.sp, color: const Color(0xFF12723D), weight: FontWeight.w500));
                      if (ctr.errorOTP != null) {
                        return Row(children: [
                          Expanded(child: LabelCustom(ctr.errorOTP!, size: 42.sp, color: Colors.red, weight: FontWeight.w400)),
                          temp
                        ]);
                      }
                      return Container(width: 1.sw, alignment: Alignment.centerRight, child: temp);
                    }
                  }),

                const Expanded(child: SizedBox()),
                Container(width: 1.sw, padding: EdgeInsets.symmetric(vertical: 48.sp),
                  child: BlocBuilder(buildWhen: (_,newS) => newS is MissionEmptyState || newS is WarningPostHomeState,
                    bloc: bloc, builder: (_,state) {
                      int hexColor = 0xFFCBD8D1;
                      if (state is MissionEmptyState && state.value || !ctr.isEmptyOTP()) hexColor = 0xFF2B9846;
                      else if (ctr.errorOTP != null) hexColor = 0xFFCBD8D1;
                      return ButtonImageWidget(10, ctr.verifyOTP, Padding(padding: EdgeInsets.all(40.sp),
                          child: LabelCustom('Xác thực', size: 48.sp, weight: FontWeight.w500, align: TextAlign.center)),
                        color: Color(hexColor));
                    })
                )
              ]));
            }
            return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              LabelCustom('Đăng nhập', color: const Color(0xFF2C2E35), size: 66.sp),
              Padding(padding: EdgeInsets.only(top: 40.sp, bottom: 20.sp),
                  child: LabelCustom('Nhập số điện thoại của bạn', size: 48.sp,
                      color: const Color(0xFF2C2E35), weight: FontWeight.w400)),

              TextFieldCustom(ctr.loginKey, null, null, 'Nhập Số Điện Thoại',
                  borderColor: const Color(0xFFC5C5C5), sizeBorder: 8,
                  padding: EdgeInsets.all(60.sp), size: 48.sp, maxLength: 10,
                  textColor: Colors.black,
                  labelBehavior: FloatingLabelBehavior.always,
                  type: TextInputType.phone, inputAction: TextInputAction.done,
                  inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'[0-9]'))],
                  prefix: Padding(padding: EdgeInsets.symmetric(horizontal: 30.sp),
                      child: Icon(Icons.phone_android, size: 60.sp)),
                  onChanged: (edit, value) => ctr.changeTextPhone(value),
                  onSubmit: ctr.requestOTP
              ),

              SizedBox(height: 40.sp),
              Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
                GestureDetector(child: BlocBuilder(bloc: bloc,
                  buildWhen: (_,n) => n is CollapseHeaderScrollState,
                  builder: (_,__) => Icon(ctr.isPolicies == true ?
                    Icons.check_box_outlined : Icons.check_box_outline_blank,
                    color: StyleCustom.primaryColor, size: 72.sp)
                ), onTap: () => bloc!.add(CollapseHeaderScrollEvent(true))),
                const SizedBox(width: 5),
                Expanded(child: RichText(text: TextSpan(text: 'Tôi đồng ý với ',
                    style: TextStyle(color: Colors.black, fontSize: 42.sp, fontWeight: FontWeight.w400),
                    children: <TextSpan>[
                      TextSpan(text: 'Điều khoản và điều kiện sử dụng dịch vụ',
                        style: TextStyle(color: StyleCustom.primaryColor, fontSize: 42.sp, fontWeight: FontWeight.w400)),
                      TextSpan(text: ' và ',
                        style: TextStyle(color: Colors.black, fontSize: 42.sp, fontWeight: FontWeight.w400)),
                      TextSpan(recognizer: TapGestureRecognizer()..onTap = () => UtilUI.goToNextPage(context, const UserGuidePage()),
                        text: 'Chính sách bảo vệ dữ liệu của 2 Nông',
                        style: TextStyle(color: StyleCustom.primaryColor, fontSize: 42.sp, fontWeight: FontWeight.w400, decoration: TextDecoration.underline))
                    ]
                )))
              ]),

              Container(width: 1.sw, padding: EdgeInsets.symmetric(vertical: 48.sp),
                child: BlocBuilder(buildWhen: (_,newS) => newS is ShowClearSearchState, bloc: bloc, builder: (_,state) {
                  int hexColor = 0xFFCBD8D1;
                  if ((state is ShowClearSearchState && state.value || ctr.loginKey.text.length > 9) && ctr.isPolicies == true) hexColor = 0xFF2B9846;
                  return ButtonImageWidget(10, ctr.requestOTP, Padding(padding: EdgeInsets.all(40.sp),
                      child: LabelCustom('Tiếp tục', size: 48.sp, weight: FontWeight.w500, align: TextAlign.center)),
                    color: Color(hexColor));
                })
              ),

              Row(children: [
                Expanded(child: Image.asset('assets/images/v11/ic_line_v11.png')),
                LabelCustom('   hoặc Đăng nhập với   ', color: const Color(0xFF2C2E35), weight: FontWeight.w500, size: 42.sp),
                Expanded(child: Image.asset('assets/images/v11/ic_line_v11.png'))
              ]),

              SizedBox(height: 36.sp),
              Row(children: [
                Flexible(child: ButtonImageWidget(10, ctr.loginViaZalo, Image.asset('assets/images/v11/ic_btn_zalo_v11.png',
                    height: 0.052.sh, filterQuality: FilterQuality.high))),
                SizedBox(width: 20.sp),
                Flexible(child: ButtonImageWidget(10, ctr.loginViaGoogle, Image.asset('assets/images/v11/ic_btn_gmail_v11.png',
                    height: 0.052.sh, filterQuality: FilterQuality.high))),
                //SizedBox(width: 20.sp),
                //Flexible(child: ButtonImageWidget(10, ctr.loginViaFaceBook, Image.asset('assets/images/v11/ic_btn_fb_v11.png',
                //    height: 0.052.sh, filterQuality: FilterQuality.high))),
                if (Platform.isIOS) SizedBox(width: 20.sp),
                if (Platform.isIOS) Flexible(child: ButtonImageWidget(10, ctr.loginViaAppleID,
                  Image.asset('assets/images/v11/ic_btn_apple_v11.png',
                    height: 0.052.sh, filterQuality: FilterQuality.high))),
                //SizedBox(width: 20.sp),
                //Flexible(child: ButtonImageWidget(10, ctr.loginViaFaceBook, Image.asset('assets/images/v11/ic_btn_fb_v11.png',
                //    height: 0.052.sh, filterQuality: FilterQuality.high)))
              ], mainAxisAlignment: MainAxisAlignment.center),

              Container(padding: EdgeInsets.all(48.sp), alignment: Alignment.center,
                child: ButtonImageWidget(5, () => UtilUI.goToPage(context, Main2Page(), null),
                  Text('Tiếp tục với tư cách của khách', style: TextStyle(
                    fontWeight: FontWeight.w500, color: const Color(0xFF363636),
                    decoration: TextDecoration.underline, fontSize: 45.sp))))
            ]);
          })
        ),
      ]),
      Loading(bloc)
    ]), backgroundColor: color));
  }

  void _showRefPopup() => showDialog(context: context, barrierDismissible: false, builder: (_) {
    final ctr = bloc as LoginOneTimeBloc;
    ctr.allowClosePopup = null;
    return Dialog(elevation: 0, insetPadding: EdgeInsets.all(40.sp),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)), backgroundColor: const Color(0xFF1AB686),
      child: Padding(padding: EdgeInsets.all(40.sp), child: ReferralQR(ctr.otp1!, ctr.sendRefQR, ctr.openScanQR,
        onOffUnQuestioning: Padding(padding: EdgeInsets.symmetric(vertical: 40.sp), child: Row(children: [
          GestureDetector(onTap: ctr.turnOnOffUnQuestioning,
            child: BlocBuilder(bloc: bloc, buildWhen: (_,newS) => newS is LoadMembersState, builder: (_,state) {
              bool active = state is LoadMembersState ? state.resp : ((bloc as LoginOneTimeBloc).unQuestion??false);
              return Icon(active ? Icons.check_box : Icons.check_box_outline_blank, color: Colors.white, size: 48.sp);
            })),
          LabelCustom(' Không hỏi lại', size: 42.sp, weight: FontWeight.w400)
        ])),
        ignore: ButtonImageWidget(5, ctr.ignoreQuestion, Padding(padding: EdgeInsets.symmetric(horizontal: 40.sp, vertical: 20.sp),
          child: LabelCustom('Bỏ qua', size: 42.sp)), color: Colors.orangeAccent, elevation: 5))));
  }).whenComplete(() {
    if ((bloc as LoginOneTimeBloc).allowClosePopup == null) _showRefPopup();
  });
}

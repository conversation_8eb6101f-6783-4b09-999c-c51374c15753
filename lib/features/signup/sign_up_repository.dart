import 'package:hainong/common/base_repository.dart';
import 'package:hainong/common/base_response.dart';
import 'package:hainong/common/constants.dart';
import 'package:hainong/common/models/item_list_model.dart';
import 'package:hainong/features/login/login_model.dart';

class SignUpRepository extends BaseRepository {

  String _listToStringJson(List<ItemModel> list, String sub) {
    String tmp = "[";
    list.forEach((element) => tmp += "$sub${element.id}$sub,");
    tmp += "]";
    return tmp.replaceAll(",]", "]");
  }

  Future<BaseResponse> updateProfile(String image, String fullName, String phone, String birthday, String gender,
      String email, String website, String address, String province, String district, String ward,
      List<ItemModel> userTypes, List<ItemModel> hashTags, List<ItemModel> trees, String acreage) async {
    List<String> files = [];
    if (image.isNotEmpty) files.add(image);

    String _userTypes = _listToStringJson(userTypes, '"');
    String _hashTags = _listToStringJson(hashTags, '');
    String _trees = _listToStringJson(trees, '"');

    final body = {
      'name': fullName,
      'phone': phone,
      'birthdate': birthday,
      'gender': gender,
      'email': email,
      'website': website,
      'address': address,
      'province_id': province != '0' ? province : '',
      'district_id': district != '0' ? district : '',
      'ward_id': ward != '0' ? ward : '',
      'user_type': _userTypes,
      'hash_tags': _hashTags,
      'family_tree': _trees,
      'acreage': acreage
    };

    final response = await apiClient.postAPI('${Constants().apiVersion}account/update_user', 'POST', LoginModel(),
        body: body, files: files, paramFile: 'image');
    return response;
  }

  Future<BaseResponse> loadProvince() => apiClient.getAPI('${Constants().apiVersion}locations/list_provinces', ItemListModel(), hasHeader: false);

  Future<BaseResponse> loadDistrict(String id) => apiClient.getAPI('${Constants().apiVersion}locations/list_districts?province_id=$id', ItemListModel(), hasHeader: false);

  Future<BaseResponse> loadCatalogue(int page, {int limit = 50}) {
    return apiClient.getAPI('${Constants().apiVersion}catalogues/post_catalogues?page=$page&limit=$limit',
        ItemListModel(), hasHeader: false);
  }

  Future<BaseResponse> sendContact(String name, String phone, String email, String content) =>
      apiClient.postAPI('${Constants().apiVersion}contacts', 'POST', BaseResponse(),
        body: {'name': name, 'phone': phone, 'email': email, 'content': content});
}

import 'package:hainong/common/count_down_bloc.dart';
import 'package:hainong/common/ui/import_lib_base_ui.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class Main2DragDropIcon extends StatefulWidget {
  final Widget child;
  final double dx, dy;
  final double? dyChat;
  final bool? isWheel;
  final int startDrag, endDrag;
  const Main2DragDropIcon(this.dx, this.dy, this.child, {this.startDrag = 12, this.endDrag = 22, this.dyChat, this.isWheel, Key? key}) : super(key: key);
  @override
  _Main2DragDropIconState createState() => _Main2DragDropIconState();
}

class _Main2DragDropIconState extends State<Main2DragDropIcon> {
  final CountDownBloc subBloc = CountDownBloc();
  Offset? prePos, curPos;
  bool isRemove = false;

  @override
  void dispose() {
    subBloc.close();
    super.dispose();
  }

  @override
  void initState() {
    if (widget.isWheel == true) {
      prePos = Offset(0, 0.682.sh);
      curPos = Offset(1.sw - widget.dx - 40.sp, 0.682.sh);
    } else {
      prePos = Offset(0, 0.78.sh);
      curPos = Offset(1.sw - widget.dx, widget.dyChat??0.78.sh);
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) => BlocBuilder(bloc: subBloc,
    builder: (_,state) {
      if (isRemove) return const SizedBox();
      return Stack(children: [
        Positioned(left: prePos!.dx > curPos!.dx ? curPos!.dx : null, top: curPos!.dy,
          right: prePos!.dx < curPos!.dx ? 1.sw - curPos!.dx - widget.dx : null,
          child: Draggable(child: widget.child, feedback: widget.child,
            childWhenDragging: widget.child,
            onDragStarted: () => subBloc.add(CountDownEvent(value: widget.startDrag)),
            onDragEnd: (detail) {
              double minL = 0.5.sw - widget.dx, minR = 0.5.sw;
              double minT = 1.sh - widget.dy - 100, minB = 1.sh - widget.dy;
              isRemove = (minL < detail.offset.dx && minR > detail.offset.dx
                          && minT < detail.offset.dy && minB > detail.offset.dy);

              prePos = curPos;
              curPos = detail.offset;
              subBloc.add(CountDownEvent(value: widget.endDrag));
            }
          )
        ),

        if  (state is CountDownState && state.value == widget.startDrag)
          Container(color: Colors.white60, width: 1.sw, height: 1.sh,
            alignment: Alignment.bottomCenter, padding: EdgeInsets.only(bottom: 80.sp),
            child: Image.asset('assets/images/v10/ic_remove_v10.png', width: 100, height: 100, fit: BoxFit.fill))
      ]);
    });
}
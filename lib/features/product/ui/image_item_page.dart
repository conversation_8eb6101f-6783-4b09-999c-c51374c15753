import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hainong/common/base_bloc.dart';
import 'package:hainong/common/models/file_byte.dart';
import 'package:hainong/common/ui/button_image_widget.dart';
import 'package:hainong/common/ui/import_lib_base_ui.dart';
import 'package:hainong/common/ui/label_custom.dart';
import '../../function/tool/diagnose_pests/diagnose_pests_bloc.dart';

class ImageItemProduct extends StatefulWidget {
  final File file;
  final Function delete;
  const ImageItemProduct(this.file, this.delete, {Key? key}) : super(key: key);
  @override
  _ImageItemProductState createState() => _ImageItemProductState();
}

class _ImageItemProductState extends State<ImageItemProduct> with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Container(alignment: Alignment.topRight, width: 240.sp,
        margin: EdgeInsets.only(right: 20.sp),
        decoration: BoxDecoration(image: DecorationImage(image: Image.file(widget.file, width: 240.sp,
            cacheHeight: 480.sp.toInt()).image, fit: BoxFit.cover)),
        child: SizedBox(height: 24, width: 24, child: IconButton(onPressed: () => widget.delete(),
            icon: const Icon(Icons.close, color: Colors.white,size: 20), padding: EdgeInsets.zero)));
  }
}

class ImageItemPests extends StatefulWidget {
  final DiagnosePestsBloc? bloc;
  final FileByte file;
  final int index;
  final Function funDelete, funAdd;
  const ImageItemPests(this.bloc, this.file, this.index, this.funAdd, this.funDelete, {Key? key}) : super(key: key);
  @override
  _ImageItemPestsState createState() => _ImageItemPestsState();
}

class _ImageItemPestsState extends State<ImageItemPests> with AutomaticKeepAliveClientMixin {
  bool _show = false;
  bool? _process;

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Stack(children: [
      ClipRRect(borderRadius: BorderRadius.circular(24.sp),
          child: Image.memory(Uint8List.fromList(widget.file.bytes), height: 0.28.sh, cacheHeight: 0.28.sh.toInt(), fit: BoxFit.cover)),

      Positioned.fill(child: BlocBuilder(bloc: widget.bloc, buildWhen: (_,n) => n is DetectState && n.index == widget.index,
        builder: (_,s) {
          if (s is BaseState && s.isShowLoading && widget.file.detectSize == null) {
            _process = true;
            _show = false;
          }
          if (s is DetectState && s.stopDetect) _process = null;

          return Stack(alignment: Alignment.bottomLeft, children: [
            if (widget.file.detections != null) Positioned.directional(textDirection: TextDirection.ltr,
              child: Container(alignment: Alignment.centerLeft, padding: const EdgeInsets.all(5),
                decoration: BoxDecoration(color: Colors.black26, borderRadius: BorderRadius.vertical(bottom: Radius.circular(24.sp))),
                child: LabelCustom('Ảnh không phù hợp để nhận dạng sâu bệnh', color: Colors.white, size: 32.sp, weight: FontWeight.normal)
              ), start: 0, bottom: 0, end: 0),

            if (_process == true) Positioned.directional(textDirection: TextDirection.ltr,
              child: Container(alignment: Alignment.centerLeft, padding: const EdgeInsets.all(5),
                decoration: BoxDecoration(color: Colors.black26, borderRadius: BorderRadius.vertical(bottom: Radius.circular(24.sp))),
                child: Row(children: [
                        Container(width: 12, height: 12, margin: const EdgeInsets.only(right: 5),
                          child: const CircularProgressIndicator(strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white))),
                        Flexible(child: LabelCustom('Đang kiểm tra ảnh ...', color: Colors.white, size: 32.sp, weight: FontWeight.normal))
                      ])
              ), start: 0, bottom: 0, end: 0),

            if (widget.file.detections != null && widget.file.start != null && widget.file.end != null)
              Positioned.directional(textDirection: TextDirection.ltr,
                child: Container(alignment: Alignment.centerLeft, padding: const EdgeInsets.all(5),
                  decoration: BoxDecoration(color: Colors.black26, borderRadius: BorderRadius.only(topLeft: Radius.circular(24.sp))),
                  child: LabelCustom(widget.file.start! + '\n' + widget.file.end!, color: Colors.white, size: 32.sp, weight: FontWeight.normal)
                ), top: 0),

            if (widget.file.detections != null) Container(decoration: BoxDecoration(borderRadius: BorderRadius.circular(24.sp),
                border: Border.all(color: Colors.orange, width: 2))),
          ]);
        })),

      BlocBuilder(bloc: widget.bloc, buildWhen: (_,n) {
            if (n is ShowResultState && n.index == widget.index && widget.file.detections != null) {
              _show = !_show;
              return true;
            }
            return false;
          }, builder: (_,__) => _show && widget.file.detections != null ?
            Positioned.fill(child: CustomPaint(painter: BoxPainter(widget.file.detections!, widget.file.w!,
                //widget.file.h!, widget.file.detectSize!, isAPI: widget.bloc!.isAPI)
                widget.file.h!, widget.file.detectSize!, isAPI: true)
      )) : const SizedBox()),

      BlocBuilder(bloc: widget.bloc, buildWhen: (_,n) => n is DetectState,
        builder: (_,__) => widget.bloc!.countDetecting == null ?
          ButtonImageWidget(100, () => widget.funDelete(widget.index), Container(
            margin: EdgeInsets.all(10.sp), padding: EdgeInsets.all(10.sp),
            decoration: BoxDecoration(color: Colors.black26, borderRadius: BorderRadius.circular(100)),
            child: Icon(Icons.clear, color: Colors.white, size: 42.sp))) : const SizedBox()),

      BlocBuilder(bloc: widget.bloc, buildWhen: (o,n) => n is DetectState,
        builder: (_,s) => widget.bloc!.countDetecting == null || _process == null ?
          Padding(padding: EdgeInsets.only(top: 80.sp),
            child: ButtonImageWidget(100, _edit, Container(
              margin: EdgeInsets.all(10.sp), padding: EdgeInsets.all(10.sp),
              decoration: BoxDecoration(color: Colors.black26, borderRadius: BorderRadius.circular(100)),
              child: Icon(Icons.edit, color: Colors.white, size: 42.sp)))) :
          const SizedBox()),

      BlocBuilder(bloc: widget.bloc, buildWhen: (_,n) => n is DetectState && n.index == widget.index,
          builder: (_,__) => widget.file.detections == null ? const SizedBox() :
              Padding(child: ButtonImageWidget(100, _showResult,
                      Padding(padding: EdgeInsets.all(15.sp), child: Icon(Icons.warning_rounded, color: Colors.orange, size: 52.sp))),
                  padding: EdgeInsets.only(top: 160.sp)))
    ], alignment: Alignment.topRight);
  }

  void _edit() {
    _show = true;
    _showResult();
    widget.funAdd(widget.index);
  }

  void _showResult() => widget.bloc!.add(ShowResultEvent(widget.index));
}

class BoxPainter extends CustomPainter {
  final List detections;
  final double imageWidth, imageHeight, inputSize;
  final bool? isAPI;
  BoxPainter(
    this.detections,
    this.imageWidth,
    this.imageHeight,
    this.inputSize,
    {this.isAPI}
  );

  @override
  void paint(Canvas canvas, Size size) {
    final Paint boxPaint = Paint()
      ..color = Colors.red
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    double scaleX = size.width / inputSize;
    double scaleY = size.height / inputSize;
    if (isAPI != null) {
      scaleX = imageWidth > size.width ? size.width/imageWidth : imageWidth/size.width;
      scaleY = imageHeight > size.height ? size.height/imageHeight : imageHeight/size.height;
    }

    double x,y,w,h;
    Rect rect;
    TextPainter tp;
    for (final Map<String, dynamic> det in detections) {
      x = det['box']['x1'] * scaleX;
      y = det['box']['y1'] * scaleY;
      w = det['box']['x2'] * scaleX;
      h = det['box']['y2'] * scaleY;
      rect = isAPI == null ? Rect.fromLTWH(x, y, w, h) : Rect.fromLTRB(x, y, w, h);
      canvas.drawRect(rect, boxPaint);

      tp = TextPainter(
        text: TextSpan(
          text: '${det['name']} ${(det['confidence'] * 100).toStringAsFixed(0)}%',
          style: const TextStyle(
            color: Colors.red,
            fontSize: 12,
            backgroundColor: Colors.white,
          ),
        ),
        textAlign: TextAlign.left,
        textDirection: TextDirection.ltr,
      );
      tp.layout();
      tp.paint(canvas, Offset(rect.left, rect.bottom - 15));
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => true;
}
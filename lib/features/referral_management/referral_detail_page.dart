import 'dart:io';
import 'package:flutter/services.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:hainong/common/ui/button_image_widget.dart';
import 'package:hainong/common/ui/image_network_asset.dart';
import 'package:hainong/common/ui/import_lib_ui.dart';
import 'package:hainong/common/ui/label_custom.dart';
import 'package:hainong/common/ui/loading.dart';
import 'package:hainong/common/ui/permission_image_page_state.dart';
import 'package:hainong/common/ui/textfield_custom.dart';
import 'package:permission_handler/permission_handler.dart';
import 'referral_bloc.dart';

class RefDetailPage extends BasePage {
  RefDetailPage({Key? key}) : super(pageState: _RefDetailPageState(), key: key);
}

class _RefDetailPageState extends PermissionImagePageState {

  @override
  void loadFiles(List<File> files) {}

  @override
  void initState() {
    bloc = RefBloc('detail');
    super.initState();
    bloc!.data = this;
  }

  @override
  Widget build(BuildContext context, {Color color = Colors.white}) {
    return GestureDetector(child: Scaffold(backgroundColor: const Color(0xFFF8FFFC),
        appBar: AppBar(centerTitle: true, elevation: 5, title: UtilUI.createLabel('Mã giới thiệu'),
          actions: [
            IconButton(onPressed: _showMenu, icon: Icon(Icons.menu, color: Colors.white, size: 58.sp))
          ]),
        body: Stack(children: [
          ListView(padding: EdgeInsets.all(40.sp), children: [
            Image.asset('assets/images/v11/ic_bg_ref_v11.png', height: 0.25.sh, fit: BoxFit.scaleDown),
            SizedBox(height: 40.sp),
            LabelCustom('Xây dựng cộng đồng nông nghiệp số\nHai Nông vững mạnh', color: const Color(0xFF1AB686),
                size: 42.sp, weight: FontWeight.w500, align: TextAlign.center),

            SizedBox(height: 88.sp),
            Row(children: [
              Expanded(child: LabelCustom('Mã giới thiệu của bạn:', color: Colors.black, size: 36.sp, weight: FontWeight.w300)),
              Expanded(child: Row(children: [
                Expanded(child: BlocBuilder(bloc: bloc,
                  builder: (_,__) {
                    final ctr = bloc as RefBloc;
                    if (ctr.result == null) return const SizedBox();
                    return LabelCustom(ctr.result!.current_referral_code, color: Colors.black, size: 42.sp, weight: FontWeight.w500);
                  })),
                ButtonImageWidget(0, _copyRef, Icon(Icons.copy, size: 64.sp, color: const Color(0x94000000)))
              ]))
            ]),

            SizedBox(height: 48.sp),
            Row(children: [
              Expanded(child: LabelCustom('QR giới thiệu của bạn:', color: Colors.black, size: 36.sp, weight: FontWeight.w300)),
              Expanded(child: Row(children: [
                ButtonImageWidget(0, _showQR, Image.asset('assets/images/v11/ic_qr2_ref_v11.png', width: 80.sp)),
                ButtonImageWidget(0, _downloadQR, Icon(Icons.file_download_outlined, color: const Color(0x91000000), size: 80.sp)),
                ButtonImageWidget(0, _shareTo, Icon(Icons.share, color: const Color(0x91000000), size: 80.sp)),
              ], mainAxisAlignment: MainAxisAlignment.spaceBetween))
            ], crossAxisAlignment: CrossAxisAlignment.end),

            BlocBuilder(bloc: bloc, buildWhen: (_,newS) => newS is LoadCatalogueState || newS is LoadDistrictState,
              builder: (_,__) {
                final ctr = bloc as RefBloc;
                if (ctr.result == null) return const SizedBox();

                if (ctr.result!.referral_code == null) {
                  return Container(padding: EdgeInsets.all(40.sp), margin: EdgeInsets.only(top: 80.sp),
                      decoration: BoxDecoration(color: const Color(0xFF1AB686), borderRadius: BorderRadius.circular(10)),
                      child: ReferralQR(ctr.ctrQR!, ctr.sendRefQR, ctr.openScanQR, fcQR: ctr.fcQR));
                }

                final bool isUser = ctr.result!.referral_id != null;
                return Container(padding: EdgeInsets.only(top: isUser ? 72.sp : 86.sp),
                  //child: ButtonImageWidget(5, (){}, isUser ? Column(children: [
                  child: isUser ? Column(children: [
                    Row(children: [
                      Expanded(child: LabelCustom('Bạn được giới thiệu bởi:', color: Colors.black, size: 36.sp, weight: FontWeight.w300)),
                      Expanded(child: Row(children: [
                        ClipRRect(borderRadius: BorderRadius.circular(50),
                            child: ImageNetworkAsset(path: ctr.result!.referraler_avatar??'',
                                asset: 'assets/images/v2/ic_avatar_drawer_v2.png', width: 80.sp, height: 80.sp)),
                        SizedBox(width: 20.sp),
                        Flexible(child: ctr.result!.referraler_name == null ?
                          LabelCustom((ctr.result!.referral_code??''), color: Colors.black, size: 42.sp, weight: FontWeight.w500) :
                          LabelCustom(ctr.result!.referraler_name!, color: Colors.black, size: 42.sp, weight: FontWeight.w300))
                      ]))
                    ], crossAxisAlignment: CrossAxisAlignment.center),
                    if (ctr.result!.referraler_name != null) Row(children: [
                      const Expanded(child: SizedBox()),
                      Expanded(child: Row(children: [
                        SizedBox(width: 100.sp, height: 80.sp),
                        Flexible(child: LabelCustom(ctr.result!.referral_code!, color: Colors.black, size: 42.sp, weight: FontWeight.w500))
                      ]))
                    ], crossAxisAlignment: CrossAxisAlignment.center),
                  ]) : Row(children: [
                    Expanded(child: LabelCustom('Bạn được giới thiệu bởi:', color: Colors.black, size: 36.sp, weight: FontWeight.w300)),
                    Expanded(child: LabelCustom('2Nông', color: Colors.black, size: 42.sp, weight: FontWeight.w500))
                  ])
                  //)
                );
              })
          ]),
          Loading(bloc)
        ])), onTap: clearFocus);
  }

  void _copyRef() {
    final ctr = bloc as RefBloc;
    if (ctr.checkPhone()) return;
    if (ctr.result == null || ctr.result!.current_referral_code.isEmpty) return;
    Clipboard.setData(ClipboardData(text: ctr.result!.current_referral_code));
    ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text("Mã giới thiệu đã được sao chép")));
  }

  void _showQR() {
    if ((bloc as RefBloc).checkPhone()) return;
    if ((bloc as RefBloc).hasNotQR()) return;
    showDialog(context: context, barrierDismissible: false,
      builder: (_) {
        final ctr = bloc as RefBloc;
        return Stack(children: [
          Container(decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(8)),
            child: Column(children: [
              ImageNetworkAsset(path: ctr.result!.qr_code??'', width: 0.8.sw, height: 0.8.sw),
              Row(children: [
                ButtonImageWidget(0, _downloadQR, Icon(Icons.file_download_outlined, color: const Color(0xFF1AB686), size: 100.sp)),
                SizedBox(width: 80.sp),
                ButtonImageWidget(0, _shareTo, Icon(Icons.share, color: const Color(0xFF1AB686), size: 80.sp))
              ], mainAxisSize: MainAxisSize.min),
              SizedBox(height: 40.sp)
            ], mainAxisSize: MainAxisSize.min)),
          Padding(padding: EdgeInsets.only(left: 0.8.sw, bottom: 0.8.sw + 140.sp),
            child: ButtonImageWidget(50, () => Navigator.pop(context),
              Padding(padding: const EdgeInsets.all(5), child: Icon(Icons.clear, color: Colors.white, size: 48.sp)),
              color: const Color(0xFF1AB686))),
          Loading(bloc)
        ], alignment: Alignment.center);
      });
  }

  void _downloadQR() async {
    final List<Permission> pers = [];
    if (Platform.isAndroid) {
      final deviceInfo = await DeviceInfoPlugin().androidInfo;
      if ((deviceInfo.version.sdkInt??1) > 32) {
        pers.add(Permission.audio);
        pers.add(Permission.videos);
        pers.add(Permission.photos);
      }
    }
    if (pers.isEmpty) pers.add(Permission.storage);
    funCheckPermissions(arrayPer: pers).then((value) {
      if (value == PermissionStatus.granted) {
        if (!(bloc as RefBloc).checkPhone()) bloc!.add(LoadProvinceEvent());
      }
    });
  }

  void _shareTo() {
    final ctr = bloc as RefBloc;
    if (ctr.checkPhone()) return;
    if (ctr.result == null || ctr.result!.referral_link.isEmpty) return;
    UtilUI.shareTo(context, ctr.result!.referral_link, 'Open Share Dialog', 'Referral Management Screen', hasDomain: true);
  }

  void _showMenu() => showDialog(context: context, barrierColor: Colors.transparent,
    builder: (context) => MediaQuery.removeViewInsets(
      removeLeft: true, removeTop: true,
      removeRight: true, removeBottom: true, context: context,
      child: Container(padding: EdgeInsets.only(top: 0.05.sh, right: 40.sp),
        alignment: Alignment.topRight,
        child: Material(color: Colors.white, elevation: 5,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
          type: MaterialType.card, clipBehavior: Clip.none,
          child: ButtonImageWidget(5, () {
            Navigator.pop(context);
            UtilUI.goToNextPage(context, _RefTopListPage());
          }, Padding(padding: EdgeInsets.symmetric(horizontal: 40.sp, vertical: 20.sp), child: Row(children: [
              Image.asset('assets/images/v11/ic_top_ref_v11.png', width: 60.sp),
              SizedBox(width: 40.sp),
              LabelCustom('Top giới thiệu người dùng', color: const Color(0xFF1AB686), size: 36.sp, weight: FontWeight.w400)
            ], mainAxisSize: MainAxisSize.min)))
          )
      )
    ));
}

class ReferralQR extends StatelessWidget {
  final TextEditingController ctrQR;
  final FocusNode? fcQR;
  final dynamic funSendQR, funOpenScanQR;
  final Widget? ignore, onOffUnQuestioning;
  final bool enable;
  const ReferralQR(this.ctrQR, this.funSendQR, this.funOpenScanQR, {this.fcQR, this.ignore, this.onOffUnQuestioning, this.enable = true, Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) => Column(mainAxisSize: MainAxisSize.min, children: [
    Image.asset('assets/images/v11/ic_hand_ref_v11.png', width: 80.sp, height: 80.sp, opacity: AlwaysStoppedAnimation(enable ? 1 : 0.2)),
    Padding(padding: EdgeInsets.only(top: 10.sp, bottom: 40.sp),
        child: LabelCustom('Bạn biết đến Hai Nông từ người bạn nào? Hãy thêm mã giới thiệu tại đây nhé!',
            size: 42.sp, weight: FontWeight.w400, align: TextAlign.center,
            color: enable ? Colors.white : Colors.white60)),

    Row(crossAxisAlignment: CrossAxisAlignment.center, children: [
      Expanded(child: TextFieldCustom(ctrQR, fcQR, null,
          'Nhập hoặc quét QR mã giới thiệu từ bạn bè', sizeBorder: 6, maxLength: 20,
          borderColor: const Color(0xFFC5C5C5), padding: EdgeInsets.all(40.sp), size: 42.sp,
          textColor: const Color(0xFF878787), inputAction: TextInputAction.done,
          suffix: Column(children: [
            GestureDetector(onTap: enable ? funSendQR : null,
                child: LabelCustom('Gửi', size: 42.sp, color: Color(enable ? 0xFF1AB686 : 0xFFCDCDCD)))
          ], mainAxisAlignment: MainAxisAlignment.center),
          onSubmit: funSendQR, readOnly: !enable, color: enable ? Colors.white : Colors.white60,
      )),
      SizedBox(width: 40.sp),
      ButtonImageWidget(0, enable ? funOpenScanQR : (){}, Image.asset('assets/images/v11/ic_qr3_ref_v11.png', height: 124.sp, opacity: AlwaysStoppedAnimation(enable ? 1 : 0.2)))
    ]),

    if (onOffUnQuestioning != null) onOffUnQuestioning!,
    if (ignore != null) ignore!
  ]);
}

class _RefTopListPage extends BasePage {
  _RefTopListPage({Key? key}) : super(pageState: _RefTopListPageState(), key: key);
}

class _RefTopListPageState extends BasePageState {
  @override
  void initState() {
    bloc = RefBloc('top_list');
    super.initState();
    bloc!.data = this;
  }

  @override
  Widget build(BuildContext context, {Color color = Colors.white}) =>
    Scaffold(appBar: AppBar(centerTitle: true, elevation: 0, title: UtilUI.createLabel('Mã giới thiệu')),
      backgroundColor: const Color(0xFFF8FFFC),
      body: Stack(children: [
        BlocBuilder(bloc: bloc, buildWhen: (_,newS) => newS is LoadCatalogueState,
          builder: (_,state) {
            double total = 0;
            List? list;
            if (state is LoadCatalogueState) {
              total = (state.resp['total']??0).toDouble();
              list = state.resp['referralers'];
            }
            return Column(children: [
              Container(padding: EdgeInsets.all(40.sp), margin: EdgeInsets.all(40.sp),
                decoration: BoxDecoration(color: const Color(0xB81AB686), borderRadius: BorderRadius.circular(6)),
                child: Row(children: [
                  LabelCustom('Số người bạn đã\ngiới thiệu thành công', size: 36.sp, weight: FontWeight.w400),
                  LabelCustom(Util.doubleToString(total), size: 90.sp, weight: FontWeight.w400, color: const Color(0xFFFF8C3D))
                ], mainAxisAlignment: MainAxisAlignment.spaceBetween)
              ),
              if (list != null && list.isNotEmpty) Expanded(child: Stack(children: [
                Container(margin: EdgeInsets.fromLTRB(40.sp, 200.sp, 40.sp, 80.sp),
                    decoration: BoxDecoration(borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: const Color(0xFF1AB686), width: 1)),
                    child: ListView.separated(padding: EdgeInsets.fromLTRB(40.sp, 120.sp, 40.sp, 40.sp),
                        itemCount: list.length, shrinkWrap: true,
                        separatorBuilder: (_,index) => const Divider(height: 40, thickness: 0.5, color: Colors.grey),
                        itemBuilder: (_,index) {
                          return Row(children: [
                            Container(padding: const EdgeInsets.all(10), width: 40, height: 40, alignment: Alignment.center,
                              decoration: index < 3 ? BoxDecoration(color: Colors.orange, borderRadius: BorderRadius.circular(50)) : null,
                              child: LabelCustom((index+1).toString(), color: Colors.black, size: 36.sp),
                            ),
                            Padding(padding: EdgeInsets.only(left: 80.sp, right: 40.sp), child: ClipRRect(borderRadius: BorderRadius.circular(50),
                                child: ImageNetworkAsset(path: list![index]['image']??'', width: 120.sp, height: 120.sp,
                                    asset: 'assets/images/v2/ic_avatar_drawer_v2.png'))),
                            Expanded(child: Column(children: [
                              LabelCustom(list[index]['name']??'', color: Colors.black, size: 36.sp, weight: FontWeight.w400),
                              const SizedBox(height: 10),
                              Row(children: [
                                Icon(Icons.location_on_outlined, color: Colors.black, size: 40.sp),
                                Expanded(child: LabelCustom(list[index]['province_name']??'', color: Colors.black, size: 36.sp, weight: FontWeight.w400))
                              ]),
                            ], crossAxisAlignment: CrossAxisAlignment.start)),
                            LabelCustom((list[index]['total_referrals']??0).toString(), color: Colors.black, size: 36.sp)
                          ]);
                        })),
                Padding(padding: EdgeInsets.only(top: 15.sp),
                    child: Image.asset('assets/images/v11/ic_top_cup_ref_v11.png', fit: BoxFit.scaleDown, width: 100.sp)),
                Container(padding: EdgeInsets.all(40.sp), margin: EdgeInsets.only(top: 135.sp), width: 0.6.sw, height: 130.sp,
                  decoration: BoxDecoration(image: DecorationImage(
                    image: Image.asset('assets/images/v11/ic_top_bg_ref_v11.png').image,
                    fit: BoxFit.fill
                  )), alignment: Alignment.center,
                  child: LabelCustom('Bảng xếp hạng', color: Colors.black, size: 38.sp, weight: FontWeight.w400)
                )
              ], alignment: Alignment.topCenter))
            ]);
          }),
        Loading(bloc)
      ]));
}
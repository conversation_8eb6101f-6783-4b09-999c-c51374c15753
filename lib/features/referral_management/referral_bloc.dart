import 'dart:async';
import 'dart:convert';
import 'package:firebase_dynamic_links/firebase_dynamic_links.dart';
import 'package:flutter/material.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:hainong/common/import_lib_system.dart';
import 'package:hainong/common/ui/scan_qr_bc.dart';
import 'package:hainong/common/api_client.dart';
import 'package:hainong/features/function/support/mission/mission_bloc.dart';
import '../login/login_model.dart';
import '../profile/ui/profile_page.dart';

export 'package:hainong/features/function/support/mission/mission_bloc.dart';

class RefBloc extends BaseBloc implements ProfilePageCallback {
  TextEditingController? ctrQR;
  FocusNode? fcQR;
  LoginModel? result;

  @override
  Future<void> close() async {
    ctrQR?.dispose();
    fcQR?.dispose();
    super.close();
  }

  @override
  updateProfile() {
    SharedPreferences.getInstance().then((prefs) {
      result!.phone = prefs.getString('phone')??'';
      if (result!.phone.isNotEmpty) add(LoadCatalogueEvent());
    });
  }

  RefBloc(String type) {
    switch(type) {
      case 'detail': _initDetail(); break;
      case 'top_list': _initTopList(); break;
    }
  }

  /// Start Detail
  void _initDetail() {
    //download referral QR
    on<LoadProvinceEvent>((event, emit) async {
      if (hasNotQR()) return;
      emit(const BaseState(isShowLoading: true));
      //List<File> files = await Util.loadFilesFromNetwork(ProductRepository(), [ItemModel(id: 'download', name: result!.qr_code!)]);
      final bytes = await ApiClient().getBytes(result!.qr_code!, timeout: 120);
      if (bytes != null) {
        final rs = await ImageGallerySaver.saveImage(bytes, quality: 100);
        if (rs != null && rs is Map && rs['isSuccess'] == true) UtilUI.showCustomDialog(data.context, 'Tải ảnh thành công');
      }
      emit(const BaseState());
    });
    on<LoadDistrictEvent>(_sendRefQR); //send referral QR
    on<LoadCatalogueEvent>(_loadInfo); //load user info
    add(LoadCatalogueEvent());
  }

  bool hasNotQR() => result == null || result!.qr_code == null || result!.qr_code!.isEmpty;

  void openScanQR() {
    if (checkPhone()) return;
    UtilUI.goToNextPage(data.context, ScanQRBCPage(), funCallback: _scanQRCallback);
  }
  void _scanError() => UtilUI.showCustomDialog(data.context, 'Mã không hợp lệ');
  void _scanQRCallback(value) {
    try {
      if (value != null) {
        if (!value.contains('http')) {
          _scanError();
          return;
        }

        FirebaseDynamicLinks.instance.getDynamicLink(Uri.parse(value)).then((link) {
          if (link != null) {
            ctrQR!.text = link.link.queryParameters['referral_code']??'';
            if (ctrQR!.text.isNotEmpty) sendRefQR();
          }
        }).catchError((_) {
          _scanError();
        }).onError((_,__) {
          _scanError();
        });
      }
    } catch (_) {
      _scanError();
    }
  }

  void sendRefQR() => add(LoadDistrictEvent(''));
  FutureOr<void>  _sendRefQR(LoadDistrictEvent event, emit) async {
    if (checkPhone()) return;

    if (ctrQR!.text.trim().isEmpty) {
      UtilUI.showCustomDialog(data.context, 'Nhập hoặc quét QR mã giới thiệu').whenComplete(() => fcQR!.requestFocus());
      return;
    }

    emit(const BaseState(isShowLoading: true));
    final resp = await ApiClient().postAPI(Constants().apiVersion + 'login_otp/update_referral_code', 'POST',
      LoginModel(), body: {'referral_code': ctrQR!.text});
    if (data.isResponseNotError(resp)) {
      result = resp.data;
      emit(LoadDistrictState(null));
      UtilUI.saveInfo(data.context, result, null, null);
      if (result!.referral_code == null) {
        Timer(const Duration(seconds: 2), () {
          ctrQR!.dispose();
          fcQR!.dispose();
          ctrQR = null;
          fcQR = null;
        });
      }
    } else emit(const BaseState());
  }

  bool checkPhone() {
    if (result != null && result!.phone.isEmpty) {
      UtilUI.showCustomDialog(data.context, 'Bổ sung số điện thoại để thực hiện tiếp tính năng này', isActionCancel: true).then((value) {
        if (value == true) UtilUI.goToNextPage(data.context, ProfilePage(callback: this, showEditPhone: true));
      });
      return true;
    }
    return false;
  }

  FutureOr<void>  _loadInfo(LoadCatalogueEvent event, emit) async {
    emit(const BaseState(isShowLoading: true));
    final prefs = await SharedPreferences.getInstance();
    result = LoginModel(
      referral_id: prefs.getInt('referral_id'),
      referral_link: prefs.getString('referral_link')??'',
      current_referral_code: prefs.getString('current_referral_code')??'',
      referral_code: prefs.getString('referral_code'),
      referraler_name: prefs.getString('referraler_name'),
      referraler_avatar: prefs.getString('referraler_avatar'),
      qr_code: prefs.getString('qr_code'),
      phone: prefs.getString('phone')??'',
    );
    if (result!.referral_code == null) {
      ctrQR ??= TextEditingController();
      fcQR ??= FocusNode();
    }
    emit(LoadCatalogueState(null));
  }
  // End Detail

  /// Start Top List
  void _initTopList() {
    on<LoadCatalogueEvent>((event, emit) async {
      emit(const BaseState(isShowLoading: true));
      final resp = await ApiClient().getAPI2(Constants().apiVersion + 'account/report_top_ten_referraler');
      if (resp.isNotEmpty) {
        final json = jsonDecode(resp);
        if (Util.checkKeyFromJson(json, 'data')) {
          emit(LoadCatalogueState(json['data']));
          return;
        }
      }
      emit(const BaseState());
    });
    add(LoadCatalogueEvent());
  }
  // End Top List
}
import 'package:flutter/material.dart';
import 'package:hainong/common/models/item_list_model.dart';
import 'package:hainong/common/multi_language.dart';

class ProfileVariant {
  final TextEditingController ctrFullName = TextEditingController();
  final TextEditingController ctrPhone = TextEditingController();
  final TextEditingController ctrBirthday = TextEditingController();
  final TextEditingController ctrGender = TextEditingController();
  final TextEditingController ctrEmail = TextEditingController();
  final TextEditingController ctrWebsite = TextEditingController();
  final TextEditingController ctrAcreage = TextEditingController();
  final FocusNode focusFullName = FocusNode();
  final FocusNode focusPhone = FocusNode();
  final FocusNode focusBirthday = FocusNode();
  final FocusNode focusGender = FocusNode();
  final FocusNode focusEmail = FocusNode();
  final FocusNode focusWebsite = FocusNode();
  final FocusNode focusAcreage = FocusNode();
  final List<ItemModel> catalogueUserType = [], catalogueHashTag = [];
  final Map<String, String> values = {};
  final List<ItemModel> imageTypes = [
    ItemModel(id: 'lbl_camera', name: MultiLanguage.get('lbl_camera')),
    ItemModel(id: 'lbl_gallery', name: MultiLanguage.get('lbl_gallery')),
  ];
  late String locale;
  String image = '';

  void dispose() {
    ctrFullName.dispose();
    ctrPhone.dispose();
    ctrBirthday.dispose();
    ctrGender.dispose();
    ctrEmail.dispose();
    ctrWebsite.dispose();
    ctrAcreage.dispose();
    focusFullName.dispose();
    focusPhone.dispose();
    focusBirthday.dispose();
    focusGender.dispose();
    focusEmail.dispose();
    focusWebsite.dispose();
    focusAcreage.dispose();
    catalogueUserType.clear();
    catalogueHashTag.clear();
    values.clear();
    imageTypes.clear();
  }
}
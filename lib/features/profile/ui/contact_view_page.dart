import 'package:hainong/common/ui/box_dec_custom.dart';
import 'package:hainong/common/ui/button_image_widget.dart';
import 'package:hainong/common/ui/import_lib_base_ui.dart';
import 'package:hainong/common/ui/label_custom.dart';
import 'package:url_launcher/url_launcher.dart';

class ContactViewPage extends StatelessWidget {
  const ContactViewPage({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) => Scaffold(
    appBar: AppBar(centerTitle: true, elevation: 0,
      title: LabelCustom('Thông tin liên hệ', size: 50.sp, align: TextAlign.center)
    ),
    body: Container(margin: EdgeInsets.symmetric(horizontal: 40.sp, vertical: 80.sp),
      decoration: BoxDecCustom(radius: 10), width: 1.sw,
      child: ListView(padding: EdgeInsets.all(40.sp), shrinkWrap: true,
        children: [
          Image.asset('assets/images/ic_logo_login.png', height: 180.sp),

          const SizedBox(height: 8),
          LabelCustom('CÔNG TY CỔ PHẦN PHÂN BÓN DẦU KHÍ CÀ MAU', color: Colors.green, size: 56.sp, weight: FontWeight.w500, align: TextAlign.center),

          const SizedBox(height: 25),
          Row(children: [
            LabelCustom('Đại Diện: ', color: Colors.black54, size: 48.sp, weight: FontWeight.w400),
            LabelCustom('Nguyễn Tấn Đạt', color: Colors.black87, size: 56.sp, weight: FontWeight.w400),
          ], crossAxisAlignment: CrossAxisAlignment.end),

          const SizedBox(height: 10),
          LabelCustom('GPKD số 2001012298 - Cấp ngày 24/03/2011', color: Colors.black87, size: 50.sp, weight: FontWeight.w400, align: TextAlign.justify),

          const SizedBox(height: 10),
          Row(children: [
            LabelCustom('Địa chỉ: ', color: Colors.black54, size: 48.sp, weight: FontWeight.w400),
            Expanded(child: LabelCustom('173 - 179 đường Trương Văn Bang, phường Thạnh Mỹ Lợi, Thành phố Thủ Đức, Thành phố Hồ Chí Minh',
                color: Colors.black87, size: 50.sp, weight: FontWeight.w400, align: TextAlign.justify))
          ], crossAxisAlignment: CrossAxisAlignment.start),

          const SizedBox(height: 10),
          Row(children: [
            LabelCustom('Tel: ', color: Colors.black54, size: 48.sp, weight: FontWeight.w400),
            ButtonImageWidget(0, () {
              launchUrl(Uri.parse('tel://19009099'));
            }, LabelCustom('19009099', color: Colors.orangeAccent, size: 52.sp, weight: FontWeight.w400))
          ]),

          const SizedBox(height: 10),
          Row(children: [
            LabelCustom('Email: ', color: Colors.black54, size: 48.sp, weight: FontWeight.w400),
            ButtonImageWidget(0, () {
              launchUrl(Uri(scheme: 'mailto', path: '<EMAIL>'));
            }, LabelCustom('<EMAIL>', color: Colors.orangeAccent, size: 52.sp, weight: FontWeight.w400))
          ]),

          const SizedBox(height: 10),
          Row(children: [
            LabelCustom('Trợ giúp: ', color: Colors.black54, size: 48.sp, weight: FontWeight.w400),
            ButtonImageWidget(0, () {
              launchUrl(Uri.parse('https://help.hainong.vn'), mode: LaunchMode.externalApplication);
            }, LabelCustom('help.hainong.vn', color: Colors.orangeAccent, size: 52.sp, weight: FontWeight.w400))
          ]),
      ]))
  );
}
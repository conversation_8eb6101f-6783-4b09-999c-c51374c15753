import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/material.dart';
import '../bloc/scroll_bloc.dart';

class AnimationHeaderFooter extends StatelessWidget {
  final ScrollBloc scrollBloc;
  final String type;
  final Widget firstChild, secondChild;
  const AnimationHeaderFooter(this.scrollBloc, this.type, this.firstChild, this.secondChild, {Key? key}):super(key:key);

  @override
  Widget build(context) => BlocBuilder(bloc: scrollBloc,
      buildWhen: (_,newState) {
        if (type == 'header') return newState is CollapseHeaderScrollState;
        if (type == 'footer') return newState is CollapseFooterScrollState;
        return false;
      },
      builder: (_,state) {
        bool value = false;
        if (type == 'header' && state is CollapseHeaderScrollState) value = state.value;
        else if (type == 'footer' && state is CollapseFooterScrollState) value = state.value;
        return AnimatedCrossFade(crossFadeState: value ? CrossFadeState.showSecond : CrossFadeState.showFirst,
            firstChild: firstChild, secondChild: secondChild, duration: Duration(milliseconds: type == 'footer' ? 1200 : 500));
      });
}

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:url_launcher/url_launcher.dart';
import 'common/api_client.dart';
import 'common/constants.dart';
import 'common/database_helper.dart';
import 'common/multi_language.dart';
import 'common/util/util_ui.dart';
import 'features/main2/ui/main2_page.dart';

class SplashPage extends StatefulWidget {
  const SplashPage({Key? key}) : super(key: key);
  @override
  _SplashPageState createState() => _SplashPageState();
}
class _SplashPageState extends State<SplashPage> {
  int _count = 0;

  @override
  void dispose() {
    Constants().splashBG = null;
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    Constants().splashBG == null ? _getBackground() : _count++;
    ApiClient().checkVersion().then((url) {
      _count++;
      if (url.isEmpty) return _onAfterBuild();
      UtilUI.showCustomDialog(context, MultiLanguage.get('msg_new_version'),
        isActionCancel: true,title: MultiLanguage.get('ttl_notify')).then((value) {
          if (value != null && value) launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
      }).whenComplete(() => _onAfterBuild());
    });
  }

  @override
  Widget build(BuildContext context) => Constants().splashBG == null ? Image.asset('assets/images/v2/bg_splash_temp.png', width: 1.sw, height: 1.sh, fit: BoxFit.cover) :
    Image.network(Constants().splashBG!, width: 1.sw, height: 1.sh, fit: BoxFit.cover,
      errorBuilder: (_,__,___) => Image.asset('assets/images/v2/bg_splash_temp.png', width: 1.sw, height: 1.sh, fit: BoxFit.cover));

  void _onAfterBuild() {
    if (_count < 2) return;
    Timer(const Duration(milliseconds: 1500), () {
      UtilUI.clearAllPages(context);
      Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => Main2Page()));
    });
  }

  Future<void> _getBackground() async {
    dynamic temp = await DBHelperUtil().getSetting('first_screen_image');
    _count++;
    if (temp != null) {
      Constants().splashBG = temp;
      setState((){});
    }
    _onAfterBuild();
  }
}

import 'dart:async';
import 'package:hainong/common/ui/permission_image_page_state.dart';
import 'package:hainong/common/util/util_ui.dart';
import 'package:hainong/features/function/tool/nutrition_map/nutrition_location_page.dart';
import 'package:trackasia_gl/trackasia_gl.dart';
import '../models/item_list_model.dart';
import '../base_bloc.dart';
import '../util/util.dart';
import 'import_lib_base_ui.dart';

abstract class AutoFillAddressState extends PermissionImagePageState {
  final TextEditingController ctrAddress = TextEditingController(), ctrProvince = TextEditingController(),
      ctrDistrict = TextEditingController(), ctrWard = TextEditingController();
  final FocusNode fcProvince = FocusNode(), fcDistrict = FocusNode(), fcWard = FocusNode(), fcAddress = FocusNode();
  final List<ItemModel> provinces = [], districts = [], wards = [];
  final ItemModel province = ItemModel(), district = ItemModel(), ward = ItemModel();
  LatLng? address;
  String? districtTemp, wardTemp, addressTemp, keyStreet, showProDisWard, streetTemp;
  bool? showPopupAddress, isFirst, byPassWard;

  @override
  void dispose() {
    ctrProvince.dispose();
    fcProvince.dispose();
    ctrDistrict.dispose();
    fcDistrict.dispose();
    ctrWard.dispose();
    fcWard.dispose();
    ctrAddress.dispose();
    fcAddress.dispose();
    provinces.clear();
    districts.clear();
    wards.clear();
    super.dispose();
  }

  void listenBloc(state);

  void initBloc(dynamic blocInit) {
    bloc = blocInit;
    bloc!.stream.listen((state) {
      if (state is LoadProvinceBaseState) {
        provinces.addAll(state.list);
      } else if (state is LoadDistrictBaseState) {
        districts.addAll(state.list);
        showProDisWard = 'district';
        autoFillDistrict();
      } else if (state is LoadWardBaseState) {
        wards.addAll(state.list);
        showProDisWard = 'ward';
        autoFillWard();
      } else if (state is GetAddressBaseState) {
        final json = state.address;

        streetTemp = json['street']??'';

        if (Util.checkKeyFromJson(json, 'province_id')) {
          setProvince(ItemModel(id: json['province_id'].toString(), name: json['province_name']));
        }
        if (Util.checkKeyFromJson(json, 'district_id')) {
          setDistrict(ItemModel(id: json['district_id'].toString(), name: json['district_name']));
        }
        if (Util.checkKeyFromJson(json, 'ward_id')) {
          setWard(ItemModel(id: json['ward_id'].toString(), name: json['ward_name']));
        }

        if (keyStreet == 'pro_dis_ward') {
          String rest = ward.name;
          String temp = district.name;
          if (temp.isNotEmpty) rest += ', $temp';
          temp = province.name;
          if (temp.isNotEmpty) rest += ', $temp';
          ctrAddress.text = rest;
        } else if (Util.checkKeyFromJson(json, keyStreet??'address_full')) {
          ctrAddress.text = json[keyStreet??'address_full'];
        }
      } else if (state is GetLocationBaseState) {
        try {
          double lat = double.parse((state.latLng['lat']??.0).toString());
          double lng = double.parse((state.latLng['lng']??.0).toString());
          address = LatLng(lat, lng);
        } catch (_) {}
      } else if (state is GetAddressFromMapState) {
        showPopupAddress = true;
        if (bloc!.startAutoSearch == true) autoSearch();
      } else if (state is HideAddressState) showPopupAddress = null;
      listenBloc(state);
    });
  }

  void autoFillDistrict() {
    if (districtTemp != null && districts.isNotEmpty) {
      final temp = Util().mappingCity(districtTemp!, districts);
      if (temp != null) {
        setDistrict(temp, loadWard: district.id != temp.id);
        return;
      }
    }
    districtTemp = null;
    bloc!.add(LoadingEvent(false));
  }

  void autoFillWard() {
    if (wardTemp != null && wards.isNotEmpty) {
      final temp = Util().mappingCity(wardTemp!, wards);
      if (temp != null) setWard(temp);
    }
    wardTemp = null;
    bloc!.add(LoadingEvent(false));
  }

  void autoSearch() {
    if (bloc!.startAutoSearch != true) {
      if (bloc!.startAutoSearch == false) bloc!.startAutoSearch = null;
      return;
    }
    Timer(const Duration(milliseconds: 3000), () {
      if (ctrAddress.text.isEmpty || showPopupAddress == null) {
        autoSearch();
        return;
      }
      searchAddress(showLoading: false);
    });
  }

  void searchAddress({bool showLoading = true, bool setState = false}) =>
    bloc!.add(GetAddressFromMapEvent(ctrAddress.text, showLoading: showLoading, setState: setState));

  void changedAddress(TextEditingController ctr, String value) {
    if (address == null) return;
    final temp = addressTemp?.toLowerCase() ?? '';
    if (!temp.contains(province.name.toLowerCase()) || !temp.contains(district.name.toLowerCase())) address = null;
  }

  void selectAddress(Map addresses, {String keyAddress = 'label'}) {
    fcAddress.unfocus();
    bloc!.add(HideAddressEvent());
    try {
      final loc = addresses['geometry']['coordinates'];
      address = LatLng(loc[1], loc[0]);
    } catch (_) {}
    try {
      addressTemp = addresses['properties']['label'];
      streetTemp = addresses['properties']['name']??'';
      String temp = addresses['properties']['housenumber']??'';
      if (Util.checkKeyFromJson(addresses['properties'], 'street')) temp = temp.isEmpty ? addresses['properties']['street'] : '$temp ${addresses['properties']['street']}';
      if (!streetTemp!.contains(temp)) streetTemp = '$streetTemp, $temp';
      switch(keyAddress) {
        case 'pro_dis_ward':
          String rest = addresses['properties']['locality']??'';
          String temp = addresses['properties']['county']??'';
          if (temp.isNotEmpty) rest += rest.isEmpty ? temp : ', $temp';
          temp = addresses['properties']['region']??'';
          if (temp.isNotEmpty) rest += rest.isEmpty ? temp : ', $temp';
          ctrAddress.text = rest;
          break;
        case 'name':
          ctrAddress.text = streetTemp!;
          break;
        default:
          ctrAddress.text = addresses['properties'][keyAddress];
      }
    } catch (_) {}
    try {
      wardTemp = (addresses['properties']['locality']).toLowerCase();
    } catch (_) {}
    try {
      districtTemp = (addresses['properties']['county']).toLowerCase();
    } catch (_) {}
    try {
      final temp = Util().mappingCity((addresses['properties']['region']).toLowerCase(), provinces);
      if (temp != null) setProvince(temp, loadDistrict: true);
    } catch (_) {}
  }

  void setProvince(ItemModel value, {bool loadDistrict = true, bool clearDistrict = true}) {
    if (province.id != value.id) {
      ctrProvince.text = value.name;
      province.setValue(value.id, value.name);

      if (loadDistrict) {
        if (clearDistrict) {
          districts.clear();
          ctrDistrict.text = '';
          district.setValue('', '');
          _clearWard();
        }
        if (province.id.isNotEmpty) bloc!.add(LoadDistrictBaseEvent(province.id));
      }
    } else autoFillDistrict();
  }

  void selectProvince({bool isView = false}) {
    if (isView) return;
    UtilUI.showOptionDialog(context, 'Chọn tỉnh/Thành phố', provinces, province.id).then((value) {
      if (value != null) setProvince(value);
    });
  }

  void setDistrict(ItemModel value, {bool loadWard = true, bool clearWard = true}) {
    if (district.id != value.id) {
      ctrDistrict.text = value.name;
      district.setValue(value.id, value.name);

      if (loadWard) {
        if (clearWard) _clearWard();
        bloc!.add(LoadWardBaseEvent(district.id));
      }
    } else autoFillWard();
  }

  void selectDistrict({bool isView = false}) {
    if (isView) return;
    UtilUI.showOptionDialog(context, 'Chọn quận/Huyện', districts, district.id).then((value) {
      if (value != null) setDistrict(value);
    });
  }

  void _clearWard() {
    wards.clear();
    ctrWard.text = '';
    ward.setValue('', '');
  }

  void setWard(ItemModel value) {
    if (ward.id != value.id || byPassWard == true) {
      ctrWard.text = value.name;
      ward.setValue(value.id, value.name);
      callbackPrePage();
    }
    byPassWard = null;
  }

  void selectWard({bool isView = false}) {
    if (isView) return;
    UtilUI.showOptionDialog(context, 'Chọn phường/Xã', wards, ward.id).then((value) {
      if (value != null) setWard(value);
    });
  }

  void openMap({bool isView = false}) {
    if (isView) return;
    UtilUI.goToNextPage(context, NutritionLocPage(current: address), funCallback: (value) {
      if (value != null) setAddress(value);
    });
  }

  void setAddress(LatLng value) {
    if (address == null || address!.latitude != value.latitude || address!.longitude != value.longitude) {
      address = value;
      bloc!.add(GetAddressBaseEvent(value, keyStreet: keyStreet));
    }
  }

  void callbackPrePage() {}
}
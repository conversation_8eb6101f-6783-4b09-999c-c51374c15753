import 'dart:io';
import 'dart:ui' as ui;
import 'package:crop_image/crop_image.dart';
import 'package:flutter/material.dart';
import 'package:hainong/common/base_bloc.dart';
import 'package:hainong/common/ui/loading.dart';
import '../models/file_byte.dart';
import 'crop_image_body.dart';

class CropImagePage extends StatefulWidget {
  final File file;
  final FileByte? orgImage;
  final bool? isAPI;
  const CropImagePage(this.file, {this.orgImage, this.isAPI, Key? key}) : super(key: key);

  @override
  _MyHomePageState createState() => _MyHomePageState();
}

class _MyHomePageState extends State<CropImagePage> {
  final BaseBloc bloc = BaseBloc();
  final imgKey = GlobalKey();
  final controller = CropController();
  CropRotation rotate = CropRotation.up;
  double? width, height, imgW, imgH;
  Size? size;

  @override
  void dispose() {
    bloc.close();
    controller.removeListener(_listener);
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    controller.addListener(_listener);
  }

  @override
  Widget build(BuildContext context) {
    postFrameCallback();
    return Scaffold(
        appBar: AppBar(centerTitle: true,
            title: const Text('Cắt ảnh', style: TextStyle(color: Colors.white)),
            actions: [
              IconButton(onPressed: () async {
                bloc.add(LoadingEvent(true));
                try {
                  final image = await controller.croppedBitmap(quality: FilterQuality.high);
                  if (widget.orgImage != null) {
                    widget.orgImage!.w = image.width.toDouble();
                    widget.orgImage!.h = image.height.toDouble();
                  }
                  final temp = (await image.toByteData(format: ui.ImageByteFormat.png))!.buffer.asUint8List();
                  Navigator.of(context).pop(temp);
                } catch (_) {
                  if (!bloc.isClosed && mounted) bloc.add(LoadingEvent(false));
                }
              }, icon: Icon(Icons.check))
            ]
        ),
        backgroundColor: Colors.black,
        body: Stack(children: [
          CropImageBody(key: imgKey,
            controller: controller,
            image: Image.file(widget.file),
            paddingSize: 0.0,
            alwaysMove: true,
            gridColor: Colors.white,
            gridCornerSize: 50,
            touchSize: 60,
            gridThickWidth: 8,
            isAPI: widget.isAPI,
            orgImage: widget.orgImage,
            onCrop: (Rect rect) {
              if (imgW == null) _setDefaultWidthHeight();

              double w = controller.cropSize.width;
              double h = controller.cropSize.height;
              if (w < 448 || h < 448) {
                if (w < 448) w = 448;
                if (h < 448) h = 448;
                controller.cropSize = Rect.fromLTWH(controller.cropSize.left, controller.cropSize.top, w, h);
              }
            },
          ),
          Loading(bloc)
        ]),
        /*bottomNavigationBar: Container(padding: const EdgeInsets.all(20), color: Colors.white, child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            *//*IconButton(
            icon: const Icon(Icons.close, color: Colors.black),
            onPressed: () {
              controller.rotation = CropRotation.up;
              controller.crop = const Rect.fromLTRB(0.1, 0.1, 0.9, 0.9);
              controller.aspectRatio = 1.0;
            },
          ),*//*
            *//*IconButton(
              icon: const Icon(Icons.aspect_ratio, color: Colors.black),
              onPressed: _aspectRatios,
            ),*//*
            *//*IconButton(
              icon: const Icon(Icons.rotate_90_degrees_ccw_outlined, color: Colors.black),
              onPressed: _rotateLeft,
            ),
            IconButton(
              icon: const Icon(Icons.rotate_90_degrees_cw_outlined, color: Colors.black),
              onPressed: _rotateRight,
            ),*//*
          ],
        ))*/
    );
  }

  void postFrameCallback() {
    if (size != null && imgW != null && size!.width != imgW) return;
    Future.delayed(const Duration(milliseconds: 1000)).whenComplete(() {
      if (imgKey.currentContext!= null) size = imgKey.currentContext!.size;
    });
  }

  void _listener() {
    if (imgW == null || controller.rotation == CropRotation.left || controller.rotation == CropRotation.right) return;

    if (controller.cropSize.right > width! || controller.cropSize.bottom > height!) {
      double l = controller.cropSize.left, w = controller.cropSize.width;
      if (controller.cropSize.right > width!) l -= controller.cropSize.right - width!;

      double t = controller.cropSize.top, h = controller.cropSize.height;
      if (controller.cropSize.bottom > height!) t -= controller.cropSize.bottom - height!;

      controller.cropSize = Rect.fromLTWH(l, t, w, h);
    }
  }

  void _setDefaultWidthHeight({bool swap = false}) async {
    if (imgW == null) {
      ui.Image? image = controller.getImage();
      if (image != null) {
        imgW = image.width.toDouble();
        imgH = image.height.toDouble();
      }
    }

    //print('before: ${controller.cropSize}');

    if (imgW != null) {
      width = swap && size != null ? size!.width : imgW;
      height = swap && size != null ? size!.height : imgH;

      double l = controller.cropSize.left, t = controller.cropSize.top;
      if (l < 0 || t < 0) {
        double w = controller.cropSize.width;
        if (l < 0) {
          w += l;
          l = 0;
        }

        double h = controller.cropSize.height;
        if (t < 0) {
          h += t;
          t = 0;
        }

        controller.cropSize = Rect.fromLTWH(l, t, w, h);
      }
    }

    //print('screen size: ${1.sw} - ${1.sh}');
    //print('img size: $imgW - $imgH');
    //print('cur size: $width - $height');
    //print('after: ${controller.cropSize}');
  }

  void _setWidthHeight() {
    rotate = controller.rotation;
    _setDefaultWidthHeight(swap: rotate == CropRotation.left || rotate == CropRotation.right);
  }

  Future<void> _aspectRatios() async {
    final value = await showDialog<double>(
      context: context,
      builder: (context) {
        return SimpleDialog(
          title: const Text('Chọn tỷ lệ cắt', textAlign: TextAlign.center),
          children: [
            // special case: no aspect ratio
            SimpleDialogOption(
              onPressed: () => Navigator.pop(context, -1.0),
              child: const Text('Tự do', style: TextStyle(fontSize: 18), textAlign: TextAlign.center),
            ),
            const Divider(height: 0.5, color: Color(0xFFCDCDCD), thickness: 0.5),
            SimpleDialogOption(
              onPressed: () => Navigator.pop(context, 1.0),
              child: const Text('Vuông', style: TextStyle(fontSize: 18), textAlign: TextAlign.center),
            ),
            const Divider(height: 0.5, color: Color(0xFFCDCDCD), thickness: 0.5),
            SimpleDialogOption(
              onPressed: () => Navigator.pop(context, 2.0),
              child: const Text('2:1', style: TextStyle(fontSize: 18), textAlign: TextAlign.center),
            ),
            const Divider(height: 0.5, color: Color(0xFFCDCDCD), thickness: 0.5),
            SimpleDialogOption(
              onPressed: () => Navigator.pop(context, 1 / 2),
              child: const Text('1:2', style: TextStyle(fontSize: 18), textAlign: TextAlign.center),
            ),
            const Divider(height: 0.5, color: Color(0xFFCDCDCD), thickness: 0.5),
            SimpleDialogOption(
              onPressed: () => Navigator.pop(context, 4.0 / 3.0),
              child: const Text('4:3', style: TextStyle(fontSize: 18), textAlign: TextAlign.center),
            ),
            const Divider(height: 0.5, color: Color(0xFFCDCDCD), thickness: 0.5),
            SimpleDialogOption(
              onPressed: () => Navigator.pop(context, 16.0 / 9.0),
              child: const Text('16:9', style: TextStyle(fontSize: 18), textAlign: TextAlign.center),
            ),
          ],
        );
      },
    );
    if (value != null) {
      controller.aspectRatio = value == -1 ? null : value;
      controller.crop = const Rect.fromLTRB(0.1, 0.1, 0.9, 0.9);
    }
  }

  Future<void> _rotateLeft() async {
    controller.rotateLeft();
    _setWidthHeight();
  }

  Future<void> _rotateRight() async {
    controller.rotateRight();
    _setWidthHeight();
  }
}
import 'dart:io';
import 'package:hainong/common/import_lib_system.dart';
import 'package:hainong/common/ui/select_pro_dis_ward.dart';
import 'package:hainong/features/login/login_model.dart';
import 'package:hainong/features/profile/profile_bloc.dart';
import 'package:hainong/common/ui/button_custom.dart';
import 'package:hainong/common/ui/button_image_widget.dart';
import 'package:hainong/common/ui/label_custom.dart';
import 'package:hainong/common/ui/select_address.dart';
import 'package:hainong/common/ui/textfield_custom.dart';

import '../api_client.dart';
import '../models/item_list_model.dart';
import 'import_lib_base_ui.dart';
import 'auto_fill_address_state.dart';
import 'base_page.dart';
import 'loading.dart';

class PopupAddress extends BasePage {
  PopupAddress({Key? key, bool? requireGenderProvince}):super(key: key, pageState: _PopupAddressState(requireGenderProvince));
}

class _PopupAddressState extends AutoFillAddressState {
  final bool? requireGenderProvince;
  String? gender;
  late TextEditingController ctrGender;
  late FocusNode fcGender;

  _PopupAddressState(this.requireGenderProvince) {
    if (requireGenderProvince == true) {
      ctrGender = TextEditingController();
      fcGender = FocusNode();
      gender = 'other';
    }
    keyStreet = 'street';
    initBloc(_AddressBloc());
    fcAddress.addListener(_listener);
    SharedPreferences.getInstance().then((prefs) {
      province.id = prefs.getString('province_id') ?? '';
      province.name = prefs.getString('province_name') ?? '';
      district.id = prefs.getString('district_id') ?? '';
      district.name = prefs.getString('district_name') ?? '';
      ward.id = prefs.getString('ward_id') ?? '';
      ward.name = prefs.getString('ward_name') ?? '';
      if (requireGenderProvince == true) {
        gender = prefs.getString('gender') ?? 'other';
        ctrGender.text = MultiLanguage.get(gender!);
      }
      _fillAddress();
    });
  }

  @override
  void dispose() {
    fcAddress.removeListener(_listener);
    if (requireGenderProvince == true) {
      ctrGender.dispose();
      fcGender.dispose();
    }
    super.dispose();
  }

  @override
  loadFiles(List<File> files) {}

  @override
  void listenBloc(state) {
    if (state is UpdateProfileState && isResponseNotError(state.response)) {
      UtilUI.saveInfo(context, state.response.data, null, null);
      UtilUI.showCustomDialog(context, 'Bổ sung thông tin thành công').whenComplete(_close);
    } else if (state is LoadProvinceBaseState) {

    }
  }

  @override
  void initState() {
    super.initState();
    bloc!.add(LoadProvinceBaseEvent());
  }

  @override
  Widget build(BuildContext context, {Color color = Colors.white}) =>
    Stack(children: [
      GestureDetector(onTap: clearFocus, child: Container(color: Colors.black45, alignment: Alignment.center,
          child: SafeArea(child: Container(margin: EdgeInsets.symmetric(horizontal: 40.sp),
              decoration: BoxDecoration(color: color, borderRadius: BorderRadius.circular(10)),
              child: Column(mainAxisSize: MainAxisSize.min, children: [
                Container(width: 1.sw, padding: EdgeInsets.all(40.sp), margin: EdgeInsets.only(bottom: 40.sp),
                    decoration: const BoxDecoration(color: StyleCustom.buttonColor,
                        borderRadius: BorderRadius.vertical(top: Radius.circular(10))),
                    child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                      SizedBox(width: 60.sp),
                      LabelCustom('BỔ SUNG THÔNG TIN', size: 46.sp, weight: FontWeight.w500),
                      ButtonImageWidget(0, _close, Icon(Icons.close, color: Colors.white, size: 60.sp))
                    ])
                ),

                Padding(padding: EdgeInsets.symmetric(horizontal: 40.sp),
                  child: LabelCustom('Vui lòng bổ sung thông tin ${requireGenderProvince == true?'giới tính và':''}'
                    ' địa chỉ hiện tại của bạn', size: 40.sp, weight: FontWeight.w400, align: TextAlign.center, color: StyleCustom.primaryColor)),

                if (requireGenderProvince == true) Padding(padding: EdgeInsets.fromLTRB(40.sp, 40.sp, 40.sp, 0),
                  child: Row(mainAxisAlignment: MainAxisAlignment.start, children: [
                    LabelCustom('Giới tính:  ', size: 42.sp, weight: FontWeight.w300, color: Colors.black87),
                    SizedBox(width: 0.4.sw, child: TextFieldCustom(ctrGender, fcGender, fcAddress, 'Giới tính *',
                      readOnly: true, suffix: const Icon(Icons.arrow_drop_down),
                      onPressIcon: () => UtilUI.showOptionDialog(context, 'Chọn giới tính',
                        Util.getGenderOption(), gender!).then(_setGender)))
                  ])),

                Padding(padding: EdgeInsets.fromLTRB(40.sp, 40.sp, 40.sp, 0),
                    child: TextFieldCustom(ctrAddress, fcAddress, null, 'Số nhà, Tên đường *', maxLine: 0, onChanged: changedAddress,
                        inputAction: TextInputAction.newline, type: TextInputType.multiline, padding: EdgeInsets.all(30.sp))),

                Flexible(child: SingleChildScrollView(padding: EdgeInsets.symmetric(horizontal: 40.sp, vertical: 8),
                    child: SelectAddress(selectAddress, bloc!, keyAddress: 'name'))),

                Padding(padding: EdgeInsets.symmetric(horizontal: 40.sp),
                    child: TextFieldCustom(ctrProvince, fcProvince, null, 'Tỉnh/TP, Quận/Huyện, Phường/Xã', maxLine: 0,
                        inputAction: TextInputAction.newline, type: TextInputType.multiline, padding: EdgeInsets.all(30.sp),
                        readOnly: true, suffix: const Icon(Icons.arrow_drop_down), onPressIcon: _selectProDisWard)),

                Container(width: 1.sw - 160.sp, margin: EdgeInsets.all(40.sp), child: BlocBuilder(bloc: bloc,
                    buildWhen: (_,newS) => newS is LoadProfileState, builder: (_,__) =>
                        ButtonCustom(_saveAddress, 'TIẾP TỤC', color: _validateAddress() ? StyleCustom.buttonColor : const Color(0xFFCDCDCD))))
              ])
          ))
      )),
      Loading(bloc)
    ]);

  @override
  void setDistrict(ItemModel value, {bool loadWard = true, bool clearWard = true}) {
    super.setDistrict(value, loadWard: loadWard, clearWard: clearWard);
    _fillAddress();
  }

  @override
  void setWard(ItemModel value) {
    super.setWard(value);
    _fillAddress();
  }

  @override
  void changedAddress(TextEditingController ctr, String value) {
    if (ctr.text.length < 2) bloc!.add(LoadProfileEvent());
    super.changedAddress(ctr, value);
  }

  bool _validateAddress({bool byPassAddress = false}) {
    if (ctrAddress.text.isEmpty) {
      if (!byPassAddress) return false;
    }
    if (province.id.isEmpty || district.id.isEmpty || ward.id.isEmpty) return false;
    return true;
  }

  void _close() => UtilUI.goBack(context, null);

  void _listener() {
    if (!fcAddress.hasFocus) {
      showPopupAddress = null;
      if (bloc!.startAutoSearch != null) bloc!.startAutoSearch = false;
      return;
    }

    if (!_validateAddress(byPassAddress: true)) {
      UtilUI.showCustomDialog(context, 'Chọn Tỉnh/Thành phố, Quận/Huyện, Phường/Xã').whenComplete(fcAddress.unfocus);
      return;
    }

    if (fcAddress.hasFocus && bloc!.startAutoSearch != true) {
      bloc!.startAutoSearch = true;
      showPopupAddress = true;
      autoSearch();
    }
  }

  void _fillAddress() {
    ctrProvince.text = province.name;
    if (district.name.isNotEmpty) ctrProvince.text += ', ${district.name}';
    if (ward.name.isNotEmpty) ctrProvince.text += ', ${ward.name}';
    bloc!.add(LoadProfileEvent());
  }

  void _selectProDisWard() => UtilUI.goToNextPage(context, SelectProDisWard(province, district, ward, provinces),
      funCallback: (value) {
        if (value != null) {
          setProvince(value['province']!, loadDistrict: false);
          setDistrict(value['district']!, loadWard: false);
          setWard(value['ward']!);
          if (value['location'] != null) address = value['location'];
          if (value['address'].isNotEmpty) ctrAddress.text = value['address'];
          _fillAddress();
        }
      }
  );

  void _setGender(value) {
    if (value != null) {
      gender = value.id;
      ctrGender.text = value.name;
    }
  }

  void _saveAddress() {
    if (ctrAddress.text.isEmpty) {
      UtilUI.showCustomDialog(context, 'Nhập Số nhà, Tên đường');
      return;
    }
    if (province.id.isEmpty || district.id.isEmpty || ward.id.isEmpty) {
      UtilUI.showCustomDialog(context, 'Chọn Tỉnh/Thành phố, Quận/Huyện, Phường/Xã');
      return;
    }
    bloc!.add(UpdateProfileEvent('', '', '', '', gender??'', '', '', ctrAddress.text, province.id, district.id, ward.id, [], [], [], ''));
  }
}

class _AddressBloc extends BaseBloc {
  _AddressBloc() : super(hasAddressAPI: true, hasProDis: true, hasGetAddressMap: true, hasHandleAddress: true) {
    on<UpdateProfileEvent>((event, emit) async {
      emit(const BaseState(isShowLoading: true));
      final response = await ApiClient().postAPI('${Constants().apiVersion}account/update_user', 'POST', LoginModel(),
        body: {
          'address': event.address,
          'province_id': event.province,
          'district_id': event.district,
          'ward_id': event.ward,
          if (event.gender.isNotEmpty) 'gender': event.gender
        });
      emit(UpdateProfileState(response));
    });
    on<LoadProfileEvent>((event, emit) => emit(LoadProfileState(LoginModel())));
  }
}
import 'button_image_circle_widget.dart';
import 'import_lib_base_ui.dart';

class StarRate extends StatelessWidget {
  final Function(int)? onClick;
  final MainAxisAlignment? mainAxisAlignment;
  final bool hasFunction;
  final dynamic rate;
  final double? size;
  final Color color;
  final Color? unSelectColor;
  const StarRate({this.onClick, this.color = Colors.yellow, this.size, this.hasFunction = false, this.mainAxisAlignment,
    this.rate = 0, this.unSelectColor, Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final temp = size??40.sp;
    return Row(mainAxisAlignment: mainAxisAlignment ?? MainAxisAlignment.center, children: [
      _createButtonStar(1, color, hasFunction, rate > 0, temp, onClick),
      _createButtonStar(2, color, hasFunction, rate > 1, temp, onClick),
      _createButtonStar(3, color, hasFunction, rate > 2, temp, onClick),
      _createButtonStar(4, color, hasFunction, rate > 3, temp, onClick),
      _createButtonStar(5, color, hasFunction, rate > 4, temp, onClick)
    ]);
  }

  Widget _createButtonStar(index, Color color, bool hasFunction, bool isOn, double size, Function(int)? onClick) => hasFunction
    ? ButtonImageCircleWidget(size, () => onClick!(index),
      child: Icon(Icons.star, color: isOn ? color : Colors.grey.shade300, size: size))
    : Icon(Icons.star, color: isOn ? color : Colors.grey.shade300, size: size);
}
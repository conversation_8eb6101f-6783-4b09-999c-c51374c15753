import 'package:hainong/common/style_custom.dart';
import 'package:hainong/common/ui/loading.dart';
import 'package:hainong/common/util/util_ui.dart';
import 'package:hainong/features/main/bloc/main_bloc.dart';
import '../box_dec_custom.dart';
import 'chatbot_bloc.dart';
import '../button_image_widget.dart';
import '../label_custom.dart';
import '../import_lib_base_ui.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_markdown/flutter_markdown.dart';

class ChatBot extends StatefulWidget {
  final Function? chatBotLink;
  const ChatBot(this.chatBotLink, {Key? key}) : super(key: key);
  @override
  _ChatBotState createState() => _ChatBotState();
}

class _ChatBotState extends State<ChatBot> with AutomaticKeepAliveClientMixin implements IBaseBloc {
  final ChatBotBloc _bloc = ChatBotBloc();

  @override
  bool get wantKeepAlive => true;

  @override
  void clearFocus() {}

  @override
  bool isResponseNotError(state, {bool passString = false, bool showError = true, bool hasClearError = true, bool isNewUI = false, bool? isBack}) => false;

  @override
  void dispose() {
    _bloc.close();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _bloc.initState(this, widget.chatBotLink);
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return GestureDetector(onTap: _bloc.fc.unfocus, child: Stack(children: [
      SizedBox(height: 0.7.sh, width: 1.sw - 40.sp, //decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(20)),
          child: Scaffold(backgroundColor: Colors.transparent, body: Column(children: [
            Container(padding: EdgeInsets.fromLTRB(40.sp, 30.sp, 30.sp, 30.sp),
                decoration: const BoxDecoration(color: StyleCustom.primaryColor,
                    borderRadius: BorderRadius.vertical(top: Radius.circular(20))),
                child: Row(children: [
                  Expanded(child: LabelCustom('Trợ lý 2Nông', size: 42.sp, weight: FontWeight.normal)),
                  ButtonImageWidget(20, () => UtilUI.goBack(context, false), Padding(padding: EdgeInsets.all(10.sp),
                      child: Icon(Icons.close, color: Colors.white, size: 48.sp)))
                ])),
            Expanded(child: Container(alignment: Alignment.topCenter,
                color: Colors.white, child:
            BlocBuilder(bloc: _bloc, buildWhen: (oldS, newS) => newS is BotAnswerState,
                builder: (context, state) => ListView.separated(shrinkWrap: true,
                    padding: EdgeInsets.all(20.sp), reverse: true,
                    itemCount: _bloc.list.length, controller: _bloc.scroll,
                    separatorBuilder: (context, index) => SizedBox(height: 40.sp),
                    itemBuilder: (context, index) => _ItemChatBot(_bloc.list.values.elementAt(index),
                        _bloc.list.keys.elementAt(index), _bloc))))),
            Container(constraints: BoxConstraints(maxHeight: 200.sp),
                margin: EdgeInsets.only(bottom: 20.sp),
                padding: EdgeInsets.all(20.sp), color: Colors.white,
                child: Container(
                    decoration: BoxDecoration(color: const Color(0xFFF4F7F9), borderRadius: BorderRadius.circular(5)),
                    child: Row(children: [
                      Expanded(child: TextField(controller: _bloc.ctr, focusNode: _bloc.fc, maxLines: null,
                          keyboardType: TextInputType.multiline, textInputAction: TextInputAction.newline,
                          decoration: InputDecoration(fillColor: Colors.transparent, hintText: 'Nội dung', border: InputBorder.none,
                              filled: true, contentPadding: EdgeInsets.symmetric(horizontal: 20.sp)), onChanged: _bloc.changedText
                      )),
                      ButtonImageWidget(30, _bloc.sendQuestion, Padding(padding: EdgeInsets.all(10.sp),
                          child: BlocBuilder(bloc: _bloc, buildWhen: (oldS, newS) => newS is SendButtonState,
                              builder: (context, state) {
                                final show = state is SendButtonState && state.status;
                                return show ? Icon(Icons.send, color: StyleCustom.primaryColor, size: 60.sp) : const SizedBox();
                              }))),
                      ButtonImageWidget(30, _bloc.startSpeed, Padding(padding: EdgeInsets.all(10.sp),
                          child: BlocBuilder(bloc: _bloc, buildWhen: (oldS, newS) => newS is SpeedState,
                              builder: (context, state) {
                                Color color = StyleCustom.primaryColor;
                                String icon = 'mic.png';
                                if (state is SpeedState && state.status) {
                                  color = Colors.red;
                                  icon = 'record.gif';
                                }
                                return Image.asset('assets/images/chatbot/ic_' + icon, color: color, width: 98.sp);
                              })))
                    ])
                )
            )
          ], mainAxisAlignment: MainAxisAlignment.start))),
      SizedBox(height: 0.7.sh, child: Loading(BlocProvider.of<MainBloc>(context)))
    ], alignment: Alignment.bottomCenter));
  }
}

class _ItemChatBot extends StatelessWidget {
  final Map item;
  final int id;
  final ChatBotBloc bloc;
  const _ItemChatBot(this.item, this.id, this.bloc, {Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    if (item['type'] != 'send_question') {
      if (item['type'] == 'quick_reply') {
        final List<Widget> list = [];
        item['data'].forEach((ele) => list.add(
            ButtonImageWidget(10, () => bloc.askQuestion(ele['title'], ele['payload']),
                Container(padding: EdgeInsets.all(30.sp), decoration: BoxDecCustom(radius: 10, bgColor: Colors.green.withOpacity(0.9)),
                    child: LabelCustom(ele['title'], color: Colors.white, weight: FontWeight.normal))
            )
        ));
        return Wrap(children: list, spacing: 20.sp, runSpacing: 20.sp);
      }

      final isAnswer = item['type'] == 'answer';
      return Row(children: [
        Flexible(child: Container(padding: EdgeInsets.all(40.sp), constraints: BoxConstraints(maxWidth: 0.8.sw),
            decoration: BoxDecoration(color: Color(isAnswer ? 0xFFF2F6F8 : 0xFF0E986F),
                borderRadius: isAnswer ? const BorderRadius.only(topRight: Radius.circular(15),
                    bottomLeft: Radius.circular(15), bottomRight: Radius.circular(15)) : BorderRadius.circular(15)),
            child: isAnswer ? MarkdownBody(selectable: true, data: item['data'], onTapLink: bloc.onTapLink) :
              LabelCustom(item['data'], color: Colors.white, weight: FontWeight.normal, align: TextAlign.left)
        )),

        if (isAnswer) ButtonImageWidget(50, () => bloc.getAudio(id),
          BlocBuilder(bloc: bloc, buildWhen: (oldS, newS) => (newS is GetAudioState && newS.id == id) ||
              (newS is ReadTextState && newS.id == id) || bloc.isPlaying == id || bloc.isLoading == id,
            builder: (context, state) {
              String asset = 'audio_off.png';
              if ((state is ReadTextState && state.id == id && state.status) || bloc.isPlaying == id) asset = 'audio_on.gif';
              else if ((state is GetAudioState && state.id == id && state.status) || bloc.isLoading == id) asset = 'loading_audio.gif';
              return Image.asset('assets/images/chatbot/ic_' + asset, width: 120.sp, height: 120.sp, fit: BoxFit.scaleDown);
            }))
      ], mainAxisAlignment: isAnswer ? MainAxisAlignment.start : MainAxisAlignment.end);
    }

    if (id == 0) {
      return Align(alignment: Alignment.centerLeft,
        child: Image.asset('assets/images/chatbot/ic_loading.gif', height: 20.sp,
          color: const Color(0xFFCDCDCD), fit: BoxFit.scaleDown));
    }
    return const SizedBox();
  }
}
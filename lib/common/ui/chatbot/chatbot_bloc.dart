export 'package:hainong/common/base_bloc.dart';
import 'package:hainong/common/api_client.dart';
import 'package:hainong/common/base_bloc.dart';
import 'package:hainong/common/constants.dart';
import 'package:hainong/common/database_helper.dart';
import 'package:hainong/common/util/util.dart';
import 'package:hainong/common/util/util_ui.dart';
import 'package:hainong/features/shop/ui/import_ui_shop.dart';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:just_audio/just_audio.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:socket_io_client/socket_io_client.dart';
//import 'package:speech_to_text/speech_to_text.dart';
import 'package:flutter_speech/flutter_speech.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:firebase_dynamic_links/firebase_dynamic_links.dart';
import 'package:audio_session/audio_session.dart';

class ChatBotBloc extends BaseBloc {
  final ScrollController scroll = ScrollController();
  final TextEditingController ctr = TextEditingController();
  final FocusNode fc = FocusNode();
  final Map<int, Map> list = {};
  final AudioPlayer _player = AudioPlayer();
  final List<dynamic> _responseQueue = [];
  late IBaseBloc callback;
  late Socket socket;
  int isPlaying = -1, isLoading = -1;
  String session = '';
  List<int>? _queueReads;
  //SpeechToText? speechToText;
  SpeechRecognition? speechToText;
  Function? _chatBotLink;
  bool? _startConvert = true, _lock, _startSpeech, speechActive;
  //List<String>? _array;

  @override
  Future<void> close() async {
    scroll.dispose();
    ctr.dispose();
    fc.removeListener(_listenText);
    fc.dispose();
    list.clear();
    _responseQueue.clear();
    _clearQueue();
    try {
      socket.clearListeners();
      socket.ondisconnect();
      if (speechToText != null) {
        await speechToText!.stop();
        speechToText == null;
      }
    } catch (_) {}
    try {
      if (isPlaying > -1) await _player.stop();
      await _player.dispose();
    } catch (_) {}
    _setupAudioSession();
    super.close();
  }

  ChatBotBloc() {
    _initServer();
    _getChatList();
    on<SendButtonEvent>((event, emit) => emit(SendButtonState(event.status)));
    on<QuestionBotEvent>((event, emit) => emit(BotAnswerState(event.status)));
    on<SpeedEvent>((event, emit) {
      if (event.status) _stopReadText();
      emit(SpeedState(event.status));
    });
    on<ReadTextEvent>((event, emit) {
      if (isPlaying > -1) emit(ReadTextState(isPlaying, false));
      isPlaying = event.id;
      if (isPlaying > -1) emit(ReadTextState(isPlaying, event.status));
    });
    on<GetAudioEvent>((event, emit) async {
      if ((list[event.id]!['link']??'').isNotEmpty) {
        _readText(event.id, list[event.id]!['link']);
        return;
      }

      final item = list[event.id]!;
      final tempList = list.values.toList();
      for (int i = tempList.length - 1; i > -1; i--) {
        if ((tempList[i]['link']??'').isNotEmpty && tempList[i]['data_formatted'] == item['data_formatted']) {
          _readText(event.id, tempList[i]['link']);
          return;
        }
      }

      emit(GetAudioState(isLoading, true));
      final link = await _getAudioLinkFromText(event.id, list[event.id]!['data_formatted']);
      if (link != null) {
        _readText(event.id, link);
        return;
      }

      final temp = isLoading;
      isLoading = -1;
      emit(GetAudioState(temp, false));
    });
    _funCheckPermission(Permission.microphone).then((microphone) {
      if (microphone == PermissionStatus.granted) _initSpeed();
    });
  }

  Future<PermissionStatus> _funCheckPermission(Permission permission) async {
    PermissionStatus status = await permission.status;
    if (status == PermissionStatus.denied || status == PermissionStatus.limited) status = await permission.request();
    if (status != PermissionStatus.granted) {
      UtilUI.showCustomDialog(callback.context, 'Quyền truy xuất giọng nói đã bị từ chối.'
          '\nHãy vào phần cài đặt của hệ thống trên thiết bị để cấp quyền lại', alignMessageText: TextAlign.left);
    }
    return status;
  }

  Future<bool> _isStartedConversation() async {
    final db = DBHelper();
    final rows = await db.getAllJsonWithCond('chat', limit: 1);
    final rs = rows != null && rows.isNotEmpty;
    if (rs) {
      list.remove(0);
      add(QuestionBotEvent());
    }
    return rs;
  }

  bool _isSendQuestion() => list.values.first['type'] == 'send_question';

  String _formatData(dynamic data) {
    String temp = data.toString();
    RegExp exp = RegExp(r'\(\S+\)');
    Iterable<RegExpMatch> matches = exp.allMatches(temp);
    for(int i = matches.length - 1; i > -1; i--) {
      final match = matches.elementAt(i);
      temp = temp.replaceRange(match.start, match.end, '**');
    }
    temp = temp.replaceAll('**', '').replaceAll(RegExp(r'[\[\]_]'), '');
    return temp;
  }

  void onTapLink(String text, String? href, String title) {
    if (href != null) {
      if (href.contains('cho2nong.page.link')) {
        if (_lock != null) return;
        _lock = true;
        FirebaseDynamicLinks.instance.getDynamicLink(Uri.parse(href)).then((value) {
          if (value != null && _chatBotLink != null) {
            _stopSpeed();
            _stopReadText();
            _chatBotLink!(value, clearAll: false);
          }
          _lock = false;
        })
        .catchError((_) {_lock = false;})
        .onError((_,__) {_lock = false;})
        .whenComplete(() {
          if (_lock != null && !_lock!) Timer(const Duration(seconds: 2), () => _lock = null);
        });
      } else {
        _stopSpeed();
        _stopReadText();
        launchUrl(Uri.parse(href), mode: LaunchMode.externalApplication);
      }
    }
  }

  Future<void> _getChatList() async {
    final now = DateTime.now();
    final db = DBHelper();
    final data = await db.getJsonById('id', 'open_date', 'setting');
    if (data != null && data.isNotEmpty) {
      final temp = Util.stringToDateTime(data['value']??'0001-01-01T01:01:01');
      if (now.difference(temp).inMinutes > 14) {
        await db.insertHelper(tblName: 'setting', values: {'id': 'open_date', 'value': Util.dateToString(now, pattern: 'yyyy-MM-ddTHH:mm:ss')});
        db.clearTable('chat');
        db.clearTable('link_audio');
        _connectServer();
        return;
      }
    } else await db.insertHelper(tblName: 'setting', values: {'id': 'open_date', 'value': Util.dateToString(now, pattern: 'yyyy-MM-ddTHH:mm:ss')});
    _connectServer();

    db.getAllJsonWithCond('chat').then((chats) {
      if (chats != null && chats.isNotEmpty) {
        for (int i = chats.length - 1; i > -1; i--) {
          dynamic data = chats[i]['type'] != 'quick_reply' ? chats[i]['data'] : jsonDecode(chats[i]['data']);
          list.putIfAbsent(chats[i]['id'], () => {
            'type': chats[i]['type'],
            'data': data,
            if (Util.checkKeyFromJson(chats[i], 'data_formatted')) 'data_formatted': chats[i]['data_formatted'],
            if (Util.checkKeyFromJson(chats[i], 'link')) 'link': chats[i]['link']
          });
        }
      }
    });
  }

  void initState(IBaseBloc callback, Function? chatBotLink) {
    list.putIfAbsent(0, () => {'type': 'send_question'});
    this.callback = callback;
    _chatBotLink = chatBotLink;
    add(QuestionBotEvent(status: true));
    _initPlayer();
    fc.addListener(_listenText);
    //if (Platform.isIOS) Timer(const Duration(milliseconds: 1500), () => _initSpeed());
  }

  void _listenText() {
    if (fc.hasFocus) {
      if (_isSendQuestion()) {
        fc.unfocus();
        return;
      }
      _stopSpeed();
    }
  }

  void changedText(String value) => add(SendButtonEvent(value.trim().isNotEmpty));

  void _initServer() async {
    socket = io('https://core.chatbot.hainong.vn:0', OptionBuilder().setTransports(['websocket']).disableAutoConnect().build());
    socket.connect();
    socket.onConnectError((data) {
      UtilUI.showCustomDialog(callback.context, 'Không kết nối được đến máy chủ.\nVui lòng thử lại lần sau')
          .whenComplete(() => UtilUI.goBack(callback.context, false));
    });
    socket.onAny((event, data) async {
      switch(event) {
        case 'session_confirm':
          if (_startConvert == null) return;
          _startConvert = null;
          Constants().isLogin ? _setAuth() : _startConversation(checkStarted: true);
          break;
        case 'bot_uttered':
          _responseQueue.add(data);
          if (_responseQueue.length == 1) _processResponseQueue();
      }
    });
  }

  void _setupAudioSession() async {
    final session = await AudioSession.instance;
    await session.configure(const AudioSessionConfiguration.music());
  }

  void _initPlayer() {
    _player.playerStateStream.listen((playerState) {
      if (playerState.processingState == ProcessingState.completed) {
        add(ReadTextEvent(-1));
        if (_queueReads != null && _queueReads!.isNotEmpty) _readTextFromQueue();
      }
    });
  }

  void _initSpeed() async {
      /*speechToText = SpeechToText();
      speechToText!.initialize().then((value) async {
        if (!value || speechToText!.hasError || !(await speechToText!.hasPermission)) _initSpeedError();
        else if (state is SpeedState && (state as SpeedState).status) startSpeed();
      }).onError((e, stack) {
        _initSpeedError();
      }).catchError((e) {
        _initSpeedError();
      });*/

    if (Platform.isAndroid) {
      _initSpeedAndroid();
      return;
    }

    speechToText ??= SpeechRecognition();
    speechToText!.setAvailabilityHandler(_setSpeech);
    speechToText!.activate('vi_VN').then((active) {
      if (active is bool) _setSpeech(active);
    });
  }

  void _initSpeedAndroid() async {
    if (speechToText == null) {
      speechToText = SpeechRecognition();
      speechToText!.activate('vi_VN').then((active) {
        if (active) {
          speechToText!.listen().then((_) => speechToText!.stop()).whenComplete(() {
            speechToText!.setAvailabilityHandler((value) {
              if (value && speechActive == null) {
                speechActive = true;
                speechToText!.setRecognitionStartedHandler(() {
                  _startSpeech = true;
                });
                speechToText!.setRecognitionResultHandler((result) {
                  if (_startSpeech == true) ctr.text = result.trimRight();
                });
                speechToText!.setRecognitionCompleteHandler((result) {
                  if (_startSpeech == null) return;
                  _startSpeech = null;
                  ctr.text = result.trimRight();
                  sendQuestion();
                  add(SpeedEvent());
                });
                speechToText!.setErrorHandler(_initSpeedError);
              }
            });
          });
        }
      });
    }
    speechToText ??= SpeechRecognition();
    speechToText!.setAvailabilityHandler(_setSpeech);
    speechToText!.activate('vi_VN').then((active) {
      if (active is bool) _setSpeech(active);
    });
  }

  void _setSpeech(bool value) {
    if (value && speechActive == null) {
      speechActive = true;
      speechToText!.setRecognitionStartedHandler(() {
        _startSpeech = true;
      });
      speechToText!.setRecognitionResultHandler((result) {
        if (_startSpeech == true) ctr.text = result.trimRight();
      });
      speechToText!.setRecognitionCompleteHandler((result) {
        if (_startSpeech == null) return;
        _startSpeech = null;
        ctr.text = result.trimRight();
        sendQuestion();
        add(SpeedEvent());
      });
      speechToText!.setErrorHandler(_initSpeedError);
    }
  }

  void _initSpeedError({bool showMsg = true}) async {
    add(SpeedEvent());
    Platform.isAndroid ? await speechToText?.cancel() : await speechToText?.stop();
    if (Platform.isAndroid && speechToText != null) speechToText == null;
    _startSpeech = null;
    //if (showMsg) UtilUI.showCustomDialog(callback.context, 'Không khởi tạo được giọng nói. Vui lòng thử lại');
  }

  void startSpeed() async {
    if (speechActive == null) {
      UtilUI.showCustomDialog(callback.context, 'Chưa khởi tạo giọng nói xong. Vui lòng thử lại sau!');
      return;
    }
    if (Platform.isAndroid && speechToText == null) {
      _initSpeed();
      return;
    }
    //if (speechToText!.isListening) {
    if (_startSpeech == true) {
      _stopSpeed(force: true);
      return;
    }
    if (state is BotAnswerState && (state as BotAnswerState).status) return;

    fc.unfocus();
    add(SpeedEvent(status: true));

    /*speechToText!.listen(onResult: (result) async {
      if (!speechToText!.isListening) {
        await _stopSpeed();
        _array = result.recognizedWords.split(' ');
        _fillRecordToText();
      }
    }, localeId: 'vi_VN', listenFor: const Duration(seconds: 10));*/

    _startSpeech = true;
    speechToText!.activate('vi_VN').then((value) {
      if (!value) return;
      speechToText!.listen().catchError((e) {
        _initSpeedError();
      }).onError((e, stack) {
        _initSpeedError();
      });
    });

    _clearQueue();
  }

  dynamic _stopSpeed({bool? force}) => speechToText?.stop().catchError((e) {
      _initSpeedError(showMsg: false);
    }).onError((e, stack) {
      _initSpeedError(showMsg: false);
    }).whenComplete(() {
      add(SpeedEvent());
      if (force != true) _startSpeech = null;
      _setupAudioSession();
    });

  /*void _fillRecordToText() {
    if (_array == null) return;
    if (_array!.isNotEmpty) {
      ctr.text += _array![0] + ' ';
      _array!.removeAt(0);
      Timer(const Duration(milliseconds: 200), _fillRecordToText);
      return;
    }
    _array = null;
    ctr.text = ctr.text.trimRight();
    sendQuestion();
  }*/

  void _connectServer() async {
    final now = DateTime.now();
    String strPreTime = '0001-01-01T01:01:01';
    DateTime preTime;
    final db = DBHelper();
    final data = await db.getJsonById('id', 'open_date', 'setting');
    if (data != null && data.isNotEmpty) {
      preTime = Util.stringToDateTime(data['value']??strPreTime);
    } else preTime = now;
    strPreTime = Util.dateToString(preTime, pattern: '_yyyy-MM-ddTHH:mm_');

    final pref = await SharedPreferences.getInstance();
    session = (pref.getString('token_user')??'') + strPreTime + await Util.getDeviceId();
    if (socket.disconnected || !socket.active) {
      socket.onConnect((data) async => socket.emit("session_request", {"session_id": session}));
    } else {
      socket.emit("session_request", {"session_id": session});
    }
  }

  void _setAuth() async {
    final pref = await SharedPreferences.getInstance();
    final body = [{
        "event": "slot",
        "name": "user_token",
        "value": pref.getString('token2_user')??''
      },
      {
        "event": "slot",
        "name": "user_name",
        "value": pref.getString('name')??''
      }];
    final resp = await ApiClient().postJsonAPI('https://core.chatbot.hainong.vn/conversations/$session/tracker/events',
        null, body, hasDomain: true, hasHeader: false, dataIsJson: true);
    if (resp.data != null && resp.data.isNotEmpty && Util.checkKeyFromJson(resp.data, 'sender_id')
        && !(await _isStartedConversation())) _startConversation();
  }

  void _startConversation({bool checkStarted = false}) async {
    if (checkStarted && await _isStartedConversation()) return;
    socket.emit("user_uttered", {
      "message": '/start_conversation',
      "customData": {"language": "vi"},
      "session_id": session
    });
  }

  Future<void> _processResponseQueue() async {
    await _chatBotResponse(_responseQueue[0]);
    _responseQueue.removeAt(0);
    if (_responseQueue.isNotEmpty) _processResponseQueue();
  }

  Future<void> _chatBotResponse(dynamic data) async {
    int id = DateTime.now().millisecondsSinceEpoch;
    if (_isSendQuestion()) {
      list.remove(0);
      //if (ctr.text.isNotEmpty) await _insertData(id, 'question', ctr.text);
      _clearQueue();
      add(SendButtonEvent(false));
      //ctr.text = '';
    }

    if (!data.containsKey('text')) return;

    id++;
    final String formatted = _formatData(data['text']);
    _getAudioLinkFromText(id, formatted);
    await _insertData(id, 'answer', data['text'], dataFormat: formatted);
    _startQueueRead(id, formatted);

    if (data.containsKey('quick_replies')) await _insertData(id + 1, 'quick_reply', data['quick_replies']);

    add(QuestionBotEvent());
  }

  void _addList(int id, Map values) {
    final Map<int, Map> temp = {id: values};
    temp.addAll(list);
    list.clear();
    list.addAll(temp);
    temp.clear();
  }

  Future<int> _insertData(int id, String type, dynamic data, {String? dataFormat}) async {
    _addList(id, {
      'type': type,
      'data': data,
      if (dataFormat != null) 'data_formatted': dataFormat
    });
    return DBHelper().insertHelper(tblName: 'chat', values: {
      'id': id,
      'type': type,
      'data': type != 'quick_reply' ? data : jsonEncode(data),
      if (dataFormat != null) 'data_formatted': dataFormat
    });
  }

  Future<int> _updateData(int id, String link) async {
    if (list.containsKey(id) && list[id] != null) {
      final item = list[id]!;
      item.putIfAbsent('link', () => link);
      list.update(id, (v) => item);
    }

    return DBHelper().updateHelper('id', id, tblName: 'chat', values: {'link': link});
  }

  void sendQuestion({String? code}) async {
    fc.unfocus();
    if ((state is BotAnswerState && (state as BotAnswerState).status) ||
        (state is GetAudioState && (state as GetAudioState).status)) return;

    if (socket.active && socket.connected) {
      if (ctr.text.isNotEmpty) {
        await _insertData(DateTime.now().millisecondsSinceEpoch, 'question', ctr.text);
        add(SendButtonEvent(false));
      }

      if (!_isSendQuestion()) _addList(0, {'type': 'send_question'});
      add(QuestionBotEvent(status: true));
      socket.emit('user_uttered', {"message": code??ctr.text,"customData":{"language":"vi"}, 'session_id': session});
      ctr.text = '';
    }
    scroll.jumpTo(0);
  }

  void askQuestion(String question, String code) {
    if (question.isEmpty) return;
    ctr.text = question;
    add(SendButtonEvent(true));
    sendQuestion(code: code);
  }

  void getAudio(int index, {bool clearQueue = true}) {
    if (clearQueue) _clearQueue();
    if (index != isPlaying) {
      isLoading = index;
      add(GetAudioEvent(index));
    } else if (isPlaying > -1) _stopReadText();
  }

  void _readText(int index, String link) {
    if (_queueReads != null && _queueReads!.isNotEmpty) _queueReads!.removeAt(0);
    isLoading = -1;
    _stopSpeed();
    _player.setUrl(link).whenComplete(() => _player.play());
    add(ReadTextEvent(index, status: true));
  }

  void _stopReadText() {
    _player.stop();
    add(ReadTextEvent(-1));
  }

  Future<String?> _getAudioLinkFromText(int id, String data) async {
    final resp = await ApiClient().postAPI2(Constants().apiVersion + 'text_to_speeds', body: {'content': data});
    if (resp.isNotEmpty) {
      try {
        final json = jsonDecode(resp);
        final link = json['data']??'';
        if (link.isNotEmpty) {
          _updateData(id, link);
          return link;
        }
      } catch (_) {}
    }
    return null;
  }

  void _startQueueRead(int id, String dataFormatted) async {
    if (_queueReads == null) {
      _queueReads = [];
      _queueReads!.add(id);
      _readTextFromQueue();
    } else {
      _queueReads!.add(id);
      final item = list[id]!;
      if ((item['link']??'').isNotEmpty) return;

      final tempList = list.values.toList();
      for (int i = tempList.length - 1; i > -1; i--) {
        if ((tempList[i]['link']??'').isNotEmpty && tempList[i]['data_formatted'] == item['data_formatted']) {
          _updateData(id, tempList[i]['link']);
          return;
        }
      }

      _getAudioLinkFromText(id, dataFormatted);
    }
  }

  Future<void> _readTextFromQueue() async {
    if (list.containsKey(_queueReads![0])) {
      getAudio(_queueReads![0], clearQueue: false);
      return;
    }
    _queueReads = null;
  }

  void _clearQueue() {
    _queueReads?.clear();
    _queueReads = null;
  }
}

class QuestionBotEvent extends BaseEvent {
  final bool status;
  QuestionBotEvent({this.status = false});
}
class BotAnswerState extends BaseState {
  final bool status;
  BotAnswerState(this.status);
}

class SpeedEvent extends BaseEvent {
  final bool status;
  SpeedEvent({this.status = false});
}
class SpeedState extends BaseState {
  final bool status;
  SpeedState(this.status);
}

class ReadTextEvent extends BaseEvent {
  final bool status;
  final int id;
  ReadTextEvent(this.id, {this.status = false});
}
class ReadTextState extends BaseState {
  final bool status;
  final int id;
  ReadTextState(this.id, this.status);
}

class GetAudioEvent extends BaseEvent {
  final int id;
  GetAudioEvent(this.id);
}
class GetAudioState extends BaseState {
  final bool status;
  final int id;
  GetAudioState(this.id, this.status);
}

class SendButtonEvent extends BaseEvent {
  final bool status;
  SendButtonEvent(this.status);
}
class SendButtonState extends BaseState {
  final bool status;
  SendButtonState(this.status);
}
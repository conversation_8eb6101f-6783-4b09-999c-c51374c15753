import 'package:hainong/common/ui/button_image_widget.dart';
import 'package:hainong/common/ui/import_lib_base_ui.dart';
import 'package:hainong/common/ui/label_custom.dart';
import 'package:hainong/common/ui/shadow_decoration.dart';
import 'package:hainong/common/base_bloc.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class SelectAddress extends StatefulWidget {
  final Function funSelect;
  final BaseBloc bloc;
  final String keyAddress;
  const SelectAddress(this.funSelect, this.bloc, {Key? key, this.keyAddress = 'label'}) : super(key: key);
  @override
  _SelectAddressState createState() => _SelectAddressState();
}

class _SelectAddressState extends State<SelectAddress> {
  dynamic addresses;

  @override
  Widget build(BuildContext context) => BlocBuilder(bloc: widget.bloc,
    buildWhen: (oldS, newS) {
        if (newS is GetAddressFromMapState) {
          if (newS.resp.isEmpty) {
            //UtilUI.showCustomDialog(context, 'Không tìm thấy địa chỉ');
            return false;
          }
          addresses = newS.resp;
          return true;
        }
        if (newS is HideAddressState) {
          addresses.clear();
          addresses = null;
        }
        return newS is HideAddressState;
    },
    builder: (context, state) {
      if (addresses == null || state is HideAddressState) return const SizedBox();
      return Container(padding: EdgeInsets.all(40.sp),
        decoration: ShadowDecoration(opacity: 0.3),
        child: Column(crossAxisAlignment: CrossAxisAlignment.end, mainAxisSize: MainAxisSize.min, children: [
          ButtonImageWidget(50, () => widget.bloc.add(HideAddressEvent()),
              Icon(Icons.close, size: 56.sp, color: const Color(0xFFACACAC))),
          ListView.separated(itemCount: addresses.length, padding: EdgeInsets.zero,
              shrinkWrap: true, physics: const NeverScrollableScrollPhysics(),
              separatorBuilder: (context, index) => SizedBox(height: 20.sp),
              itemBuilder: (context, index) =>
                  ButtonImageWidget(0, () => widget.funSelect(addresses[index], keyAddress: widget.keyAddress),
                      LabelCustom(addresses[index]['properties']['label'],
                          color: Colors.black, size: 40.sp, weight: FontWeight.normal)))
        ]));
    });
}
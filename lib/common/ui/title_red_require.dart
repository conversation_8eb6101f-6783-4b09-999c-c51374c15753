import 'package:hainong/common/ui/import_lib_base_ui.dart';

class TitleRedRequire extends StatelessWidget {
  final String title;
  final Color? color;
  final FontWeight? weight;
  final double? size;
  final EdgeInsets? padding;
  const TitleRedRequire(this.title, {this.size, this.weight, this.color, this.padding, Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    final temp = RichText(text: TextSpan(text: title,
        style: TextStyle(color: color??Colors.black, fontSize: size??52.sp, fontWeight: weight??FontWeight.w600),
        children: <TextSpan>[
          TextSpan(style: TextStyle(color: Colors.red, fontSize: size??52.sp), text: ' *')
        ]
    ));
    return padding != null ? Padding(padding: padding!, child: temp) : temp;
  }
}
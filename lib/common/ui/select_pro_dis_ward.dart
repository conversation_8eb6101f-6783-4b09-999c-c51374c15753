import 'dart:async';
import 'dart:io';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:geolocator/geolocator.dart';
import 'package:hainong/common/ui/box_dec_custom.dart';
import 'package:hainong/common/ui/button_image_widget.dart';
import 'package:hainong/common/ui/label_custom.dart';
import 'package:hainong/common/ui/textfield_custom.dart';
import 'package:trackasia_gl/trackasia_gl.dart';

import '../base_bloc.dart';
import '../models/item_list_model.dart';
import '../util/util_ui.dart';
import 'auto_fill_address_state.dart';
import 'base_page.dart';
import 'import_lib_base_ui.dart';
import 'loading.dart';
import 'select_address.dart';

class SelectProDisWard extends BasePage {
  final String keyAddress;
  final ItemModel province, district, ward;
  SelectProDisWard(this.province, this.district, this.ward, List<ItemModel> provinces, {Key? key, this.keyAddress = 'label'}) : super(key: key, pageState: _SelectProDisWardState(provinces));
}

class _SelectProDisWardState extends AutoFillAddressState {
  int curIndex = -1;

  _SelectProDisWardState(provinces) {
    isFirst = true;
    keyStreet = 'pro_dis_ward';
    this.provinces.addAll(provinces);
  }

  @override
  void dispose() {
    fcAddress.removeListener(_listener);
    super.dispose();
  }

  @override
  loadFiles(List<File> files) {}

  @override
  void listenBloc(state) {
    if (state is LoadDistrictBaseState) {
      bloc!.add(SelectLevelEvent('district'));
    } else if (state is LoadWardBaseState) {
      bloc!.add(SelectLevelEvent('ward'));
    } else if (state is SelectLevelState) {
      showProDisWard = state.level;
      curIndex = -1;
      _fillAddress();
    }
  }

  @override
  void initState() {
    initBloc(BaseBloc(hasAddressAPI: true, hasProDis: true, hasGetAddressMap: true, hasHandleAddress: true));
    _initData();
    super.initState();
    fcAddress.addListener(_listener);
  }

  @override
  Widget build(BuildContext context, {Color color = Colors.white}) => GestureDetector(onTap: clearFocus,
    child: Scaffold(
      appBar: AppBar(
        shadowColor: Colors.black26, elevation: 10,
        backgroundColor: color, centerTitle: true,
        leadingWidth: 86.sp,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.red),
          onPressed: () => UtilUI.goBack(context, null)
        ),
        title: TextFieldCustom(ctrAddress, fcAddress, null, 'Tìm kiếm Quận/Huyện, Phường/Xã',
            inputAction: TextInputAction.search, onSubmit: searchAddress,
            color: const Color(0xFFF5F5F5), padding: EdgeInsets.all(30.sp),
            prefix: GestureDetector(onTap: searchAddress,
                child: Icon(Icons.search, color: const Color(0xFF7B7B7B), size: 64.sp)),
          ),

        bottom: PreferredSize(preferredSize: Size(1.sw, 40.sp), child: const SizedBox())
      ),
      body: SafeArea(child: Stack(children: [
        Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Container(color: const Color(0xFFF5F5F5), padding: EdgeInsets.fromLTRB(40.sp, 40.sp, 40.sp, 0),
            child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
              LabelCustom('Khu vực được chọn', size: 42.sp, color: Colors.black45, weight: FontWeight.w300),
              GestureDetector(onTap: _reset,
                child: LabelCustom('Thiết lập lại', size: 42.sp, color: Colors.red, weight: FontWeight.w400))
            ])),

          Container(padding: EdgeInsets.all(40.sp), color: const Color(0xFFF5F5F5), child: ButtonImageWidget(5, _getMyLocation,
            Container(padding: EdgeInsets.all(40.sp),
              decoration: BoxDecCustom(radius: 5, bgColor: Colors.white),
              child: Row(mainAxisAlignment: MainAxisAlignment.center, children: [
                Icon(Icons.location_on, color: Colors.red, size: 60.sp),
                LabelCustom(' Sử dụng vị trí hiện tại của tôi', size: 40.sp, weight: FontWeight.w500, color: const Color(0xFF212121)),
              ])), color: Colors.white, elevation: 5)),

          _SelectItem(_selectLevel, province, 'province', bloc, hideTop: true),
          _SelectItem(_selectLevel, district, 'district', bloc),
          _SelectItem(_selectLevel, ward, 'ward', bloc, hideBottom: true),

          Container(width: 1.sw, color: const Color(0xFFF5F5F5), padding: EdgeInsets.all(40.sp),
            child: BlocBuilder(bloc: bloc, buildWhen: (_,newS) => newS is LoadProvinceBaseState ||
              newS is LoadDistrictBaseState || newS is LoadWardBaseState || newS is SelectLevelState,
              builder: (_,__) {
                return LabelCustom(_getCurrentSelect(), size: 40.sp, weight: FontWeight.w300, color: Colors.black87);
              })),

          Expanded(child: RefreshIndicator(onRefresh: _refreshList,
            child: BlocBuilder(bloc: bloc, buildWhen: (_,newS) => newS is LoadProvinceBaseState ||
                newS is LoadDistrictBaseState || newS is LoadWardBaseState || newS is SelectLevelState,
              builder: (_,state) {
                final list = _getCurrentList();
                if (list.isEmpty) return SingleChildScrollView(child: SizedBox(height: 1.sh, width: 1.sw));
                return ListView.builder(padding: EdgeInsets.only(left: 40.sp),
                  itemCount: list.length, key: Key(showProDisWard??'province'),
                  itemBuilder: (_,index) {
                    return GestureDetector(onTap: () => _selectItem(index),
                      child: Row(children: [
                        SizedBox(width: 60.sp,
                          child: LabelCustom(_getFirstLetter(list, index), size: 46.sp, weight: FontWeight.w300, color: const Color(0xFFBABABA))),
                        Expanded(child: Container(
                          padding: EdgeInsets.symmetric(vertical: 40.sp),
                          decoration: const BoxDecoration(
                            border: Border(bottom: BorderSide(color: Colors.grey, width: 0.5))
                          ),
                          child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                            BlocBuilder(bloc: bloc, buildWhen: (_,newSelected) => newSelected is HideAddressState,
                              builder: (_,__) => LabelCustom(list[index].name, size: 46.sp, weight: FontWeight.w400,
                                  color: list[index].selected ? Colors.red : const Color(0xFF212121))),
                            _getSelected(list[index], index)
                          ])
                        ))
                      ]));
                  });
              }))),
        ]),

        Container(margin: const EdgeInsets.all(10), width: 1.sw, constraints: BoxConstraints(maxHeight: 0.8.sh),
          child: SingleChildScrollView(child: SelectAddress(selectAddress, bloc!, keyAddress: keyStreet!))),
        Loading(bloc)
      ]))
    ));

  @override
  void setProvince(ItemModel value, {bool loadDistrict = true, bool clearDistrict = true}) {
    super.setProvince(value, loadDistrict: loadDistrict, clearDistrict: clearDistrict);
    bloc!.add(SelectLevelEvent('province', loading: loadDistrict && province.id != value.id));
  }

  @override
  void setDistrict(ItemModel value, {bool loadWard = true, bool clearWard = true}) {
    super.setDistrict(value, loadWard: loadWard, clearWard: clearWard);
    bloc!.add(SelectLevelEvent('district', loading: loadWard && district.id != value.id));
  }

  @override
  void setWard(ItemModel value) {
    super.setWard(value);
    bloc!.add(SelectLevelEvent('ward', loading: (bloc!.count??0) > 0));
  }

  @override
  void selectAddress(Map addresses, {String keyAddress = 'label'}) {
    byPassWard = true;
    super.selectAddress(addresses, keyAddress: keyAddress);
  }

  @override
  void callbackPrePage() {
    if (isFirst == true) {
      isFirst = null;
      return;
    }
    UtilUI.goBack(context, {
      'location': address,
      'address': streetTemp??'',
      'province': province,
      'district': district,
      'ward': ward,
    });
  }

  Future<void> _initData() async {
    final page = widget as SelectProDisWard;
    if (provinces.isEmpty) {
      bloc!.count = 3;
      if (page.ward.id.isEmpty) bloc!.count = 2;
      if (page.district.id.isEmpty) bloc!.count = 1;
      bloc!.add(LoadProvinceBaseEvent());
    } else {
      bloc!.count = 2;
      if (page.ward.id.isEmpty) bloc!.count = 1;
      if (page.district.id.isEmpty) bloc!.count = null;
    }
    page.ward.id.isEmpty ? isFirst = null : setWard(page.ward);
    if (page.province.id.isNotEmpty) setProvince(page.province, loadDistrict: true, clearDistrict: false);
    if (page.district.id.isNotEmpty) {
      Timer(const Duration(milliseconds: 1000), () {
        setDistrict(page.district, loadWard: true, clearWard: false);
        _fillAddress();
      });
    } else _fillAddress();
  }

  void _fillAddress() {
    ctrAddress.text = ward.name;
    if (district.name.isNotEmpty) ctrAddress.text += ctrAddress.text.isEmpty ? district.name : ', ${district.name}';
    if (province.name.isNotEmpty) ctrAddress.text += ctrAddress.text.isEmpty ? province.name : ', ${province.name}';
  }

  void _listener() {
    if (!fcAddress.hasFocus) {
      showPopupAddress = null;
      if (bloc!.startAutoSearch != null) bloc!.startAutoSearch = false;
      return;
    }

    if (fcAddress.hasFocus && bloc!.startAutoSearch != true) {
      bloc!.startAutoSearch = true;
      showPopupAddress = true;
      autoSearch();
    }
  }

  void _getMyLocation() {
    if (isFirst == true) isFirst = false;
    Geolocator.getCurrentPosition().then((value) => setAddress(LatLng(value.latitude, value.longitude)));
  }

  String _getFirstLetter(List<ItemModel> list, int index) {
    final temp = list[index].name.substring(0, 1).toUpperCase();
    return index == 0 || (index > 0 && list[index - 1].name.substring(0, 1).toUpperCase() != temp) ? temp : ' ';
  }

  String _getCurrentSelect() {
    switch(showProDisWard) {
      case 'district':
        return 'Quận/Huyện';
      case 'ward':
        return 'Phường/Xã';
      default:
        return 'Tỉnh/Thành phố';
    }
  }

  List<ItemModel> _getCurrentList() {
    switch(showProDisWard) {
      case 'district':
        return districts;
      case 'ward':
        return wards;
      default:
        return provinces;
    }
  }

  void _selectItem(int index) {
    if (bloc!.count != null) return;
    if (curIndex != index) {
      switch(showProDisWard) {
        case 'district':
          _setItem(districts, index);
          setDistrict(districts[index]);
          showProDisWard = 'ward';
          break;
        case 'ward':
          _setItem(wards, index);
          setWard(wards[index]);
          break;
        default:
          _setItem(provinces, index);
          setProvince(provinces[index]);
          showProDisWard = 'district';
      }
    }
  }

  void _setItem(List<ItemModel> list, int index) {
    if (curIndex > -1 && curIndex < list.length) list[curIndex].selected = false;
    list[index].selected = true;
    curIndex = index;
  }

  Widget _getSelected(ItemModel item, int index) {
    switch(showProDisWard) {
      case 'district':
        if (district.id != item.id) {
          item.selected = false;
          if (districts.isNotEmpty) districts[index].selected = false;
          return const SizedBox();
        }
        break;
      case 'ward':
        if (ward.id != item.id) {
          item.selected = false;
          if (wards.isNotEmpty) wards[index].selected = false;
          return const SizedBox();
        }
        break;
      default:
        if (province.id != item.id) {
          item.selected = false;
          if (provinces.isNotEmpty) provinces[index].selected = false;
          return const SizedBox();
        }
    }
    curIndex = index;
    item.selected = true;
    bloc!.add(HideAddressEvent());
    return Padding(padding: EdgeInsets.only(right: 40.sp), child: Icon(Icons.check, color: Colors.red, size: 60.sp));
  }

  Future<void> _refreshList() async {
    switch(showProDisWard) {
      case 'district':
      case 'ward':
        if (wards.isEmpty && district.id.isNotEmpty) bloc!.add(LoadWardBaseEvent(district.id));
        break;
      default:
        if (districts.isEmpty && province.id.isNotEmpty) bloc!.add(LoadDistrictBaseEvent(province.id));
    }
  }

  void _reset() {
    showProDisWard = 'province';
    curIndex = -1;
    province.id = '';
    province.name = '';
    province.selected = false;
    district.id = '';
    district.name = '';
    district.selected = false;
    ward.id = '';
    ward.name = '';
    ward.selected = false;
    ctrAddress.text = '';
    address = null;
    addressTemp = null;
    streetTemp = null;
    for (var item in provinces) {
      item.selected = false;
    }
    districts.clear();
    wards.clear();
    bloc!.add(SelectLevelEvent('province'));
  }

  void _selectLevel(String level) {
    switch(level) {
      case 'district':
        if (province.id.isEmpty) return;
        bloc!.add(districts.isEmpty ? LoadDistrictBaseEvent(province.id) : SelectLevelEvent('district'));
        break;
      case 'ward':
        if (district.id.isEmpty) return;
        bloc!.add(wards.isEmpty ? LoadWardBaseEvent(district.id) : SelectLevelEvent('ward'));
        break;
      default:
        bloc!.add(provinces.isEmpty ? LoadProvinceBaseEvent() : SelectLevelEvent('province'));
    }
  }
}

class _SelectItem extends StatelessWidget {
  final Function fnSelectLevel;
  final ItemModel item;
  final String type;
  final BaseBloc? bloc;
  final bool hideTop, hideBottom;
  const _SelectItem(this.fnSelectLevel, this.item, this.type, this.bloc, {this.hideTop = false, this.hideBottom = false});

  @override
  Widget build(BuildContext context) => GestureDetector(onTap: () => fnSelectLevel(type),
    child: BlocBuilder(bloc: bloc, buildWhen: (_,newS) => newS is LoadProvinceBaseState ||
      newS is LoadDistrictBaseState || newS is LoadWardBaseState || newS is SelectLevelState,
      builder: (_,state) {
        final active = state is SelectLevelState && state.level == type;
        return Container(padding: EdgeInsets.symmetric(horizontal: 40.sp), color: Colors.white,
          child: Container(padding: EdgeInsets.symmetric(horizontal: 40.sp),
            margin: EdgeInsets.only(top: hideTop && active ? 40.sp : 0, bottom: hideBottom && active ? 40.sp : 0),
            decoration: active ? BoxDecoration(
              borderRadius: BorderRadius.circular(5), border: Border.all(color: Colors.black45, width: 0.5)
            ) : null,
            child: Row(children: [
              active ? Icon(Icons.radio_button_checked, color: Colors.red, size: 40.sp)
              : Column(mainAxisSize: MainAxisSize.min, children: [
                Container(width: 0.5, color: hideTop ? Colors.transparent : Colors.black45, height: 60.sp),
                Container(width: 20.sp, height: 20.sp, margin: EdgeInsets.symmetric(horizontal: 10.sp),
                    decoration: BoxDecoration(color: Colors.black45, borderRadius: BorderRadius.circular(10))),
                Container(width: 0.5, color: hideBottom ? Colors.transparent : Colors.black45, height: 60.sp),
              ]),
              SizedBox(width: 40.sp),
              Padding(padding: EdgeInsets.symmetric(vertical: 40.sp), child: LabelCustom(item.name.isEmpty ?
              _getCurrentSelect() : item.name, size: 42.sp, weight: FontWeight.w300, color: active ? Colors.red : const Color(0xFF212121)))
            ])
          ));
      }));

  String _getCurrentSelect() {
    switch(type) {
      case 'district':
        return 'Quận/Huyện';
      case 'ward':
        return 'Phường/Xã';
      default:
        return 'Tỉnh/Thành phố';
    }
  }
}
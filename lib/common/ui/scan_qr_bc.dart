import 'dart:io';
import 'package:hainong/common/ui/import_lib_ui.dart';
import 'package:hainong/common/ui/permission_image_page_state.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:qr_code_scanner/qr_code_scanner.dart';

class ScanQRBCPage extends BasePage {
  ScanQRBCPage({Key? key}) : super(pageState: _ScanQRBCPageState(), key: key);
}

class _ScanQRBCPageState extends PermissionImagePageState {
  bool _isInit = true;
  QRViewController? controller;

  _ScanQRBCPageState() {
    showCamGal = false;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    funCheckPermission(Permission.camera).then((value) {
      if (value == PermissionStatus.granted) setState(() => _isInit = false);
    });
  }

  @override
  void reassemble() {
    super.reassemble();
    if (Platform.isAndroid) controller?.resumeCamera();
  }

  @override
  Widget build(BuildContext context, {Color color = Colors.white}) =>
      Scaffold(appBar: AppBar(elevation: 0, titleSpacing: 0,
          title: UtilUI.createLabel('Quét mã QR'), centerTitle: true),
          body: _isInit ? const SizedBox() : QRView(key: GlobalKey(debugLabel: 'QR'),
              onQRViewCreated: _onQRViewCreated, onPermissionSet: _onPermissionSet,
              overlay: QrScannerOverlayShape(borderColor: Colors.white,
                  borderLength: 20, borderWidth: 5, cutOutSize: 0.75.sw))
      );

  @override
  void loadFiles(List<File> files) {}

  void _onPermissionSet(QRViewController ctrl, bool p) {
    if (Platform.isAndroid) controller?.resumeCamera();
  }

  void _onQRViewCreated(QRViewController controller) {
    this.controller = controller;
    this.controller!.scannedDataStream.listen((scanData) {
      if (scanData.code != null && scanData.code!.isNotEmpty && mounted) {
        this.controller!.dispose();
        Navigator.pop(context, scanData.code!);
      }
    });
  }
}
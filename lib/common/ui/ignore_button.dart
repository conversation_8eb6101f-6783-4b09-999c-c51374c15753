import 'package:flutter_bloc/flutter_bloc.dart';
import '../count_down_bloc.dart';
import '../style_custom.dart';
import 'button_image_widget.dart';
import 'import_lib_base_ui.dart';
import 'label_custom.dart';

class IgnoreButton extends StatelessWidget {
  final CountDownBloc bloc;
  final Function fnAction;
  final String label;
  const IgnoreButton(this.bloc, this.fnAction, {Key? key, this.label = ' Không hiển thị lần nữa'}) : super(key: key);

  @override
  Widget build(BuildContext context) => Padding(padding: EdgeInsets.fromLTRB(20.sp, 0, 20.sp, 40.sp),
    child: Row(children: [
      ButtonImageWidget(0, fnAction, BlocBuilder(bloc: bloc, builder: (_,state) {
        bool active = state is CountDownState && state.value > 0;
        return Icon(active ? Icons.check_box : Icons.check_box_outline_blank, color: active ? StyleCustom.primaryColor : Colors.blue, size: 48.sp);
      })),
      Expanded(child: LabelCustom(label, color: const Color(0xFF1F1F1F), weight: FontWeight.normal))
    ]));
}
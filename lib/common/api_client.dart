import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:developer' as logDev;
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'package:package_info/package_info.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http_parser/http_parser.dart';
import 'package:video_thumbnail/video_thumbnail.dart';
import 'base_response.dart';
import 'constants.dart';
import 'database_helper.dart';
import 'models/file_byte.dart';
import 'util/util.dart';

/*http.MultipartFile createPartFile(File file, String paramFile) =>
    getMultipartFile(paramFile, file.path.split('/').last, file.readAsBytesSync());

http.MultipartFile createPartFileByte(FileByte file, String paramFile) =>
    getMultipartFile(paramFile, file.name.split('/').last, file.bytes);

http.MultipartFile getMultipartFile(String paramFile, String fileName, List<int> bytes) =>
    http.MultipartFile.fromBytes(paramFile, bytes, filename: fileName,
        contentType: Util.isImage(fileName) ? MediaType("image", "*") : MediaType("video", "*"));*/

Future<http.Response> getAPIIsolate(Map params) async => http.Client().get(Uri.parse(params['url']), headers: params['header']).timeout(Duration(seconds: params['timeout']));

Future<http.Response> postAPIIsolate(Map params) async {
  try {
    final request = http.MultipartRequest(params['method'], Uri.parse(params['url']));
    if (params['header'] != null) request.headers.addAll(params['header']);
    if (params['body'] != null) request.fields.addAll(params['body']);
    int timeout = request.files.isEmpty ? params['timeout'] : Constants().timeout;
    if (params.containsKey('forceTimeout') && params['forceTimeout'] == true) timeout = params['timeout'];
    final streamResponse = await request.send().timeout(Duration(seconds: timeout));
    return http.Response.fromStream(streamResponse);
  } catch (e) {
    rethrow;
  }
}

Future<http.Response> postJsonAPIIsolate(Map params) async {
  try {
    final headers = {'Content-Type': 'application/json', 'accept': '*/*'};
    if (params['header'] != null) headers.addAll(params['header']);
    final uri = Uri.parse(params['url']);
    switch(params['method']) {
      case 'POST': return http.post(uri, headers: headers, body: jsonEncode(params['body'])).timeout(Duration(seconds: params['timeout']));
      case 'DELETE': return http.delete(uri, headers: headers, body: jsonEncode(params['body'])).timeout(Duration(seconds: params['timeout']));
      case 'PUT': return http.put(uri, headers: headers, body: jsonEncode(params['body'])).timeout(Duration(seconds: params['timeout']));
      default: return http.patch(uri, headers: headers, body: jsonEncode(params['body'])).timeout(Duration(seconds: params['timeout']));
    }
  } catch (e) {
    rethrow;
  }
}

Future<void> trackAppIsolate(Map params) async {
  try {
    final body = jsonEncode({
      'device_id': params['device_id'],
      'user_id': params['user_id'],
      'main_function': params['function'],
      'client_key': '7459fbbd78f4f9971d0507ccec6963e08e0bf8be',
      'payload': {
        'method': params['method'],
        'path': params['path'],
        'params': {'env': 'App - ${Platform.isAndroid ? 'Android' : 'iOS'}'}
      }
    });
    //final resp = await http.post(Uri.parse(params['url'] + '/ssos/v1/track_activities'),
    await http.post(Uri.parse(params['url'] + '/ssos/v1/track_activities'),
        headers: {'Content-Type': 'application/json; charset=utf-8'},
        body: body).timeout(const Duration(seconds: 10));
    //print('\n\nrequest POST: ${resp.request!.url}');
    //print('\nrequest body: $body');
    //print('\nresponse body: ${resp.body}');
  } catch (_) {}
}

class ApiClient {
  http.Client getClient() => http.Client();

  static ApiClient? _instance;

  ApiClient._();

  factory ApiClient() {
    _instance ??= ApiClient._();
    return _instance!;
  }

  Future<void> _log(String url, String method, String response, {dynamic header, dynamic request}) async {
    return;
    //if (!(await DBHelperUtil().hasLogFile())) return;
    String temp = '\n\nadvn-time: ' + DateTime.now().toString() + '\nadvn-url $method: ' + url;
    if (header != null) temp += '\nadvn-header: ' + header.toString();
    if (request != null) temp += '\nadvn-request: ' + request.toString();
    temp += '\nadvn-response: $response\n\n';
    logDev.log(temp);
    //Util().logFile(temp);
  }

  Future<Map<String, String>> _getHeader() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    final token = prefs.getString('token_user') ?? '';
    if (token.isEmpty) return {};
    if (Constants().baseUrl != 'https://admin.hainong.vn' && (Constants().token == null || Constants().token!.isEmpty)) Constants().token = token;
    //logDev.log(token);
    return {'Authorization': token};
  }

  Future<void> trackApp(String path, String function, {String method = 'onTap'}) async {
    final prefs = await SharedPreferences.getInstance();
    compute(trackAppIsolate, {
      'url': Constants().baseUrlIPortal,
      'function': function,
      'method': method,
      'path': path,
      'device_id': prefs.getString('device_id') ?? '',
      'user_id': prefs.getInt('id') ?? -1
    });
  }

  Future<void> logout() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final device = prefs.getString('device_id') ?? '';
      final token = prefs.getString('token_user') ?? '';
      final headers = {'Content-Type': 'application/json; charset=utf-8', 'Authorization' : token};
      http.post(Uri.parse(Constants().baseUrl + Constants().apiVersion + 'account/logout'), headers: headers, body: jsonEncode({'device_id': device})).timeout(const Duration(seconds: 10));
    } catch (_) {}
  }

  Future<BaseResponse> getAPI(String url, data, {bool hasHeader = true, int timeout = 20, bool fullPath = false}) async {
    final baseResponse = BaseResponse();
    try {
      http.Response response = await compute(getAPIIsolate, {
        'timeout': timeout,
        'url': fullPath ? url : (Constants().baseUrl + url),
        if (hasHeader && Constants().isLogin) 'header': await _getHeader()
      });
      _log(response.request!.url.toString(), 'GET', response.body);
      if (Constants().token != null) baseResponse.data = response.body;
      final json = await compute(jsonDecode, response.body);
      baseResponse.fromJson(json, data);
    } catch (e) {
      return _responseError(baseResponse, e);
    }
    return baseResponse;
  }

  http.MultipartFile createPartFile(File file, String paramFile) =>
      getMultipartFile(paramFile, file.path.split('/').last, file.readAsBytesSync());

  http.MultipartFile createPartFileByte(FileByte file, String paramFile) =>
      getMultipartFile(paramFile, file.name.split('/').last, file.bytes);

  http.MultipartFile getMultipartFile(String paramFile, String fileName, List<int> bytes) =>
      http.MultipartFile.fromBytes(paramFile, bytes, filename: fileName,
          contentType: Util.isImage(fileName) ? MediaType("image", "*") : MediaType("video", "*"));

  Future<BaseResponse> postAPI(
    String url,
    String method,
    data, {
    bool hasHeader = true,
    bool fullPath = false,
    bool? forceTimeout,
    int timeout = 50,
    Map<String, String>? body,
    List<String>? files,
    List<FileByte>? realFiles,
    String paramFile = 'attachment[file][]',
    bool dataIsJson = false
  }) async {
    final baseResponse = BaseResponse();
    try {
      http.Response response;
      if ((files == null || files.isEmpty) && (realFiles == null || realFiles.isEmpty)) {
        response = await compute(postAPIIsolate, {
          if (forceTimeout == true) 'forceTimeout': forceTimeout,
          'timeout': timeout,
          'method': method,
          'url': fullPath ? url : (Constants().baseUrl + url),
          if (hasHeader) 'header': await _getHeader(),
          'body': body,
          //'paramFile': paramFile,
          //'files': files,
          //'realFiles': realFiles
        });
      } else {
        final request = http.MultipartRequest(method, Uri.parse(fullPath ? url : (Constants().baseUrl + url)));

        if (hasHeader) request.headers.addAll(await _getHeader());

        if (body != null) request.fields.addAll(body);

        if (files != null && files.isNotEmpty) {
          for (String path in files) request.files.add(createPartFile(File(path), paramFile));
        } else if (realFiles != null && realFiles.isNotEmpty) {
          for (FileByte file in realFiles) request.files.add(createPartFileByte(file, paramFile));
        }

        int requestTimeout = request.files.isEmpty ? timeout : Constants().timeout;
        if (forceTimeout == true) requestTimeout = timeout;

        final streamResponse = await request.send().timeout(Duration(seconds: requestTimeout));
        response = await http.Response.fromStream(streamResponse);
      }

      _log(response.request!.url.toString(), method, response.body, request: body);
      if (Constants().token != null) baseResponse.data = response.body;
      final json = await compute(jsonDecode, response.body);
      if (dataIsJson) {
        baseResponse.isJson = true;
        baseResponse.data = json;
        baseResponse.formatData();
      } else baseResponse.fromJson(json, data);
    } catch (e) {
      return _responseError(baseResponse, e);
    }
    return baseResponse;
  }

  Future<String> getString(String url, {int timeout = 20}) async {
    try {
      final response = await compute(getAPIIsolate, {
        'timeout': timeout,
        'url': url
      });
      _log(response.request!.url.toString(), 'GET', response.body);
      if (response.statusCode == 200) return response.body;
    } catch (_) {}
    return '';
  }

  Future<Uint8List?> getBytes(String url, {int timeout = 20}) async {
    try {
      final response = await compute(getAPIIsolate, {
        'timeout': timeout,
        'url': url
      });
      _log(response.request!.url.toString(), 'GET', response.body);
      if (response.statusCode == 200) return response.bodyBytes;
    } catch (_) {}
    return null;
  }

  Future<Uint8List?> downloadPart(String url, int start, int end, {int timeout = 20}) async {
    try {
      final response = await http.get(Uri.parse(url), headers: {"Range": "bytes=$start-$end"}).timeout(Duration(seconds: timeout));
      if (response.statusCode == 206 || response.statusCode == 200) return response.bodyBytes;
    } catch (_) {}
    return null;
  }

  Future<String> getAPI2(String url, {int timeout = 20, bool hasHeader = true, bool isFullPath = false, bool checkSttCode = true}) async {
    try {
      http.Response response = await compute(getAPIIsolate, {
        'timeout': timeout,
        'url': isFullPath ? url : (Constants().baseUrl + url),
        if (hasHeader && Constants().isLogin) 'header': await _getHeader()
      });
      _log(response.request!.url.toString(), 'GET', response.body);
      if (checkSttCode) return response.statusCode == 200 ? response.body : '';
      return response.body;
    } catch (_) {}
    return '';
  }

  Future<String> postAPI2(String url,
      {int timeout = 20,
      String method = 'POST',
      Map<String, String>? body,
      Map<String, String>? header,
      bool hasHeader = true}) async {
    try {
      final response = await compute(postAPIIsolate, {
        'timeout': timeout,
        'method': method,
        'url': Constants().baseUrl + url,
        if (hasHeader) 'header': await _getHeader(),
        'body': body
      });
      _log(response.request!.url.toString(), 'POST', response.body, request: body, header: header);
      if (response.statusCode == 200) return response.body;
    } catch (_) {}
    return '';
  }

  Future<BaseResponse> postJsonAPI(String path, data, body,
      {bool hasHeader = true, bool hasDomain = false, int timeout = 20, String method = 'POST', bool dataIsJson = false}) async {
    final baseResponse = BaseResponse();
    try {
      http.Response response = await compute(postJsonAPIIsolate, {
        'timeout': timeout,
        'method': method,
        'url': hasDomain ? path : (Constants().baseUrl + path),
        if (hasHeader && Constants().isLogin) 'header': await _getHeader(),
        'body': body
      });

      _log(response.request!.url.toString(), method, response.body, request: body, header: response.request!.headers);

      if (Constants().token != null) baseResponse.data = response.body;
      final json = await compute(jsonDecode, response.body);
      if (dataIsJson) {
        baseResponse.isJson = true;
        baseResponse.data = json;
        baseResponse.formatData();
      } else baseResponse.fromJson(json, data);
    } catch (e) {
      return _responseError(baseResponse, e);
    }
    return baseResponse;
  }

  Future<BaseResponseTranslate> postAPIResponseTranslate(
      String url,
      String method,
      data, {
        bool hasHeader = true,
        bool fullPath = false,
        int timeout = 20,
        Map<String, String>? body,
        List<String>? files,
        List<FileByte>? realFiles,
        String paramFile = 'attachment[file][]',
      }) async {
    final baseResponse = BaseResponseTranslate();
    try {
      final uri = Uri.parse(fullPath ? url : (Constants().baseUrl + url));
      final request = http.MultipartRequest(method, uri);

      if (hasHeader) request.headers.addAll(await _getHeader());

      if (body != null) request.fields.addAll(body);

      if (files != null && files.isNotEmpty) {
        for (String path in files) request.files.add(_createPartFile(File(path), paramFile));
      } else if (realFiles != null) {
        for (FileByte file in realFiles) request.files.add(_createPartFileByte(file, paramFile));
      }

      final streamResponse =
      await request.send().timeout(Duration(seconds: request.files.isEmpty ? timeout : Constants().timeout));
      final response = await http.Response.fromStream(streamResponse);
      _log(uri.toString(), method, response.body, request: body);
      baseResponse.fromJson(jsonDecode(response.body), data);
    } catch (e) {
      return _responseErrorResponseTranslate(baseResponse, e);
    }
    return baseResponse;
  }

  /// path = 'path1/path2?' => no domain and api version (https://panel.hainong.vn/api/v2/)
  /// path = 'path1/path2?param1=xxx&param2=xxx&' => no domain and api version (https://panel.hainong.vn/api/v2/)
  /// isOnePage = false => will get all from current page to last page
  Future<List> getList(String path, {int page = 1, int limit = 50, bool isOnePage = false}) async {
    String resp;
    List temp;
    final List list = [];
    dynamic json;
    while (page > 0) {
      resp = await getAPI2(Constants().apiVersion + path + 'page=$page&limit=$limit');
      if (resp.isNotEmpty) {
        try {
          json = await compute(jsonDecode, resp);
          if (Util.checkKeyFromJson(json, 'success') && Util.checkKeyFromJson(json, 'data') && json['success'] && json['data'] is List) {
            temp = json['data'].toList();
            temp.isNotEmpty ? list.addAll(temp) : page = 0;
            temp.length == limit ? page++ : page = 0;
          } else page = 0;
        } catch (_) {page = 0;}
      } else page = 0;
      if (isOnePage) return list;
    }
    return list;
  }

  Future<dynamic> getData(String path, {bool hasHeader = true, bool getError = false}) async {
    final resp = await getAPI2(Constants().apiVersion + path, hasHeader: hasHeader);
    return getDataFromString(resp, getError: getError);
  }

  Future<dynamic> getDataFromString(String value, {bool getError = false}) async {
    if (value.isNotEmpty) {
      try {
        dynamic json = await compute(jsonDecode, value);
        if (Util.checkKeyFromJson(json, 'success') && Util.checkKeyFromJson(json, 'data') && json['success']) return json['data'];
        if (getError && json['data'] != null) return {'error': json['data'].toString()};
      } catch (e) {
        if (getError) {
          final error = await getMsgError(e.runtimeType.toString());
          return error == null ? null : {'error': error};
        }
      }
    }
    return {'error': ''};
  }

  Future<String> checkVersion() async {
    final response = await getString(Constants().baseUrl+'/api/v2/base/option?key=update_version');
    if (response.isNotEmpty) {
      int versionApp = int.parse((await PackageInfo.fromPlatform()).version.replaceAll('.', ''));
      int version = 0;
      try {
        final json = await compute(jsonDecode, response);
        if (Util.checkKeyFromJson(json, 'success')) {
          final data = json['data'][0];
          version = int.parse((data['value']??'0').replaceAll('.', ''));
        }
      } catch (_) {}
      if (version > versionApp) {
        return (Platform.isIOS ? 'http://apps.apple.com/app/id1540198381' : 'https://play.google.com/store/apps/details?id=com.advn.hainong');
      }
    }
    return '';
  }

  Future<BaseResponse> _responseError(BaseResponse baseResponse, error) async {
    String? temp = await getMsgError(error.runtimeType.toString());
    if (Constants().token != null) temp = '$temp\n\nError: $error\n\nData: ${baseResponse.data}';
    baseResponse.setDataError(error: temp);
    return baseResponse;
  }

  Future<String?> getMsgError(String type) async {
    switch(type) {
      case 'FormatException': return 'Tính năng đang bảo trì';
      case 'TimeoutException': return 'Kết nối bị gián đoạn. Vui lòng kiểm tra internet hoặc thử lại sau vài phút.';
      case 'SocketException':
      case '_ClientSocketException':
        String html = await ApiClient().getString('https://google.com', timeout: 5);
        return html.isEmpty ? 'Lỗi không kết nối! Vui lòng kiểm tra lại internet của bạn' : 'Hệ thống đang bảo trì';
    }
    return null;
  }

  Future<BaseResponseTranslate> _responseErrorResponseTranslate(BaseResponseTranslate baseResponse, error) async {
    String? temp = await getMsgError(error.runtimeType.toString());
    if (Constants().token != null) temp = '$temp\n\nError: $error\n\nData: ${baseResponse.data}';
    baseResponse.setDataError(error: temp);
    return baseResponse;
  }

  http.MultipartFile _createPartFile(File file, String paramFile) =>
      _getMultipartFile(paramFile, file.path.split('/').last, file.readAsBytesSync());

  http.MultipartFile _createPartFileByte(FileByte file, String paramFile) =>
      _getMultipartFile(paramFile, file.name.split('/').last, file.bytes);

  http.MultipartFile _getMultipartFile(String paramFile, String fileName, List<int> bytes) =>
      http.MultipartFile.fromBytes(paramFile, bytes,
          filename: fileName, contentType: Util.isImage(fileName) ? MediaType("image", "*") : MediaType("video", "*"));

  Future<void> downloadVideo(String url, int id) async {
    if (url.isNotEmpty) {
      //int start = 0, end = 3*1024*1024 - 1;
      String path = '';
      //final DBHelper db = DBHelper();

      /*dynamic raw = await db.getJsonById('id', id, 'videos');
      if (raw != null) {
        path = raw['video_path']??'';
        final file = File(path);
        if (!file.existsSync()) {
          path = '';
        } else {
          if (raw['is_full'] == 1) return;
          start = raw['byte_start'];
          end = raw['byte_end'];
        }
      }*/

      //if (path.isEmpty) {
        final folder = await getTemporaryDirectory();
        //path = folder.path + '/videos/$id-' + url.split('/').last;
        path = folder.path + '/images/$id-' + url.split('/').last;
      //}

      //_handleDownloadVideo(url, id, start, end, path);
      _getThumbnail(path, url);
    }
  }

  void _handleDownloadVideo(String url, int id, int start, int end, String path) async {
    final resp = await downloadPart(url, start, end, timeout: 120);
    if (resp != null && resp.isNotEmpty) {
      File file = File(path);
      if (!file.existsSync()) file.createSync(recursive: true);
      final size = file.lengthSync();
      if (size == 0 && start == 0) ;
      else if (size == 0 && start > 0) return;
      else if (size > 0 && start > 0 && size != start) return;

      file.writeAsBytesSync(resp, mode: FileMode.writeOnlyAppend, flush: true);
      if (file.lengthSync() > start) {
        _getThumbnail(file.path, url);

        if (resp.length < 3*1024*1024) {
          start += resp.length;
          end = start;
        } else {
          start = end + 1;
          end += 3*1024*1024;
        }

        _saveToDB(url, id, start, end ,path, start == end ? 1 : 0);
      }
    }
  }

  Future<void> _getThumbnail(String path, String url) async {
    //path = path.replaceFirst('/videos/', '/images/');
    //path = path.replaceFirst('.mp4', '.jpeg');
    final file = File(path);
    if (file.existsSync() && file.lengthSync() > 0) return;
    file.createSync(recursive: true);

    final data = await VideoThumbnail.thumbnailData(video: url, quality: 25, imageFormat: ImageFormat.JPEG);
    if (data != null && data.isNotEmpty) {
      file.writeAsBytesSync(data, mode: FileMode.writeOnly, flush: true);
      if (file.lengthSync() == 0) file.delete();
    } else file.delete();
  }

  Future<void> _saveToDB(String url, int id, int start, int end, String path, int isFull) async {
    final DBHelper db = DBHelper();
    final raw = await db.getJsonById('id', id, 'videos');
    int value = raw != null ?
    await db.updateHelper('id', id, tblName: 'videos', values: {
      'byte_start': start,
      'byte_end': end,
      'is_full': isFull
    }) :
    await db.insertHelper(tblName: 'videos', values: {
      'id': id,
      'video_url': url,
      'video_path': path,
      'byte_start': start,
      'byte_end': end,
      'is_full': isFull
    });
    if (value > 0 && isFull == 0) {
      await Future.delayed(const Duration(seconds: 2));
      _handleDownloadVideo(url, id, start, end, path);
    }
  }
}

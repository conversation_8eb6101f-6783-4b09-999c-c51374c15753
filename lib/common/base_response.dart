import 'constants.dart';
import 'multi_language.dart';

class BaseResponse {
  /*final String _dataInvalidToken = 'Invalid token';
  final String _dataUnAuthorize = 'Unauthorize';
  final String _msgForgetPassword = 'forget_password';
  final String msgCreatePost = 'create post';
  final String msgUpdatePost = 'update post';
  final String msgCreateProduct = 'create_products';
  final String msgUpdateProduct = 'update_products';
  final String msgDeletePost = 'destroy post';*/
  bool success;
  String msg;
  dynamic data, isJson;

  BaseResponse({this.success = false, this.msg = '', this.data, this.isJson});

  BaseResponse fromJson(Map<String, dynamic> json, dynamic data) {
    if (json.containsKey('success')) success = json['success'];
    if (json.containsKey('msg')) msg = json['msg'];
    if (json.containsKey('data')) {
      try {
        if (data is BaseResponse) {
          this.data = data.isJson == true && success ? json['data'] : json['data'].toString();
        } else {
          this.data = success ? data.fromJson(json['data']) : json['data'].toString();
        }
      } catch (e) {
        setDataError(error: Constants().token == null ? 'Tính năng đang bảo trì.' : e.toString());
      }
    }
    return this;
  }

  setDataError({String? error}) => data = error??MultiLanguage.get('msg_system_error');

  bool checkOK({bool passString = false}) => (success && (passString || data is! String || msg == 'forget_password'));

  bool checkTimeout() => (data is String && (data == 'Invalid token' || data == 'Unauthorize'));

  void formatData() {
    if (data is! Map) return;
    success = data['success'] ?? false;
    msg = data['msg'] ?? '';
    if (data.containsKey('data') && data['data'] != null) data = data['data'];
  }
}

class BaseResponseTranslate extends BaseResponse {
  dynamic translate;
  dynamic paginate;
  @override
  BaseResponseTranslate fromJson(Map<String, dynamic> json, data,) {
    if (json.containsKey('success')) success = json['success'];
    if (json.containsKey('msg')) msg = json['msg'];
    if (json.containsKey('data')) {
      try {
        this.data = data is BaseResponseTranslate && success ? json['data']
            : (success ? data.fromJson(json['data']) : json['data'].toString());
      } catch (e) {
        setDataError(error: 'Tính năng đang bảo trì');
      }
    }
    if (json.containsKey('translate_data')) {
      try {
        translate = data is BaseResponseTranslate ? json['translate_data']
            : (success ? data.fromJson(json['translate_data']) : json['translate_data'].toString());
      } catch (e) {
        setDataError();
      }
    }
    if (json.containsKey('paginate')) {
      try {
        paginate = data is BaseResponseTranslate ? json['paginate']
            : (success ? data.fromJson(json['paginate']) : json['paginate'].toString());
      } catch (e) {
        setDataError();
      }
    }
    return this;
  }
}

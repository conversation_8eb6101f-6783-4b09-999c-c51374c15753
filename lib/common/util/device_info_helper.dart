import 'dart:io';
import 'package:device_info/device_info.dart';
import 'package:flutter/services.dart';

class DeviceInfoHelper {
  Future<int> getAndroidSdk() async {
    try {
      MethodChannel channel = const MethodChannel('com.device_info.sdk');
      final int? sdkInt = await channel.invokeMethod<int>('getSdkInt');
      return sdkInt??1;
    } catch (e) {
      return 1;
    }
  }

  Future<String> get getDeviceId async {
    final DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
    String deviceId = '';
    try {
      if (Platform.isAndroid) {
        AndroidDeviceInfo androidInfo = await deviceInfoPlugin.androidInfo;
        deviceId = androidInfo.androidId;
      } else if (Platform.isIOS) {
        IosDeviceInfo iosInfo = await deviceInfoPlugin.iosInfo;
        deviceId = iosInfo.identifierForVendor;
      }
    } on Exception {
      return '';
    }
    return deviceId;
  }
}
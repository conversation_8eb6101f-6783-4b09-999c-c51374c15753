import 'package:hainong/common/util/util.dart';
import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';
import '../features/function/module_model.dart';

class DBHelper {
  static DBHelper? _instance;
  Future<Database>? db;

  DBHelper._() {
    getDatabasesPath().then((path) {
      db = openDatabase(join(path, '2nong.db'), version: 11, onCreate: (dataB, version) async {
        _addSetting(dataB);
        _upgradeAds(dataB);
        _addModules(dataB);
        _addBanners(dataB);
        _addChatBot(dataB);
        _addNews(dataB);
        return _addAddresses(dataB);
        /// return _add???(dataB);
      }, onUpgrade: (dataB, oldV, newV) async {
        if (newV == 5) {
          _addSetting(dataB);
          return _upgradeAds(dataB);
        }
        if (newV == 8) {
          _addSetting(dataB);
          _upgradeAds(dataB);
          _addModules(dataB);
          return _addBanners(dataB);
        }
        if (newV == 9) { //build 1.0.61
          _addSetting(dataB);
          _upgradeAds(dataB);
          _addModules(dataB);
          _addBanners(dataB);
          return _addChatBot(dataB);
        }
        if (newV == 10) { //build 1.0.69
          _addSetting(dataB);
          _upgradeAds(dataB);
          _addModules(dataB);
          _addBanners(dataB);
          _addChatBot(dataB);
          return _addNews(dataB);
        }
        if (newV == 11) { //build 1.0.74
          _addSetting(dataB);
          _upgradeAds(dataB);
          _addModules(dataB);
          _addBanners(dataB);
          _addChatBot(dataB);
          _addNews(dataB);
          return _addAddresses(dataB);
        }
        /// next version x.x.x
        /*if (newV == ?) { //build 1.0.?
          _addSetting(dataB);
          _upgradeAds(dataB);
          _addModules(dataB);
          _addBanners(dataB);
          _addChatBot(dataB);
          _addNews(dataB);
          _addNews(dataB);
          _addAddresses(dataB);
          return _add?(dataB);
        }*/
      });
    });
  }

  factory DBHelper() => _instance!;

  static void initDB() => _instance ??= DBHelper._();

  Future<void> _addSetting(Database dataB) {
    return dataB.execute('CREATE TABLE IF NOT EXISTS setting ('
        'id TEXT PRIMARY KEY,'
        'value TEXT);');
  }

  Future<void> _upgradeAds(Database dataB) {
    dataB.execute('DROP TABLE IF EXISTS ads');
    return dataB.execute('CREATE TABLE IF NOT EXISTS ads5 ('
        'id INTEGER PRIMARY KEY,'
        'is_view INTEGER,'
        'is_use INTEGER,'
        'popup_type INTEGER,'
        'image TEXT,'
        'name TEXT,'
        'description TEXT,'
        'show_position TEXT,'
        'classable_type TEXT,'
        'classable_id INTEGER,'
        'display_order INTEGER);');
  }

  Future<void> _addModules(Database dataB) {
    return dataB.execute('CREATE TABLE IF NOT EXISTS module ('
        'app_type TEXT PRIMARY KEY,'
        'group_type TEXT,'
        'group_name TEXT,'
        'name TEXT,'
        'status TEXT,'
        'icon TEXT);');
  }

  Future<void> _addBanners(Database dataB) {
    return dataB.execute('CREATE TABLE IF NOT EXISTS banner ('
        'id INTEGER PRIMARY KEY,'
        'name TEXT,'
        'description TEXT,'
        'image TEXT,'
        'show_position TEXT,'
        'order_ban INTEGER,'
        'is_use INTEGER,'
        'is_view INTEGER,'
        'classable_type TEXT,'
        'classable_id INTEGER,'
        'location TEXT);');
  }

  Future<void> _addChatBot(Database dataB) {
    return dataB.execute('CREATE TABLE IF NOT EXISTS chat ('
        'id INTEGER,'
        'type TEXT,'
        'link TEXT,'
        'data TEXT,'
        'data_formatted TEXT);');
  }

  Future<void> _addNews(Database dataB) {
    return dataB.execute('CREATE TABLE IF NOT EXISTS news ('
        'id INTEGER,'
        'content TEXT);');
  }

  /*Future<void> _addVideos(Database dataB) {
    return dataB.execute('CREATE TABLE IF NOT EXISTS videos ('
        'id INTEGER,'
        'video_url TEXT,'
        'video_path TEXT,'
        'byte_start INTEGER,'
        'byte_end INTEGER,'
        'is_full INTEGER);');
  }*/

  Future<void> _addAddresses(Database dataB) {
    return dataB.execute('CREATE TABLE IF NOT EXISTS addresses ('
        'id INTEGER,'
        'address TEXT,'
        'province_id INTEGER,'
        'province_name TEXT,'
        'district_id INTEGER,'
        'district_name TEXT,'
        'ward_id INTEGER,'
        'ward_name TEXT,'
        'lat TEXT,'
        'lng TEXT,'
        'is_current INTEGER);');
  }

  Future<int> _insert(String tblName, {dynamic objModel, Map<String, dynamic>? values, ConflictAlgorithm conflict = ConflictAlgorithm.replace}) async {
    if (db == null) return -1;
    final temp = await db!;
    if (objModel != null) {
      return temp.insert(objModel.getTblName(), objModel.toJson(), conflictAlgorithm: conflict);
    } else if (values != null) {
      return temp.insert(tblName, values, conflictAlgorithm: conflict);
    }
    return -1;
  }

  Future<int> insertHelper({String tblName = '', dynamic objModel, Map<String, dynamic>? values, ConflictAlgorithm conflict = ConflictAlgorithm.replace}) =>
    _insert(tblName, objModel: objModel, values: values, conflict: conflict);

  Future<int> _update(String tblName, String fieldName, dynamic value, {dynamic objModel, Map<String, dynamic>? values}) async {
    try {
      if (db == null) return -1;
      final temp = await db!;
      if (objModel != null) {
        return temp.update(objModel.getTblName(), objModel.toJson(), where: ' $fieldName = ? ', whereArgs: [value]).catchError((e) => -1).onError((error, stackTrace) => -1);
      } else if (values != null) {
        return temp.update(tblName, values, where: ' $fieldName = ? ', whereArgs: [value]).catchError((e) => -1).onError((error, stackTrace) => -1);
      }
    } catch(_) {}
    return -1;
  }

  Future<int> updateHelper(String fieldName, dynamic value, {String tblName = '', dynamic objModel,
    Map<String, dynamic>? values}) => _update(tblName, fieldName, value, objModel: objModel, values: values);

  Future<int> _delete(String tblName, String fieldName, dynamic value) async {
    if (db == null) return -1;
    final temp = await db!;
    return temp.delete(tblName, where: ' $fieldName = ? ', whereArgs: [value]).catchError((e) {
      return -1;
    }).onError((error, stackTrace) {
      return -1;
    });
  }

  Future<int> deleteHelper(String fieldName, dynamic value, String tblName) => _delete(tblName, fieldName, value);

  Future<List<dynamic>> getAllModelWithCond(dynamic objModel, {String? orderBy, String? cond, int? limit, int? offset, bool isNew = true}) async {
    List<dynamic> list = [];
    if (db == null) return list;
    final temp = await db!;
    final query = await temp.query(objModel.getTblName(), orderBy: orderBy, where: cond, limit: limit, offset: offset);
    for (var ele in query) {
      list.add(objModel.fromJson(ele, isSQL: true, isNew: isNew));
    }
    return list;
  }

  Future<List<dynamic>?> getAllJsonWithCond(String tblName, {String? orderBy, String? cond, int? limit, int? offset}) async {
    try {
      if (db == null) return null;
      final temp = await db!;
      return temp.query(tblName, orderBy: orderBy, where: cond, limit: limit, offset: offset);
    } catch (_) {}
    return null;
  }

  Future<dynamic> getModelById(String fieldName, int id, dynamic objModel) async {
    if (db == null) return objModel;
    final temp = await db!;
    final query = await temp.query(objModel.getTblName(), where: '$fieldName = ?', whereArgs: [id]);
    if (query.isNotEmpty) objModel.fromJson(query.first, isSQL: true);
    return objModel;
  }

  Future<dynamic> getJsonById(String fieldName, dynamic id, String tblName) async {
    try {
      if (db == null) return null;
      final temp = await db!;
      final query = await temp.query(tblName, where: '$fieldName = ?', whereArgs: [id]);
      if (query.isNotEmpty) return query.first;
    } catch (_) {}
    return null;
  }

  /*Future<void> clearDB() async {
    if (db == null) return;
    await (await db!).delete('ads5').onError((e,s) => -1).catchError((_) {});
    await (await db!).delete('setting').onError((e,s) => -1).catchError((_) {});
    await (await db!).delete('module').onError((e,s) => -1).catchError((_) {});
    await (await db!).delete('banner').onError((e,s) => -1).catchError((_) {});
    await (await db!).delete('chat').onError((e,s) => -1).catchError((_) {});
    return;
  }*/

  Future<void> clearTable(String table) async {
    if (db == null) return;
    await (await db!).delete(table).onError((e,s) => -1).catchError((_) => -1);
    return;
  }
}

class DBHelperUtil {
  final db = DBHelper();
  DBHelperUtil();

  Future<bool> setLogFile() async {
    bool isOn = true;
    try {
      final values = await db.getJsonById('id', 'log_file', 'setting');
      if (values == null || values.isEmpty) {
        db.insertHelper(tblName: 'setting', values: {'id': 'log_file', 'value': 'on'});
      } else {
        isOn = values['value'] == 'on';
        db.updateHelper('id', 'log_file', tblName: 'setting', values: {'id': 'log_file', 'value': isOn ? 'off' : 'on'});
        isOn = !isOn;
      }
    } catch (_) {}
    return isOn;
  }

  Future<bool> hasLogFile() async {
    bool isOn = false;
    try {
      final values = await db.getJsonById('id', 'log_file', 'setting');
      if (values != null && values.isNotEmpty) isOn = values['value'] == 'on';
    } catch (_) {}
    return isOn;
  }

  Future<bool> isSwipeLeft() async {
    bool isOn = true;
    final values = await db.getJsonById('id', 'news_swipe_left', 'setting');
    if (values == null || values.isEmpty) {
      db.insertHelper(tblName: 'setting', values: {'id': 'news_swipe_left', 'value': 'off'});
    } else {
      isOn = false;
    }
    return isOn;
  }

  Future<void> clearAdsModule() async {
    db.clearTable('ads5');
    db.clearTable('banner');
    await db.clearTable('module');
  }

  Future<void> setModule(newValues) async {
    try {
      final values = await db.getJsonById('app_type', newValues['app_type'], 'module');
      if (values == null || values.isEmpty) {
        db.insertHelper(tblName: 'module', values: newValues);
      } else {
        db.updateHelper('app_type', newValues['app_type'], tblName: 'module', values: newValues);
      }
    } catch (_) {}
  }

  Future<ModuleModels?> getModules() async {
    final list = await db.getAllModelWithCond(ModuleModel());
    if (list.isNotEmpty) {
      final modules = ModuleModels();
      final Map<String, Map<String, ModuleModel>> listTemp = {};
      for(ModuleModel item in list) {
        if (!listTemp.containsKey(item.group_type)) listTemp.putIfAbsent(item.group_type, () => {});
        listTemp[item.group_type]!.update(item.app_type, (value) => item, ifAbsent: () => item);
        modules.list2.putIfAbsent(item.app_type, () => item);
      }
      modules.list.addAll(listTemp.values);
      return modules;
    }
    return null;
  }

  Future<bool> showIgnorePhonePopup() async {
    bool isIgnore = true;
    try {
      final values = await db.getJsonById('id', 'ignore_phone_popup', 'setting');
      if (values != null && values.isNotEmpty) {
        final DateTime now = DateTime.now();
        final DateTime temp = DateTime(now.year, now.month, now.day);
        return temp.compareTo(Util.stringToDateTime(values['value']??'0001-01-01', pattern: 'yyyy-MM-dd')) > 0;
      }
    } catch (_) {}
    return isIgnore;
  }

  Future<String?> getSetting(String key) async {
    final values = await db.getJsonById('id', key, 'setting');
    if (values != null && values.isNotEmpty) return values['value'];
    return null;
  }

  Future<void> setSetting(String key, String value) async {
    try {
      final values = await db.getJsonById('id', key, 'setting');
      if (values == null || values.isEmpty) {
        db.insertHelper(tblName: 'setting', values: {'id': key, 'value': value});
      } else {
        db.updateHelper('id', key, tblName: 'setting', values: {'value': value});
      }
    } catch (_) {}
  }

  Future<void> clearSetting(String value) async {
    try {
      db.deleteHelper('id', value, 'setting');
    } catch (_) {}
  }

  Future<void> clearChatBot() async {
    try {
      db.clearTable('chat');
      db.deleteHelper('id', 'open_date', 'setting');
    } catch (_) {}
  }

  Future<void> clearIgnores(prefs, String id) async {
    final temp = await getSetting('id_user');
    if (temp != id) {
      setSetting('id_user', id);
      clearSetting('ignore_phone_popup');
      clearSetting('ignore_address_popup');
      clearSetting('ignore_info_popup');
      prefs.setString('carts', '');
      prefs.setString('cart_info', '');
    }
  }
}
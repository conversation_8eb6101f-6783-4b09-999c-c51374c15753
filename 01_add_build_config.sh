#!/bin/bash

# Script này thêm buildFeatures { buildConfig true } vào tất cả các file build.gradle của các module

# Đếm số lượng file đã sửa
fixed_count=0

# Hàm để thêm buildFeatures { buildConfig true } vào build.gradle
add_build_config() {
  local build_gradle="$1"
  
  echo "Đang kiểm tra: $build_gradle"
  
  # Kiểm tra xem đã có buildFeatures { buildConfig true } chưa - cải thiện cách kiểm tra
  if grep -q "buildFeatures" "$build_gradle" && grep -A5 "buildFeatures" "$build_gradle" | grep -q "buildConfig true"; then
    echo "  ✅ $build_gradle đã có buildFeatures { buildConfig true }"
    return 1
  fi

  # Đọc file để kiểm tra cấu trúc
  local content=$(cat "$build_gradle")
  
  # Kiểm tra xem đã có khối buildFeatures nhưng không có buildConfig
  if grep -q "buildFeatures" "$build_gradle" && ! grep -A5 "buildFeatures" "$build_gradle" | grep -q "buildConfig true"; then
    echo "  ℹ️ $build_gradle đã có khối buildFeatures nhưng không có buildConfig true, sẽ thêm vào"
    
    # Cải thiện cách thêm buildConfig true vào khối buildFeatures đã tồn tại
    # Sử dụng awk để thêm vào khối buildFeatures hiện có đúng vị trí
    awk '
      /buildFeatures[[:space:]]*\{/ {
        print $0
        print "        buildConfig true"
        next
      }
      { print }
    ' "$build_gradle" > "$build_gradle.new"
    
    mv "$build_gradle.new" "$build_gradle"
    
    # Kiểm tra xem đã thêm thành công chưa
    if grep -q "buildConfig true" "$build_gradle"; then
      echo "  ✅ Đã thêm buildConfig true vào khối buildFeatures hiện có trong $build_gradle"
      grep -A3 "buildFeatures" "$build_gradle"
      ((fixed_count++))
      return 0
    else
      echo "  ⚠️ Không thể thêm buildConfig true vào khối hiện có, thử cách khác..."
    fi
  fi
  
  # Thêm cả khối buildFeatures mới vào android {}
  echo "  ℹ️ $build_gradle chưa có buildFeatures { buildConfig true }, sẽ thêm vào"
  
  # Tìm vị trí khối android
  local has_android=$(grep -n "android[[:space:]]*{" "$build_gradle" | head -1 | cut -d: -f1)
  
  if [ -n "$has_android" ]; then
    # Cải thiện cách tìm dấu } đóng khối android và thêm buildFeatures
    local android_end_line=$(awk -v start="$has_android" '
      BEGIN { level = 0; }
      NR >= start {
        for (i = 1; i <= length($0); i++) {
          c = substr($0, i, 1)
          if (c == "{") level++
          if (c == "}") {
            level--
            if (level == 0) {
              print NR
              exit
            }
          }
        }
      }
    ' "$build_gradle")
    
    # Nếu tìm thấy vị trí kết thúc khối android, thêm buildFeatures vào
    if [ -n "$android_end_line" ]; then
      # Thêm đoạn code vào trước dấu } đóng khối android
      awk -v end_line="$android_end_line" '
        NR == end_line {
          print "    buildFeatures {"
          print "        buildConfig true"
          print "    }"
          print $0
          next
        }
        { print }
      ' "$build_gradle" > "$build_gradle.new"
      
      mv "$build_gradle.new" "$build_gradle"
      echo "  ✅ Đã thêm buildFeatures { buildConfig true } vào $build_gradle"
      grep -A3 "buildFeatures" "$build_gradle"
      ((fixed_count++))
      return 0
    else
      # Nếu không tìm thấy vị trí khối android phù hợp, thử cách thêm đơn giản
      echo "  ⚠️ Không thể tìm thấy vị trí thích hợp, thử sử dụng cách thêm đơn giản..."
      
      # Kiểm tra xem đã có khối android chưa
      if grep -q "android[[:space:]]*{" "$build_gradle"; then
        # Thêm vào cuối khối android đã tồn tại
        sed -i'' -e '/android[[:space:]]*{/,/}/s/}/    buildFeatures {\n        buildConfig true\n    }\n}/' "$build_gradle"
      else
        # Thêm khối android mới vào cuối file
        cat >> "$build_gradle" << 'EOL'

android {
    buildFeatures {
        buildConfig true
    }
}
EOL
      fi
      
      echo "  ✅ Đã thêm buildFeatures { buildConfig true } vào $build_gradle"
      ((fixed_count++))
      return 0
    fi
  else
    # Nếu không có khối android, thêm vào cuối file
    cat >> "$build_gradle" << 'EOL'

android {
    buildFeatures {
        buildConfig true
    }
}
EOL
    echo "  ✅ Đã thêm khối android với buildFeatures vào cuối file $build_gradle"
    ((fixed_count++))
    return 0
  fi
  
  # Nếu tất cả các cách không thành công, thông báo lỗi
  echo "  ❌ Không thể thêm buildFeatures { buildConfig true } vào $build_gradle"
  return 1
}

echo "=== Bắt đầu cập nhật buildFeatures cho tất cả thư viện Flutter ==="

# Xử lý các file build.gradle trong pub cache
for build_gradle in $(find ~/.pub-cache/hosted/pub.dev/ -path "*/android/build.gradle" | grep -v "/example/"); do
  add_build_config "$build_gradle"
done
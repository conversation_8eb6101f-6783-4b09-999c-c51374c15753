<!DOCTYPE html>
<html lang="vi">

  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://unpkg.com/trackasia-gl@2.0.1/dist/trackasia-gl.js"></script>
    <link href="https://unpkg.com/trackasia-gl@2.0.1/dist/trackasia-gl.css" rel="stylesheet" />
    <script src="https://unpkg.com/deck.gl@8.9.36/dist.min.js"></script>
    <script src="https://unpkg.com/@trackasia/wind-gl@2.0.0/dist/wind-gl.min.js"></script>

    <style>
      body {
        margin: 0;
        padding: 0;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      }

      #map {
        position: absolute;
        top: 0;
        bottom: 0;
        width: 100%;
      }

      .map-overlay {
        display: none;
        background: rgba(255, 255, 255, 0.95);
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        font-size: 12px;
        padding: 15px;
        position: absolute;
        top: 20px;
        right: 20px;
        width: 280px;
        backdrop-filter: blur(10px);
      }

      .legend {
        margin-bottom: 15px;
      }

      .temperature-legend-bar,
      .rainfall-legend-bar,
      .wind-legend-bar {
        height: 20px;
        width: 100%;
        border-radius: 4px;
        margin: 10px 0;
      }

      .temperature-legend-bar {
        background: linear-gradient(to right,
            rgb(0, 0, 150) 0%,
            rgb(0, 0, 200) 20%,
            rgb(0, 255, 0) 50%,
            rgb(173, 255, 47) 60%,
            rgb(255, 255, 0) 70%,
            rgb(255, 200, 0) 76%,
            rgb(255, 165, 0) 80%,
            rgb(255, 100, 0) 84%,
            rgb(255, 69, 0) 88%,
            rgb(255, 0, 0) 94%,
            rgb(128, 0, 0) 100%);
      }

      .rainfall-legend-bar {
        background: linear-gradient(to right,
            rgba(255, 255, 255, 0) 0%,
            rgb(195, 228, 254) 12.5%,
            rgb(90, 154, 226) 25%,
            rgb(60, 113, 236) 37.5%,
            rgb(11, 66, 168) 50%,
            rgb(12, 0, 102) 62.5%,
            rgb(198, 30, 0) 75%,
            rgb(96, 0, 0) 87.5%);
      }

      .wind-legend-bar {
        background: linear-gradient(to right,
            rgb(100, 100, 100) 0%,
            rgb(120, 120, 120) 30%,
            rgb(140, 140, 140) 60%,
            rgb(160, 160, 160) 100%);
      }

      .legend-labels {
        display: flex;
        justify-content: space-between;
        font-size: 10px;
        color: #666;
        margin-bottom: 5px;
        /* Adjusted margin */
      }

      .controls {
        margin-top: 15px;
      }

      .control-group {
        margin-bottom: 10px;
      }

      label {
        display: block;
        margin-bottom: 5px;
        font-weight: 600;
        color: #333;
      }

      input[type="range"] {
        width: 100%;
        margin-bottom: 5px;
      }

      .status {
        font-size: 11px;
        color: #666;
        margin-top: 10px;
        padding-top: 10px;
        border-top: 1px solid #eee;
      }

      .loading {
        display: none;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 10px 20px;
        border-radius: 4px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 1000;
      }

      button {
        background: #007cbf;
        color: white;
        border: none;
        padding: 8px 12px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 11px;
        margin: 2px;
      }

      button:hover {
        background: #005a8b;
      }

      button:disabled {
        background: #ccc;
        cursor: not-allowed;
      }
    </style>
  </head>

  <body>
    <div id="map"></div>
    <div id="loading" class="loading">Đang tải dữ liệu...</div>

    <div class="map-overlay">
      <!-- Map type selector ẩn vì WebView chỉ dành cho wind -->
      <div class="control-group" style="display: none;">
        <label>Chọn loại bản đồ:</label>
        <input type="radio" id="map-type-temperature" name="map-type" value="temperature">
        <label for="map-type-temperature" style="display: inline; margin-right: 10px;">Nhiệt độ</label>
        <input type="radio" id="map-type-rainfall" name="map-type" value="rainfall">
        <label for="map-type-rainfall" style="display: inline; margin-right: 10px;">Lượng mưa</label>
        <input type="radio" id="map-type-wind" name="map-type" value="wind" checked>
        <label for="map-type-wind" style="display: inline;">Gió</label>
      </div>

      <!-- Ẩn temperature và rainfall legends -->
      <div id="temperature-legend" class="legend" style="display: none;">
        <h3 style="margin: 0 0 10px 0; color: #333;">🌡️ Bản đồ nhiệt độ</h3>
        <div class="legend-labels">
          <span>Lạnh cực độ</span>
          <span>Ấm</span>
          <span>Cực điểm</span>
        </div>
        <div class="temperature-legend-bar"></div>
        <div class="legend-labels">
          <span>-5°C</span>
          <span>25°C</span>
          <span>45°C</span>
        </div>
      </div>

      <div id="rainfall-legend" class="legend" style="display: none;">
        <h3 style="margin: 0 0 10px 0; color: #333;">🌧️ Bản đồ lượng mưa</h3>
        <div class="rainfall-legend-bar"></div>
        <div class="legend-labels">
          <span>(mm) 0</span>
          <span>1</span>
          <span>3</span>
          <span>7</span>
          <span>15</span>
          <span>25</span>
          <span>50</span>
          <span>100</span>
          <span>200</span>
          <span>1000</span>
        </div>
      </div>

      <!-- Wind legend - luôn hiển thị -->
      <div id="wind-legend" class="legend">
        <h3 style="margin: 0 0 10px 0; color: #333;">💨 Bản đồ gió</h3>
        <div class="legend-labels">
          <span>Nhẹ</span>
          <span>Trung bình</span>
          <span>Mạnh</span>
        </div>
        <div class="wind-legend-bar"></div>
        <div class="legend-labels">
          <span>0 m/s</span>
          <span>10 m/s</span>
          <span>25+ m/s</span>
        </div>
        
        <!-- Thêm controls cho gió và mây -->
        <div class="controls" style="margin-top: 15px;">
          <button id="toggle-btn" style="margin-right: 5px;">👁️ Ẩn/Hiện</button>
          <button id="toggle-clouds-btn">☁️ Ẩn mây</button>
        </div>
      </div>

      <div class="status">
        <div>Trạng thái: <span id="status">Đang tải...</span></div>
        <div style="margin-top: 8px; font-size: 11px; color: #666; font-style: italic;">
          ⏱️ Timeline điều khiển từ Flutter Panel
        </div>
      </div>
      <hr>
      <div class="status">
        <div>Trạng thái: <span id="status">Đang tải...</span></div>
      </div>
    </div>

    </div>

    <script>
      const rainfallTimeSteps = [];
      const temperatureTimeSteps = [];
      const weatherPointsTimeSteps = [];
      const hostUrl = "https://dev.gis-api.hainong.vn";

      const pad = (n) => n.toString().padStart(2, '0');
      const now = new Date();
      for (let day_diff = 0; day_diff <= 6; day_diff++) {
        const date = new Date(now);
        date.setDate(now.getDate() + day_diff);
        const day = pad(date.getDate());
        const month = pad(date.getMonth() + 1);
        const year = date.getFullYear().toString().slice(-2);
        for (let hour_index = 0; hour_index <= 7; hour_index++) {
          const hour = pad(hour_index * 3 + 1);
          const time_step = `${day}-${month}-${year}_${hour}`;
          rainfallTimeSteps.push(time_step);
          temperatureTimeSteps.push(time_step);
          weatherPointsTimeSteps.push(time_step);
        }
      }

      const iconUrlBase = "https://dev.gis-api.hainong.vn/demo/weather_maps/icons/svg/";
      const preloadIcons = async (map) => {
        const existingIcons = new Set(map.listImages());

        const iconNames = [
          '01n', '03d', '04n', '10d', '11n', '50d',
          '02d', '03n', '09d', '10n', '13d', '50n',
          '01d', '02n', '04d', '09n', '11d', '13n'
        ];

        for (const iconName of iconNames) {
          if (!existingIcons.has(iconName)) {
            const url = `${iconUrlBase}${iconName}.svg`;
            try {
              createMapGLIcon(url, 24, 24).then(imageData => {
                map.addImage(iconName, imageData);
              });
            } catch (err) {
              console.error(`Không thể tải icon '${iconName}':`, err);
            }
          }
        }
      };

      const createMapGLIcon = (imageFile, width, height) => {
        return new Promise((resolve, reject) => {
          const img = new Image(width, height);
          img.onload = () => {
            resolve(img);
          };
          img.onerror = reject;
          img.src = imageFile;
        });
      };


      class WeatherMap {
        constructor() {
          const now = new Date();
          const day = pad(now.getDate());
          const month = pad(now.getMonth() + 1);
          const year = now.getFullYear().toString().slice(-2);
          const hour = pad(now.getHours());
          const currentTimeStep = `${day}-${month}-${year}_${hour}`;
          this.map = null;
          this.currentWeatherType = 'temperature'; // 'temperature' or 'rainfall'
          this.currentTimestamp = currentTimeStep;
          this.isVisible = true;
          this.cloudsVisible = true; // Thêm state để điều khiển visibility của mây
          this.timestamps = [];
          this.animationTimeout = null;

          this.init();
        }

        async init() {
          await this.initMap();
          this.updateTimestampsByType(this.currentWeatherType);
          this.setupControls();

          this.map.on('load', async () => {
            await this.loadWeatherData(this.currentWeatherType, this.currentTimestamp);
            await preloadIcons(this.map);
          });
        }

        async initMap() {
          this.map = new trackasiagl.Map({
            hash: true,
            container: 'map',
            style: 'https://maps.hainong.vn/styles/v1/streets.json?key=public_key',
            center: [106.0, 16.0], 
            zoom: 5,
            maxZoom: 18,
            minZoom: 2
          });
          // DeckGL
          const deckOverlay = new deck.MapboxOverlay({
            id: 'wind-particle',
            interleaved: true,
            layers: []
          });

          this.deckOverlay = deckOverlay;

          this.map.on('load', () => {
            this.map.addSource('weather', {
              "type": "vector",
              "tiles": [`${hostUrl}/weather_tiles/{z}/{x}/{y}.pbf`],
              "maxzoom": 11,
              "minzoom": 3
            });

            this.map.addLayer({
              "id": "weather_points",
              "type": "symbol",
              "source": "weather",
              "source-layer": "weather_points",
              "filter": ["==", ["get", "time_step"], this.currentTimestamp],
              "layout": {
                "icon-image": ["get", "icon"],
                // "text-field": ["concat", ["get", "feels_like_day"], "°C"],
                "icon-size": 1, // Size of the icon
                "icon-allow-overlap": false,
                "visibility": this.cloudsVisible ? "visible" : "none" // Thêm điều khiển visibility
              }
            });

            this.map.addControl(deckOverlay);
          });
        }

        updateTimestampsByType(weatherType) {
          const selectElement = document.getElementById('timestamp-select');
          selectElement.innerHTML = '';
          this.timestamps = weatherType === 'rainfall' ? rainfallTimeSteps : temperatureTimeSteps;
          this.currentTimestamp = this.timestamps[0];
          this.timestamps.forEach(layerId => {
            const option = document.createElement('option');
            option.value = layerId;
            option.textContent = layerId;
            if (layerId === this.currentTimestamp) {
              option.selected = true;
            }
            selectElement.appendChild(option);
          });
          selectElement.value = this.currentTimestamp;
        }

        async loadWeatherData(weatherType, timestamp) {
          this.showLoading(true);
          this.updateStatus('Đang tải...');
          try {
            this.currentWeatherType = weatherType;
            this.currentTimestamp = timestamp;
            await this.updateWeatherLayerVisibility(weatherType, timestamp);
            this.updateStatus('Hoạt động');
            this.updateLegendVisibility();
          } catch (error) {
            console.error('Failed to load weather data:', error);
            this.updateStatus('Lỗi: ' + error.message);
          } finally {
            this.showLoading(false);
          }
        }

        async updateWeatherLayerVisibility(weatherType, timestamp) {
          ['temperature', 'rainfall'].forEach(layerId => {
            try {
              const layer = this.map.getLayer(layerId);
              if (layer) {
                const isVisible = this.isVisible && (layerId === weatherType);
                this.map.setLayoutProperty(layerId, 'visibility', isVisible ? 'visible' : 'none');
              }
            } catch (error) {
              console.warn(`Layer ${layerId} not found or error setting visibility:`, error);
            }
          });

          // Cập nhật visibility của weather_points (mây) riêng biệt
          try {
            const weatherPointsLayer = this.map.getLayer('weather_points');
            if (weatherPointsLayer) {
              this.map.setLayoutProperty('weather_points', 'visibility', this.cloudsVisible ? 'visible' : 'none');
              console.log('Weather points visibility set to:', this.cloudsVisible);
            } else {
              console.warn('Layer weather_points not found in updateWeatherLayerVisibility');
            }
          } catch (error) {
            console.error('Error setting weather_points visibility:', error);
          }

          // Cập nhật filters
          try {
            const weatherTypeLayer = this.map.getLayer(weatherType);
            if (weatherTypeLayer) {
              this.map.setFilter(weatherType, ['==', ['get', 'time_step'], timestamp]);
            }
            
            const weatherPointsLayer = this.map.getLayer('weather_points');
            if (weatherPointsLayer) {
              this.map.setFilter('weather_points', ['==', ['get', 'time_step'], timestamp]);
            }
          } catch (error) {
            console.error('Error setting filters:', error);
          }
          
          this.updateWindLayer(timestamp);
        }

        async updateWindLayer(timestamp) {
          const image = `${hostUrl}/weather_tiles/wind/${timestamp}.png`;
          const imageMeta = await fetch(`${hostUrl}/weather_tiles/wind/${timestamp}.json`)
            .then(response => response.json())
            .catch(err => {
              console.error('Failed to load wind layer metadata:', err);
              return null;
            });
          const bounds = imageMeta ? imageMeta.bounds : [102, 8.4, 109.6, 23.5];
          const uRange = [imageMeta.uMin, imageMeta.uMax];
          const vRange = [imageMeta.vMin, imageMeta.vMax];

          this.deckOverlay.setProps({
            layers: [
              new WindGL.ParticleLayer({
                id: 'particle',
                image,
                uRange,
                vRange,
                bounds,
                numParticles: 500,
                maxAge: 15,
                speedFactor: 80,
                width: 2,
                color: [100, 100, 100],
                opacity: 0.4,
                animate: true,
                // getPolygonOffset: () => [0, -1000],
              }),
            ]
          });
        }

        setupControls() {
          const opacitySlider = document.getElementById('opacity');
          const opacityValue = document.getElementById('opacity-value');

          opacitySlider.addEventListener('input', (e) => {
            const opacity = e.target.value / 100;
            opacityValue.textContent = e.target.value + '%';

            if (this.map.getLayer(this.currentWeatherType)) {
              this.map.setPaintProperty(this.currentWeatherType, 'fill-opacity', opacity);
            }
          });

          document.getElementById('timestamp-select').addEventListener('change', (e) => {
            this.loadWeatherData(this.currentWeatherType, e.target.value);
          });

          document.getElementById('run-btn').addEventListener('click', () => {
            this.runAnimation();
          });

          document.getElementById('stop-btn').addEventListener('click', () => {
            this.stopAnimation();
          });

          document.getElementById('toggle-btn').addEventListener('click', () => {
            this.toggleVisibility();
          });

          // Thêm event listener cho button toggle mây
          document.getElementById('toggle-clouds-btn').addEventListener('click', () => {
            this.toggleCloudsVisibility();
          });

          document.querySelectorAll('input[name="map-type"]').forEach(radio => {
            radio.addEventListener('change', (e) => {
              this.updateTimestampsByType(e.target.value);
              this.isVisible = true;
              this.loadWeatherData(e.target.value, this.currentTimestamp);
            });
          });
        }

        updateLegendVisibility() {
          document.getElementById('temperature-legend').style.display =
            this.currentWeatherType === 'temperature' ? 'block' : 'none';
          document.getElementById('rainfall-legend').style.display =
            this.currentWeatherType === 'rainfall' ? 'block' : 'none';
        }


        async runAnimation() {
          this.stopAnimation();
          let i = this.timestamps.indexOf(this.currentTimestamp);
          const interval = 1000;
          const animate = async () => {
            this.currentTimestamp = this.timestamps[i];
            document.getElementById('timestamp-select').value = this.currentTimestamp;
            await this.loadWeatherData(this.currentWeatherType, this.currentTimestamp);
            i = (i + 1) % this.timestamps.length;
            this.animationTimeout = setTimeout(animate, interval);
          };
          this.animationTimeout = setTimeout(animate, interval);
        }

        stopAnimation() {
          clearTimeout(this.animationTimeout);
        }

        toggleVisibility() {
          this.isVisible = !this.isVisible;
          this.updateWeatherLayerVisibility(this.currentWeatherType, this.currentTimestamp);
          document.getElementById('toggle-btn').textContent =
            this.isVisible ? '👁️ Ẩn/Hiện' : '👁️ Hiện';
        }

        // Thêm method để toggle visibility của mây
        toggleCloudsVisibility() {
          console.log('toggleCloudsVisibility called, current state:', this.cloudsVisible);
          this.cloudsVisible = !this.cloudsVisible;
          
          try {
            // Kiểm tra xem layer weather_points có tồn tại không
            const layer = this.map.getLayer('weather_points');
            if (layer) {
              this.map.setLayoutProperty('weather_points', 'visibility', this.cloudsVisible ? 'visible' : 'none');
              console.log('Weather icons visibility toggled to:', this.cloudsVisible);
              
              const toggleBtn = document.getElementById('toggle-clouds-btn');
              if (toggleBtn) {
                toggleBtn.textContent = this.cloudsVisible ? '☁️ Hiện mây' : '☁️ Ẩn mây';
              }
            } else {
              console.warn('Layer weather_points not found in toggleCloudsVisibility');
            }
          } catch (error) {
            console.error('Error in toggleCloudsVisibility:', error);
          }
        }

        showLoading(show) {
          document.getElementById('loading').style.display = show ? 'block' : 'none';
        }

        updateStatus(status) {
          const statusElement = document.querySelectorAll('#status');
          statusElement.forEach(el => el.textContent = status);
        }
      }

      const weatherMap = new WeatherMap();

      // JavaScript Bridge để nhận dữ liệu từ Flutter
      window.updateWeatherFromFlutter = function(data) {
        console.log('Nhận dữ liệu từ Flutter:', data);
        
        // Cập nhật thông tin thời tiết trên UI
        updateWeatherUI(data);
        
        // Cập nhật các điểm thời tiết trên bản đồ
        if (data.locations && weatherMap.map) {
          updateWeatherLocations(data.locations);
        }
      };

      // JavaScript Bridge để điều khiển visibility của mây từ Flutter
      window.toggleWeatherIconsVisibility = function() {
        if (weatherMap) {
          weatherMap.toggleCloudsVisibility();
        }
      };

      // JavaScript Bridge để set visibility của mây từ Flutter
      window.setWeatherIconsVisibility = function(visible) {
        console.log('setWeatherIconsVisibility called with:', visible);
        if (weatherMap && weatherMap.map) {
          try {
            // Kiểm tra xem layer weather_points có tồn tại không
            const layer = weatherMap.map.getLayer('weather_points');
            if (layer) {
              weatherMap.cloudsVisible = visible;
              weatherMap.map.setLayoutProperty('weather_points', 'visibility', visible ? 'visible' : 'none');
              console.log('Weather icons visibility set to:', visible);
              
              // Debug: Kiểm tra source và features
              const source = weatherMap.map.getSource('weather');
              if (source) {
                console.log('Weather source found:', source);
                // Kiểm tra features trong weather_points layer
                const features = weatherMap.map.querySourceFeatures('weather', {
                  sourceLayer: 'weather_points'
                });
                console.log('Weather points features count:', features.length);
                if (features.length > 0) {
                  console.log('Sample weather point feature:', features[0]);
                } else {
                  console.warn('No weather points features found in source');
                }
              } else {
                console.warn('Weather source not found');
              }
              
              // Cập nhật button text nếu có
              const toggleBtn = document.getElementById('toggle-clouds-btn');
              if (toggleBtn) {
                toggleBtn.textContent = visible ? '☁️ Hiện mây' : '☁️ Ẩn mây';
              }
            } else {
              console.warn('Layer weather_points not found');
            }
          } catch (error) {
            console.error('Error setting weather icons visibility:', error);
          }
        } else {
          console.warn('weatherMap or weatherMap.map not available');
        }
      };

      // Cập nhật UI thời tiết
      function updateWeatherUI(data) {
        // Cập nhật trạng thái
        const statusElements = document.querySelectorAll('#status');
        statusElements.forEach(el => {
          el.textContent = `${data.temperature}°C - ${data.condition}`;
          el.style.color = data.color || '#333';
          el.style.fontWeight = 'bold';
        });

        // Thêm thông tin chi tiết vào overlay
        const overlay = document.querySelector('.map-overlay');
        
        // Xóa thông tin cũ
        const oldInfo = overlay.querySelector('#flutter-weather-info');
        if (oldInfo) oldInfo.remove();

        // Tạo danh sách thành phố
        let locationsList = '';
        if (data.locations && data.locations.length > 0) {
          locationsList = data.locations.map(loc => 
            `<div style="display: flex; justify-content: space-between; margin: 3px 0; padding: 3px; background: rgba(255,255,255,0.5); border-radius: 3px;">
              <span>${loc.name}</span>
              <span style="font-weight: bold; color: ${data.color};">${loc.temp}°C</span>
            </div>`
          ).join('');
        }

        // Thêm thông tin mới
        const infoDiv = document.createElement('div');
        infoDiv.id = 'flutter-weather-info';
        infoDiv.innerHTML = `
          <hr>
          <h4 style="margin: 10px 0; color: ${data.color || '#333'};">📱 Dữ liệu từ Flutter</h4>
          <div style="font-size: 12px; margin-bottom: 10px;">
            <div style="margin: 2px 0;">🌡️ Nhiệt độ: <span style="font-weight: bold; color: ${data.color};">${data.temperature}°C</span></div>
            <div style="margin: 2px 0;">💧 Độ ẩm: ${data.humidity}%</div>
            <div style="margin: 2px 0;">🌬️ Tốc độ gió: ${data.windSpeed} km/h</div>
            <div style="margin: 2px 0;">☀️ Tình trạng: ${data.condition}</div>
            ${data.timestamp ? `<div style="margin: 2px 0; color: #666; font-size: 10px;">🕐 Cập nhật: ${new Date(data.timestamp).toLocaleTimeString('vi-VN')}</div>` : ''}
          </div>
          ${locationsList ? `
            <div style="margin-top: 10px;">
              <h5 style="margin: 5px 0; color: #333;">🏙️ Các thành phố:</h5>
              <div style="font-size: 11px; max-height: 150px; overflow-y: auto;">
                ${locationsList}
              </div>
            </div>
          ` : ''}
        `;
        
        // Thêm hiệu ứng fade in
        infoDiv.style.opacity = '0';
        infoDiv.style.transition = 'opacity 0.5s ease-in-out';
        overlay.appendChild(infoDiv);
        
        // Trigger fade in
        setTimeout(() => {
          infoDiv.style.opacity = '1';
        }, 50);
      }

      // Cập nhật các điểm thời tiết trên bản đồ
      function updateWeatherLocations(locations) {
        locations.forEach(location => {
          // Tạo marker cho mỗi điểm
          const marker = document.createElement('div');
          marker.className = 'weather-marker';
          marker.innerHTML = `
            <div style="
              background: white;
              padding: 5px;
              border-radius: 5px;
              box-shadow: 0 2px 5px rgba(0,0,0,0.3);
              font-size: 10px;
              text-align: center;
              cursor: pointer;
            ">
              <div>${location.name}</div>
              <div style="font-weight: bold; color: #e74c3c;">${location.temp}°C</div>
            </div>
          `;
          
          marker.addEventListener('click', () => {
            if (window.FlutterBridge) {
              window.FlutterBridge.postMessage(`locationClicked:${location.name}`);
            }
          });
          console.log(`Marker cho ${location.name}: ${location.temp}°C tại [${location.lat}, ${location.lng}]`);
        });
      }

    </script>
  </body>

</html>
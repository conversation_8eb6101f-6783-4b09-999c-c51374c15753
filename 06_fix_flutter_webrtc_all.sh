#!/bin/bash

# Script để sửa lỗi trong flutter_webrtc-0.12.12+hotfix.1
# Thay thế toàn bộ file SurfaceTextureRenderer.java bằng phiên bản đúng

echo "==============================================="
echo "Script sửa lỗi Flutter WebRTC 0.12.12+hotfix.1"
echo "==============================================="

# Tìm .pub-cache của người dùng
find_pub_cache() {
    local possible_paths=(
        "$HOME/.pub-cache"
        "$USERPROFILE/.pub-cache"
        "$APPDATA/Pub/Cache"
        "$PUB_CACHE"
    )
    
    for path in "${possible_paths[@]}"; do
        if [ -d "$path" ]; then
            echo "$path"
            return
        fi
    done
    echo ""
}

# Tìm pub-cache
PUB_CACHE=$(find_pub_cache)

if [ -z "$PUB_CACHE" ]; then
    echo "Không tìm thấy thư mục .pub-cache. Vui lòng nhập đường dẫn đến .pub-cache:"
    read -r PUB_CACHE
    
    if [ ! -d "$PUB_CACHE" ]; then
        echo "Đường dẫn không hợp lệ. Thoát."
        exit 1
    fi
fi

echo "Sử dụng .pub-cache: $PUB_CACHE"

# Đường dẫn đến file cần sửa
TARGET_FILE="$PUB_CACHE/hosted/pub.dev/flutter_webrtc-0.12.12+hotfix.1/android/src/main/java/com/cloudwebrtc/webrtc/SurfaceTextureRenderer.java"

# Nếu không tìm thấy ở vị trí mặc định, tìm kiếm trong toàn bộ .pub-cache
if [ ! -f "$TARGET_FILE" ]; then
    echo "Tìm kiếm file SurfaceTextureRenderer.java trong .pub-cache..."
    FOUND_FILES=$(find "$PUB_CACHE" -name "SurfaceTextureRenderer.java" -path "*flutter_webrtc*" 2>/dev/null)
    
    if [ -z "$FOUND_FILES" ]; then
        echo "Không tìm thấy file SurfaceTextureRenderer.java trong .pub-cache."
        exit 1
    fi
    
    # Sử dụng file đầu tiên tìm thấy
    TARGET_FILE=$(echo "$FOUND_FILES" | head -n 1)
    echo "Tìm thấy file: $TARGET_FILE"
fi

# Tạo bản sao lưu
cp "$TARGET_FILE" "${TARGET_FILE}.backup"
echo "Đã tạo bản sao lưu tại: ${TARGET_FILE}.backup"

# Tạo nội dung chính xác cho file SurfaceTextureRenderer.java
cat > "$TARGET_FILE" << 'EOF'
package com.cloudwebrtc.webrtc;

import android.graphics.SurfaceTexture;
import android.view.Surface;

import org.webrtc.EglBase;
import org.webrtc.EglRenderer;
import org.webrtc.GlRectDrawer;
import org.webrtc.RendererCommon;
import org.webrtc.ThreadUtils;
import org.webrtc.VideoFrame;

import java.util.concurrent.CountDownLatch;

import io.flutter.view.TextureRegistry;

/**
 * Display the video stream on a Surface.
 * renderFrame() is asynchronous to avoid blocking the calling thread.
 * This class is thread safe and handles access from potentially three different threads:
 * Interaction from the main app in init, release and setMirror.
 * Interaction from C++ rtc::VideoSinkInterface in renderFrame.
 * Interaction from SurfaceHolder lifecycle in surfaceCreated, surfaceChanged, and surfaceDestroyed.
 */
public class SurfaceTextureRenderer extends EglRenderer {
  // Callback for reporting renderer events. Read-only after initilization so no lock required.
  private RendererCommon.RendererEvents rendererEvents;
  private final Object layoutLock = new Object();
  private boolean isRenderingPaused;
  private boolean isFirstFrameRendered;
  private int rotatedFrameWidth;
  private int rotatedFrameHeight;
  private int frameRotation;

  /**
   * In order to render something, you must first call init().
   */
  public SurfaceTextureRenderer(String name) {
    super(name);
  }

  public void init(final EglBase.Context sharedContext,
                   RendererCommon.RendererEvents rendererEvents) {
    init(sharedContext, rendererEvents, EglBase.CONFIG_PLAIN, new GlRectDrawer());
  }

  /**
   * Initialize this class, sharing resources with |sharedContext|. The custom |drawer| will be used
   * for drawing frames on the EGLSurface. This class is responsible for calling release() on
   * |drawer|. It is allowed to call init() to reinitialize the renderer after a previous
   * init()/release() cycle.
   */
  public void init(final EglBase.Context sharedContext,
                   RendererCommon.RendererEvents rendererEvents, final int[] configAttributes,
                   RendererCommon.GlDrawer drawer) {
    ThreadUtils.checkIsOnMainThread();
    this.rendererEvents = rendererEvents;
    synchronized (layoutLock) {
      isFirstFrameRendered = false;
      rotatedFrameWidth = 0;
      rotatedFrameHeight = 0;
      frameRotation = -1;
    }
    super.init(sharedContext, configAttributes, drawer);
  }
  @Override
  public void init(final EglBase.Context sharedContext, final int[] configAttributes,
                   RendererCommon.GlDrawer drawer) {
    init(sharedContext, null /* rendererEvents */, configAttributes, drawer);
  }
  /**
   * Limit render framerate.
   *
   * @param fps Limit render framerate to this value, or use Float.POSITIVE_INFINITY to disable fps
   *            reduction.
   */
  @Override
  public void setFpsReduction(float fps) {
    synchronized (layoutLock) {
      isRenderingPaused = fps == 0f;
    }
    super.setFpsReduction(fps);
  }
  @Override
  public void disableFpsReduction() {
    synchronized (layoutLock) {
      isRenderingPaused = false;
    }
    super.disableFpsReduction();
  }
  @Override
  public void pauseVideo() {
    synchronized (layoutLock) {
      isRenderingPaused = true;
    }
    super.pauseVideo();
  }
  // VideoSink interface.
  @Override
  public void onFrame(VideoFrame frame) {
    if(surface == null) {
      producer.setSize(frame.getRotatedWidth(),frame.getRotatedHeight());
      surface = producer.getSurface();
      createEglSurface(surface);
    }
    updateFrameDimensionsAndReportEvents(frame);
    super.onFrame(frame);
  }

  private Surface surface = null;

  private TextureRegistry.SurfaceProducer producer;

  public void surfaceCreated(final TextureRegistry.SurfaceProducer producer) {
    ThreadUtils.checkIsOnMainThread();
    this.producer = producer;
    this.producer.setCallback(
            new TextureRegistry.SurfaceProducer.Callback() {
              @Override
              public void onSurfaceCreated() {
                // Do surface initialization here, and draw the current frame.
              }

              @Override
              public void onSurfaceDestroyed() {
                surfaceDestroyed();
              }
            }
    );
  }

  public void surfaceDestroyed() {
    ThreadUtils.checkIsOnMainThread();
    final CountDownLatch completionLatch = new CountDownLatch(1);
    releaseEglSurface(completionLatch::countDown);
    ThreadUtils.awaitUninterruptibly(completionLatch);
    surface = null;
  }

  // Update frame dimensions and report any changes to |rendererEvents|.
  private void updateFrameDimensionsAndReportEvents(VideoFrame frame) {
    synchronized (layoutLock) {
      if (isRenderingPaused) {
        return;
      }
      if (!isFirstFrameRendered) {
        isFirstFrameRendered = true;
        if (rendererEvents != null) {
          rendererEvents.onFirstFrameRendered();
        }
      }
      if (rotatedFrameWidth != frame.getRotatedWidth()
              || rotatedFrameHeight != frame.getRotatedHeight()
              || frameRotation != frame.getRotation()) {
        if (rendererEvents != null) {
          rendererEvents.onFrameResolutionChanged(
                  frame.getBuffer().getWidth(), frame.getBuffer().getHeight(), frame.getRotation());
        }
        rotatedFrameWidth = frame.getRotatedWidth();
        rotatedFrameHeight = frame.getRotatedHeight();
        producer.setSize(rotatedFrameWidth, rotatedFrameHeight);
        frameRotation = frame.getRotation();
      }
    }
  }
}
EOF

echo "Đã hoàn thành sửa file: $TARGET_FILE"
echo "Vui lòng chạy 'flutter clean' và 'flutter pub get' để áp dụng thay đổi." 